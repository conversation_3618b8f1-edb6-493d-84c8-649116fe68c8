const path = require("path");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const glob = require("glob");
const tailwindcss = require("tailwindcss");
const purgecss = require("@fullhuman/postcss-purgecss");
const SymSyncPlugin = require("./utilities/symsync");
const docsFiles = require("./utilities/docs");
const MergeIntoSingleFilePlugin = require("webpack-merge-and-include-globally");

const controller = require("./brands/controller")
const config = require('./brands/brands.config.js');

const projectDir = path.join(__dirname)

const defaultConfig = async (env, argv) => ({
  entry: glob.sync(path.join(projectDir, "components", "**", "index.js")).reduce(
    (acc, path) => {
      const entry_split = path.split("/").reverse();
      let entry = entry_split[1];

      acc[entry] = path;
      return acc;
    },
    { main: path.join(projectDir, "components", "main.js") }
  ),
  output: {
    path: path.join(projectDir, "theme"),
    filename: path.join(".", "assets", "[name].js"),
  },
  cache: true,
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /(node_modules)/,
        use: {
          loader: "babel-loader",
          options: {
            presets: ["@babel/preset-env"],
          },
        },
      },
      {
        test: /\.(sa|sc|c)ss$/,
        use: [
          MiniCssExtractPlugin.loader,
          "css-loader",
          {
            loader: "postcss-loader",
            options: {
              postcssOptions: {
                plugins: [
                  tailwindcss,
                  ...(argv.mode === "production"
                    ? [require("cssnano")({ preset: "default" })]
                    : []),
                ],
              },
            },
          },
          {
            loader: "sass-loader",
            options: {
              sassOptions: {
                includePaths: [path.join(projectDir, "components")],
              },
            },
          },
        ],
      }
    ],
  },
  optimization: {
    runtimeChunk: "single",
    splitChunks: {
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: "vendors",
          chunks: "all",
        },
      },
    },
  },
  plugins: [
    new SymSyncPlugin({
      //debug: true,
      origin: path.join("components", "**", "*.liquid"),
      destination: path.join("theme", "[parentDir]", "[file]"),
      host: 'destination'
    }),
    new MiniCssExtractPlugin({
      filename: path.join(".", "assets", "[name].css"),
    }),
    ...(argv.mode === "production" ? [new MergeIntoSingleFilePlugin({
      files: {
        "./assets/README.md": await docsFiles
      }
    })] : []),
    // new BundleAnalyzerPlugin(),
  ],
});


// This is your opportunity to override the default Neptune configuration

const newConfig = async (env, argv) => {
	const configObj = await defaultConfig(env, argv)
	const newEntry = await setBrandEntry()
	
	async function setBrandEntry() {
		const projectDir = __dirname
		const brand = await controller.getCurrentBrand()
		const brandData = config[brand]
		const brandPath = brandData.entry_point.replace('./', '').split('/')

		return configObj.entry = glob.sync(path.join(projectDir,"components","**","index.js")).reduce(
			(acc, pathname) => {
				const entry_root = path.parse(pathname)
				let entry = entry_root.dir.split(entry_root.root).reverse()[0];

				acc[entry] = pathname;
				return acc;
			},
			{ main: path.join(projectDir, ...brandPath) }
		)
	}

	configObj.entry = newEntry

	return configObj
}

module.exports = newConfig
