# archipelago-caffeinated-theme
A utility-first component based modern Shopify theme using Shopify CLI, Tailwind CSS, AlpineJS and Utility Functions.

## Working in this theme

When working within the Archipelago shared theme, there are a few important (albeit opinionated) concepts to understand, and some strict dos+don’ts to adhere to.

### 🍦 Vanilla Over Library

Use the latest stable version of **ECMAScript** with maximum browser support. No libraries are utilized for shortcutting native functionality (no jquery allowed). Exceptions to these rules include Alpine.js (and an associated dedicated controller) for templating, Quicklink for smart prefetching,  and Swiper.js for carousels. Any proposition of importing additional libraries should be examined against the core mission and vision of the site, where simplicity and minimalism of codebase are top priorities.

### 🛠️ Utility-First functionality

A fundamental concept in the development of the Archipelago site, is the practice of examining any site feature, and picking apart into what the underlying utilities of that feature truly are. By thinking in this way, we create the opportunity to combine utilities into features in a way that maintains the utilities availability for future feature development and administrative flexibility. 

<aside>
💡 **Example**: Think of a “Back in Stock” feature, as simply a button and a modal, each which may access global functions, but are not built into a canned feature.

</aside>

### ✌️ Separation of Template Framework and Model / Controller

While the Archipelago theme relies on Alpine for templating, Alpine is intentionally used strictly within templates, and not referenced in any other controller. This allows for the core controllers of the site to remain agnostic to templating engine, and should facilitate an easier transition to other templating libraries in the future should that become necessary, and also allow for one-off implementations when necessary that use an alternate templating library.

### 🌐 Global Models + Controllers

Models and controllers for any feature or component are evaluated against their potential benefit to be accessed by outside features. If there is any potential benefit, the model or controller is made to be “global”, by assigning it as a property of the window. Models and controllers are typically paired by the same name with a different case structure.

```jsx
window.cart = { items:[] } // <-- model
window.Cart = { add:items=>{} } // <-- controller
```

Models are data which may be referenced within dynamic theme settings, and for templating. Controllers are typically objects with methods that affect the associated model. In some cases, a controller exists without a model.

```jsx
Modal.open('cart')
```

### 🔀 Internal Functions with External Integrations

A strict principal of the structure of the theme is to create internal functions that emit events rather than integrating any SDK, API, or other vendor-specific functionality into any core global controller. Where an integration to a third party is necessary, rather than adding vendor specific code to a core controller, the function or utility the the vendor provides should be emulated as an internal, vendor agnostic function.

<aside>
💡 **Example**: Rather than integrate to a Back in Stock application or allow its widget to be injected into the site, a `Product.request(product, variant)` method is created as an internal function, which serves to do little more than dispatch events. Then, this function may be shared within the Archipelago brands ubiquitously, regardless of which vendor is serving the backed functionality of notifying the consumer of stock changes.

</aside>

Because integrations and third-party vendors are a revolving door, and may vary by brands using the shared theme, this maintains a scalable and functional core, with peripherals being bolted on, rather than woven in.

### 💾 Programmable Theme

The Archipelago theme features many opportunities to program within the theme. This presents most notably as:

- Liquid logic for inclusion of features
- Liquid for dynamic output
- JavaScript Logic for inclusion of features or conditional constraints
- Javascript Logic for user actions

This concept supports shared components and utility-first functionality, by enabling the store owners to program the components to function as they see fit. The availability of globally-available models and controllers empowers store owners to condition and action features across components.

### 🙌🏾 Shared Components become Utilities

Components are developed to meet specific needs of a feature, but they and their sub-components may be useful for inclusion into other components or features. Anywhere a block or element seems repurposable in other contexts, it should be extracted into a globally-available snippet. Anywhere a piece of JavaScript logic is redundant or repeatable, it should be extracted into a globally-available utility.

### 💙 Styling Strategy + Tailwind CSS

Styles are applied in a specific order by making use of the native cascade and @layers. Styles are applied in the following order, with the higher numbers taking the highest priority of impact 

**Shared Styles** - functional helpers that all brands benefit from

**Brand Globals** - applied first and available as Tailwind @apply

**Brand Helpers** - applied with Brand Globals in Main.scss

**Tailwind Classes** - classes applied to elements may override globals

**Brand Theme** - paint and aesthetic finish that can override Tailwind classes

### 🦓 Z-Index Guide

Base: 1

Stack:
Nested Element requiring modification: 10-20

Popover:
Functionally higher z-index within content: 20

Underlay:
Full Screen Blocking element that lays under header: 30

Sticky:
Header / Sticky Elements: 40

Overlay: 50
Full Screen Blocking element that lays under header: 50

---

## Folder Structure

📂 Project
    📁 brands
    📁 components
    📁 theme
    📁 utilities
    📁 node_modules

### 📂 Brands Folder

The Archipelago theme is a multi brand theme, highly customized and stylized to represent each brand online without aesthetic compromise, with shared functionality available to all. This is accomplished through a brands controller, and brand-specific assets.

☕ **JavaScript** 

Each brand has a JavaScript Entry Point, main.js, as well as it’s own Tailwind config file for brand specific utility class generation.

🎨 **SCSS/CSS**

Each brand has its own scss files for site globals, helpers, theme, and primary main.scss

globals.scss defines all of the core styling for global components on the site. Buttons, form elements, colors, spacing scales, typographic styles, etc.

**helpers.scss** provides functional utilities 

**main.scss** brings helpers and globals together

**theme.scss** adds a layer of paint and adjustment over the top of globals, helpers and  Tailwind utilities.

During deployment, an environment variable defines which brand’s assets are compiled and included. (This is supplemented by a top-level theme setting with a value that matches the brand folder name). 

During development, the Brands Controller CLI may be used to select which brand’s entry point folder should be used at runtime and for in-browser preview.

### 📂 Components Folder

The development folder structure is separated out as components, the individual building blocks of the site. Each component has a named directory. Within the named directory a core index.js file. 

<aside>
💡 At runtime and during build, an `index.js` file at the root of any component where the js file is **written as a module with an export declaration**, is compiled independently into the `theme/assets` directory, with the same name as the component folder. 
`components/cart/index.js` compiles to `theme/assets/cart.js`.

</aside>

### 🔗 Symlinks

Symlinks are used to connect the theme directory to the components directory. All liquid files created in a component are moved into the appropriate theme directory (sections, snippets, etc), and a symbolic link is created in place of the original file. This allows for development to happen on either the file in the theme folder, or the file in the component directory with all updates happening in the same actual file. This removes the requirement for a compiler like webpack to move files at runtime and removes any associated latency impacts on HMR at runtime.

### 📂 Theme Folder

The theme folder follows the standard Shopify theme folder structure. Best practice is for all sections and snippets to originate from components. Liquid Templates should also be originated in components, where JSON Templates should have heir origin in the Shopify theme editor and be downloaded into the code repo during development of features that live within the templates. Liquid Layout files exist only in the theme directory and are not associated with a component.

### 📂 Utilities Folder

The utilities folder contains tools for development and build, including symbolic links, documentation rollup, webpack and tailwind config files.

---

## 🧩 Components

Components are the building blocks of the Archipelago theme. Components may utilize any of the technologies included in the theme. Several kinds of components exist. 

### 🧰 @utilities

The utilities component is a collection of useful javascript utilities that are used throughout the other components of the project. You can think of this as a simplified and proprietary version of lodash or underscore. Utilities are grouped into subject-specific files. 

### 🌐 Controllers

Global controllers like Cart, Products, Collection, etc are created as Components.

### 🧱 Sections

Content-authoring Sections+Blocks or functional and specific Sections+Blocks. Anything from the header, to a universally-available product carousel, to a page-specific user tool.

<aside>
💡 Some robust Components feature a model, controller, section+blocks, and globally-available snippets!

</aside>

Each component *should* have its own README at it’s folder base which describes its core function and any associated methods.

---

## ⚡ Shared Schema Utility

Elements which are shared among components have the same functional model, regardless of their placement, and so require data to be fed to them in a consistent structure. Updates to an element that is used in multiple places requires updates to schema in every section in which it exists. To support this need, add significant efficiencies in the authoring of sections, and provide an identical admin experience across sections, a shared schema utility was developed. 

The shared schema utility provides a means for groups of settings or setting options to be authored in a single place, and then have them included into section schema, much like how a snippet may be included/rendered into a liquid file.

All global schema is either written or imported into the `schema-util.config.js` file in the project root directory. The schema utility config contains a single Object with properties for each piece of schema to be includable in section schema. The value of each property is an array of settings or options (for selects, etc). 

A simple example of defined includable schema is `SectionDisplay`

```jsx
  ...,
  "SectionDisplay": [
    {
      "type": "header",
      "content": "{{ label_prefix }} Settings"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_display",
      "label": "{{ label_prefix }}",
      "options": [
        {
          "value": "",
          "label": "Mobile & Desktop"
        },
        {
          "value": "lg:hidden",
          "label": "Mobile"
        },
        {
          "value": "max-lg:hidden",
          "label": "Desktop"
        }
      ]
    }
  ],
  ...
```

To include schema into a section, a dedicated specialized paragraph setting is added. 

```jsx
   ...
    {
      "type": "paragraph",
      "content": "@include SectionDisplay, label_prefix:Display, id_prefix:wrapper_class"
    },
  ...
```

The syntax for includes can be broken down into three parts:
**@include** - declares an include
**SchemaKey** - the key of the group of settings to be imported
**A comma-separated series of key:value properties** to send along with the include, which can be used to modify the output of the schema inclusion. 

In the example above, the id of the included select will become `wrapper_class_display`, and the label will become `Display`.

🪆 **Nesting** 
Yes, schema includes can be nested. Unlimited levels. You’re welcome.

Settings are generated at runtime when the `watch` script is executed. If you make changes to your schema config, stop and start `watch` again. 

Every setting that is generated by an include is prefixed by @global, so that dynamically generated schema settings can be recognized programmatically and empirically by a developer. However, during build, @global flags are not included, and the original @include setting is removed entirely, providing the ad store owner with a clean and uncluttered admin experience in the theme editor.

⚠️ Always remember to quit the watch command prior to making any commits, unless you like unravelling merge conflicts within schema 🤮

---

## 🪠 Liquid Helpers

### Class Settings

Many theme settings, particularly those generated by shared schema output classes. These are commonly tailwind classes, but can also be other aesthetic or function-defining classes. By including the classes in the schema settings output directly, we avoid having to write conditional statements that would otherwise output a class based on a related setting. 

To take this a step further in efficiency and cleanliness of code, a special snippet called `class-settings.liquid` renders these settings based on a prefix.

```html
<section class="{% render 'class-settings' prefix:'wrapper_class' settings:section.settings %}">
...
</section>
```

In this example, the shared schema in the section above, alsong with any other settings in the section which share an id prefix of `wrapper_class` will be output as a class of the element.

Working in this way requires a 

<aside>
💡 Working in this way provides extreme efficiency and the ability to define aesthetic and functionality from the schema utility config, which requires a heightened level of awareness when coding. 😵‍💫

</aside>

### Style Settings

Very similar to **class settings** are **style settings—** a special liquid snippet that outputs styles based on the keys and values supplied. The main difference between class settings and style settings, is that style settings will not only recognize the setting id by its prefix, but extract the style property from the id as well, with everything after the prefix becoming the property with underscores replaced by hyphens. The  value of the setting becomes the value of the style property.

```html
{
  "type": "color",
  "id": "wrapper_style_background_color",
  "label": "Menu Background Color"
}
```

```html
<section style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}">
...
</section>
```

Becomes

```html
<section style="background-color:#ff99ff;">
...
</section>
```

---

## 🚀 Let’s go.

Now that the prerequisite reading has been taken care of, let’s spin up the theme.

First time, install dependencies by running `yarn`

Then, access the Brands Controller CLI with `yarn brand`

Select the brand you are currently developing against.

Lastly in this terminal window, run `yarn watch`

Open a second terminal window.

Ensure you have the Shopify CLI version 3.x installed.

Run `yarn serve`

Shopify CLI will present an authentication flow and prompts, after which you should be able to preview the theme at [`http://127.0.0.1:9292/`](http://127.0.0.1:9292/)

👋🏾 Don’t forget to make a new branch!