const Cart = {
  api: (endpoint = '', data) => {
    return new Promise(async (resolve, reject) => {
      try {
        const eventName = `Cart:${endpoint || 'get'}`;
        const processedData = await Util.hooks.process(eventName, data);
        
        if (!Util.events.dispatch(eventName, processedData)) {
          resolve(false);
          return;
        }

        const config = {
          method: data ? 'POST' : 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          ...(data && { body: JSON.stringify(processedData) })
        };

        const response = await fetch(
          `${window.Shopify.routes.root}cart${endpoint && endpoint !== 'cart' ? `/${endpoint}` : ''}.js`,
          config
        );

        if (!response.ok) throw new Error(`Cart API error: ${response.status}`);
        const result = await response.json();
        
        resolve(result);
      } catch (error) {
        console.error('Cart API error:', error);
        reject(error);
      }
    });
  },

  get: async () => {
    const cart = await Cart.api();
    await Cart.set(cart);
    return cart;
  },

  set: async (cart) => {
    const processedCart = await Util.hooks.process('Cart:set', cart);
    
    // Process all items in parallel with error handling
    const subtitlePromises = processedCart.items.map(item => 
      Cart.apitoproduct({ id: item.id }, { handle: item.handle })
        .then(subtitle => {
          item.subtitle = subtitle;
          return item;
        })
        .catch(error => {
          console.error(`Error fetching subtitle for ${item.handle}:`, error);
          item.subtitle = null;
          return item;
        })
    );

    await Promise.all(subtitlePromises);
    window.cart = processedCart;
    
    Util.events.dispatch('Cart:set');
    Offers.run();
    
    return processedCart;
  },

  apitoproduct: async (id, handle) => {
    const baseDomain = window.location.hostname;
    // const brand = window.store.brand;
    
    // const domain = baseDomain.includes('.ca') ? '.ca' :
    //               baseDomain.includes('.co.uk') ? '.co.uk' : 
    //               '.com';
                  
    const url = `https://${baseDomain}/products/${handle.handle}?view=metaData`;

    const response = await fetch(url, {
      headers: { 'Content-Type': 'application/json' }
    });

    if (!response.ok) throw new Error(`Product API error: ${response.status}`);
    const data = await response.json();
    return data.subtitle;
  },

  add: async (items) => {
    const normalizedItems = (Array.isArray(items) ? items : [items])
      .map(item => {
        const normalized = {
          id: item.id || item,
          quantity: item.quantity || 1,
          properties: item.properties || {}
        };

        if (normalized.properties['selling_plan']) {
          normalized.selling_plan = normalized.properties['selling_plan'];
          delete normalized.properties['selling_plan'];
        }

        return normalized;
      })
      .filter(item => item.id && Util.events.dispatch('Cart:addItem', item));

    if (!normalizedItems.length || !Util.events.dispatch('Cart:addItems', normalizedItems)) {
      return false;
    }

    const result = await Cart.api('add', { items: normalizedItems });
    
    if (result.status) {
      window.cart.error = result;
      throw result;
    }

    const cart = await Cart.get();
    cart.added = result;

    // Use Promise.all for parallel event dispatching
    await Promise.all([
      Util.events.promise('Cart:itemAdded', result.items[0]),
      Util.events.promise('Cart:itemsAdded', result),
      result.items[0].properties.Bundle ? Offers.accept(result.items[0].properties.Bundle) : null
    ].filter(Boolean));

    return cart;
  },

  remove: async (remove) => {
    Util.events.dispatch('Cart:removeItem', remove);

    if (typeof remove === 'number' && remove < 1000) {
      const change = { line: remove + 1, quantity: 0 };
      const item = cart.items[remove];
      const updatedCart = await Cart.change(change);
      await Util.events.dispatch('Cart:itemRemoved', item);
      return updatedCart;
    }

    const updates = {};
    (Array.isArray(remove) ? remove : [remove]).forEach(v => updates[v] = 0);
    const item = cart.items.find(i => i.id == remove);
    const updatedCart = await Cart.update(updates);
    await Util.events.dispatch('Cart:itemRemoved', item);
    return updatedCart;
  },

  change: async (change) => {
    const item = cart.items[change.line - 1];
    const evt = { item, quantity: change.quantity };

    if (!Util.events.dispatch('Cart:change', evt)) return false;
    if (Object.keys(change).length === 0) throw new Error('no change');

    const result = await Cart.api('change', change);
    await Util.events.dispatch('Cart:changed', evt);
    return Cart.get();
  },

  update: async (updates) => {
    if (!Util.events.dispatch('Cart:update', updates)) return false;
    if (Object.keys(updates).length === 0) throw new Error('no updates');

    const cart = await Cart.api('update', { updates });
    return Cart.set(cart);
  },

  attributes: async (attributes) => {
    const cart = await Cart.api('update', { attributes });
    return Cart.set(cart);
  },

  note: (note) => Cart.update({ note })
};

window.Cart = Cart;

export default Cart;