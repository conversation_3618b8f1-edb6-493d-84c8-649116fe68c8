## Collection Controller Technical Documentation

The **Collection Controller** is a comprehensive JavaScript module designed to enhance the functionality of product collection pages on a web application. It facilitates dynamic interactions with remote data sources, implements both paginated and scroll-based pagination, and provides support for filters and sorting options. This documentation provides a detailed overview of how the Collection Controller works and how it can be integrated into a project.

### Overview

The Collection Controller is responsible for managing the presentation and manipulation of product collection data. It offers features such as:

- Fetching collection data from a specified remote URL.
- Paginating data using traditional paginated navigation or infinite scroll.
- Applying filters to narrow down the displayed products.
- Sorting products based on specified criteria.

### Object Structure

#### Global `collection` Object

The `collection` object serves as a container for collection-related data and methods.

##### Properties

- `products` (Array): An array of product objects.
- `filters` (Object): An object containing filter-related information.
- `sort` (String): The current sorting option applied to the collection.
- `pagination` (Object): An object holding pagination-related data.

##### Methods

- `page(page=1)`: Loads a specific page of the collection.
- `filter(key, vals, clear)`: Applies filters to the collection.
- `clear()`: Clears applied filters and sorting.
- `sort(by)`: Applies sorting to the collection.
- `reset()`: Resets the collection data and pagination.
- `load(config)`: Loads collection data based on the provided configuration.
- `init()`: Initializes the Collection Controller upon page load.
- `url(config={})`: Generates the collection URL with applied filters and sorting.
- `map(data, map)`: Maps fetched data to a desired format using a mapping schema.
- `paginate(page)`: Updates pagination information based on the current page.

#### `Collection` Controller

The `Collection` object serves as the main controller, managing collection-related actions.

##### `page(page=1)` Method

The `page` method fetches a specific page of the collection from the remote URL. It performs the following steps:

1. Checks whether the requested page is already loaded.
2. Sends a fetch request to the remote URL with the requested page number and sorting information.
3. Parses the response JSON data.
4. Maps the fetched data using the specified mapping schema.
5. Appends the products to the `collection.products` array.
6. Updates pagination information.

##### `filter(key, vals, clear)` Method

The `filter` method applies filters to the collection. It takes filter key(s) and value(s) as arguments and performs the following steps:

1. Modifies the `collection.filters.applied` object based on the provided filter information.
2. Updates the URL to reflect the applied filters.
3. Dispatches a filter event.
4. Resets the collection and loads the first page.

##### `clear()` Method

The `clear` method clears applied filters and sorting. It performs the following steps:

1. Resets sorting and applied filter options.
2. Clears the URL parameters related to sorting and filters.
3. Resets the collection and loads the first page.

##### `sort(by)` Method

The `sort` method applies sorting to the collection. It takes a sorting option as an argument and performs the following steps:

1. Updates the `collection.sort` property with the specified sorting option.
2. Resets the collection and loads the first page.

##### `reset()` Method

The `reset` method resets the collection data and pagination. It performs the following steps:

1. Clears the `collection.products` array.
2. Resets pagination information.
3. Scrolls to the top of the page.

##### `load(config)` Method

The `load` method fetches collection data based on the provided configuration. It performs the following steps:

1. Sets the `collection.loading` flag to `true`.
2. Constructs the fetch configuration based on remote URL and settings.
3. Sends a fetch request to the remote URL with the provided configuration.
4. Parses the response JSON data and maps it.
5. Updates the collection data, filters, and pagination.
6. Sets the `collection.loading` flag to `false`.

##### `init()` Method

The `init` method initializes the Collection Controller when the DOM is ready. It performs the following steps:

1. Retrieves filter and sorting information from URL parameters.
2. Checks if a specific page is stored in the browser history.
3. Initializes the collection with filters, sorting, and pagination.
4. Loads the initial page of the collection.

##### `url(config={})` Method

The `url` method generates the collection URL with applied filters and sorting. It performs the following steps:

1. Constructs the URL based on provided configuration and current filters/sorting.
2. Updates the browser history to reflect the applied filters and sorting.
3. Returns the generated URL.

##### `map(data, map)` Method

The `map` method transforms fetched data using a mapping schema. It performs the following steps:

1. Checks if a mapping schema is provided.
2. Applies the mapping schema to the fetched data.
3. Returns the transformed data.

##### `paginate(page)` Method

The `paginate` method updates pagination information. It performs the following steps:

1. Initializes pagination data if it's not already available.
2. Marks the specified page as loaded.
3. Updates pagination data.

### Conclusion

The Collection Controller offers a robust solution for handling product collection pages with remote data fetching, pagination, filtering, and sorting capabilities. By integrating this controller into your web application, you can provide a seamless and dynamic user experience for navigating and exploring product collections.

Please note that the implementation of this controller may require additional setup, including integrating it with your application's existing utilities and APIs. Ensure that the provided code aligns with your project's structure and requirements before deployment. If you have any further questions or need assistance, feel free to ask.