// render against this data
window.collection = {
  ...window.collection, 
  ...{
    products:[],
    filters:{
      all:[],
      applied:{}
    },
    pagination: {
      pages:[]
    }
  },
  params:{}
};

// base controller
window.Collection = {

  loading:false,
  
  page: (page=1) => {
    if (collection.pagination.pages.filter(p=>p.loaded).map(p=>p.number).includes(page)) return false;
    return Collection.load({page:page,sort:collection.sort||''})
  },
  
  filter: (key, vals, handle = null,selecttype = false , clear) => {
    return new Promise( (res, rej) => {
      // let shouldContinue = true;

      Array.isArray(vals)?vals:[vals].forEach(val => {
        if (!collection.filters.applied[key])collection.filters.applied[key]=[];
        if(selecttype){
          if (collection.filters.applied[key].includes(val)){
            shouldContinue = false;
            return;
          }
          if (collection.filters.applied.hasOwnProperty(key)) {
              collection.filters.applied[key] = [];
            } 
        }
        if (collection.filters.applied[key].includes(val)) {
          collection.filters.applied[key].splice(collection.filters.applied[key].indexOf(val),1)
          let filterOption = collection.filters.all.find(f => f.key === key).options.find(o => o.value === val);
          if (filterOption) {
            filterOption.active = false;
          }

        }
        else {
          collection.filters.applied[key].push(val)
          let filterOption = collection.filters.all.find(f => f.key === key).options.find(o => o.value === val);
          if (filterOption) {
            filterOption.active = true;
          }
        }
        if (collection.filters.applied[key].length == 0)
          delete collection.filters.applied[key];
      })

      // if (!shouldContinue) {
      //   return res(false); // Stop the function if the flag is false
      // }

      if(key == 'pf_c_collection' && handle != null){
        collection.id = vals;
        var newPath = document.location.pathname.replace(/\/collections\/(.*)/, '/collections/' + handle.toLowerCase());

        // Update the URL
        window.history.replaceState(history.state, null, `${newPath}?${Util.urlparams.build(
          {
            ...Object.fromEntries(Object.entries(collection.filters.applied).map(e => { e[0] = `filter[${e[0]}]`; return e })),
            ...collection.params
          }
        )}`);
      }
      else {
      window.history.replaceState(history.state,null,`${document.location.pathname}?${Util.urlparams.build(
        {
          ...Object.fromEntries(Object.entries(collection.filters.applied).map(e=>{e[0] = `filter[${e[0]}]`; return e})),
          ...collection.params
        }
      )}`)
      }

      Util.events.dispatch('Collection:filter', collection.filters.applied)
      Collection.reset().then( () => {
        Collection.page()
      }).then( () => {
        res(true)
      })
    })
  },

  clear: () => {
    return new Promise( (res, rej) => {
      Object.keys(collection.filters.applied).forEach(key => {
        collection.filters.applied[key].forEach(val => {

          let filterOption = collection.filters.all.find(f => f.key === key).options.find(o => o.value === val);
          if (filterOption) {
            filterOption.active = false;
          }
        });
      });
      collection.sort = ''
      collection.filters.applied = {}

      window.history.replaceState(history.state,null,`${document.location.pathname}?${Util.urlparams.build(
        {
          ...Object.fromEntries(Object.entries(collection.filters.applied).map(e=>{e[0] = `filter[${e[0]}]`; return e})),
          ...collection.params
        }
      )}`)
      Util.events.dispatch('Collection:filter', collection.filters.applied)

      Collection.reset().then( () => {
        Collection.page()
      }).then( () => {
        res(true)
      })
    })
  },

  sort: (by) => {
    return new Promise( (res, rej) => {
      collection.sort = by
      Collection.reset().then( () => {
        Collection.page()
      }).then( () => {
        res(true)
      })
    })
  },

  reset: () => {
    return new Promise( (res, rej) => {
      collection.products = [];
      collection.pagination = {
        pages:[]
      }
      
      const parentSection = document.querySelector('section main a[prefetch]')?.closest('section.section')
      if (typeof(parentSection) != 'undefined' && parentSection != null) {
        let elementPosition = parentSection.getBoundingClientRect().top + window.scrollY;
        elementPosition = elementPosition - 50
        window.scrollTo({
          top: elementPosition,
          behavior: "smooth"
        });
      } else {
        window.scrollTo(0, 0)
      }
      res(true)
    })
  },
  
  load:(config) => {

    collection.loading = true;

    return new Promise( (res, rej) => {

      let fetch_config = {}
      Object.keys(collection.settings.remote).forEach(key=>{
        if (key.includes('config_') && !!collection.settings.remote[key])
          fetch_config[key.replace('config_','')] = Util.literal(collection.settings.remote[key], config)
      })
      if(!!fetch_config.headers && typeof fetch_config.headers == 'string')
      fetch_config.headers = JSON.parse(fetch_config.headers)
      fetch(Collection.url(config),fetch_config)
        .then(response=>response.json())
        .then(data=>Collection.map(data))
        .then(async data => {
          data.products = await Util.hooks.process('Products:enhance', data.products)
          return data
        })
        .then(data=>{
          // wrap this in a hook for endless
          // collection.total_products = data.total_products;
          let loaded = collection.pagination.pages.filter(p=>p.loaded).map(p=>p.number)
          
          data.products = data.products.map((p,i)=>{
            p.page = config.page
            p.index = i
            return p
          })
          
          collection.products = (loaded.length && config.page < Math.min(...loaded))?[...data.products,...collection.products]:[...collection.products,...data.products];
          
          Util.events.dispatch('Collection:products')

          collection.total_products = data.total_products;
          collection.suggestions = data.suggestions;
          collection.filters = {
            all:data.filters.map(set=>{
              if(Array.isArray(set.options)){
                set.options = (set.options).map(option=>{
                  option.active = collection.filters.applied[set.key] && collection.filters.applied[set.key].includes(option.value)
                  return option
                })
                if(set.options.every(o=>!isNaN(o.label))){
                  set.options.sort((a, b) => (Number(a.label) > Number(b.label)) ? 1 : -1)
                }
              }
              return set
            }),
            applied:collection.filters.applied
          };

        })
        .then(()=>{
          Collection.paginate(config.page)
        })
        .then(()=>{
          collection.loading = false;
          setTimeout(()=>{Util.events.dispatch('Templates:render');},10) 

          res(true)
        })

    })

  },
  
  init:() => {

    const params = Object.fromEntries(  
      new URLSearchParams(window.location.search)
    )

    for (key in params) {
      if (key.includes('filter[')) {
        collection.filters.applied[key.split('[')[1].split(']')[0]] = params[key].split(',')
      } else if (key == 'sort') {
        collection.sort = params['sort']
      } else {
        // preserve
        collection.params[key] = params[key]
      }
    }
    
    if(!!window.history.state && !!window.history.state.page) {
      history.scrollRestoration = 'manual';
      Collection.page(window.history.state.page).then(()=>{
        window.scrollTo(0,document.querySelectorAll('[product-grid] article')[window.history.state.index].offsetTop - 100)
        window.history.replaceState({},window.document.title,document.location.pathname)
      })
    } else {
      history.scrollRestoration = 'auto';
      Collection.page(Number(collection.params.page) || 1, !!collection.params.page)
    }

    Util.events.dispatch('Collection:init')

  },

  url: (config={}) => {
    config = {...{sort:collection.sort||'', filters:collection.filters.applied}, ...config}

    // window.history.replaceState(
    //   window.history.state, 
    //   window.document.title, 
    //   `${document.location.pathname}?
    //     ${config.sort?`&sort=${config.sort}`:``}
    //     ${Object.keys(config.filters).length?`${Object.keys(config.filters).map(key=>`&filter[${key}]=${config.filters[key].map(value=>`${value}`).join(`,`)}`).join(``)}`:``}
    //   `.replace(/ /g,'')
    // )
    return Util.literal(collection.settings.remote_url, config)
  },

  map: (data, map) => {

    return new Promise(res=>{

      map = map || collection.settings.map || false;

      if(!map) res(data);

      res(Util.map(data,map))

    })
    
  },

  paginate: page => {

    return new Promise( (res, rej) => {

      collection.pagination.pages = collection.pagination.pages || []

      if(collection.pagination.pages.length==0){
        
        collection.pagination.total_products = collection.total_products;
        collection.pagination.total_pages = Math.ceil(collection.total_products/collection.settings.limit);
        collection.pagination.pages = [...Array(collection.pagination.total_pages).keys()].map(n=>{
          return {
            index: n,
            number: n+1,
            loaded: false
          }
        })
        
      }
      collection.pagination.current_page = page
      
      collection.pagination.pages.find(p=>p.number==page).loaded = true
      
      res(true)  
    })
    
  }

}

document.addEventListener('DOMContentLoaded', Collection.init)


document.addEventListener('DOMContentLoaded', async function() {
  if (!collection.settings) await Util.wait(500)
  if (collection.settings && !collection.settings.sidebar_calculate_height) return false
  // calculateFilterHeight()
  setTimeout(function() {
    // calculateFilterBodyPadding()
    // calculateFilterHeight()
  }, 1000)

  document.querySelector('.collection__sidebar').querySelector('.collection__sidebar-body .collection-filters')?.addEventListener('click', function() {
    calculateFilterHeight()
  })

  let observer = new MutationObserver(mutationRecords => {
    calculateFilterHeight()
    window.dispatchEvent(new Event('resize'));
  });
  observer.observe(document.querySelector('.collection__sidebar').querySelector('.collection__sidebar-body .collection-filters'), {
    childList: true,
    subtree: true,
    characterDataOldValue: true
  });
})

window.addEventListener('resize', function() {
  // calculateFilterBodyPadding()
  // calculateFilterHeight()
})

function calculateFilterBodyPadding() {
  const filterElement = document.querySelector('.collection__sidebar')
  const filterBodyElement = filterElement.querySelector('.collection__sidebar-body')
  const filterCollections = filterBodyElement.querySelector('.collection-filters')

  const distanceFromTop = filterElement.getBoundingClientRect().top
  const vHeight = window.innerHeight
  const topToBottom = vHeight - distanceFromTop
  // filterElement.style.paddingBottom = `${vHeight}px`
  const filterBodySpacing = vHeight + topToBottom
  filterCollections.style.marginBottom = `${vHeight}px`
  // filterElement.style.height = `100%`
  filterBodyElement.style.height = `${vHeight}px`


}

function calculateFilterHeight() {
  
  let aside = document.querySelector('aside[data-modal="filters"]')
  let main = aside.querySelector('main.collection__sidebar-body')
  let filterHeight = main.querySelector('div').offsetHeight
  const isDesktop = window.matchMedia('(min-width: 768px)').matches

  if (aside) {
    let section = aside.closest('section')
      if (section) {
          let height = main.scrollHeight
          let distanceFromTop = height - section.offsetTop
          let hiddenDivHeight = [...main.querySelectorAll('div details div')].map((elm)=>elm.offsetHeight).reduce((a, b) => a + b, 0)
          hiddenDivHeight = hiddenDivHeight + [...main.querySelectorAll('div details summary')].map((elm)=>elm.offsetHeight).reduce((a, b) => a + b, 0)
          hiddenDivHeight = hiddenDivHeight + 140

          if (isDesktop) {
            aside.style.position = 'static'
            if(hiddenDivHeight > height ){
              aside.style.height = hiddenDivHeight + "px"
            } else {
              aside.style.height = height + "px"
            }
          } else {
            aside.style.overflowY = "hidden";
            // aside.style.height = distanceFromTop + "px"
          }
          if (main) {
            if(isDesktop){
              if(hiddenDivHeight > height ){
                main.style.height = hiddenDivHeight + "px"
              } else {
                main.style.height = height + "px"
              }
              main.style.overflowY = "auto";
            } else {
              main.style.height = document.documentElement.clientHeight - main.offsetTop+'px'
            }
          }
      }
  }
  
}
