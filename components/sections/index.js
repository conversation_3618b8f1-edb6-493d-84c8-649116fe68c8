const Sections = {

  init: () => {
    document.querySelectorAll('[id*="shopify-section-"]').forEach(section=>{
      window.sections.push({
        id:section.id.replace('shopify-section-',''),
        type:(Array.from(section.classList).find(c=>c.includes('shopify-section--'))||'').replace('shopify-section--',''),
        el:section,
        async:section.querySelectorAll('script[type="async/section"]').length>0,
        watch:true,
        view:{},
        data:{}
      })
    })
    Sections.watch()
  },

  watch: () => {

    sections = sections.map(section=>{

      const view = {
        position: section.el.getBoundingClientRect()
      }
      view.approach = (view.position.top < (window.innerHeight + 500) && view.position.bottom >= 0)
      view.partial = (view.position.top < (window.innerHeight) && view.position.bottom >= 0)
      view.total = (view.position.top >= 0 && view.position.bottom <= window.innerHeight)
      view.exit = !view.partial

      // Observe Section state changes
      if(section.watch){
        ['approach','partial','total','exit'].forEach(state =>{
          if(section.view[state] !== view[state] || !section.view[state]) {
            
            Util.events.dispatch(`Section:${state}`,{section:section,view:view})

            if(
              section.async && 
              !section.asyncLoaded && 
              state == 'approach' && 
              view[state]){

                Sections.fetch(section.id)
                section.asyncLoaded = true
            
            }

          }
        })
      }

      section.view = Object.assign(section.view, view)

      return section

    })
  },

  fetch: (ids, url='') => {

    if(ids==='*'){
      ids = sections.map(s=>s.id)
    } else if (ids.includes('*')) {
      ids = sections.map(s=>s.id).filter(id=>id.includes(ids.replace('*','')))
    } else if (ids.includes('!')) {
      ids = sections.map(s=>s.id).filter(id=>!id.includes(ids.replace('*','')))
    }

    fetch(`${url}?sections=${Util.array(ids).join(',')}`).then(r=>r.json()).then(template=>{
      Util.array(ids).forEach(id=>{
        document.querySelector(`#shopify-section-${id}`).innerHTML = template[id]
      })
    })
  }
  
}

window.sections = window.sections || []
window.Sections = Sections
Sections.init()
window.addEventListener('scroll',Util.throttle(e=>{Sections.watch()},500))
window.addEventListener('scroll',Util.debounce(e=>{Sections.watch()},500))

export default Sections

 
