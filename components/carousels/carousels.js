// import Swiper JS
import Swiper from 'swiper/bundle';
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/a11y'
// import 'swiper/css/pagination'

const Carousels = {
  init: (p) => {
    p = p || document
    p.querySelectorAll('[data-swiper], [swiper]').forEach( ( el ) => {
      if(!el.classList.contains('swiper-container-initialized') && 
        !el.classList.contains('swiper-hold') && 
        !el.querySelector('[x-for]') && 
        !el.querySelector('[x-if]')
        )
        Carousels.create(el)
    })
  },

  create(el, passedConfig) {

    if (!el) return;
    if (!!el.swiper) return;
    // if(el.querySelectorAll('.swiper-slide').length < 2 ) return;

    let config = {
      cssMode:true,
      a11y: {
        enabled: true,
      },
      on: {
        init: swiper => {
        },
        activeIndexChange: swiper => {
        },
        slideChange: function () {
          updateProgressBarWidth(this);
        }
      },
      ...passedConfig
    };

    if(el.querySelectorAll('.swiper-scrollbar').length) {
      config.scrollbar = {
        el: '.swiper-scrollbar',
        hide: true,
      }
    }

    if(el.querySelectorAll('.swiper-button-next').length) {
      config.navigation = {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
      }
    }

    if(!!el.getAttribute('swiper')){
      config = Object.assign(eval('(' + el.getAttribute('swiper') + ')'), config)
    }

    if(!!el.querySelector('[type="swiper/config"]')) {
      config = Object.assign(eval('(' + el.querySelector('[type="swiper/config"]').innerText + ')'),config)
    }

    el.querySelectorAll(".swiper-wrapper > *:not(style,script)").forEach((_el) => {
      if (_el.classList.contains("swiper-slide")) return;
      if (['template','script'].includes(_el.nodeName.toLowerCase())) return;
      
      const slide = document.createElement("div");
      slide.classList.add("swiper-slide", "swiper-slide--wrapped");
      _el.parentNode.insertBefore(slide, _el);
      return slide.appendChild(_el); 
      
    });

    Util.hooks.process('Carousel:init', {el: el, config: config})
      .then(obj => {
        if (Util.events.dispatch('Carousel:init', obj.config, obj.el)) {
          obj.el.swiper = new Swiper(obj.el, obj.config);
        }
      })
      .catch(error => {
        console.error('Error initializing carousel:', error);
      });

    // function to support Roark progress bar styling on iOS
    function updateProgressBarWidth(swiper) {
      if (!swiper || !swiper.pagination || !swiper.pagination.el) {
        return;
      }
      if (swiper.params.pagination && swiper.params.pagination.type !== 'progressbar') {
        return;
      }

      const progressBarFill = swiper.pagination.el.querySelector('.swiper-pagination-progressbar-fill');
      if (!progressBarFill) {
        return;
      }
      const slidesPerView = swiper.params.slidesPerView;
      const totalPages = Math.ceil((swiper.slides.length - slidesPerView + 1) / swiper.params.slidesPerGroup);
      const currentPage = Math.ceil((swiper.realIndex + 1) / swiper.params.slidesPerGroup);
      const progress = currentPage / totalPages;

      progressBarFill.style.width = `${progress * 100}%`;
      progressBarFill.style.transform = 'translate3d(0px, 0px, 0px) scaleX(1) scaleY(1)';
    } 

    if (el.querySelectorAll('.swiper-next').length) {
        el.querySelector('.swiper-next').addEventListener('click', function(){
        el.swiper.slideNext()
        })
    }
    if (el.querySelectorAll('.swiper-prev').length) {
        el.querySelector('.swiper-prev').addEventListener('click', function(){
        el.swiper.slidePrev()
        })
    }

    return el.swiper

  },

}

window.Swiper = Swiper

window.Carousels = Carousels

Carousels.init()

export default Carousels
