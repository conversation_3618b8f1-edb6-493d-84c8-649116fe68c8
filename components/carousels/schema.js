module.exports = {
  "Carousel": [
    {
      "type": "header",
      "content": "Product Settings"
    },
    {
      "type": "number",
      "id": "limit",
      "label": "Max Number of Products",
      "default": 8
    },
    {
      "type": "checkbox",
      "id": "quick_add",
      "label": "Include Quick Add",
      "default": true
    },
    {
      "type": "paragraph",
      "content": "To select data source for products, add a block to this Section"
    },
    {
      "type": "header",
      "content": "Slideshow Settings"
    },
    {
      "type": "checkbox",
      "id": "show_pagination",
      "label": "Show Dot/Bullet Indicators",
      "default": false
    },
    {
      "type": "number",
      "id": "slides_per_view",
      "label": "Slides per view (Desktop)",
      "default": 4
    },
    {
      "type": "number",
      "id": "slides_per_group",
      "label": "Slides per group (Desktop)",
      "default": 4
    },
    {
      "type": "number",
      "id": "spacebetween",
      "label": "Space between slides (Desktop)",
      "default": 16
    },
    {
      "type": "number",
      "id": "slides_per_view_mobile",
      "label": "Slides per view (Mobile)",
      "default": 2
    },
    {
      "type": "number",
      "id": "slides_per_group_mobile",
      "label": "Slides per group (Mobile)",
      "default": 2
    },
    {
      "type": "number",
      "id": "spacebetween_mobile",
      "label": "Space between slides (Mobile)",
      "default": 16
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay"
    },
    {
      "type": "checkbox",
      "id": "loop",
      "label": "Loop"
    },
    {
      "type": "checkbox",
      "id": "center",
      "label": "Center"
    },
    {
      "type": "range",
      "id": "autoplay_slide_duration",
      "label": "Autoplay Slide Duration",
      "min": 3,
      "max": 8,
      "step": 1,
      "default": 8
    },
    {
      "type": "checkbox",
      "id": "arrows",
      "label": "Show arrows (Desktop)",
      "default":true
    },
    {
      "type": "checkbox",
      "id": "arrows_mobile",
      "label": "Show arrows (Mobile)",
      "default":true
    },
    {
      "type": "checkbox",
      "id": "show_arrows_on_hover",
      "label": "Show arrows only on hover (Desktop)",
      "default":true
    },
    {
      "type": "checkbox",
      "label": "Show outer slides on hover",
      "id": "outer_slides",
      "default": false
    },
    {
			"type": "checkbox",
			"label": "Increase arrow tap target",
			"id": "increase_arrow_tap_target",
			"default": false
		}
  ]
}
