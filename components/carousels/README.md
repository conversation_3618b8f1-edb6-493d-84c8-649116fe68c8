# Carousels Controller

The carousels controller 

## Quick Start

The simplest way to initialize a carousel is to copy the code below and use the `swiper` attribute on the wrapping `.swiper-container` element

```
<!-- Slider main container -->
<div class="swiper swiper-container" swiper>
  <!-- Additional required wrapper -->
  <div class="swiper-wrapper">
    <!-- Slides -->
    <div class="swiper-slide">Slide 1</div>
    <div class="swiper-slide">Slide 2</div>
    <div class="swiper-slide">Slide 3</div>
    ...
  </div>
  <!-- If we need pagination -->
  <div class="swiper-pagination"></div>

  <!-- If we need navigation buttons -->
  <div class="swiper-button-prev"></div>
  <div class="swiper-button-next"></div>

  <!-- If we need scrollbar -->
  <div class="swiper-scrollbar"></div>
</div>
```


## Calling the `create` method 

You can also use the global `Carousels.create(element, configObject)` method to create a carousel with supporting html.

```
<div class="relative w-10/12 mx-auto flex flex-row">

  <div class="swiper-container w-full" swiper="{ init: true }">
    <div class="absolute inset-y-0 left-0 z-10 flex items-center">
      <button class="swiper-prev bg-white -ml-2 lg:-ml-4 flex justify-center items-center w-10 h-10 rounded-full shadow focus:outline-none">
        <svg viewBox="0 0 20 20" fill="currentColor" class="chevron-left w-6 h-6"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
      </button>
    </div>

    <div class="swiper-wrapper">
      <!-- Slides -->
      {% for slide in (i..5) %}
        <div class="swiper-slide p-4">
          <div class="flex flex-col rounded shadow overflow-hidden">
            <div class="flex-shrink-0">
              <img class="h-48 w-full object-cover" src="https://images.unsplash.com/photo-1496128858413-b36217c2ce36?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1679&q=80" alt="">
            </div>
          </div>
        </div>
      {% endfor %}

    </div>

    <div class="absolute inset-y-0 right-0 z-10 flex items-center">
      <button class="swiper-next bg-white -mr-2 lg:-mr-4 flex justify-center items-center w-10 h-10 rounded-full shadow focus:outline-none">
        <svg viewBox="0 0 20 20" fill="currentColor" class="chevron-right w-6 h-6"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>
      </button>
    </div>
  </div>

</div>
```

```
<div class="relative w-10/12 mx-auto flex flex-row">

  <div class="swiper-container w-full" swiper>
    <script type="swiper/config">
      { init: true }
    </script>

    <div class="absolute inset-y-0 left-0 z-10 flex items-center">
      <button class="swiper-prev bg-white -ml-2 lg:-ml-4 flex justify-center items-center w-10 h-10 rounded-full shadow focus:outline-none">
        <svg viewBox="0 0 20 20" fill="currentColor" class="chevron-left w-6 h-6"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
      </button>
    </div>

    <div class="swiper-wrapper">
      <!-- Slides -->
      {% for slide in (i..5) %}
        <div class="swiper-slide p-4">
          <div class="flex flex-col rounded shadow overflow-hidden">
            <div class="flex-shrink-0">
              <img class="h-48 w-full object-cover" src="https://images.unsplash.com/photo-1496128858413-b36217c2ce36?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1679&q=80" alt="">
            </div>
          </div>
        </div>
      {% endfor %}

    </div>

    <div class="absolute inset-y-0 right-0 z-10 flex items-center">
      <button class="swiper-next bg-white -mr-2 lg:-mr-4 flex justify-center items-center w-10 h-10 rounded-full shadow focus:outline-none">
        <svg viewBox="0 0 20 20" fill="currentColor" class="chevron-right w-6 h-6"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>
      </button>
    </div>
  </div>

</div>
```

## Using the controller with AlpineJS

You can use the create method which returns the instance of the swiper carousel to access the Swiper API for further customization.

```
  <div 
   x-data="{swiper: null }"
    x-init="swiper = Carousels.create($refs.container);"
    class="relative w-10/12 mx-auto flex flex-row"
  >
    <div class="absolute inset-y-0 left-0 z-10 flex items-center">
      <button 
         @click="swiper.slidePrev()" 
         class="bg-white -ml-2 lg:-ml-4 flex justify-center items-center w-10 h-10 rounded-full shadow focus:outline-none"
      >
        <svg viewBox="0 0 20 20" fill="currentColor" class="chevron-left w-6 h-6"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
      </button>
    </div>

    <div class="swiper-container w-full" x-ref="container">
      <div class="swiper-wrapper">
        <!-- Slides -->
        {% for slide in (i..5) %}
          <div class="swiper-slide p-4">
            <div class="flex flex-col rounded shadow overflow-hidden">
              <div class="flex-shrink-0">
                <img class="h-48 w-full object-cover" src="https://images.unsplash.com/photo-1496128858413-b36217c2ce36?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1679&q=80" alt="">
              </div>
            </div>
          </div>
        {% endfor %}

      </div>
    </div>

    <div class="absolute inset-y-0 right-0 z-10 flex items-center">
      <button 
        @click="swiper.slideNext()" 
        class="bg-white -mr-2 lg:-mr-4 flex justify-center items-center w-10 h-10 rounded-full shadow focus:outline-none"
      >
        <svg viewBox="0 0 20 20" fill="currentColor" class="chevron-right w-6 h-6"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>
      </button>
    </div>
  </div>
```

### Advanced AlpineJS config

If you want to render slides with reactive data you'll have to use a more complex configuration link the one below.

Here the slides are rendered by the `slideCount` number so we have to pass a config object in the create method with `init` set to `false`.

After Alpine renders the slides we can use Alpine's `$nextTick` magic to initialize the carousel when the slides are rendered in the DOM.

We also want to use Alpine's `$watch` magic to execute `swiper.update()` any time that the `slideCount` changes.

```
  <div 
   x-data="{
     swiper: null,
     slideCount: 5
   }"
    x-init="
      swiper = Carousels.create($refs.container, { init: false }); 

      $nextTick(() => {
        swiper.init()
        swiper.update()
      })

      $watch('slideCount',() => $nextTick(() => swiper.update()))
    "
    class="relative w-10/12 mx-auto flex flex-row"
  >
    <div class="absolute inset-y-0 left-0 z-10 flex items-center">
      <button 
         @click="swiper.slidePrev()" 
         class="bg-white -ml-2 lg:-ml-4 flex justify-center items-center w-10 h-10 rounded-full shadow focus:outline-none"
      >
        <svg viewBox="0 0 20 20" fill="currentColor" class="chevron-left w-6 h-6"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
      </button>
    </div>

    <div class="swiper-container w-full" x-ref="container">
      <div class="swiper-wrapper">
        <!-- Slides -->
        <template x-for="i in slideCount" hidden>
          <div class="swiper-slide p-4">
            <div class="flex flex-col rounded shadow overflow-hidden">
              <div class="flex-shrink-0">
                <img class="h-48 w-full object-cover" src="https://images.unsplash.com/photo-1496128858413-b36217c2ce36?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1679&q=80" alt="">
              </div>
            </div>
          </div>
        </template>

      </div>
    </div>

    <div class="absolute inset-y-0 right-0 z-10 flex items-center">
      <button @click="swiper.slideNext()" 
              class="bg-white -mr-2 lg:-mr-4 flex justify-center items-center w-10 h-10 rounded-full shadow focus:outline-none">
        <svg viewBox="0 0 20 20" fill="currentColor" class="chevron-right w-6 h-6"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>
      </button>
    </div>
  </div>
```
