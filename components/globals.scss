// /* Color Guidelines
//   ==========================================================================
//   primary: Highest level user attention.
//   secondary: Second highest level user attention.
//   tertiary: Third highest level user attention.
//   light: Most prominent light background color. Must be able to overlay on top of dark.
//   dark: Most prominent dark background color. Must be able to overlay on top of light.
//   pop: Usage examples are badges.
//   highlight: Think about this as using a highlighter pen.
//   body: Most common text color.
//   header: Most common text color for headers.
// */

// :root {

//   --color-primary: #000000;
//   --color-secondary: #F5F5F1;
//   --color-tertiary: #F4F4F4;
//   --color-light: #F4F4F4;
//   --color-dark: #000000;
//   --color-pop: #C32033;
//   --color-highlight: #1E3E58;
//   --color-body: #000000;
//   --color-heading: #000000;

//   --swiper-theme-color: #000;

//   --header-height:76px;
//   --preheader-height: 34px;
//   --header-height-half:38px;
//   --unscrolled-header-height: 100px;
//   --scrolled-header-height: 76px;

//   @media screen and (max-width: 1024px) {
//     --header-height:100px;
//     --preheader-height: 32px;
//     --header-height-half: 34px;
//     --unscrolled-header-height: 100px;
//     --scrolled-header-height: 68px;
//   }

//   --font-body-weight: 400;
//   --font-body-style: normal;
//   --font-heading-weight: 700;
//   --font-heading-style: normal;
//   --font-subheading-weight: 600;
//   --font-subheading-style: normal;


//   --header-height:48px;

// }


//   /* Document
//   ========================================================================== */



//   /* Type Scale (Build your scale https://type-scale.com/) - This theme is using the Perfect Fourth
//   ========================================================================== */

//   html {font-size: 100%;} /*16px*/

//   body, .body {
//     background: white;
//     font-family: var(--font-body-family);
//     font-weight: var(--font-body-weight);
//     line-height: 1.75;
//     color: var(--color-body);
//     @apply tracking-wide;
//   }

//   p {

//     margin-bottom: 1rem;  
    
//     .reset-p & {
//       margin-bottom: 0;
//     }

//     &:last-child {
//       margin-bottom: 0;
//     }

//   }

//   strong {
//     font-weight: 700;
//   }

//   h1, h2, h3, h4, h5, h6 {
//     margin: 3rem 0 1.38rem;
//     font-family: var(--font-heading-family);
//     line-height: 1.3;
//     color: var(--color-heading);
//   }

//   .headings {
//     margin: 3rem 0 1.38rem;
//     font-family: var(--font-heading-family);
//     font-weight: var(--font-heading-weight);
//     font-style: var(--font-heading-style);
//     line-height: 1.3;
//     color: var(--color-heading);
//   }

//   h1, .h1 { @apply headings lg:text-7xl text-6xl; }

//   h2, .h2 { @apply headings lg:text-7xl text-6xl; }

//   h3, .h3 { @apply headings lg:text-6xl text-5xl; }

//   h4, .h4 { @apply headings lg:text-5xl text-4xl; }

//   h5, .h5 {  @apply headings lg:text-4xl text-3xl;  }

//   small, .text_small {  @apply text-sm;  }

//    /* Type Styles
//   ========================================================================== 
//    Guidelines
//   ==========================================================================
//     **Type Styles**

//     primary: Primary Headline or Title type.
//     secondary: Commonly used as Subtitle type, compliments Primary.
//     tertiary: Third highest level user attention, smaller but stylistically similar to primary.

//     **Type Layouts / Spacing**

//     page: Most common text color.
//     section: Most common text style for headers.
//     article: Most common text style for headers.

//   */

//   .font-body {
//     font-family: var(--font-body-family);
//     font-weight: var(--font-body-weight);
//     font-weight: var(--font-body-style);
//   }

//   .font-heading {
//     font-family: var(--font-heading-family);
//     font-weight: var(--font-heading-weight);
//     font-style: var(--font-heading-style);
//   }

//   .font-subheading {
//     font-family: var(--font-subheading-family);
//     font-weight: var(--font-subheading-weight);
//     font-style: var(--font-subheading-style);
//   }

//   .type, .title {

//     &--primary {
//       @apply h1;
//     }
//     &--secondary {
//       @apply h1;
//     }
//     &--page {
//       @apply h2;
//     }
//     &--section {
//       @apply h1;
//     }
//     &--article {
//       @apply h2;
//     }
//     &--eyebrow {
//       @apply font-subheading uppercase text-base;
//     }
//     &--headline {
//       @apply h1 lg:text-12xl text-7xl;
//     }
//     &--subline {
//       @apply lg:text-xl text-base font-body normal-case;
//       &-bold {
//         @apply font-subheading;
//       }
//     }
//   }

//    /* Buttons and Controls
//   ========================================================================== */

//   .button, .btn {
//     @apply font-subheading leading-normal py-2 px-8 lg:text-base text-sm border  bg-primary text-white cursor-pointer text-center rounded-full inline-flex justify-center;
//     &--primary { @apply  bg-primary text-white; }
//     &--secondary { @apply border-dark bg-secondary text-dark; }
//     &--tertiary { @apply border-tertiary bg-tertiary text-dark;  }
//     &--light { @apply border-dark bg-light text-dark; }
//     &--dark { @apply border-light bg-dark text-light; }
//     &--pop { @apply bg-pop border-pop text-white; }
//     &--highlight { @apply bg-highlight border-highlight text-white; }
//     &--link { @apply bg-transparent text-dark p-0 border-0 hover:underline; }
//     &--w-icon { 
//       @apply flex items-stretch border-dark bg-white text-dark;
//       span {
//         &:first-child {
//           @apply mr-3 flex items-center;
//         }
//       }
//     }
//   }

//   .btn-control {
//     @apply bg-transparent p-3 transform z-10 transition-all select-none items-center flex-col justify-center;

//     &.swiper-button-disabled {
//       @apply hidden;
//     }

//     .btn-next {

//     }
//     .btn-prev {
      
//     }
//   }

//   .pagination {
//     @apply text-center flex items-center justify-center p-2;
//     .pagination-bullet, .swiper-pagination-bullet {
//       @apply bg-current w-2 h-2 rounded-full mx-8 cursor-pointer relative opacity-70;
//       &.active, &.swiper-pagination-bullet-active {
//         @apply opacity-100 scale-150;
//       }
//     }
//     &.pagination-dash {
//       .pagination-bullet, .swiper-pagination-bullet {
//         @apply bg-dark w-20 h-1 rounded-none mx-3;
//         &.active, &.swiper-pagination-bullet-active {
//           @apply opacity-100 bg-light;
//         }
//       }
//     }
//     &.swiper-pagination-progressbar.swiper-pagination-horizontal {
//       height: 2px;
//       @apply relative p-0 bg-gray-100;
//       .swiper-pagination-progressbar-fill {
//         @apply bg-dark;
//       }
//     }
//     &.swiper-pagination-bullets.swiper-pagination-horizontal {
//       @apply absolute left-0 bottom-0 z-20;
//     }
//   }

//   .pagination--page {
//     @apply inline-flex bg-transparent;
//     li {
//       @apply w-16 h-16;
//       a:not(.pp-control),
//       button:not(.pp-control) {
//         @apply block w-full h-full lg:flex hidden items-center justify-center flex-grow-0 flex-shrink-0;
//       }
//       [aria-current="page"], .active {
//         @apply block w-full h-full text-dark flex underline items-center justify-center flex-grow-0 flex-shrink-0;
//       }
//     }
//     .pp-control {
//       @apply text-dark h-full px-4 lg:px-0 flex justify-center items-center;
//     }
//     .btn-next {

//     }
//     .btn-prev {
      
//     }
//   }


//   /* Form
//   ========================================================================== */

//   .field, .swym-input-container {
//     @apply relative mb-2;

//     &:last-child {
//       @apply mb-0;
//     }
    
//     label {
      
//       @apply flex items-center text-sm font-body leading-normal mb-2 text-left;

//       & ~ label { @apply mt-2; }
    
//       input[type="checkbox"], input[type="radio"] {
//         display:none;

//         & ~ span {
//           @apply select-none;
//         }

//         & + span {
//           @apply w-6 h-6 mr-2 border border-dark bg-white flex items-center justify-center cursor-pointer;
//           svg {
//             display:none;
//           }
//         }

//         &[checked], &:checked {
//           & + span {
//             @apply bg-dark;
//           }
//         }
//       }
//       input[type="radio"] + span { @apply rounded-full; }

//     }
//     input, textarea, select {
//       @apply block text-base font-body leading-normal py-2 px-4 border border-light bg-light w-full rounded;
//       &:focus, &:focus-visible {
//         @apply border border-dark;
//       }
//     }

//     select {
//       @apply pr-16;
//     }

//     &--select {
//       &:after {
//         @apply pointer-events-none absolute block inset-y-0 right-0 flex items-center px-2 bg-light text-dark bg-no-repeat bg-center w-12 border-l-0 border border-light rounded;
//         background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='1' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-down'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
//         content: '';
//       }
//       select {
//         @apply appearance-none cursor-pointer;
//       }
//     }

//     &.field-plus-minus {
//       button {
//         @apply text-base border border-dark bg-white text-black cursor-pointer h-8 w-8 text-center flex items-center justify-center;

//         svg {
//           @apply stroke-2;
//         }
//       }

//       button:first-child {
//         @apply border-r-0;
//       }

//       button:last-child {
//         @apply border-l-0;
//       }

//       input {
//         @apply border border-black border-l-0 border-r-0 bg-white text-black text-base w-12 h-8 text-center rounded-none;
//       }
//     }

//     &.field-plus-minus-light {
//       button {
//         @apply border-gray-600 text-gray-600;
//       }
//     }

//     &--w-button {
//       @apply border border-dark rounded-full flex p-2.5;
//       input {
//         @apply rounded-full w-auto bg-white border-0 mr-2;
//       }
//     }
    
//   }

//   /* Sections
//   ========================================================================== */

//   .list {
//     .accordion {
//       &:last-child {
//         @apply border-b border-gray-300;
//       } 
//     }
//   }

//   .accordion {
//     @apply mb-1;
//     &:last-child {
//       @apply border-b border-gray-300;
//     }
//     .accordion-title {
//       @apply bg-transparent text-dark font-subheading flex flex-row w-full border border-gray-300 border-l-0 border-b-0 border-r-0 text-left items-stretch leading-normal px-0 py-5 text-base cursor-pointer;
//       span:first-child {
//         @apply flex items-center;
//       }
//       .accordion-control {
//         @apply ml-auto p-0 bg-transparent text-dark border-0;
//         .icon {}
//       }
//     }
//     .accordion-panel {
//       > div {
//         @apply py-4 px-0 border-0 font-body;
//       }
//       ul {
//         @apply list-disc pl-8 my-4;
//       }
//       p:last-child {
//         @apply mb-4;
//       }
//     }
//   }

//   .tabs {
//     ul:not(.start) {
//       @apply pl-0 flex justify-end w-full;
//       li {
//         @apply ml-5;
//       }
//     }

//     ul.start {
//       @apply space-x-4;
//     }

//     .tab-title {
//       @apply btn btn--tertiary;
//       &.active {
//         @apply btn--light; 
//       }
//     }
//     .tab-panel {
//       > div {
//         @apply py-6;
//       }
//     }
//   }

//   /* Cart
//   ========================================================================== */

//   .cart {
//     &__footer {
//       @apply lg:pt-8 pt-4 sticky bottom-0 self-end w-full mt-auto bg-white border-t text-left;
//       .cart__page & {
//         @apply lg:sticky static lg:w-1/3 top-0 lg:top-16 border-0 mt-0 self-start pt-0; 
//       }
//       &--summary {
//         @apply font-heading text-sm uppercase mb-2 mt-0;
//       }
//       &--taxes-shipping, &--subtotal {
//         @apply text-left my-2;
//       }
//       &--price {
//         @apply text-right my-2;
//       }
//     }
//     &__header {
//       @apply flex flex-row flex-shrink-0 pb-4 border-b;
//       &--title {
//         @apply my-0;
//         .cart__page & {
//           @apply my-0 flex-col ;
//           margin: 0 !important;
//         }
//       }
//     }
//   }

// /* Global Helpers
// ========================================================================== */

// .nav__off_canvas {
//   li {
//     ul {
//       @apply absolute lg:static lg:translate-x-0 transform translate-x-full top-0 right-0 lg:h-auto h-full max-h-main overflow-hidden lg:overflow-visible w-full z-10 transition-transform duration-500 ease-in-out;
//     }
//     &.active {
//       ul {
//         @apply translate-x-0;
//       }
//     }
//   }
// }

// .container {
//   @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
// }


// .header {
//   @apply flex flex-row justify-center w-full h-auto px-4 py-4 lg:py-0 lg:px-8 text-dark bg-white;
//   &__logo {
//     @apply flex flex-col items-start justify-center flex-1 w-1/3 lg:mr-auto lg:w-auto;
//     &--link {
//       @apply flex flex-col items-start justify-center w-2/4 max-w-xs lg:mr-auto lg:w-auto;
//     }
//     &--extra {
//       @apply pb-1;
//     }
//   }
//   &__nav {
//     @apply fixed top-0 left-0 z-40 flex flex-col justify-start w-full px-0 overflow-x-hidden transition-transform duration-300 ease-in-out lg:static h-main lg:w-auto lg:h-auto lg:flex lg:flex-1 lg:items-center lg:self-stretch lg:px-0 lg:py-0 lg:overflow-visible lg:visible invisible;
//   }
//   &__tools {
//     @apply flex items-center justify-end w-1/3 px-2 ml-auto lg:flex-1 lg:w-auto;
//   }
// }

// [data-active-modal="search"] .search-results {
//   @apply block;
// }

// .nav {
  
//   @apply w-full flex flex-col items-center pt-4 tracking-wide list-none border-light lg:py-0 lg:border-none lg:flex-1 lg:flex-row;
  
//   &__item {
//     @apply w-full lg:w-auto lg:h-full whitespace-nowrap;
//     &--back {
//       @apply lg:hidden;
//     }
//   }

//   &__link {
    
//     @apply relative flex flex-row items-center lg:flex-col justify-between lg:justify-start w-full h-full px-8 lg:px-2 py-4 lg:py-6 overflow-hidden cursor-pointer text-dark lg:overflow-visible;
  
//     &--back {
//       @apply justify-start
//     }

//   }
  
//   &__trigger {
//     @apply absolute top-0 right-0 lg:py-0 lg:px-0 w-full py-4 px-8 h-full bg-transparent flex items-center justify-center;
//   }
  
//   &__icon {
//     width:1.5rem;
//     height:1.5rem;
//   }
// }

// .mega-menu {
//   &__item {
//     @apply w-full;
//   }

//   &__list {
//     @apply w-full;
//   }
// }

// @media (min-width: 1024px) {
//   .w-content {
//     width: fit-content;
//   } 
// }

// .tools {
//   &__search {
//     @apply flex items-center p-2;
//     &--mobile {
//       @apply flex justify-center;
//     }
//     &--trigger {
//       @apply justify-center hidden lg:flex;
//     }
//   }
//   &__account {
//     @apply px-2 py-2 hidden lg:block;
//   }
//   &__cart {
//     &--button {
//       @apply relative flex items-center p-2 pr-0;
//     }
//     &--bubble {
//       @apply text-2xs flex flex-col items-center justify-center w-4 h-4 leading-none rounded-full text-dark bg-pop ml-1;
//     }
//   }
//   &__menu {
//     @apply p-2 m-0 bg-transparent border-none text-dark leading-none font-heading lg:hidden;
//   }
// }

// [tooltip]:not([tooltip=""]){
//   @apply before:content-[attr(tooltip)] before:hidden before:bg-gray-800 before:py-2 before:px-3 before:text-light before:rounded-md before:bottom-full before:leading-tight before:w-48 hover:before:block before:absolute;

//   &[tooltip-center]:before {
//     @apply text-center left-1/2 transform -translate-x-1/2;
//   }
// }

// // Swatch styles
// input[data-availability="false"] + label,
// input[data-availability="0"] + label,
// .swatch--disabled {
//   color: #c2c2c2;
//   background: #efefef;
//   cursor:not-allowed;
// }

// /* Account Pages
//   ========================================================================== */

// .errors {
//   @apply text-pop;
// }

// .account {
//   &__header {
//     @apply p-4 lg:p-8 border-b border-light flex flex-wrap lg:items-end lg:flex-row flex-col;
//   }
//   &__nav {
//     @apply p-4 lg:p-8 w-full lg:w-1/5 flex flex-col order-none lg:border-r lg:border-b-0 border-b border-light;
//     &--list {

//     }
//   }
//   &__container {
//     @apply mx-auto lg:p-8 p-4 min-h-75v;
//   }
// }

// .shopify-payment-button {
//   @apply mt-4;

//   &[data-has-selling-plan="false"] {
//     @apply hidden;
//   }

//   &__button,
//   &__more-options {
//     @apply lg:hidden;

//     &[disabled] {
//       @apply hidden;
//     }
//   }
// }
