Util.hooks.register('Products:enhance', data => {
  if (!window.product_item_enhanced_data_key) return false
  return new Promise(async (resolve, reject) => {
    let sessionSavedSiblings = sessionStorage.getItem('productItemEnhanced')
    sessionSavedSiblings = sessionSavedSiblings ? JSON.parse(sessionSavedSiblings) : {}
    const productIds = data.map((product, i) => {
      if (!!sessionSavedSiblings[product.id]) {
        product.siblings = sessionSavedSiblings[product.id]
        product.siblings.map(sibling => sibling.siblings = product.siblings)
        return false
      }
      return `gid://shopify/Product/${product.id}`
    }).filter(id => !!id);
    const accessToken = window.product_item_enhanced_data_key;
    const url = `https://${Shopify.shop}/api/2024-01/graphql.json`;
    const query = `
      query EnhancedProductData {
        nodes(ids: ${JSON.stringify(productIds)}) {
          ... on Product {
            id
            title
            metafield(namespace:"product" key: "style") {
              id
              value
              reference {
                ... on Metaobject {
                  handle
                  field(key: "subcollection") {
                    __typename
                    key
                    value
                    reference {
                      ... on Collection {
                        products(first: 30) {
                          nodes {
                            id
                            title
                            available: availableForSale
                            handle
                            tags
                            type: productType
                            options {
                              name
                              values
                            }
                            priceRange {
                              maxVariantPrice {
                                amount
                              }
                            }
                            compareAtPriceRange {
                              maxVariantPrice {
                                amount
                              }
                            }
                            variants(first: 40) {
                              nodes {
                                available: availableForSale
                                id
                                image {
                                  url
                                  altText
                                }
                                price {
                                  amount
                                }
                                sku
                                title
                              }
                            }
                            images(first: 10) {
                              nodes {
                                id
                                width
                                height
                                url
                                altText
                              }
                            }
                            featuredImage {
                              url
                              altText
                              id
                            }
                            metafields(identifiers: [
                            {namespace: "product", key: "swatch_color_1"},
                            {namespace: "product", key: "swatch_color_2"},
                            {namespace: "product", key: "swatch_color_3"},
                            {namespace: "product", key: "swatch_image"}
                            ]) {
                              key
                              value
                              reference {
                                ... on MediaImage {
                                  image {
                                    url
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;
    const body = JSON.stringify({ query });
    let modifiedData = data
    if (productIds.length) await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Storefront-Access-Token': accessToken,
      },
      body,
    })
      .then(response => response.json())
      .then(results => {
        modifiedData = data.map(product => {          
          const match = results.data.nodes.find(node => {
            if (node && node.id) {
              const nodeId = node.id.split('/').pop();
              return nodeId === product.id.toString();
            }
            return false;
          });

          if (match && !product.siblings) {
            try {
              product.siblings = match.metafield.reference.field.reference.products.nodes;
              product.siblings = Util.map(product, {
                siblings: {
                  from: 'siblings',
                  each: {
                    available: 'available',
                    compare_at_price: 'maxVariantPrice.amount|*100',
                    handle: 'handle',
                    featuredImage: 'featuredImage',
                    images: {
                      from: 'images.nodes',
                      each: {
                        src: 'url',
                        width: 'width',
                        height: 'height',
                        alt: 'altText'
                      }
                    },
                    metafields: 'metafields',
                    title: 'title',
                    id: 'id|.split("/").reverse()[0]',
                    price: 'priceRange.maxVariantPrice.amount|*100',
                    type: 'type',
                    tags: 'tags',
                    variants: {
                      from: 'variants.nodes',
                      each: {
                        available: 'available',
                        id: 'id|.split("/").reverse()[0]',
                        image: 'image.url',
                        price: 'price.amount|*100',
                        sku: 'sku',
                        title: 'title'
                      }
                    }
                  }
                }
              }).siblings
              product.siblings.map(sibling => {
                sibling.siblings = product.siblings
                return sibling
              })
            } catch (err) {}
          }
          return product;
        });

        modifiedData.map(product => {
          sessionSavedSiblings[product.id] = product.siblings
          return product
        })
        try {
          sessionStorage.setItem('productItemEnhanced', stringify(sessionSavedSiblings))
        } catch (error) {}
      })
      .catch(error => {
        reject(error);
      });
      resolve(modifiedData);
  });
});

function stringify(obj) {
  let cache = [];
  let str = JSON.stringify(obj, function(key, value) {
    if (typeof value === "object" && value !== null) {
      if (cache.indexOf(value) !== -1) {
        // Circular reference found, discard key
        return;
      }
      // Store value in our collection
      cache.push(value);
    }
    return value;
  });
  cache = null; // reset the cache
  return str;
}
