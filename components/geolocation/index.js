
const Geolocation = {

	init:() => {
		
		try{
			geolocation.location.country = navigator.language.split('-')[1];	
		} catch(err) {}
		
		if(Util.storage.get('local','geolocation')){
			geolocation.location = Util.storage.get('local','geolocation')
		}

		geolocation.bypass = !!Util.storage.get('local','geolocationBypass')
		
		Geolocation.set()
		
		if(!geolocation.location.ip) {
			Geolocation.get().then(loc=>{
				geolocation.location = loc
				Geolocation.set()
			})
		}
	},

	get:() => {

		return new Promise((res, rej) => {

			if(geolocation.location.ip) {
				res(geolocation.location);
				return
			}
			fetch(geolocation.services.geoip).then(r=>{
				r.json().then(d=>{
					d.ip = d.ip || true
					res(d);
				})
			})

		})

	},

	set:() => {

		// geolocation.location.country = 'CA'

		if(!geolocation.location.country) return;

		Util.storage.set('local','geolocation', geolocation.location);

		Util.events.dispatch('Geolocation:set', geolocation.location);

		Customer.identify('location', geolocation.location);

		if(Shopify.country != geolocation.location.country ) {

			Util.events.dispatch('Geolocation:mismatch');
			Geolocation.action()

		}

	},

	action: () => {

		geolocation.current_site = geolocation.sites.find(s=>s.current)
		geolocation.target_site = geolocation.sites.find(s=>s.countries.includes(geolocation.location.country))
		
		if(geolocation.bypass || !geolocation.target_site) return;

		if(geolocation.target_site.redirect) window.location = geolocation.url;

		if(geolocation.target_site.welcome) Modal.open('geolocation');
	
	},

	bypass: () => {
		Util.storage.set('local','geolocationBypass', true);
	}

	
}
Geolocation.init()
window.Geolocation = Geolocation;
export default Geolocation;