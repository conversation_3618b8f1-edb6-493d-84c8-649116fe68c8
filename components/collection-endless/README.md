## Collection Controller with Scroll-Based Pagination Extension

The **Collection Controller** has been extended to include scroll-based pagination functionality. This extension allows users to seamlessly load more products as they scroll through the collection page. This documentation outlines the implementation of scroll-based pagination and how it integrates with the existing Collection Controller.

### Collection.Endless Object

The `Collection.Endless` object is an extension to the Collection Controller that adds scroll-based pagination functionality.

#### Properties

- `scrollListener` (Boolean): Indicates whether the scroll event listener is active.
- `scrollDirection` (String): Tracks the scroll direction ('up' or 'down').
- `oldScroll` (Number): Stores the previous scroll position.
- `selector` (String): CSS selector for the collection container element.

#### Methods

- `scroll()`: The main scroll event handler that determines when to load more products based on scrolling behavior.

### Scroll Event Handling

The `scroll()` method of the `Collection.Endless` object is responsible for triggering the loading of more products as the user scrolls through the collection page.

#### Functionality

1. The method first checks if the scroll listener is enabled and if the collection is currently being loaded.
2. It determines the scroll direction based on the change in scroll position.
3. If the user is scrolling down and the bottom of the collection container is close (within 2000 pixels) to the viewport, it calculates the next page number and checks if it has already been loaded. If so, it triggers the loading of the next page.
4. Similarly, if the user is scrolling up and the top of the collection container is close to the top of the viewport, it calculates the previous page number and checks if it has already been loaded. If so, it triggers the loading of the previous page and ensures the scroll position is maintained.
5. After performing the appropriate actions, the method updates the `oldScroll` property to store the current scroll position.

### Event Listeners

- The `scroll` event listener is added to the `window` to monitor scroll behavior and trigger the `scroll()` method.
- The `alpine:init` event listener is used to enable the scroll listener when Alpine.js is initialized, ensuring that the scroll-based pagination is ready to function.

### Integration

The provided code should be integrated with the existing Collection Controller code and dependencies. Ensure that the Collection Controller and the extended `Collection.Endless` object are both included and properly referenced in your project.

### Conclusion

By extending the Collection Controller to include scroll-based pagination, you enhance the user experience on product collection pages. As users scroll through the page, more products are loaded seamlessly, offering a continuous and engaging browsing experience. Make sure to adjust the code to your project's specific requirements and structure for successful integration. If you have any further questions or need assistance, feel free to ask.