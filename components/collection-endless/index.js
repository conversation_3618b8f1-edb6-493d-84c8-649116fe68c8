Collection.Endless = {

    scrollListener: false,
    scrollDirection: false,
    oldScroll: 0,
    selector: '[product-grid]',

    scroll: () => {

        if (!Collection.Endless.scrollListener) return;
        if (Collection.loading) return;

        Collection.Endless.scrollDirection = Collection.Endless.oldScroll > window.scrollY ? 'up' : 'down'

        if(Collection.Endless.scrollDirection == 'down' && document.querySelector(Collection.Endless.selector) && document.querySelector(Collection.Endless.selector).getBoundingClientRect().bottom < 2000){

            let next = Math.max(...collection.pagination.pages.filter(p=>p.loaded).map(p=>p.number))+1;
            
            if(collection.pagination.pages.map(p=>p.number).includes(next)){
            
              Collection.Endless.scrollListener = false
              Collection.page(next).then(()=>{
                setTimeout(()=>{Collection.Endless.scrollListener = true},100)  
              })
              

            }

        }

        if(Collection.Endless.scrollDirection == 'up' && document.querySelector(Collection.Endless.selector).getBoundingClientRect().top > 0){

            let previous = Math.min(...collection.pagination.pages.filter(p=>p.loaded).map(p=>p.number))-1;
            
            if(collection.pagination.pages.map(p=>p.number).includes(previous)){
            
              Collection.Endless.scrollListener = false

              let beforeHeight = document.querySelector(Collection.Endless.selector).clientHeight+0;

              Collection.page(previous).then(()=>{
                let diffHeight = document.querySelector(Collection.Endless.selector).clientHeight - beforeHeight;
                window.scrollTo(0, window.scrollY + diffHeight );
                setTimeout(()=>{Collection.Endless.scrollListener = true},100)
              })
              
            }


        }

        Collection.Endless.oldScroll = window.scrollY;

    }

};

  // document.addEventListener('alpine:initialized',Collection.init)
window.addEventListener('scroll', Collection.Endless.scroll );

window.addEventListener('alpine:init', e => {
    Collection.Endless.scrollListener = true
}, {once: true} )

window.addEventListener('Collection:loaded', () => {
    Collection.Endless.scrollListener = true
})
