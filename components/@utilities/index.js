import './dom'
import './format'
import './map'
import './wait'
import './events'
import './math'
import './storage'
import './remote'
import './visible'
import './details'
import './attributes'
import './scroll_active'
import './url'

String.prototype.handle = function() {
  var handle = this.normalize("NFD").replace(/%/g,'').replace("'", "").toLowerCase().replace(/[^\w\u00C0-\u024f]+/g, "-").replace(/^-+|-+$/g, "")
  try {
    handle = decodeURIComponent(this.replace(/%/g,'')).replace("'", "").toLowerCase().replace(/[^\w\u00C0-\u024f]+/g, "-").replace(/^-+|-+$/g, "")
  } catch(err){}
  return handle
}
