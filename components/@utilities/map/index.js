window.Util = window.Util || {}

Util.map = (data, map) => {

  if(!!!map) return data

  let mapped_data = Array.isArray(data)?data:[data].map((data) => {
    let mapped = {};
    for (let prop in map) {
      
      if(!!map[prop].from){
        mapped[prop] = map[prop].from.split('.').reduce((data, i) => !!data[i] && data[i], data)

        if(!!map[prop].each && Array.isArray(mapped[prop])){
          mapped[prop] = mapped[prop].map(i=>{
            return Util.map(i,map[prop].each)
          })
        }
      } else if (Array.isArray(map[prop])) {
        mapped[prop] = Util.map(data[map[prop][0]], map[prop][1]);
      } else if ( typeof map[prop] == 'string' && map[prop].includes(".")) {
        try {
          mapped[prop] = map[prop].split("|")[0].split(".").reduce((ob, i) => ob[i], data);
        } catch (err) {}
      } else if ( typeof map[prop] == 'string') {
        mapped[prop] = data[map[prop].split("|")[0]];
      } else if (typeof map[prop] == 'object') {
        mapped[prop] = Util.map(data, map[prop]);
      }

      // piped functions
      if(typeof map[prop] == 'string' && map[prop].includes("|")){
        map[prop].split("|").forEach( (pf,i) => {
          if ( i === 0 ) return;
          mapped[prop] = eval(
            pf.includes('this') ? pf.replace('this','mapped[prop]'):'mapped[prop]'+pf
          )
        })
      }
    }
    return mapped;
  });
  
  if (!Array.isArray(data)) return mapped_data[0];
    return mapped_data;

}




Util.nested = (obj, path) => {
  return path.split('.').reduce((o, i) => {
      if(typeof o[i] === 'undefined') return null;
      return !!o[i] && o[i];
  }, obj);
}
Util.dot = (path, obj) => Util.nested(obj, path);



Util.array = (val) => {
  if(Array.isArray(val))
    return val
  if(NodeList.prototype.isPrototypeOf(val) || HTMLCollection.prototype.isPrototypeOf(val) || DOMTokenList.prototype.isPrototypeOf(val)) 
    return Array.from(val)
  if(typeof val == 'string' || typeof val == 'number') 
    return [val]
  if(typeof val == 'object') 
    return [val]
  if(typeof val == 'undefined')
    return [] 
};

Util.literal = (template, data={}) => {
  return new Function(...Object.keys(data), `return \`${template}\`;`)(...Object.values(data))
}
Util.express = (expression, data={}) => {
  try {
    return new Function(...Object.keys(data), `return (${expression});`)(...Object.values(data));   
  } catch(err) {
    return false;
  }
 
};

Util.urlparams = {}
Util.urlparams.build = object => Object.keys(object).map(key => encodeURIComponent(key) + '=' + encodeURIComponent(object[key])).join('&');
Util.urlparams.parse= ( url ) => {
  url = url || document.location.href
  let paramstrings = url
  if(url.includes('?')) paramstrings = url.split('?')[1]
  paramstrings = paramstrings.split('#')[0]
  const params = {}
  paramstrings.split('&').forEach((part)=>{
    const v = part.split('=')
    if(v[0]&&v[1])
      params[decodeURIComponent(v[0])] = decodeURIComponent(v[1])
  })
  return params
};
Util.urlparams.set = (key, val, url) => {
  const p = {};
  p[key] = val;
  return Util.urlparams.build({...Util.urlparams.parse(url),...p});
}
Util.urlparams.change = (key, val) => {
  history.replaceState(history.state,null,`${location.pathname}?${Util.urlparams.set(key, val)}`)
}


Util.unique = (input, path) => {
  return input.filter((value, index, self)=>{
    if(!!path){
      return self.map(i=>Util.dot(path,i)).indexOf(Util.dot(path,value)) === index;
    } else {
      return self.indexOf(value) === index;
    }
  })
}

Util.reset = obj => {
  if (!obj) return;
  return JSON.parse(JSON.stringify(obj))
}

// Deep merge two objects.
Util.merge = (target, depth=-1, ...sources) => {
  if (!sources.length || depth===0) return target;
  const source = sources.shift();

  if (Util.is.object(target) && Util.is.object(source)) {
    for (const key in source) {
      if (Util.is.object(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} });
        Util.merge(target[key], --depth, source[key]);
      } else {
        Object.assign(target, { [key]: source[key] });
      }
    }
  }
  return Util.merge(target, --depth, ...sources);
}

Util.is = {
  object: item => {
    return (item && typeof item === 'object' && !Array.isArray(item));
  },
  array: item => {
    return (item && typeof item === 'object' && Array.isArray(item));
  }
}

Util.nest = (path, obj, value) => {
  if (path === '') {
    return value; // Set the value at the base case
  } else {
    const sources = path.split('.');
    const source = sources.shift();
    const remnant = sources.join('.');
    if (!obj.hasOwnProperty(source)) {
      obj[source] = {};
    }
    obj[source] = Util.nest(remnant, obj[source], value);
    return obj; // Return obj here for all other cases
  }
};

Util.filter = (array, filter) => {
  if (!filter) return array
  if (typeof filter == 'string') {
    return array.filter(i => i[filter])
  } else if (typeof filter == 'object') {
    return array.filter(i => {
      for (let prop in filter) {
        if (i[prop] != filter[prop]) return false
      }
      return true
    })
  }
}

Util.filterDeep = (array, filter) => {
    if (!filter) return array
    if (typeof filter == 'string') {
        return array.filter(i => i[filter])
    } else if (typeof filter == 'object') {
        return array.filter(i => {
        for (let prop in filter) {
            if (i[prop] != filter[prop]) return false
        }
        return true
        })
    }
}

    /**
     * Determines if an expected key value pair exist in an object.
     * @param {Object} obj - The option in question.
     * @param {string} key - The key in object to check.
     * @param {string} value - The expected value of key.
     * @returns {Boolean} Return true if key value pair exist in object.
     */
Util.findKeyValue = (obj, key, value) => {
  if (typeof obj !== 'object' || obj === null) return false
  if (obj.hasOwnProperty(key)) return obj[key] === value
  for (let k in obj) {
    if (obj[k] && typeof obj[k] === 'object') {
      let found = Util.findKeyValue(obj[k], key, value)
      if (found) return true
    }
  }
  return false
}


Util.equal = (value, other) => {
  // Get the value type
  var type = Object.prototype.toString.call(value);

  // If the two objects are not the same type, return false
  if (type !== Object.prototype.toString.call(other)) return false;

  // If items are not an object or array, return false
  if (["[object Array]", "[object Object]"].indexOf(type) < 0) return false;

  // Compare the length of the length of the two items
  var valueLen =
    type === "[object Array]" ? value.length : Object.keys(value).length;
  var otherLen =
    type === "[object Array]" ? other.length : Object.keys(other).length;
  if (valueLen !== otherLen) return false;

  // Compare two items
  var compare = function(item1, item2) {
    // Get the object type
    var itemType = Object.prototype.toString.call(item1);

    // If an object or array, compare recursively
    if (["[object Array]", "[object Object]"].indexOf(itemType) >= 0) {
      if (!isEqual(item1, item2)) return false;
    }

    // Otherwise, do a simple comparison
    else {
      // If the two items are not the same type, return false
      if (itemType !== Object.prototype.toString.call(item2)) return false;

      // Else if it's a function, convert to a string and compare
      // Otherwise, just compare
      if (itemType === "[object Function]") {
        if (item1.toString() !== item2.toString()) return false;
      } else {
        if (item1 !== item2) return false;
      }
    }
  };

  // Compare properties
  if (type === "[object Array]") {
    for (var i = 0; i < valueLen; i++) {
      if (compare(value[i], other[i]) === false) return false;
    }
  } else {
    for (var key in value) {
      if (value.hasOwnProperty(key)) {
        if (compare(value[key], other[key]) === false) return false;
      }
    }
  }

  // If nothing failed, return true
  return true;
};