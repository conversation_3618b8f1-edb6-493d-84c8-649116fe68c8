## Util Object - Data Handling Utilities Technical Documentation

The following sections document the "Data Handling Utilities" extensions made to the `Util` object. These extensions provide a variety of data manipulation and transformation functionalities.

### `Util.map(data, map)`
This method extends the `Util` object with a function to map data based on a mapping configuration. It maps source data to a desired structure defined in the mapping configuration.

#### Parameters
- `data` (Object or Array): The source data to be mapped.
- `map` (Object): The mapping configuration that defines how to map the source data.

#### Returns
- Object or Array: The mapped data based on the mapping configuration.

#### Usage
```javascript
const sourceData = { name: '<PERSON>', age: 30 };
const mappingConfig = { fullName: 'name', yearsOld: 'age' };
const mappedData = Util.map(sourceData, mappingConfig);
```

### `Util.nested(obj, path)`
This method extends the `Util` object with a function that retrieves a value from a nested object using a dot-separated path.

#### Parameters
- `obj` (Object): The nested object from which to retrieve the value.
- `path` (String): The dot-separated path to the desired value.

#### Returns
- Any: The value retrieved from the nested object using the specified path.

#### Usage
```javascript
const nestedObject = { user: { name: 'John', age: 30 } };
const value = Util.nested(nestedObject, 'user.name');
```

### `Util.dot(path, obj)`
This method extends the `Util` object by providing an alias for `Util.nested`.

#### Parameters
- `path` (String): The dot-separated path to the desired value.
- `obj` (Object): The nested object from which to retrieve the value.

#### Returns
- Any: The value retrieved from the nested object using the specified path.

#### Usage
```javascript
const nestedObject = { user: { name: 'John', age: 30 } };
const value = Util.dot('user.name', nestedObject);
```

### `Util.array(val)`
This method extends the `Util` object by providing a utility to convert various types of values into arrays.

#### Parameters
- `val` (Any): The value to be converted into an array.

#### Returns
- Array: An array representation of the input value.

#### Usage
```javascript
const stringArray = Util.array('value');
const objectArray = Util.array({ key: 'value' });
```

### `Util.literal(template, data)`
This method extends the `Util` object with a function that generates a string by substituting placeholders with values from a data object.

#### Parameters
- `template` (String): The template string with placeholders.
- `data` (Object): The data object containing values to substitute.

#### Returns
- String: The generated string with placeholders replaced by values.

#### Usage
```javascript
const template = 'Hello, ${name}!';
const data = { name: 'John' };
const result = Util.literal(template, data);
```

### `Util.express(expression, data)`
This method extends the `Util` object with a function that evaluates an expression using data provided as variables.

#### Parameters
- `expression` (String): The expression to be evaluated.
- `data` (Object): The data object containing variables for the expression.

#### Returns
- Any: The result of evaluating the expression.

#### Usage
```javascript
const expression = 'x + y';
const data = { x: 5, y: 3 };
const result = Util.express(expression, data);
```

### `Util.urlparams.build(object)`
This method extends the `Util` object by providing a function to build URL query parameters from an object.

#### Parameters
- `object` (Object): The object containing key-value pairs for query parameters.

#### Returns
- String: The URL query parameter string.

#### Usage
```javascript
const queryParams = { key1: 'value1', key2: 'value2' };
const queryString = Util.urlparams.build(queryParams);
```

### `Util.urlparams.parse(url)`
This method extends the `Util` object by providing a function to parse URL query parameters from a URL.

#### Parameters
- `url` (String, optional): The URL to parse. If not provided, the current document location is used.

#### Returns
- Object: An object containing parsed query parameters.

#### Usage
```javascript
const url = 'https://example.com/?key1=value1&key2=value2';
const queryParams = Util.urlparams.parse(url);
```

### `Util.cookies.get(key)`
This method extends the `Util` object by providing a function to get the value of a cookie by its key.

#### Parameters
- `key` (String, optional): The key of the cookie. If not provided, returns all cookies.

#### Returns
- String or Object: The value of the specified cookie key or an object containing all cookies.

#### Usage
```javascript
const cookieValue = Util.cookies.get('my-cookie');
const allCookies = Util.cookies.get();
```

### `Util.unique(input, path)`
This method extends the `Util` object with a function to return an array of unique values from the input array or object.

#### Parameters
- `input` (Array or Object): The input array or object.
- `path` (String, optional): The dot-separated path to a property for which uniqueness should be checked.

#### Returns
- Array: An array of unique values from the input.

#### Usage
```javascript
const array = [1, 2, 3, 2, 4, 1];
const uniqueArray = Util.unique(array);

const arrayOfObjects = [{ id: 1 }, { id: 2 }, { id: 1 }];
const uniqueObjects = Util.unique(arrayOfObjects, 'id');
```

### `Util.reset(obj)`
This method extends the `Util` object with a function to create a deep copy of an object.

#### Parameters
- `obj` (Object): The object to be deep copied.

#### Returns
- Object: A deep copy of the input object.

#### Usage
```javascript
const originalObject = { key: 'value' };
const copiedObject = Util.reset(originalObject);
```

### `Util.merge(target, depth, ...sources)`
This method extends the `Util` object with a function to perform a deep merge of multiple objects into a target object.

#### Parameters
- `target` (Object): The target object into which the sources are merged.
- `depth` (Number, optional): The depth to which the merge should occur. Default is -1 (unlimited depth).
- `...sources` (Objects): The source objects to merge into the target.

#### Returns
- Object: The merged target object.

#### Usage
```javascript
const target = { a: { b: 1 } };
const source = { a: { c: 2 } };
const mergedObject = Util.merge(target, source);
```

## Util Object - Data Handling Utilities Technical Documentation (Continued)

### `Util.is.object(item)`
This method extends the `Util` object with a function that checks if an item is an object and not an array.

#### Parameters
- `item` (Any): The item to be checked.

#### Returns
- Boolean: `true` if the item is an object and not an array, otherwise `false`.

#### Usage
```javascript
const isObject = Util.is.object({ key: 'value' }); // true
```

### `Util.is.array(item)`
This method extends the `Util` object with a function that checks if an item is an array.

#### Parameters
- `item` (Any): The item to be checked.

#### Returns
- Boolean: `true` if the item is an array, otherwise `false`.

#### Usage
```javascript
const isArray = Util.is.array([1, 2, 3]); // true
```

### `Util.nest(path, obj, value)`
This method extends the `Util` object with a function that nests a value within an object at a specified dot-separated path.

#### Parameters
- `path` (String): The dot-separated path where the value should be nested.
- `obj` (Object): The object within which the value should be nested.
- `value` (Any): The value to be nested.

#### Returns
- Object: The object with the nested value.

#### Usage
```javascript
const originalObject = { user: {} };
const updatedObject = Util.nest('user.name', originalObject, 'John');
```

### Important Note
The provided code introduces "Data Handling Utilities" extensions to the `Util` object, enhancing its capabilities for data manipulation and transformation. These extensions enable you to map data, retrieve nested values, handle arrays and objects, manipulate cookies, create deep copies, merge objects, and more.

Ensure that the `Util` object is properly included and defined before using its extensions in your application. The existing extensions and methods in the `Util` object are not documented here, as per your request. If you have additional extensions or methods in the "Data Handling Utilities" class that you would like documented, please provide the code snippets, and I'll be happy to document them accordingly.