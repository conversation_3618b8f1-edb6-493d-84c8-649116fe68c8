window.Util = window.Util || {}

Util.format = {
  handle: input => {
    return input.normalize("NFD").replace(/%/g,'').replace("'", "").toLowerCase().replace(/[^\w\u00C0-\u024f]+/g, "-").replace(/^-+|-+$/g, "")
  },
  string: (input, template, clip = 'end', pad = 'end') => {
    let output = '';
    let inputIndex = clip == 'start' ? 0 : Math.max(input.length - template.replace(/[^#]/g, '').length, 0);

    // Pad input if necessary
    if (pad == 'start') {
      const padLength = Math.max(template.length - input.length, 0);
      const padChar = template[inputIndex] || ' ';
      output = padChar.repeat(padLength);
    }

    // Iterate through the template and replace placeholders with corresponding input characters
    for (let i = 0; i < template.length; i++) {
      if (inputIndex < input.length) {
        if (template[i] === '#') {
          output += input[inputIndex];
          inputIndex++;
        } else {
          output += template[i];
        }
      } else {
        break;
      }
    }

    // Pad input if necessary
    if (pad == 'end') {
      const padLength = Math.max(template.length - output.length, 0);
      const padChar = template[inputIndex] || ' ';
      output += padChar.repeat(padLength);
    }

    return output;
  },

  number: (input, template) => {
    return Util.format.string(input.toString().replace(/\D/g, ''), template);
  },

  date: (input, outputFormat = 'String', timeZone = 'UTC') => {
    const date = new Date(input);
    const options = {
      timeZone,
    };

    let formattedDate = '';

    switch (outputFormat) {
      case 'LocaleTime':
        formattedDate = date.toLocaleString(undefined, options);
        break;
      case 'Locale':
        formattedDate = date.toLocaleDateString(undefined, options);
        break;
      case 'GMT':
        formattedDate = date.toGMTString();
        break;
      case 'UTC':
        formattedDate = date.toUTCString();
        break;
      case 'ISO':
        formattedDate = date.toISOString();
        break;
      default:
        if (outputFormat.includes('/') || outputFormat.includes('-') || outputFormat.includes('.') || outputFormat.includes(':')) {
          const separator = outputFormat.match(/[/.\-:]/)[0];
          const parts = outputFormat.split(separator);
          const formatOptions = {
            year: parts.includes('y') ? 'numeric' : undefined,
            month: parts.includes('m') ? '2-digit' : parts.includes('n') ? 'numeric' : undefined,
            day: parts.includes('d') ? '2-digit' : parts.includes('j') ? 'numeric' : undefined,
            hour: parts.includes('h') ? '2-digit' : undefined,
            minute: parts.includes('i') ? '2-digit' : undefined,
            second: parts.includes('s') ? '2-digit' : undefined,
            timeZone,
          };
          formattedDate = date.toLocaleDateString(undefined, formatOptions);
        } else {
          formattedDate = date.toLocaleDateString(undefined, {
            timeZone,
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
          });
        }
        break;
    }

    return formattedDate;
  },

  time: (input, format, timeZone) => {
    const options = {
      timeZone,
      hour12: format.includes('h') && format.includes('a'),
      hour: 'numeric',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short',
    };

    const formattedTime = new Date(input).toLocaleTimeString(undefined, options);

    return Util.format.string(formattedTime, format);
  },



};