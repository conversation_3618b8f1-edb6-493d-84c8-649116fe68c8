window.Util = window.Util || {}

Util.select = (qs, el) => {
  el = el || document.body
  if (el.matches("html")) return [];
  if(el.matches(qs)) {
    return el;
  } else if (el.querySelectorAll(qs).length > 0) {
    return Array.from(el.querySelectorAll(qs));
  } else if (el.querySelectorAll(qs).length===0 && !el.matches(qs) && !!el.parentNode) {
    return Util.select(qs,el.parentNode);
  }
  return []
};

Util.form = {
  values: form => {
    let obj = {}
    Util.select('[name]', form).forEach(field => {
      if(!field.name || !field.value) return;
      if (field.type=='radio') {
        if(field.checked){
          obj[field.name] = field.value; 
        }
      }
      else if (field.type=='checkbox') {
        obj[field.name] = obj[field.name] || []
        if(field.checked){
          obj[field.name].push(field.value); 
        }
      }
      else { 
        obj[field.name] = field.value; 
      } 
      return obj 
    })
    return obj
  },
  valid: form => {
    return Array.from(form.querySelectorAll('[required]')).every(input=>input.validity.valid && !!input.value) 
  },
  error:(form, error) => {
    form.querySelector('.form-errors').textContent = error
  }
}

Util.setInnerHTML = (elm, html) => {
  elm.innerHTML = html;
  
  Array.from(elm.querySelectorAll("script"))
    .forEach( oldScriptEl => {
      const newScriptEl = document.createElement("script");
      
      Array.from(oldScriptEl.attributes).forEach( attr => {
        newScriptEl.setAttribute(attr.name, attr.value) 
      });
      
      const scriptText = document.createTextNode(oldScriptEl.innerHTML);
      newScriptEl.appendChild(scriptText);
      
      oldScriptEl.parentNode.replaceChild(newScriptEl, oldScriptEl);
  });
}

