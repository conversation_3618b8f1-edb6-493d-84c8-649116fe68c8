## Util Object - DOM Class Extensions Technical Documentation

The following sections document the "DOM" class extensions made to the `Util` object. These extensions provide additional utility functions for various DOM manipulation tasks.

### `Util.select(qs, el)`
This method extends the `Util` object by providing enhanced element selection capabilities based on a provided CSS query selector within a specified context.

#### Parameters
- `qs` (String): The CSS query selector.
- `el` (HTMLElement, optional): The context element within which to search for matching elements. Default is `document.body`.

#### Returns
- Array of HTMLElements: An array of elements matching the query selector within the specified context.

#### Usage
```javascript
const elements = Util.select('.my-class', containerElement);
```

### `Util.form.values(form)`
This method extends the `Util` object by adding a function to extract form values and return them as an object. It handles various input types like text, radio buttons, and checkboxes.

#### Parameters
- `form` (HTMLFormElement): The form element from which to extract values.

#### Returns
- Object: An object containing the extracted form values.

#### Usage
```javascript
const formElement = document.querySelector('form');
const formValues = Util.form.values(formElement);
```

### `Util.setInnerHTML(elm, html)`
This method extends the `Util` object with a function that sets the inner HTML of an element while preserving embedded scripts within the HTML content. It replaces existing script elements to ensure proper execution.

#### Parameters
- `elm` (HTMLElement): The target element whose inner HTML needs to be set.
- `html` (String): The HTML content to set as the inner HTML of the element.

#### Usage
```javascript
const targetElement = document.getElementById('my-element');
const newHTMLContent = '<p>Hello, <em>world</em>!</p>';
Util.setInnerHTML(targetElement, newHTMLContent);
```

### Important Note
The provided code introduces "DOM" class extensions to the `Util` object, enhancing its capabilities for various DOM manipulation tasks. These extensions improve the utility of the `Util` object by providing functions for enhanced element selection, form value extraction, and setting inner HTML while preserving scripts.

Ensure that the `Util` object is properly included and defined before using its extensions in your application. The existing extensions and methods in the `Util` object, outside the "DOM" class, are not documented here, as per your request. If you have additional extensions or methods in the "DOM" class that you would like documented, please provide the code snippets, and I'll be happy to document them accordingly.