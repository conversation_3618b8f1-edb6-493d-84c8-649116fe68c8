window.Util = window.Util || {};

Util.ScrollActive = {
  init: () => {
    document.addEventListener('DOMContentLoaded', () => { 
      Util.ScrollActive.scroll()
    })
  },

  scroll: () => {
    document.querySelectorAll('.scroll-to-active').forEach(container => {
      const activeEl = container.querySelector('.active') || container;
      const containerRect = container.getBoundingClientRect();
      const elementRect = activeEl.getBoundingClientRect();
      const style = getComputedStyle(container);

      if (style.overflowY === 'scroll') {
        const scrollValueY = elementRect.top - containerRect.top - container.clientHeight / 2 + elementRect.height / 2;
        container.scrollTop = scrollValueY;
      }

      if (style.overflowX === 'scroll') {
        const scrollValueX = elementRect.left - containerRect.left - container.clientWidth / 2 + elementRect.width / 2;
        container.scrollLeft = scrollValueX;
      }
    });
  }
}

window.ScrollActive = Util.ScrollActive;

Util.ScrollActive.init();