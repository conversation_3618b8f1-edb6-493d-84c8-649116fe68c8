window.Util = window.Util || {}

Util.remote = {

  get: (url, config={}) => {

    return new Promise(res=>{

      fetch(url).then( (r) => {
      
        if(!!config.path){
          return r.json()  
        }

        return r.text()

      }).then( (html) => {

        if(!!config.path){
          html = config.path.split('.').reduce((o,i)=> o[i], html);
        }

        if (!!config.select) {

          let doc = new DOMParser().parseFromString(html,'text/html')
          html = doc.querySelector(config.select).innerHTML;
        }

        res(html)

      })

    })

  },

}