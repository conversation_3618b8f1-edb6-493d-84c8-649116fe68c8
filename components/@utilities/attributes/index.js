window.Util = window.Util || {};

Util.AttrLinks = {
  init: () => Util.AttrLinks.processAttr("a[href*='#attr(']", 'href'),

  processAttr: (selector, attrName) => {
    document.querySelectorAll(selector).forEach(el => {
      const val = el.getAttribute(attrName), match = val?.match(/#attr\(([^)]+)\)/);

      if (!match) return;

      const [key, value] = match[1].split(':').map(str => str.trim());
      el.setAttribute(key, value);

      const stripped = val.replace(match[0], '').trim();
      el.setAttribute(attrName, (key === attrName && value !== '/') ? value : (stripped === '/' ? '#' : stripped));
    });
  }
};

window.AttrLinks = Util.AttrLinks;

Util.AttrLinks.init();
