
window.Util = window.Util || {}

Util.visible = (elem, checkViewport = true) => {
  if (!(elem instanceof Element)) {
    return false;
  }
  const style = getComputedStyle(elem);
  if (style.display === "none") return false;
  if (style.visibility !== "visible") return false;
  if (style.opacity < 0.1) return false;
  if (
    elem.offsetWidth +
      elem.offsetHeight +
      elem.getBoundingClientRect().height +
      elem.getBoundingClientRect().width ===
    0
  ) {
    return false;
  }

  if (checkViewport) {
    let elemOffsetWidth = elem.offsetWidth
    let elemOffsetHeight = elem.offsetHeight
    if(elem.offsetWidth === undefined || elem.offsetHeight === undefined){
      elemOffsetWidth = elem.getAttribute('width').replace("px", "");
      elemOffsetHeight = elem.getAttribute('height').replace("px", "");
    }
    const elemCenter = {
      x: elem.getBoundingClientRect().left + elemOffsetWidth / 2,
      y: elem.getBoundingClientRect().top + elemOffsetHeight / 2,
    };
    if (elemCenter.x < 0) return false;
    if (elemCenter.x > (document.documentElement.clientWidth || window.innerWidth))
      return false;
    if (elemCenter.y < 0) return false;
    if (elemCenter.y > (document.documentElement.clientHeight || window.innerHeight))
      return false;

    if (isNaN(elemCenter.x) || isNaN(elemCenter.y)) return false
    let pointContainer = document.elementFromPoint(elemCenter.x, elemCenter.y);
    do {
      if (pointContainer === elem) return true;
    } while ((pointContainer = pointContainer.parentNode || false));
  }

  return true;
};
