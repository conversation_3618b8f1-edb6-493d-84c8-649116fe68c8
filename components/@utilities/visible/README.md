## Util Object - Visibility Utility Technical Documentation

The following section documents the "Visibility Utility" extension made to the `Util` object. This extension provides a utility function to determine the visibility of an element on a web page, including checking whether the element is within the viewport.

### `Util.visible(elem, checkViewport)`
This method extends the `Util` object by providing a function to check the visibility of an element on the web page.

#### Parameters
- `elem` (Element): The element to check for visibility.
- `checkViewport` (<PERSON><PERSON><PERSON>, optional): Specifies whether to also check if the element is within the viewport. Default is `true`.

#### Returns
- <PERSON><PERSON>an: `true` if the element is visible and optionally within the viewport, otherwise `false`.

#### Usage
```javascript
const element = document.getElementById('myElement');
const isVisible = Util.visible(element); // true if visible, false if not
```

#### Important Note
The provided code introduces a "Visibility Utility" extension to the `Util` object, enhancing its capabilities for checking the visibility of elements on a web page. The function checks various conditions, including CSS display, visibility, opacity, and dimensions, to determine the element's visibility. Additionally, it checks if the element is within the viewport if the `checkViewport` parameter is set to `true`.

Ensure that the `Util` object is properly included and defined before using its extensions in your application. The existing extensions and methods in the `Util` object are not documented here, as per your request. If you have additional extensions or methods in the "Visibility Utility" class that you would like documented, please provide the code snippets, and I'll be happy to document them accordingly.