## Details

### Description
The Details component is a versatile utility designed to enhance user interactions with various UI elements such as popovers, modals, accordions, and tabs. It leverages the native HTML `<details>` and `<summary>` elements to create these interactive patterns and manage their behaviors.

### Methods

#### `init()`
This method initializes the Details component and sets up event listeners to handle interactions. It is called automatically when the script is loaded.

##### Usage
```javascript
Details.init();
```

#### `close(group)`
This method closes all `<details>` elements within a specified group by setting their `open` attribute to `false`. This is particularly useful for managing behaviors like accordions and tabs where only one element should be open at a time.

##### Parameters
- `group` (String): The identifier for the group of `<details>` elements to be closed.

##### Usage
```javascript
Details.close('accordion-group');
```

### Event Listeners

#### `click`
The Details component listens for click events on the document. Depending on the type of interaction and the clicked element, the component adjusts the behavior of various UI patterns.

- **Popover**: If a `<details>` element with the attribute `type="popover"` is clicked and the click is outside the popover, the element's `open` attribute is removed to close the popover.

- **Modal**: If a `<details>` element with the attribute `type="modal"` is clicked and the click is outside the modal, the element's `open` attribute is removed to close the modal.

- **Accordion**: If a `<details>` element with the attribute `type="accordion"` is clicked and it contains the clicked element, other `<details>` elements in the same group are closed by removing their `open` attribute.

- **Tabs**: If a `<details>` element with the attribute `type="tabs"` is clicked and it contains the clicked element, other `<details>` elements in the same group are closed by removing their `open` attribute.

#### `keyup`
The Details component listens for the `keyup` event. If the 'Escape' key is pressed, it provides an opportunity to add custom behavior. Currently, the code snippet provided is commented out, but it can be customized to suit specific requirements.

### Properties

- `type` (String): The type of interaction for the `<details>` element. Possible values include `'popover'`, `'modal'`, `'accordion'`, and `'tabs'`.

- `group` (String): The group identifier for grouping related `<details>` elements together, allowing coordinated behavior in accordions and tabs.

### Usage Example
```html
<details type="accordion" group="accordion-group">
    <summary>Accordion Item 1</summary>
    <div>Content for Item 1</div>
</details>
<details type="accordion" group="accordion-group">
    <summary>Accordion Item 2</summary>
    <div>Content for Item 2</div>
</details>

<script src="details.js"></script>
```

In this example, the Details component is used to create an accordion interaction where only one item can be open at a time within the same group.

### Dependencies
- The component relies on the `Util` object, which is expected to provide the `events.dispatch` method for triggering custom events.

### Compatibility
The Details component leverages native HTML `<details>` and `<summary>` elements and standard JavaScript event handling. It is compatible with modern browsers that support these features.

### Important Note
The provided code snippet seems to reference an external `Util` object and event system for event dispatching (`Util.events.dispatch`). Ensure that these dependencies are properly included and defined for the component to work as intended.