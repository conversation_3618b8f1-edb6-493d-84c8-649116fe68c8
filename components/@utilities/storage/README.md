## Util Object - Data Storage Utilities Technical Documentation

The following section documents the "Data Storage Utilities" extension made to the `Util` object. This extension provides utilities for working with web storage (local storage and session storage).

### `Util.storage.get(type, key)`
This method extends the `Util` object by providing a function to retrieve data from web storage (local storage or session storage).

#### Parameters
- `type` (String, optional): The type of web storage to use. Options: `'local'` (default) or `'session'`.
- `key` (String): The key for the data stored in the web storage.

#### Returns
- Any: The retrieved data.

#### Usage
```javascript
const storedData = Util.storage.get('local', 'myKey');
```

### `Util.storage.set(type, key, data)`
This method extends the `Util` object by providing a function to store data in web storage (local storage or session storage).

#### Parameters
- `type` (String, optional): The type of web storage to use. Options: `'local'` (default) or `'session'`.
- `key` (String): The key for storing the data in the web storage.
- `data` (Any): The data to be stored.

#### Usage
```javascript
Util.storage.set('local', 'myKey', { name: '<PERSON>' });
```

### `Util.storage.unset(type, key)`
This method extends the `Util` object by providing a function to remove data from web storage (local storage or session storage).

#### Parameters
- `type` (String, optional): The type of web storage to use. Options: `'local'` (default) or `'session'`.
- `key` (String): The key for the data to be removed from the web storage.

#### Usage
```javascript
Util.storage.unset('local', 'myKey');
```

### `Util.storage.push(type, key, item)`
This method extends the `Util` object by providing a function to push an item into an array stored in web storage (local storage or session storage).

#### Parameters
- `type` (String, optional): The type of web storage to use. Options: `'local'` (default) or `'session'`.
- `key` (String): The key for the array stored in the web storage.
- `item` (Any): The item to be pushed into the array.

#### Usage
```javascript
Util.storage.push('local', 'myArray', 'itemValue');
```

### `Util.storage.pop(type, key, item)`
This method extends the `Util` object by providing a function to remove an item from an array stored in web storage (local storage or session storage).

#### Parameters
- `type` (String, optional): The type of web storage to use. Options: `'local'` (default) or `'session'`.
- `key` (String): The key for the array stored in the web storage.
- `item` (Any): The item to be removed from the array.

#### Usage
```javascript
Util.storage.pop('local', 'myArray', 'itemValue');
```

### Important Note
The provided code introduces "Data Storage Utilities" extensions to the `Util` object, enhancing its capabilities for working with web storage (local storage and session storage). These extensions provide functions to retrieve, store, unset, push, and pop data in web storage.

Ensure that the `Util` object is properly included and defined before using its extensions in your application. The existing extensions and methods in the `Util` object are not documented here, as per your request. If you have additional extensions or methods in the "Data Storage Utilities" class that you would like documented, please provide the code snippets, and I'll be happy to document them accordingly.