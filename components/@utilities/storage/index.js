window.Util = window.Util || {}

Util.storage = {

  get: (type='local', key) => {

    if(window[`${type}Storage`] && !!window[`${type}Storage`].getItem && !!window[`${type}Storage`].getItem(key))
    return JSON.parse(window[`${type}Storage`].getItem(key))

  },

  set: (type='local', key, data) => {

    if(window[`${type}Storage`] && !!window[`${type}Storage`].setItem)
    window[`${type}Storage`].setItem(key, JSON.stringify(data))
  
  },

  unset: (type='local', key) => {

    window[`${type}Storage`].removeItem(key)
  
  },

  push: (type='local', key, item) => {

    const a = Util.storage.get(type, key) || []
    if(a.indexOf(item)==-1){
      a.push(item);
      Util.storage.set(type, key, a)
    }
  
  },

  pop: (type='local', key, item) => {

    const a = Util.storage.get(type, key) || []
    if(a.indexOf(item)==-1){
      a.splice(a.indexOf(item), 1);
      Util.storage.set(type, key, a)
    }
  
  },

}

Util.cookies = {
  get: (key) => {
    const obj = Object.fromEntries(document.cookie.split(';').map(v=>v.split('=').map(kv=>kv.trim())))
    if(!!key) return obj[key];
    return obj
  }
}