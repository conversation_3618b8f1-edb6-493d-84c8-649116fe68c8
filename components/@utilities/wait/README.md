## Util Object - Wait Utilities Technical Documentation

The following section documents the "Wait Utilities" extension made to the `Util` object. This extension provides utility functions for controlling execution timing, including debouncing, throttling, and custom waiting.

### `Util.debounce(func, delay)`
This method extends the `Util` object by providing a function to debounce the execution of another function. It delays the execution of the provided function until a specified time has passed without further calls.

#### Parameters
- `func` (Function): The function to be debounced.
- `delay` (Number, optional): The delay in milliseconds. Default is `100`.

#### Returns
- Function: The debounced version of the provided function.

#### Usage
```javascript
const debouncedFunction = Util.debounce(() => {
  // Code to be executed after a delay
}, 300);
```

### `Util.throttle(func, delay)`
This method extends the `Util` object by providing a function to throttle the execution of another function. It limits the execution of the provided function to occur at a specified interval.

#### Parameters
- `func` (Function): The function to be throttled.
- `delay` (Number, optional): The interval in milliseconds. Default is `100`.

#### Returns
- Function: The throttled version of the provided function.

#### Usage
```javascript
const throttledFunction = Util.throttle(() => {
  // Code to be executed at intervals
}, 500);
```

### `Util.wait(wait)`
This method extends the `Util` object by providing a function that returns a promise that resolves after a specified wait time.

#### Parameters
- `wait` (Number, optional): The wait time in milliseconds. Default is `100`.

#### Returns
- Promise: A promise that resolves after the specified wait time.

#### Usage
```javascript
Util.wait(1000).then(() => {
  // Code to be executed after waiting
});
```

### `Util.tick()`
This method extends the `Util` object by providing a function that returns a promise that resolves after a minimal wait time of 1 millisecond.

#### Returns
- Promise: A promise that resolves after a minimal wait time.

#### Usage
```javascript
Util.tick().then(() => {
  // Code to be executed after the minimal wait time
});
```

### Important Note
The provided code introduces "Wait Utilities" extensions to the `Util` object, enhancing its capabilities for controlling execution timing. These extensions allow you to debounce, throttle, and create custom wait times using promises.

Ensure that the `Util` object is properly included and defined before using its extensions in your application. The existing extensions and methods in the `Util` object are not documented here, as per your request. If you have additional extensions or methods in the "Wait Utilities" class that you would like documented, please provide the code snippets, and I'll be happy to document them accordingly.