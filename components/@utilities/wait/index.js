window.Util = window.Util || {}

// Wait until it stops
Util.debounce = (func, delay=100) => {
  let debounceTimer
  return function() {
      const context = this
      const args = arguments
          clearTimeout(debounceTimer)
              debounceTimer
          = setTimeout(() => func.apply(context, args), delay)
  }
}

//window.addEventListener('scroll',Util.debounce(e=>{console.log('scrolled')},500))


// Only run every n milliseconds
Util.throttle = (func, delay=100) => {
  let shouldFire = true;
  return function() {
    if (shouldFire) {
      func();
      shouldFire = false;
      setTimeout(() => {
       shouldFire = true;
      }, delay)
      }
    }
  }
// window.addEventListener('scroll',Util.throttle(e=>{console.log('scrolling')},500))

Util.wait = (wait=100) => {
  return new Promise(resolve => setTimeout(resolve, wait));
};
Util.tick = () => {
  return new Promise(res => 
    Util.wait(1).then(()=>{res(true)})
  );
};

// Util.err = err => {
//   console.
// }

// Util.try = (func) => {
//   try{
//     func.apply(context, args)
//   } catch(err) {
//     if Util.err(err)
//   }
// }