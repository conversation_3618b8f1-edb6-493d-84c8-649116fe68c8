## Util Object - Events and Hooks Extensions Technical Documentation

The following sections document the "Events" and "Hooks" class extensions made to the `Util` object. These extensions provide event handling and hook mechanisms for managing interactions and extending functionality.

### Util.events.dispatch(event, detail, el, config)
This method extends the `Util` object by providing a utility to dispatch custom events with specified details, target element, and configuration options.

#### Parameters
- `event` (String): The name of the event to dispatch.
- `detail` (Object): The details/data to associate with the event.
- `el` (HTMLElement, optional): The target element on which to dispatch the event. Default is `window`.
- `config` (Object, optional): Configuration options for the dispatched event.

#### Returns
- Boolean: `true` if the event was successfully dispatched, otherwise `false`.

#### Usage
```javascript
Util.events.dispatch('my-custom-event', { key: 'value' }, targetElement, { bubbles: true });
```

### Util.events.promise(event, detail, el, config)
This method extends the `Util` object by providing a utility to dispatch a custom event and return a promise that resolves when the event is successfully dispatched.

#### Parameters
- `event` (String): The name of the event to dispatch.
- `detail` (Object): The details/data to associate with the event.
- `el` (HTMLElement, optional): The target element on which to dispatch the event. Default is `window`.
- `config` (Object, optional): Configuration options for the dispatched event.

#### Returns
- Promise: A promise that resolves when the event is successfully dispatched.

#### Usage
```javascript
Util.events.promise('my-custom-event', { key: 'value' }, targetElement, { bubbles: true })
  .then(() => {
    // Do something after the event is dispatched
  });
```

### Util.hooks.register(event, func)
This method extends the `Util` object with a utility to register hooks for specific events. Hooks are functions that return promises and can be used to extend or modify behavior during event processing.

#### Parameters
- `event` (String): The name of the event to register the hook for.
- `func` (Function): The function that represents the hook. It must return a promise.

#### Returns
- Boolean: `true` if the hook was successfully registered.

#### Usage
```javascript
Util.hooks.register('my-event', data => {
  return new Promise(resolve => {
    // Perform hook-specific operations
    resolve(modifiedData);
  });
});
```

### Util.hooks.process(event, data)
This method extends the `Util` object with a utility to process registered hooks for a specific event. Hooks are processed sequentially, and the data is passed through each hook.

#### Parameters
- `event` (String): The name of the event for which to process the hooks.
- `data` (any): The data to be passed through the hooks.

#### Returns
- Promise: A promise that resolves with the processed data after all hooks have been executed.

#### Usage
```javascript
Util.hooks.process('my-event', inputData)
  .then(processedData => {
    // Processed data after all hooks have been executed
  });
```

### Important Note
The provided code introduces "Events" and "Hooks" class extensions to the `Util` object, enhancing its capabilities for event handling and hook mechanisms. These extensions enable you to dispatch events with custom details, utilize promises for event dispatching, register hooks for specific events, and process registered hooks.

Ensure that the `Util` object is properly included and defined before using its extensions in your application. The existing extensions and methods in the `Util` object are not documented here, as per your request. If you have additional extensions or methods in the "Events" and "Hooks" classes that you would like documented, please provide the code snippets, and I'll be happy to document them accordingly.