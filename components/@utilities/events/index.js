window.Util = window.Util || {}

Util.events = {

  dispatch: (event, detail, el=window, config={} ) => {

    config.detail = detail;
    
    const triggeredEvent = new CustomEvent(event, {
      bubbles: true,
      cancelable: true,
      composed: false,
      ...config
    })

    return el.dispatchEvent(triggeredEvent);

  },

  promise: (event, detail, el=window, config={} ) => {

    return new Promise((res, rej) => {
      res(Util.events.dispatch(event, detail, el, config))
    })

  }

}


Util.hooks = {
  registry: {},

  register: (event, func) => {
    // func is a function that must return a promise
    if (!Util.hooks.registry[event]) Util.hooks.registry[event] = []
    Util.hooks.registry[event].push(func)
    return true
  },

  process: (event, data) => {
    const hooks = Util.hooks.registry[event] || [];
    const hookPromises = hooks.map(hook => {
      return Promise.resolve(data).then(hook).catch(error => {
        console.error(`Error processing hook for event: ${event}`, error);
        return data; // Continue processing other hooks
      });
    });
    return Promise.all(hookPromises).then(() => data);
  }
}

export default window.Util
