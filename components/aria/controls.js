const Controls = {
  set: (element = document.body) => {
    const processElement = (el) => {
      if (el.tagName == 'IFRAME') return false
      const isHidden = !Util.visible(document.getElementById(el.getAttribute('aria-controls')));

      if (el.getAttribute('role'))

      el.setAttribute("aria-expanded", isHidden ? "false" : "true");
    };

    if (element && element.hasAttribute("aria-controls")) {
      return processElement(element);
    }

    const elementsWithAriaHidden = element.querySelectorAll("[aria-controls]");

    elementsWithAriaHidden.forEach((el) => {
      processElement(el);
    });
  },
};

export default Controls;
