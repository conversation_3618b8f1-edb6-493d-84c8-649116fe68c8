import Hidden from './hidden.js'
import Controls from './controls.js'

window.Aria = {
  interval: null,
	init() {
    Aria.interval = setInterval(() => {
      Hidden.setAriaHidden()
      Controls.set()
    }, 500)
  },
  clear() {
    if (!Aria.interval) return false

    clearInterval(Aria.interval)
    Aria.interval = null
  },
  Hidden,
  Controls,
}

document.addEventListener('DOMContentLoaded', window.Aria.init)

