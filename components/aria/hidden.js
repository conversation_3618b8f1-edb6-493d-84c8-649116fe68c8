const Hidden = {
  current_arias: new Map(),
  init: (parent = document) => {
    Hidden.attachHidden(parent);
  },
  attachObserver: (el, action, config) => {
    const observer = new MutationObserver((mutations) =>
      mutations.forEach((mutation) => action(mutation))
    );

    if (el && action && config) {
      observer.observe(el, config);
      Hidden.current_arias.set(el, observer);
    } else {
      console.error("Observers need an element, action, and config");
    }
  },
  detachObservers: () => {
    if (Hidden.current_arias.size) {
      Hidden.current_arias.forEach((observer) => observer.disconnect());
      Hidden.current_arias.clear();
    } else {
      console.warn("There are currently no dynamic aria elements");
    }
  },
  attachHidden: (parent = document) => {
    const elements = parent.querySelectorAll("[aria-hidden]");

    elements.forEach((element) => {
      const action = (mutation) => {
        if (mutation) {
          Hidden.setAriaHidden(element);
        }
      };
      const config = {
        attributeFilter: ["style", "class"],
      };

      Hidden.setAriaHidden(element);
      Hidden.attachObserver(element, action, config);
    });
  },
  setAriaHidden: (element = document.body) => {
    const processElement = (el) => {
      if (el.tagName == 'IFRAME') return false
      const isHidden = !Util.visible(el);

      el.setAttribute("aria-hidden", isHidden ? "true" : "false");
      el.setAttribute("tabindex", isHidden ? -1 : 0);
    };

    if (element && element.hasAttribute("aria-hidden")) {
      return processElement(element);
    } 

    const elementsWithAriaHidden = element.querySelectorAll("[aria-hidden]");

    elementsWithAriaHidden.forEach((el) => {
      processElement(el);
    });
  },
};

export default Hidden;
