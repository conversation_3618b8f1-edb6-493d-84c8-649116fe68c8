const _Shopify = {

  api: (endpoint, data={}) => {

    return new Promise(res => {

      if (customer.identity.email) data.email = customer.identity.email;
      
      fetch(`https://214qygctl8.execute-api.us-west-2.amazonaws.com/shopify/${endpoint || ``}`, {
        method:'POST',
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data)
      }).then(response => response.json())
        .then(r => {
          res(!!r.response ? r.response : r.result || false)
      })
      
    })

  },

  tags: (action, tags) => {
    _Shopify.api(`${action}_tags`, {tags:Util.array(tags)}).then(data=>{
      console.log('data', data)
    })
  }

};
window._Shopify = _Shopify;


window.addEventListener('Customer:subscribe', e => {
  if(e.detail=='loyalty')_Shopify.tags('add',['loyalty_member','tier_1'])
});

window.addEventListener('Customer:unsubscribe', e => {
  if(e.detail=='loyalty')_Shopify.tags('remove',['loyalty_member','tier_1','tier_2','tier_3'])
});