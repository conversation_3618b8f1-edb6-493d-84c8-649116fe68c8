const Antavo = {

  interval: 6000,
  
  source_map: {
    '/account/register':'Loyalty Registration Opt-in'
  },

  api: (endpoint, data={}) => {

    return new Promise(res => {

      data.store_name = store.name;
      data.email = customer.identity.email;
      if (customer.identity.first_name) data.first_name = customer.identity.first_name;
      if (customer.identity.last_name) data.last_name = customer.identity.last_name;
      if (Antavo.source_map[document.location.pathname]) data.source = Antavo.source_map[document.location.pathname];

      fetch(`https://214qygctl8.execute-api.us-west-2.amazonaws.com/account/${endpoint}`, {
        method:'POST',
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data)
      }).then(response => response.json())
        .then(r => {
          res(!!r.response ? r.response : r.result || false)
      })
      
    })

  },

  status: () => {
    Antavo.api('profile', {}).then(profile=>{
      if(profile.status=='active' && !customer.subscriptions.loyalty) Customer.subscriptions.subscribe('loyalty');
      if(profile.status=='opted_out' && !!customer.subscriptions.loyalty) Customer.subscriptions.unsubscribe('loyalty');
      customer.identity.loyalty.points.available = profile.spendable;
      customer.identity.loyalty.points.pending = profile.pending;
      Customer.peripheral('antavo', profile)
    })
  }

}
window.Antavo = Antavo;

/*

window.addEventListener('Customer:subscribe', e=>{
  if (Util.array(e.detail).includes('loyalty') && !!customer.identity.email ){
    Antavo.api('opt_in', {}).then()
  }
})

window.addEventListener('Customer:unsubscribe', e=>{
  if (Util.array(e.detail).includes('loyalty') && !!customer.identity.email ){
    Antavo.api('opt_out', {})
  }
})

window.addEventListener('Customer:identify', e=>{
  if(e.detail.email) {
    Antavo.status()
  }
})

window.addEventListener('DOMContentLoaded', e=>{
  try {
    if(
      (
        customer.peripherals.antavo && 
        Date.now() - customer.peripherals.antavo.updated > Antavo.interval
      ) || 
      (
        !!customer.identity.email &&
        !customer.peripherals.antavo 
      )
    ) Antavo.status();
  } catch(err){}
})

*/
