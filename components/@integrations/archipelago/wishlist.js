window.addEventListener('Wishlist:recall', () => {
  const interval = 600000

  if (!customer.peripherals || !customer.peripherals.wishlist || typeof customer.peripherals.wishlist.updated !== "number") { return false; }
  if (Date.now() - customer.peripherals.wishlist.updated <= interval) return false

  Archipelago.api('event', { event: 'wishlist' }).then(data=>{
    if (!data.properties?.line_items.length) return false

    data.properties.line_items.map(item => {
      if (wishlist.lists[0].items.find(p => p.product.id == item.product_id && p.variant.id == item.item_id)) return false

      wishlist.lists[0].items.push({ 
        product: { 
          handle: item.handle,
          id: item.product_id 
        }, 
        variant: { id: item.item_id }
      })
    })

    Customer.peripheral('wishlist', data)
    Wishlist.store()
  })
})
