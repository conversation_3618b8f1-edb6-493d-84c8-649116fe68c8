const Archipelago = {

  interval: 600000,

  api: (endpoint, data={}) => {

    return new Promise(res => {

      data.store_name = store.name;
      if (customer.identity.email) data.email = customer.identity.email;
      if (customer.identity.phone) data.phone = Util.format.number(customer.identity.phone, '+1##########');
      if (Util.cookies.get('__exponea_etc__')) data.cookie = Util.cookies.get('__exponea_etc__');

      fetch(`https://norwi2iw70.execute-api.us-west-2.amazonaws.com/exponea/${endpoint || `get`}`, {
        method:'POST',
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data)
      }).then(response => response.json())
        .then(r => {
          res(!!r.response ? r.response : r.result || false)
      })
      
    })

  },

  load: () => {
    Archipelago.api('get', {}).then(data=>{
      Customer.peripheral('archipelago',data)
    })
  }

};
window.Archipelago = Archipelago;


window.addEventListener('Customer:identified', e => {
  
  if(e.detail.email || e.detail.phone) {
    Archipelago.load()
  }

});


window.addEventListener('Customer:peripheral', e=>{

  if(!e.detail.archipelago) return;

  const remote = e.detail.archipelago
  
  const consents = remote[1].consents;
  const props = remote[2].properties;

  for(let key in consents) {
    if(consents[key] && !customer.subscriptions[key]) customer.subscriptions[key] = true; // Customer.subscriptions.subscribe(key);
    if(consents[key]===false && !!customer.subscriptions[key]) customer.subscriptions[key] = false; //Customer.subscriptions.unsubscribe(key);
  }

  Customer.update({subscriptions:customer.subscriptions})

  if(!!props.phone && !customer.identity.phone) {
    Customer.identify('phone', Util.format.number(props.phone, '+1##########'));
  }

  Customer.update({profiles:customer.profiles})

})

window.addEventListener('DOMContentLoaded',()=>{
  
  if(!customer.peripherals.archipelago && Util.cookies.get('__exponea_etc__')) 
    Archipelago.load();

  try {
    if(
      (
        customer.peripherals.archipelago && 
        Date.now() - customer.peripherals.archipelago.updated > Archipelago.interval
      ) || 
      (
        !!customer.identity.email &&
        !customer.peripherals.archipelago 
      )
    ) Archipelago.load();
  } catch(err){}

});

