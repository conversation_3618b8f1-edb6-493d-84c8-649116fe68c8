_Exponea.connect.product = {

  item: (item, index=0) => {
    const product = item.product
    const variant = item.variant || item.product.variants[0]
    const _item = {
      variant_id: variant.id || "",
      id: product.id,
      item_style:product.tags.find(t=>t.includes('style:'))?.split(':').at(-1),
      item_category_generc:product.tags.find(t=>t.includes('cat:'))?.split(':').at(-1),
      item_material:product.tags.find(t=>t.includes('material:'))?.split(':').at(-1),
      item_gender:product.tags.find(t=>t.includes('gender:'))?.split(':').at(-1),
      item_type:product.type || product.product_type || "",
      price:variant.price / 100,
      total_price:variant.price / 100,
      sku: variant.sku || "",
      title: variant.title || "",
      option1: variant.option1 || "",
      option2: variant.option2 || "",
      item_count: index+1
    }
    return _item
  },

  action: (action, data) => {
    const track = _Exponea.connect.product.item(data)
    exponea.track(action, track)
    
  }
}


_Exponea.connect.collection = {
  action: (action, data) => {

    exponea.track(action,{
      name:  data.name,
      id:  data.id 
    });
  }

}

window.addEventListener('Products:initialized', e => {

  // if the product is tagged with "Bundle", also action "view_bundle_item"
  _Exponea.connect.product.action('view_item',{product:e.detail,variant:false})

})
window.addEventListener('Products:optionSelected', e => {
  if(e.detail.name=="Color"){
      let handle
      Object.entries(products).find(([k, v]) => {
          if (v.options) {
              return v.options.some(option => {
                if (option.name === e.detail.name) {
                    let match = option.values.find(val => val.value === e.detail.selected_value)
                    if (match) {
                        handle = match.product
                        return true
                    }
                }
                return false
            })
          }
          return false
      })

      e.detail.variant ? 
      _Exponea.connect.product.action('view_item',{product:products[handle],variant:e.detail.variant})
      : _Exponea.connect.product.action('view_item',{product:products[handle],variant:products[handle].variant})
  }
})
window.addEventListener('Products:variantSelected', e => {
  _Exponea.connect.product.action('view_item_variant',{product:products[e.detail.product],variant:e.detail})
})

window.addEventListener('Search:query', e => {
  _Exponea.connect.product.action('search',{search_term:e.detail.query})
})
window.addEventListener('Collection:init', e => {
  _Exponea.connect.collection.action('view_category',{name:collection.title,id:collection.id})
})