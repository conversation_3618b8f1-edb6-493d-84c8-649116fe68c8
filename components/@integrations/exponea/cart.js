_Exponea.connect.cartItem = (item) => {

  const track = {};

  ['id','image','key','product_id','product_title','handle','product_type','quantity','requires_shipping','sku','taxable','title','url','variant_id','variant_title'].forEach(key=>{
    track[key] = item[key]
  });
  ['discounted_price','line_price','original_line_price','original_price','price'].forEach(key=>{
    track[key] = item[key] / 100
  });

  try{
    track.option1 = item.options_with_values[0].value;
  } catch(err){};
  try{
    track.option2 = item.options_with_values[1].value;
  } catch(err){};
  try{
    track.item_category_generc = item.properties._tags.filter(tag=>tag.includes('cat:')).at(-1).split(':')[1];
  } catch(err){};
  try{
    track.item_gender = item.properties._tags.filter(tag=>tag.includes('gender:')).at(-1).split(':')[1];
  } catch(err){};
  try{
    track.item_material = item.properties._tags.filter(tag=>tag.includes('material:')).at(-1).split(':')[1];
  } catch(err){};
  try{
    track.item_style = item.properties._tags.filter(tag=>tag.includes('style:')).at(-1).split(':')[1];
  } catch(err){};

  return track;

}

_Exponea.connect.cart = (item, action, source) => {
  const track = { 
    action:action,
    action_source: (action=='remove') ? 'mini_cart' : source || item.properties._source,
    ..._Exponea.connect.cartItem(item)
  };

  ['currency','item_count','token'].forEach(key=>{
    track[key] = cart[key]
  });
  ['original_total_price','total_price','total_discount'].forEach(key=>{
    track[key] = cart[key] / 100
  });

  // remove quantity from cart, keep only in items
  delete track.quantity;

  track.items = cart.items.map(i=>_Exponea.connect.cartItem(i))

  exponea.track('cart_update', track); 

}


window.addEventListener('Cart:itemAdded', e => {
  _Exponea.connect.cart(e.detail, 'add')
 
})
window.addEventListener('Cart:itemRemoved', e => {
  _Exponea.connect.cart(e.detail, 'remove')
 
})

window.addEventListener('Cart:change', e => {

  const item = e.detail.item;
  if(!item || !item.id) return;

  _Exponea.connect.cart(e.detail.item, item.quantity < e.detail.quantity ? 'add' : 'remove', 'mini_cart')
 
})