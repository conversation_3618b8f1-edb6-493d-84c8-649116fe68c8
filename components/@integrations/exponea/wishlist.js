_Exponea.connect.wishlist = {

  item: (item, index=0) => {
    const _item = {
      handle: item.product.handle,
      item_id: item.variant.id,
      product_id: item.product.id,
      item_style:item.product.tags.find(t=>t.includes('style:')).split(':').at(-1),
      category_level_1:item.product.tags.find(t=>t.includes('cat:')).split(':').at(-1),
      price:item.variant.price / 100,
      total_price:item.variant.price / 100,
      sku: item.variant.sku,
      title: item.variant.title,
      color: item.variant.option1,
      size: item.variant.option2,
      item_count: index+1
    }
    return _item
  },

  action: (action, data) => {
    const track = {
      action:action,
      ..._Exponea.connect.wishlist.item(data.item||data.items[0]),
      line_items:wishlist.lists[0].items.map((_item, _index)=>_Exponea.connect.wishlist.item(_item,_index))
    }
    exponea.track('wishlist',track)
  }
}

window.addEventListener('Wishlist:add', e => {
  _Exponea.connect.wishlist.action('add',e.detail)
})
window.addEventListener('Wishlist:remove', e => {
  _Exponea.connect.wishlist.action('remove',e.detail)
})
