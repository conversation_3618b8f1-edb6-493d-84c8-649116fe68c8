window.addEventListener('Customer:identified', e => {

  if(!e.detail.email && !e.detail.phone) return;

  if(e.detail.email) exponea.identify({ email_id: customer.identity.email });
  if(e.detail.phone) exponea.identify({ phone_id: Util.format.number(customer.identity.phone, '001##########') });

  const update = {
    "domain": store.domain
  }

  if(!!customer.identity.phone) update.phone = Util.format.number(customer.identity.phone, '001##########');
  if(!!customer.identity.email) update.email = customer.identity.email;
  if(!!customer.identity.first_name) update.first_name = customer.identity.first_name;
  if(!!customer.identity.last_name) update.last_name = customer.identity.last_name;

  exponea.update(update)

});

_Exponea.connect.subscription = (action, subscription) => {

  if (!action || !subscription) return;

  const keys = subscription.keys || Util.array(subscription);
  const source = subscription.source;

  if(!keys.length) return

  const track = {
    action: 'new',
    opt_in_type:keys.some(key=>key.includes('sms')) ? 'sms' : 'email',
    marketing_signup:keys.some(key=>key.includes('news-and-offers'))
  }

  if(!!customer.identity.phone) track.phone = Util.format.number(customer.identity.phone, '001##########');
  if(!!customer.identity.email) track.email = customer.identity.email;
  if(!!customer.account.id) track[`shopify_id_${store.brand}`] = customer.account.id;

  if(!!source) {
    track.source = source
  } else if(!!_Exponea.source_map[document.location.pathname]) {
    track.source = _Exponea.source_map[document.location.pathname];
  }

  track.consent_list = keys.map(key=>{

    if(key.includes('loyalty')) track.loyalty_signup = true;
    return {
      action:action,
      category:key,
      valid_until:'unlimited'
    }

  })

  exponea.track('double_opt_in', track);

}
window.addEventListener('Customer:subscribe', e => {

  const key = e.detail
  _Exponea.connect.subscription('accept', e.detail)

});
window.addEventListener('Customer:unsubscribe', e => {

  const key = e.detail
  _Exponea.connect.subscription('reject', e.detail)

});