_Exponea.connect.profile = (endpoint, data={}) => {

  return new Promise(res => {

    if (customer.identity.email) data.email = customer.identity.email;
    
    const sdk_data = { ...data };

    if(!!data.birthday){
      sdk_data.birthday = data.birthday 
    }

    exponea.update(sdk_data)
    exponea.track('profile_update',sdk_data)

    // const api_data = {properties:{...data, ...{store_name:store.domain}}}

    // fetch(`https://norwi2iw70.execute-api.us-west-2.amazonaws.com/exponea/${endpoint || `set`}`, {
    //   method:'POST',
    //   headers: {
    //     "Content-Type": "application/json",
    //   },
    //   body: JSON.stringify(api_data)
    // }).then(response => response.json())
    //   .then(r => {
    //     console.log('response', r)
    //     res(!!r.response ? r.response : r.result || false)
    // })
    
  })

} 


window.addEventListener('Customer:profileUpdate', e => {

  let prefix = ''
  if (e.detail.profile==1) prefix='secondary_';
  
  let data = {}
  for (key in e.detail.updates){
    if(typeof e.detail.updates[key]=='object') continue;
    data[`${prefix}${key}`] = e.detail.updates[key]
  }
  for (key in e.detail.updates.preferences){
    let val = e.detail.updates.preferences[key];
    if(Array.isArray(val)) val = val.join(', ');
    data[`${prefix}${key}`] = val
  }

  _Exponea.connect.profile('set', data)

});

window.addEventListener('Customer:profileSave', e => {

  console.log('Customer:profileSave',e.detail)

});