if(window.mparticle_enable){
  //Page visit event in mParticle

  mParticle.logPageView(
    'page_visit', {
          page: window.location.toString(),
          'referring-page': document.referrer,
          browser: navigator.userAgent,
          device: mParticle.platform ? mParticle.platform:'',
          screenWidth: window.screen.width,
          screenResolution: `${window.screen.width}x${window.screen.height}`,
          screenHeight: window.screen.height,
          postscriptSubscriberId: String(localStorage.getItem("postscriptSubscriberId") || (window.Util.url.param('utm_source') === 'postscript' && window.Util.url.param('utm_medium') === 'sms' && window.Util.url.param('s-id')) || '')
          // test : {
          //   'test':'test',
          //   'work':'work'
          //   }
      }
  );


  function debounce(fn, delay) {
    let timeoutId;
    return function (...args) {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      timeoutId = setTimeout(() => {
        fn.apply(this, args);
      }, delay);
    };
  }
  
  // Event for pdp pages (view item and view item variant)

  _mParticle ={ 
    logEventForProduct: (product, variant, action) => {
      mParticle.logEvent(
          action,
          mParticle.EventType.Navigation,
          {
            location: window.location.toString(),
            screenWidth: window.screen.width,
            screenResolution: `${window.screen.width}x${window.screen.height}`,
            screenHeight: window.screen.height,
            variant_id: variant.id || "",
            id: product.id,
            item_style: product.tags.find(t => t.includes('style:'))?.split(':').at(-1),
            item_category_generc: product.tags.find(t => t.includes('cat:'))?.split(':').at(-1),
            item_material: product.tags.find(t => t.includes('material:'))?.split(':').at(-1),
            item_gender: product.tags.find(t => t.includes('gender:'))?.split(':').at(-1),
            item_type: product.type || product.product_type || "",
            price: variant.price / 100,
            total_price: variant.price / 100,
            sku: variant.sku || "",
            title: variant.title || "",
            option1: variant.option1 || "",
            option2: variant.option2 || "",
            item_count: 1
          }
      );
    },

    onFormSubmit(data) {

      window.mParticle.ready(function() {
          let userIdentities = {}
          let email = data.email || null
          if (email) userIdentities.email = email
          let phone = data.phone || null
          if (phone) userIdentities.mobile_number = phone
          let identityRequest = { userIdentities: userIdentities }

          const identityCallback = function(result) {
                  if (result.getUser()) {
                      result.getUser().setUserAttribute('email', email)
                      if (phone) result.getUser().setUserAttribute('phone', phone)
  
                      // If previous user was anonymous or does not exist and identification is not by phone, then alias
                      let previousUser = result.getPreviousUser()
                      if (previousUser && _mParticle.isUserAnonymous(previousUser) && !phone) {
                          var aliasRequest = mParticle.Identity.createAliasRequest(result.getPreviousUser(), result.getUser())
                          mParticle.Identity.aliasUsers(aliasRequest)
                      }
                  } else {
                      console.error('Failed to set user identity with mParticle', result.getErrors())
                  }
          }
  
          // ALIASING LOGIC
          let currentUser = mParticle.Identity.getCurrentUser()

          // If phone now exists user and needs to be modified, do not alias
          if (phone && currentUser && getCurrentPhone(currentUser) !== phone) {
              mParticle.Identity.modify({
                  userIdentities: { mobile_number: phone }
              })
          }
  
          // If user is anonymous or different email, then login
          // if user email is different and NOT anonymous, then logout and login without alias
          // If logged in with the same email, do nothing
          if (!currentUser || isUserAnonymous(currentUser)) {
              mParticle.Identity.login(identityRequest, identityCallback)
          } else if (getCurrentEmail(currentUser) !== email) {
              mParticle.Identity.logout({}, function(logoutResult) {
                  if (logoutResult.getUser()) {
                      mParticle.Identity.login(identityRequest, identityCallback)
                  }
              })
          }
      })
    },

    isUserAnonymous(user) {
      return Object.keys(user.getUserIdentities().userIdentities).length === 0
    },

    getCurrentEmail(user) {
      return user.getUserIdentities().userIdentities.email || ''
    },

    getCurrentPhone(user) {
      return user.getUserIdentities().userIdentities.mobile_number || ''
    }
  }

  function isUserAnonymous(user) {
    return Object.keys(user.getUserIdentities().userIdentities).length === 0
  }

  function getCurrentEmail(user) {
      return user.getUserIdentities().userIdentities.email || ''
  }

  function getCurrentPhone(user) {
      return user.getUserIdentities().userIdentities.mobile_number || ''
  }

  window.addEventListener('Products:initialized', debounce(e => {

    const product = e.detail;
    const variant = e.detail.variant || products[handle].variants[0]

    _mParticle.logEventForProduct(product, variant , 'view_item');

  }, 300));

  window.addEventListener('Products:optionSelected', debounce(e => {
    if(e.detail.name=="Color"){

    let handle
    Object.entries(products).find(([k, v]) => {
        if (v.options) {
            return v.options.some(option => {
              if (option.name === e.detail.name) {
                  let match = option.values.find(val => val.value === e.detail.selected_value)
                  if (match) {
                      handle = match.product
                      return true
                  }
              }
              return false
          })
        }
        return false
    })

      const variant = e.detail.variant || products[handle].variants[0];

      _mParticle.logEventForProduct(products[handle], variant, 'view_item');
    }
  }, 300));


  window.addEventListener('Products:variantSelected', debounce(e => {
    const product = products[e.detail.product];
    const variant = e.detail;
    _mParticle.logEventForProduct(product, variant, 'view_item_variant');
  }, 300)); 

  _mParticle.cartItem = (item) => {

    const track = {};

    ['id','image','key','product_id','product_title','handle','product_type','requires_shipping','sku','taxable','title','url','variant_id','variant_title'].forEach(key=>{
      track[key] = item[key]
    });
    ['discounted_price','line_price','original_line_price','original_price','price'].forEach(key=>{
      track[key] = item[key] / 100
    });

    track.option1 = item.options_with_values[0].value;
    track.option2 = item.options_with_values[1].value;
    try{
      track.item_category_generc = item.properties._tags.filter(tag=>tag.includes('cat:')).at(-1).split(':')[1];
    } catch(err){};
    try{
      track.item_gender = item.properties._tags.filter(tag=>tag.includes('gender:')).at(-1).split(':')[1];
    } catch(err){};
    try{
      track.item_material = item.properties._tags.filter(tag=>tag.includes('material:')).at(-1).split(':')[1];
    } catch(err){};
    try{
      track.item_style = item.properties._tags.filter(tag=>tag.includes('style:')).at(-1).split(':')[1];
    } catch(err){};

    return track;

  }

  _mParticle.cart = (item, action, source) => {

    const track = { 
        location: window.location.toString(),
      screenWidth: window.screen.width,
      screenResolution: `${window.screen.width}x${window.screen.height}`,
      screenHeight: window.screen.height,
      action:action,
      action_source: (action=='remove') ? 'mini_cart' : source || item.properties._source,
      ..._mParticle.cartItem(item)
    };

    ['currency','item_count','token'].forEach(key=>{
      track[key] = cart[key]
    });
    ['original_total_price','total_price','total_discount'].forEach(key=>{
      track[key] = cart[key] / 100
    });

    track.items = cart.items.map(i=>_mParticle.cartItem(i))
    track.items = JSON.stringify(track.items)

    mParticle.logEvent(
        'cart_update',
      mParticle.EventType.Navigation,
        track
      
    );


  }


  window.addEventListener('Cart:itemAdded', e => {

    _mParticle.cart(e.detail, 'add')
  
  })
  window.addEventListener('Cart:itemRemoved', e => {

    _mParticle.cart(e.detail, 'remove')
  
  })

  window.addEventListener('Cart:change', e => {

    const item = e.detail.item;
    if(!item || !item.id) return;

    _mParticle.cart(e.detail.item, item.quantity < e.detail.quantity ? 'add' : 'remove', 'mini_cart')
  
  })

  window.addEventListener('Search:query', e => {
    //_Exponea.connect.product.action('search',{search_term:e.detail.query})
    mParticle.logEvent('search',
    mParticle.EventType.Search,
    {
      search_term:e.detail.query
    })
  })
  window.addEventListener('Collection:init', e => {
    //_Exponea.connect.collection.action('view_category',{name:collection.title,id:collection.id})
    mParticle.logEvent('view_category',
    mParticle.EventType.Navigation,
    {
      name:collection.title,
      id:collection.id
    })

  })

  // iframe example
  window.addEventListener('message', function(event) {
      if (event.data.type === 'test-signup') {
          _mParticle.onFormSubmit(event.data)
      }
  })

  // klaviyo forms
  window.addEventListener("klaviyoForms", function(e) { 
    if (e.detail.type == 'submit' || e.detail.type == 'stepSubmit') {
        const email = document.querySelector(".needsclick input[type='email']")
        const phone = document.querySelector(".needsclick input[type='tel']")

        const data = {}
        if (phone && phone.value) {
          data.phone = phone.value
          _mParticle.onFormSubmit(data)
        }
        if (email && email.value) {
            data.email = email.value
            _mParticle.onFormSubmit(data)
        }
    }
  })

  // attentive forms
  window.addEventListener("message", function(event) {
    if (event.data.event === "AttentiveSubmit") {
      const data = {}
      if (event.data.phone) {
        data.phone = event.data.phone
      }
      if (event.data.email) {
          data.email = event.data.email
          _mParticle.onFormSubmit(data)
      }
    }
  })

  let m_getCookie = cname => {
    const name = cname + '=';
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') {
            c = c.substring(1);
        }
        if (c.indexOf(name) === 0) {
            return c.substring(name.length, c.length);
        }
    }
    return undefined;
  };

  function isUserAnonymous_f(user) {
    const userIdentities = user.getUserIdentities().userIdentities
    return Object.keys(userIdentities).length === 0
  }

  function getCurrentEmail_f(user) {
    return user.getUserIdentities().userIdentities.email || ''
  }

  function getCurrentPhone_f(user) {
    return user.getUserIdentities().userIdentities.mobile_number || ''
  }

  window.addEventListener('Customer:identified', e => {
    if(!e.detail.email && !e.detail.phone) return;

    window.mParticle.ready(function() {
      let userIdentities_f = {}
      let email_f = ''
      if(e.detail.email){
        email_f = e.detail.email
        if (email_f) userIdentities_f.email = email_f
      }

      let phone_f = ''

      if(e.detail.phone){
        phone_f = Util.format.number(e.detail.phone, '##########')
        if (phone_f) userIdentities_f.mobile_number = phone_f
      }

      let m_cart_id_f = String(m_getCookie('cart'))
      if (m_cart_id_f) userIdentities_f.other3 = m_cart_id_f

      let m_exponea_etc_f = String(m_getCookie('__exponea_etc__'))
      if (m_exponea_etc_f) userIdentities_f.other4 = m_exponea_etc_f

      let identityRequest_f = { userIdentities: userIdentities_f }

      const identityCallback_f = function(result) {
          if (result.getUser()) {
            if (phone_f){}

            if (email_f) result.getUser().setUserAttribute('email', email_f)
            if (phone_f) result.getUser().setUserAttribute('phone', phone_f)

            // If previous user was anonymous and identification is not by phone, then alias
            if (result.getPreviousUser() && isUserAnonymous_f(result.getPreviousUser())) {
              var aliasRequest_f = mParticle.Identity.createAliasRequest(result.getPreviousUser(), result.getUser())
              mParticle.Identity.aliasUsers(aliasRequest_f)
            }
            
          } else {
            console.error('Failed to set user identity with mParticle', result.getErrors())
          }
      }

      // ALIASING LOGIC
      let currentUser_f = mParticle.Identity.getCurrentUser()

        let userIdentities
        // If user is anonymous or different email, then login
        // if user email is different and NOT anonymous, then logout and login without alias
        // If logged in with the same email, do nothing
        if (!currentUser_f || isUserAnonymous_f(currentUser_f)) {
          mParticle.Identity.login(identityRequest_f, identityCallback_f)
        } else if (email_f && getCurrentEmail_f(currentUser_f) && (getCurrentEmail_f(currentUser_f) !== email_f)) {
          mParticle.Identity.logout({}, function(logoutResult) {
            if (logoutResult.getUser()) {
              mParticle.Identity.login(identityRequest_f)
            }
          })
        }
        // else if (!phone_f && email_f && getCurrentPhone_f(currentUser_f) ) {
        //   console.log('mparticle - user is already logged in with phone and enter email')
        //   mParticle.Identity.logout({}, function(logoutResult) {
        //     if (logoutResult.getUser()) {
        //       console.log('mparticle - logout successful', logoutResult)
        //       mParticle.Identity.login(identityRequest_f, identityCallback_f)
        //     }
        //   })
        // }
        else if (phone_f && getCurrentPhone_f(currentUser_f) && (getCurrentPhone_f(currentUser_f) !== phone_f)) {
          // If user exists with phone and now added different phone then needs to be modified, do not alias.
          mParticle.Identity.modify({
              userIdentities: { mobile_number: phone_f }
          })
        } else if (!email_f && phone_f && getCurrentEmail_f(currentUser_f)) {
          mParticle.Identity.modify({
              userIdentities: { mobile_number: phone_f }
          })
        }
        // else if (getCurrentPhone_f(currentUser_f) !== phone_f) {
        //   console.log('mparticle - user is logged in with different phone')
        //   mParticle.Identity.logout({}, function(logoutResult) {
        //     if (logoutResult.getUser()) {
        //       console.log('phone mparticle - logout successful', logoutResult)
        //       mParticle.Identity.login(identityRequest_f, identityCallback_f)
        //     }
        //   })
        // }
  
    })
    
  })  

}

window.addEventListener("postscriptReady", function(ev) {
    window.addEventListener("postscriptPopup", function(ev) {
      if (ev.detail.type === 'formSubmit') {
        const { values } = ev.detail
		let userIdentities = {}
		let email = values.email || null
		if (email) userIdentities.email = email
		let phone = values.phone || null
		if (phone) userIdentities.mobile_number = phone
		let identityRequest = { userIdentities: userIdentities }
		let currentUser = mParticle.Identity.getCurrentUser()
		
		const identityCallback = function(result) {
                  if (result.getUser()) {

                      if (email) result.getUser().setUserAttribute('email', email)
                      if (phone) result.getUser().setUserAttribute('phone', phone)

                      // If previous user was anonymous or does not exist and identification is not by phone, then alias
                      let previousUser = result.getPreviousUser()
                      if (previousUser && _mParticle.isUserAnonymous(previousUser) && !phone) {
                          // console.log('mparticle - set user alias')
                          let aliasRequest = mParticle.Identity.createAliasRequest(result.getPreviousUser(), result.getUser())
                          mParticle.Identity.aliasUsers(aliasRequest)
                      }
                  } else {
                      // console.error('Failed to set user identity with mParticle', result.getErrors())
                  }
          }
		  
		
		if (!currentUser || _mParticle.isUserAnonymous(currentUser)) {
              // console.log('mparticle - user is anonymous or not logged in')
		  mParticle.Identity.login(identityRequest, identityCallback)
		} else if (_mParticle.getCurrentEmail(currentUser) !== email) {
		  // console.log('mparticle - user is logged in with different email')
		  mParticle.Identity.logout({}, function(logoutResult) {
			  if (logoutResult.getUser()) {
				  // console.log('mparticle - logout successful', logoutResult)
				  mParticle.Identity.login(identityRequest, identityCallback)
			  }
		  })
		}
	  }
    });
  });
