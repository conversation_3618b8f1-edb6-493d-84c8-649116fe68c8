{"name": "M<PERSON><PERSON><PERSON>", "settings": [{"type": "checkbox", "label": "Connect to Mparticle", "id": "mparticle_enable"}, {"type": "text", "id": "key_mparticle", "label": "Key For Mparticle"}, {"type": "checkbox", "label": "isDevelopmentMode", "id": "is_development_mode", "default": false}, {"type": "select", "id": "logLevel", "label": "Log level", "options": [{"value": "warning", "label": "warning"}, {"value": "verbose", "label": "verbose"}], "default": "warning", "info": "Select log handler"}, {"type": "checkbox", "label": "Enable logout function", "id": "logout_function", "default": false}]}