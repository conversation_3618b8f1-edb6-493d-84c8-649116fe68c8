window.addEventListener('Templates:render', Util.debounce(e=>{
    // return false;
    ratingSnippet("ruk_rating_snippet",{
      store: reviewsIoStore,
      color: 'var(--reviews-star-color)',
      textClr: 'var(--reviews-text-color)',
      linebreak: false,
      text: '',
      lang: reviewsIoLang,
      mode: 'default',
      showEmptyStars: true,
      callback:()=>{
        document.querySelectorAll('.ruk-rating-snippet-count').forEach(el=>{
          el.innerText = el.innerText.replace(' ','')
          el.setAttribute('aria-label','read-reviews');
          el.setAttribute('aria-hidden','true');
          el.setAttribute('role','link');
        })
        document.querySelectorAll('.ruk_rating_snippet').forEach(el=>{
          el.classList.remove('ruk_rating_snippet');
          el.classList.add('ruk_rating_snippet--loaded')
        })
      }
    });
},100));

document.querySelectorAll('.product-header .review-snippet').forEach(el=>{
  el.addEventListener('click', ()=>{
    document.querySelector('reviewsio-product-reviews-widget').scrollIntoViewIfNeeded()
  })
})

window.addEventListener('Collection:filter', e=>{
  document.querySelectorAll('[product-grid] .ruk_rating_snippet--loaded').forEach(el=>{
    el.classList.remove('ruk_rating_snippet--loaded');
    el.classList.add('ruk_rating_snippet')
  })
})
