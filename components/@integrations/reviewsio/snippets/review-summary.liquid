{% if product %}
  <div class="review-snippet">
<div class="ruk_rating_snippet" data-sku="{{ product.handle }};{{ product.variants | map: 'sku' | join: ';' }};{{ product.variants | map: 'id' | join: ';' }}"></div>  </div>
{% else %}
<template x-if="{% if sku_path %}{{ sku_path }}{% else %}{{ path }}variants[0].sku{% endif %}">
  <div class="review-snippet">
    <div class="ruk_rating_snippet ruk-rating-snippet" :data-sku="{% if sku_path %}{{ sku_path }}{% else %}{{ path }}product.variants[0].sku{% endif %}"></div>
  </div>
</template>
{% endif %}