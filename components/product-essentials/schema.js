module.exports = {
  "ProductForm": [
    {
      "type": "product",
      "id": "{{ id_prefix }}product",
      "label": "Product"
    }
  ],
  "ProductHeader": [
    {
      "type": "liquid",
      "id": "title_source",
      "label": "Product Title Source",
      "info": "Liquid to extract the product title"
    },
    {
      "type": "liquid",
      "id": "metafield_subtitle_value",
      "label": "Product Metafield Subtitle"
    },
    {
      "type": "checkbox",
      "id": "meta_subtitle_check",
      "label": "Display Subtitle",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "price",
      "label": "Display Price",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "type",
      "label": "Display Product Type",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "reviews",
      "label": "Display Review Summary",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "display_dynamic_product_title",
      "label": "Display Dynamic Product Title",
      "default": false
    }
  ],
  "VariantSelector": [
    {
      "type": "checkbox",
      "id": "{{ id_prefix }}history_state",
      "label": "Product State Change",
      "default": true,
      "info": "Change URL and product gallery on product sibling selection"
    },
    {
      "type": "select",
      "id": "{{ id_prefix }}availability_model",
      "label": "Option Availability Model",
      "default": "progressive",
      "options": [
        {
          "value": "cascade",
          "label": "Cascade"
        },
        {
          "value": "progressive",
          "label": "Progressive"
        },
        {
          "value": "reflexive",
          "label": "Reflexive"
        }
      ]
    },
    {
      "type": "paragraph",
      "content": "Cascade: Evaluates availability top-down. Selections in earlier options impact subsequent ones but not vice versa. For instance, choosing in option 1 affects options 2 and 3, but a choice in option 2 only impacts option 3.\n2."
    },
    {
      "type": "paragraph",
      "content": "Progressive: Assesses product options based on all previous choices, successively refining available selections.\n3."
    },
    {
      "type": "paragraph",
      "content": "Reflexive: Availability is dictated only by the latest selection, disregarding prior choices."
    },
    {
      "type": "text",
      "id": "{{ id_prefix }}unselected",
      "label": "Unselected Button Text",
      "default": "Select a Size"
    },
    {
      "type": "text",
      "id": "{{ id_prefix }}unavailable",
      "label": "Unavailable Button Text",
      "info": "For option combinations that do not have a matching variant",
      "default": "Unavailable"
    },
    {
      "type": "text",
      "id": "{{ id_prefix }}out_of_stock",
      "label": "Out of Stock Button Text",
      "default": "Out of Stock"
    },
    {
      "type": "checkbox",
      "id": "enable_strikethrough",
      "label": "Enable Strikethrough on Unavailable products",
      "default": true
    },
    {
      "type": "text",
      "id": "{{ id_prefix }}available",
      "label": "Available (Add to Cart) Button Text",
      "default": "Unavailable"
    },
    {
      "type": "range",
      "id": "variants_displayed_on_mobile",
      "label": "Displayed Variants On Mobile",
      "min": 3,
      "max": 6,
      "step": 0.1,
      "default": 3.5
    },
    {
      "type": "checkbox",
      "id": "color_option_wrap_mobile",
      "label": "Wrap Color Option On Mobile"
    },
    {
      "type": "range",
      "id": "variants_displayed_on_desktop",
      "label": "Displayed Variants On Desktop",
      "min": 3,
      "max": 12,
      "step": 1,
      "default": 4
    },
    {
      "type": "header",
      "content": "Product Siblings Settings"
    },
    {
      "type": "liquid",
      "id": "{{ id_prefix }}siblings_collection",
      "label": "Product Siblings Collection"
    },
    {
      "type": "product_list",
      "id": "{{ id_prefix }}sibling_products",
      "label": "Product Siblings",
      "info": "Specific products override Collection"
    },
    {
      "type": "header",
      "content": "Size Guide Button"
    },
    {
      "type": "checkbox",
      "id": "{{ id_prefix }}disabled",
      "label": "Disable Button",
      "default": true
    },
    {
      "type": "text",
      "id": "{{ id_prefix }}title",
      "label": "Title",
      "info": "Internal Reference only"
    },
    {
      "type": "liquid",
      "id": "{{ id_prefix }}inclusion_js",
      "label": "Inclusion Logic (JavaScript)",
      "info": "JavaScript code that evaluates to a non-false value will output this button"
    },
    {
      "type": "select",
      "id": "{{ id_prefix }}style",
      "label": "Button Style",
      "options": [
        {
          "value": "@include ButtonStyle",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "{{ id_prefix }}button_class_size",
      "label": "Button Size",
      "default": "",
      "options": [
        {
          "value": "button--small",
          "label": "Small"
        },
        {
          "value": "",
          "label": "Standard"
        },
        {
          "value": "button--large",
          "label": "Large"
        }
      ]
    },
    {
      "type": "text",
      "id": "{{ id_prefix }}button_text",
      "label": "Button Text"
    },
    {
      "type": "text",
      "id": "{{ id_prefix }}leading_icon",
      "label": "Leading Icon"
    },
    {
      "type": "text",
      "id": "{{ id_prefix }}trailing_icon",
      "label": "Trailing Icon"
    },
    {
      "type": "liquid",
      "id": "{{ id_prefix }}onclick",
      "label": "Click Event",
      "info": "JavaScript onclick event"
    },
    {
      "type": "header",
      "content": "Variant Segments"
    },
    {
      "type": "select",
      "id": "variant_segment_design",
      "label": "Design",
      "default": "tabs",
      "options": [
        {
          "value": "tabs",
          "label": "Tabs"
        },
        {
          "value": "list",
          "label": "List"
        }
      ]
    },
    {
      "type": "text",
      "id": "variant_segment",
      "label": "Variant Segment",
      "info": "This is the namespace and key for the metafield of the variant. (ex. theme.variant_split)",
      "visible_if": "{% raw %}{{ block.settings.variant_segment_design == 'list' }}{% endraw %}"
    },
    {
      "type": "text",
      "id": "variant_segment_default",
      "label": "'Default' Segment Title",
      "default": "Core",
      "visible_if": "{% raw %}{{ block.settings.variant_segment_design == 'list' }}{% endraw %}",
    },
    {
      "type": "text",
      "id": "variant_segment_all",
      "label": "'All Variants' Segment Title",
      "default": "All",
      "info": "Label for the tab that shows all colors",
      "visible_if": "{% raw %}{{ block.settings.variant_segment_design == 'tabs' }}{% endraw %}",
    },
    {
      "type": "range",
      "id": "variant_segment_max_tabs",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "Maximum number of tabs to display",
      "visible_if": "{% raw %}{{ block.settings.variant_segment_design == 'tabs' }}{% endraw %}",
    },
    {
      "type": "select",
      "id": "variant_segment_default_tab",
      "label": "Default tab",
      "options": [
        {
          "value": "all",
          "label": "All"
        },
        {
          "value": "first",
          "label": "First Available Tab"
        },
        {
          "value": "plp_selection",
          "label": "Based on PLP Selection"
        }
      ],
      "default": "all",
      "visible_if": "{% raw %}{{ block.settings.variant_segment_design == 'tabs' }}{% endraw %}",
    },
    {
      "type": "select",
      "id": "variant_segment_class_margin",
      "label": "Variant Segment Spacing",
      "default": "mt-md",
      "options": [
        {
          "value": "@include Spacing prop:mt",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "textarea",
      "id": "option_exclude",
      "label": "Option Exclusion",
      "info": "Add the names of any Variant Options you would like to exclude on a new line"
    },
    {
      "type": "header",
      "content": "Alternate Layout"
    },
    {
      "type": "select",
      "id": "variant_selector_class_layout",
      "label": "Layout",
      "options": [
        {
          "label": "Default",
          "value": ""
        },
        {
          "label": "Table",
          "value": "variant-selector--table"
        }
      ],
      default: ""
    },
    {
      "type": "header",
      "content": "Product Color"
    },
    {
      "type": "select",
      "id": "product_color_option_type",
      "label": "Product Color Option Type",
      "default": "image",
      "options": [
        {
          "label": "Image",
          "value": "image"
        },
        {
          "label": "Swatch Color",
          "value": "swatch"
        },
        {
          "label": "Swatch Image",
          "value": "swatch_image"
        }
      ]
    },
    {
      "type": "color",
      "id": "product_default_swatch_color_1",
      "label": "Product Default Swatch Color 1",
      "default": "#000000"
    },
    {
      "type": "checkbox",
      "id": "product_color_hover_info",
      "label": "Product Color Hover Info",
      "default": false
    }
  ],
  "FitGuide": [
    {
      "type": "paragraph",
      "content": "This feature is powered by tags. On a product the tag format follows: \n\"{Category}:{value}\""
    },
    {
      "type": "paragraph",
      "content": "The product tag can use the option value or a numerical value. For Example: \"size:small\" or \"width:50\" "
    },
    {
      "type": "textarea",
      "label": "Categories",
      "id": "{{ id_prefix }}categories"
    },
    {
      "type": "text",
      "label": "Categories (Dynamic)",
      "id": "{{ id_prefix }}categories_dynamic"
    },
    {
      "type": "checkbox",
      "label": "Make Collapsable",
      "id": "{{ id_prefix }}collapse"
    }
  ]
}
