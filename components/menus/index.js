const Menu = {

  open: menu => {
    document.querySelector('.menus').setAttribute('data-active-menu', menu)
  },

  toggle: menu => {
    if (document.querySelector('.menus').getAttribute('data-active-menu') == menu) return Menu.close();
    return Menu.open(menu)
  },

  close:() => {
    document.querySelector('.menus').removeAttribute('data-active-menu')
  },

  desktopOpen: (menu, element) => {
    Menu.open(menu);
    document.querySelectorAll('.main-navigation-bar-desktop .active-menu').forEach(function(elm){
      elm.classList.remove('active-menu');
    });
    element.classList.add('active-menu');
  }

}

window.addEventListener('Modal:close', e=>{Menu.close()})

window.Menu = Menu
export default Menu