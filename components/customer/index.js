const Customer = {

	init:() => {
		if(localStorage.getItem('customer')){
			window.customer = Util.merge(customer, -1, JSON.parse(localStorage.getItem('customer')))
			// window.customer = { ...customer, ...JSON.parse(localStorage.getItem('customer'))}
		}
		Customer.sync();
		Customer.profile.init()
	},

	sync: () => {
		['email','first_name','last_name','phone','id'].forEach(field => {
			if (!customer.identity[field] && !!customer.account[field]) 
				Customer.identify(field, customer.account[field]);
		});
	},

	get:() => {
		return new Promise((res, rej) => {
			fetch('/account?view=data').then(r=>{
				if (r.redirected) { res(false); return; }
				r.json().then(d=>{
					Customer.set(d.customer).then(c=>{
						res(d.customer)	
					})
				})	
			})
		})
	},

	set:customer => {
    
    return new Promise( (res, rej) => {

    	if(customer && window.customer) {
    		customer = {...window.customer, ...customer}
    		const stored_customer = {... customer}
    		delete stored_customer.account
    		Util.storage.set('local','customer',stored_customer)
    	} else {
    		localStorage.removeItem('customer')
    	}
			
		window.customer = customer
      
      	Util.events.dispatch('Customer:set', customer)
      	res(customer);

    })

	},

	logout:() => {
		return new Promise((res, rej) => {

      if(!Util.events.dispatch('Customer:logout')) { res({}); return; }

			fetch('/account/logout').then(r=>r.text()).then(d=>{
				window.customer.account = false
				Customer.set({}).then(c=>{
					res(d.customer)	
				})
			})
		})
	},

	login:(login, password) => {

		let email = login

		if(typeof login == 'object' && login.email) {
			password = login.password
			email = login.email
		}

		return new Promise((res, rej) => {

			if(!Util.events.dispatch('Customer:login', {email:email,password:password})) { res(false); return; }

			fetch('/account/login', {
				method:'POST',
				headers:{
					'Content-Type':'application/x-www-form-urlencoded'
				},
				body: Util.urlparams.build({
					form_type:'customer_login',
					utf8:'✓',
					'customer[email]': email,
				    'customer[password]': password
				})
			}).then(r=>r.text()).then(d=>{
				Customer.get().then(c=>{
					Util.events.dispatch('Customer:authenticated', c)
					res(c)
				})
			})
		})
	},

	register: (login, password, firstName, lastName, phone) => {
		return new Promise((res, rej) => {

			let email = login

			if(typeof login == 'object' && login.email) {
				password = login.password
				email = login.email
			}

			if(!Util.events.dispatch('Customer:register', {email:email,password:password,firstName:firstName,lastName:lastName,phone:phone})) { res(false); return; }

			fetch('/account', {
				method:'POST',
				headers:{
					'Content-Type':'application/x-www-form-urlencoded'
				},
				body: Util.urlparams.build({
					form_type:'create_customer',
					utf8:'✓',
					'customer[email]': email,
				    'customer[password]': password,
				    'customer[first_name]': firstName||'',
				    'customer[last_name]': lastName||'',
				    'customer[note][phone_number]': phone||'',
				})
			}).then(r=>r.text()).then(d=>{
				Customer.get().then(c=>{
					Util.events.dispatch('Customer:registered', c)
					Util.events.dispatch('Customer:authenticated', c)
					res(c)
				})
			})
		})
	},

	form: form => {
		return new Promise((res, rej) => {
			
			if(!Util.events.dispatch('Customer:formSubmit', {form:form})) { res(false); return; }

			fetch(form.dataset.action,{
				method:'POST',
				headers:{'Content-Type':'application/x-www-form-urlencoded'},
				body:Util.urlparams.build(Object.fromEntries(Array.from(form.querySelectorAll('input, select')).map(el=>{return[el.name,el.value]})))
			}).then(r=>r.text()).then(d=>{
				Customer.get().then(c=>{
					if(c) Modal.close()
					res(c)
				})
			})
		})
	},

	request: config => {
		return new Promise((res, rej) => {

			if(!Util.events.dispatch('Customer:request', config)) { res(false); return; }

			if(!!customer.account.email) {
				return res(customer);
			} else {
				
				Modal.open('login')

				window.addEventListener('Customer:authenticated', e => {
					res(customer)
				})
			}
		})
	},

	update: (updates={}) => {
    	Util.events.dispatch('Customer:update',{customer:customer,updates:updates})
    	return Customer.set(updates)
	},

	identify: (key, value) => {

		Util.events.dispatch('Customer:identify',Object.fromEntries([[key,value]]))

		if(!!customer.identity[key] && (customer.identity[key] == value || JSON.stringify(customer.identity[key]) == JSON.stringify(value)) )return;

		customer.identity[key] = value

		Customer.update({'identity':customer.identity}).then(c=>{
			Util.events.dispatch('Customer:identified',Object.fromEntries([[key,value]]))	
		});
		
	},

	preference:(key, value, profile) => {

	},

	subscriptions: {
		subscribe: (keys, source) => {
			console.log('subscribe', keys, source)
			const _keys = Util.array(keys).filter(key=>!customer.subscriptions[key.trim()]);
			_keys.forEach(key=>{
				customer.subscriptions[key.trim()] = true
			})
			Util.events.dispatch('Customer:subscribe', {keys:_keys,source:source} )
			Customer.update({subscriptions:customer.subscriptions})
		},
		unsubscribe: keys => {
			Util.events.dispatch('Customer:unsubscribe', keys)
			Util.array(keys).forEach(key=>{
				customer.subscriptions[key.trim()] = false
			})
			Customer.update({subscriptions:customer.subscriptions})
		},
		toggle: (key, source) => {
			if (customer.subscriptions[key.trim()]) return Customer.subscriptions.unsubscribe(key, source);
			return Customer.subscriptions.subscribe(key, source);
		}

	},

	profile: {
		add:(profile={})=>{
			customer.profiles.push({...{preferences:{}},...profile})
			Customer.profile.edit(customer.profiles.length-1)
			return Customer.update({profiles:customer.profiles})
		},
		remove: i => {
			if(i==0) return false
			customer.profiles.splice(i,1)
			return Customer.update({profiles:customer.profiles})
		},
		update:(updates={}, i=0) => {
			Util.events.dispatch('Customer:profileUpdate',{customer:customer,profile:i,updates:updates})
			customer.profiles[i] = {...customer.profiles[i], ...updates}
			return Customer.update({profiles:customer.profiles})
		},
		preferences: {
			set: (key, value, i=0) => {
				customer.profiles[i].preferences[key] = value
				return Customer.profile.update({preferences:customer.profiles[i].preferences},i)
			},
			unset: (key, i=0) => {
				delete customer.profiles[i].preferences[key]
				return Customer.profile.update({preferences:customer.profiles[i].preferences},i)
			},
			toggle: (key, values, i=0) => {
				if(!Array.isArray(values)) values = [values]
				if(customer.profiles[i].preferences[key] == values[0]){
					Customer.profile.preferences.set(key, values[1], i)
				} else {
					Customer.profile.preferences.set(key, values[0], i)
				}
			},
			add: (key, values, i=0) => {
				if(!customer.profiles[i].preferences[key]) customer.profiles[i].preferences[key] = []
				
				Util.array(values).forEach(value=>{
					if(!customer.profiles[i].preferences[key].includes(value))
						customer.profiles[i].preferences[key].push(value);
				})
				
				return Customer.profile.update({preferences:customer.profiles[i].preferences},i)
			},
			remove: (key, value, i=0) => {
				if(customer.profiles[i].preferences[key].includes(value))
					customer.profiles[i].preferences[key].splice(customer.profiles[i].preferences[key].indexOf(value),1);
				return Customer.profile.update({preferences:customer.profiles[i].preferences},i)
			},

		},
		edit:(index=0) => {
			customer.edit=index;
			Util.events.dispatch('Customer:profileEdit')
			return customer.edit
		},
		close:() => {
			customer.edit=false;
			Util.events.dispatch('Customer:profileEdit')
			return customer.edit	
		},
		save:(data, index=0)=>{
			let updates = customer.profiles[index]
			Util.events.dispatch('Customer:profileSave')
			Object.keys(data).map(key=>{
				if(!!data[key]){
					updates = Util.nest(key,updates,data[key])
				}
			})
			Customer.profile.update(updates, index)
			Customer.profile.close()
		},
		init:()=>{
			customer.edit=false;
		}
	},

	peripheral:(key, data) => {

		customer.peripherals = customer.peripherals || {}
		customer.peripherals[key] = {
			updated:Date.now(),
			data:data
		}
		Util.events.dispatch('Customer:peripheral', Object.fromEntries([[key,data]]));
		Customer.update({'peripherals':customer.peripherals});

	}
}
// proxies
Customer.subscribe = Customer.subscriptions.subscribe;
Customer.unsubscribe = Customer.subscriptions.unsubscribe;
Customer.init()
window.Customer = Customer;
export default Customer;