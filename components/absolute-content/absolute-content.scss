.overlay-item {
  --setting-left: 0%;
  --setting-top: 0%;
  --setting-translate: translate(0,0);
  --setting-left-mobile: 0%;
  --setting-top-mobile: 0%;
  --setting-translate-mobile: translate(0,0);

  left: var(--setting-left);
  top: var(--setting-top);
  transform: var(--setting-translate);
}

@media (max-width: 767px) {
  .overlay-item {
    left: var(--setting-left-mobile);
    top: var(--setting-top-mobile);
    transform: var(--setting-translate-mobile);
  }
}

.hotspot {
  position: relative;
}

.hotspot__button {
  position: relative;
  display: block;
  width: 24px;
  height: 24px;
  border-radius: 100%;
  --section-dot-inner-background: 255,255,255;
  --section-dot-background: 255, 255, 255;
  background: var(--section-dot-inner-background);
  box-shadow: 0 0 0 5px rgb(var(--section-dot-background)) inset, 0 1px 5px rgba(0, 0, 0, 0.1490196078);
}

.hotspot__button:hover {
  box-shadow: 0 0 0 8px rgb(var(--section-dot-background)) inset, 0 1px 5px rgba(0, 0, 0, 0.1490196078);
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(0.6);
  }

  100% {
    opacity: 0;
    transform: scale(1.3);
  }
}

.hotspot__button:after {
  content: "";
  position: absolute;
  left: -5px;
  top: -5px;
  width: 34px;
  height: 34px;
  border: 2px solid rgba(var(--section-dot-background),.6);
  border-radius: 100%;
  animation: pulse 2s ease-in-out infinite;
}

.hotspot__tooltip {
  background: white;
  color: black;
  padding: 0.5rem;
  position: absolute;
  border-radius: 4px;
  left: var(--tooltip-x);
  top: var(--tooltip-y);
  transform: var(--tooltip-translate);
}

.hotspot__tooltip-inner {
  display: flex;
  width: max-content;
  column-gap: 1rem;
  align-items: center;
}

.tooltip__title,
.tooltip__subtitle,
.tooltip__price {
  margin: 0;
}

.tooltip__title {
  font-weight: bold;
}
@media only screen and (max-width: 768px) {
  .tooltip__title,
  .tooltip__price {
    font-size: 12px;
    max-width: 110px;
    width: 100%;
  }

  .tooltip__title {    
    display: block;
  }
}

.tooltip__quickview {
  border: none;
  background: none;
  text-decoration: underline;
  line-height: 20px;
  color: #777;
}

.hotspot__tooltip:before {
  content: '';
  background: white;
  height: 2rem;
  width: 2rem;
  border-radius: 4px;
  position: absolute; 
  top: 0;
  left: 0;
  z-index: -1;
  transform: rotate(45deg);
}

.hotspot__tooltip[hotspot-tooltip="top"] {
  --tooltip-x: 50%; 
  --tooltip-y: calc(0% - 2rem); 
  --tooltip-translate: translate(-50%, -100%);
}

.hotspot__tooltip[hotspot-tooltip="top"]:before {
  top: 100%;
  left: 50%;
  transform-origin: center;
  transform: translate(-50%,-70%) rotate(45deg);
}

.hotspot__tooltip[hotspot-tooltip="right"] {
  --tooltip-x: calc(100% + 2rem);
  --tooltip-y: 50%;
  --tooltip-translate: translate(0, -50%);
}

.hotspot__tooltip[hotspot-tooltip="right"]:before {
  top: 50%;
  left: 0%;
  transform-origin: center;
  transform: translate(-30%,-50%) rotate(45deg);
}

.hotspot__tooltip[hotspot-tooltip="bottom"] {
  --tooltip-x: 50%;
  --tooltip-y: calc(100% + 2rem);
  --tooltip-translate: translate(-50%, 0%);
}

.hotspot__tooltip[hotspot-tooltip="bottom"]:before {
  top: 0%;
  left: 50%;
  transform-origin: center;
  transform: translate(-50%,-30%) rotate(45deg);
}

.hotspot__tooltip[hotspot-tooltip="left"] {
  --tooltip-x: calc(0% - 2rem);
  --tooltip-y: 50%;
  --tooltip-translate: translate(-100%, -50%);
}

.hotspot__tooltip[hotspot-tooltip="left"]:before {
  top: 50%;
  left: 100%;
  transform-origin: center;
  transform: translate(-70%,-50%) rotate(45deg);
}


.section:has(.overflow-item--allow-overflow) {
  overflow:visible;
}
.section:has(.overflow-item--crop-overflow) {
  overflow:hidden;
}
