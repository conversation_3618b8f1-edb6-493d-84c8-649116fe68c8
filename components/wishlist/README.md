# Wishlist Controller Documentation

## Introduction

The "Wishlist Controller" manages functionalities related to user wishlists, allowing users to add and remove items, manage lists, and store wishlist data locally or using browser storage.

### Usage

The Wishlist Controller code is intended to be implemented within a JavaScript file in the Shopify theme. It facilitates the management of user wishlists and provides methods to interact with wishlist items.

## Wishlist Management

The Wishlist Controller provides methods to manage wishlist items, add and remove items, and perform actions on wishlist lists.

### Wishlist Lists

- **Description:** Manages wishlist lists, including adding, removing, emptying, scrubbing, and updating lists.
- **Methods:**
  - `add(list)`: Adds a new wishlist list.
  - `remove(index)`: Removes a wishlist list by index.
  - `empty(index)`: Empties a wishlist list by index.
  - `scrub(index)`: Removes duplicate items from a wishlist list by index.
  - `update(index, key, val)`: Updates a wishlist list's property.

### Wishlist Items

- **Description:** Manages individual wishlist items, including adding, removing, and toggling items.
- **Methods:**
  - `present(item, list)`: Checks if an item is present in a wishlist list.
  - `toggle(item, list, config)`: Toggles the presence of an item in a wishlist list.
  - `add(items, list, config)`: Adds items to a wishlist list.
  - `remove(index, list)`: Removes an item from a wishlist list by index.

## Wishlist Access

The Wishlist Controller allows access control to manage wishlist actions based on customer authentication.

### `access` Method

- **Description:** Checks whether a customer is authenticated and has access to wishlist actions.
- **Parameters:**
  - `config` (object): Configuration data for customer authentication.
- **Returns:** A promise that resolves when access is granted.

## Initialization and Storage

The Wishlist Controller is initialized when the DOM content is loaded and handles storage and retrieval of wishlist data.

### `init` Method

- **Description:** Initializes the Wishlist Controller by recalling wishlist data.
- **Implementation:**
  ```javascript
  Wishlist.init = () => {
    Wishlist.recall();
  };
  ```

### `store` Method

- **Description:** Stores wishlist data in browser storage.
- **Implementation:**
  ```javascript
  Wishlist.store = () => {
    Util.storage.set(wishlist.storage, 'wishlist', wishlist);
    Util.events.dispatch('Wishlist:store');
  };
  ```

### `recall` Method

- **Description:** Retrieves wishlist data from storage and initializes wishlist lists.
- **Implementation:**
  ```javascript
  Wishlist.recall = () => {
    // Retrieve and initialize wishlist data
  };
  ```

## Configuration and Defaults

The Wishlist Controller provides configuration options and default settings for wishlist behavior.

- `auth`: A boolean indicating whether customer authentication is required.
- `storage`: The type of browser storage used to store wishlist data.
- `lists`: An array of wishlist lists containing name and items.

## Event Dispatching

The Wishlist Controller dispatches events to notify components about wishlist actions and changes.

- `Wishlist:add`: Dispatched when an item is added to a wishlist list.
- `Wishlist:remove`: Dispatched when an item is removed from a wishlist list.
- `Wishlist:store`: Dispatched when wishlist data is stored or updated.
- `Wishlist:recall`: Dispatched when wishlist data is retrieved from storage.

## Conclusion

The "Wishlist Controller" enhances user experience by providing wishlist management features. By facilitating wishlist item addition, removal, toggling, access control, and storage management, it contributes to a more comprehensive and user-friendly wishlist functionality in a Shopify theme-based website. This controller streamlines the process of tracking and managing wishlist items, enabling better engagement and conversion opportunities for customers.