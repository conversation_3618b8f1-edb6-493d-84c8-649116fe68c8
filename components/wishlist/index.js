window.wishlist = {
  auth:true,
  storage: 'local',
  lists: [
    {
      "name": "Wishlist",
      "items": [] 
    }
  ] 
};

const Wishlist = {

  lists: {

    add: list => {

    },

    remove: index => {

    },

    empty: index => {
      wishlist.lists[index||0].items = []
      Wishlist.store()
      return []
    },

    scrub: index => {

      wishlist.lists[index||0].items = Util.unique(wishlist.lists[index||0].items, 'variant.id')
    
    },

    update: (index, key, val) => {

    }

  },

  access: config =>{


    return new Promise(res=>{
      if(!!!wishlist.auth){
        res(true)
      } else {
        Customer.request(config).then(c=>{
          res(true)
        })
      }
    })
  },

  items: {

    present: (item, list=0) => {

      if(wishlist.lists[list].items.find(i=>i.product.handle==item.product&&i.variant.id==item.variant)) return true;
      return false;
        
    },

    toggle: (item, list=0, config) => {

      return new Promise(res => {

        const index = wishlist.lists[list].items.findIndex(i=>i.product.handle==item.product&&i.variant.id==item.variant)

        if(index > -1) {
          Wishlist.items.remove(index, list)
        } else {
          Wishlist.items.add(item, list, config)
        }
        

      })

    },

    add: (items, list=0, config) => {

      return new Promise(res => {

        Wishlist.access(config).then(customer=>{

          const listIndex = list ? wishlist.lists.findIndex(l=>l.name==list) : 0

          items = (Array.isArray(items) ? items : [items]).map(item => {
            return {
              product: item.product ? item.product : item,
              variant: item.variant ? item.variant : false
            }
          })

          Wishlist.updateGreeting()

          if(!!QuickAdd) {

            items.map(item=>{
              
              QuickAdd.open(
                item.product,
                item.variant,
                null,
                {
                  ...config.quickadd || {},
                  "onSelect":(qa)=>{
                    const item = {
                      product:qa.product,
                      variant:qa.variant
                    }
                    wishlist.lists[listIndex].items.push(item)
                    Wishlist.lists.scrub(listIndex)
                    
                    Util.events.dispatch('Wishlist:add', {item:item,list:wishlist.lists[listIndex]})
                    
                    Wishlist.store()
                    res(items)  
                  }
                }
        
              )  

            })

            

          } else {

            Util.events.dispatch('Wishlist:add', {items:items,list:wishlist.lists[listIndex]})

            wishlist.lists[listIndex].items.push(...items)
            Wishlist.store()
            res(items)  

          }

          

        })

      })

    },

    remove: (indexes, list=0) => {

      wishlist.removed = []

      Util.array(indexes).sort().reverse().forEach(i=>{

        const item = wishlist.lists[list].items.splice(i,1)
        wishlist.removed.push(item[0])          

      })

      Util.events.dispatch('Wishlist:remove', {items:wishlist.removed,list:wishlist.lists[list]})

      Wishlist.store()

    }

  },

  scrub: () => {
    wishlist.lists.forEach((list,listIndex)=>{
      list.items.forEach((item,itemIndex)=>{
        if(!!item.removed) Wishlist.items.remove(itemIndex,listIndex)
      })
    })
  },

  enhance: () => {

    wishlist.lists.forEach((list,listIndex)=>{
      list.items.forEach((item,itemIndex)=>{

        fetch(`/products/${item.product.handle}?view=item-data`).then(r=>r.json()).then(d=>{
            const v = d.variants.find(v=>v.id==item.variant.id)
            if(!!d)item.product = d;
            if(!!v)item.variant = v;

            Wishlist.store()
        }).catch(err=>{

            // wishlist.lists[listIndex].items.splice(itemIndex,1);
            // Wishlist.store()
        })
      })
    })

  },

  init: () => {
    Wishlist.recall()
    Wishlist.scrub()
    if(document.location.href.includes('wishlist')){Wishlist.enhance()}
  },

  store: () => {
    Util.storage.set(wishlist.storage, 'wishlist', wishlist)
    Util.events.dispatch('Wishlist:store')
  },

  recall: () => {
    const storedData = Util.storage.get(wishlist.storage, 'wishlist')
    wishlist.lists = !!storedData && storedData.lists ? storedData.lists : wishlist.lists
    Util.events.dispatch('Wishlist:recall')
  },

  updateGreeting: () => {
    const greetingElement = document.querySelector('.nav-tools__account-greeting');
    if (greetingElement) {
      const customer = window.customer
      const greeting = `${customer && customer.account.first_name 
                        ? 'Aloha, ' + customer.account.first_name + '!' 
                        : 'Sign In'}`
      greetingElement.innerHTML = greeting
    }
  },

}

window.addEventListener('DOMContentLoaded', Wishlist.init)

window.Wishlist = Wishlist

export default Wishlist


