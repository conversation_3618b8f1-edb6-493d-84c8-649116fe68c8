window.Search = {

	init: () => {

		window.addEventListener('input', Util.debounce(e => {

			console.log(e.target.name, e.target.value)

			if (e.target.name == 'q' && !e.target.value) Modal.close()
			if (e.target.name == 'q') {
				search.query = e.target.value
				e.target.setAttribute('value', e.target.value)

				if (!e.target.value & e.target.id != 'SearchModalInput') {
					Modal.close()
					Search.clear()
				} else if (e.target.value.length > 2) {
					search.form = Util.select('form', e.target).id
					Search.query(e.target.value)
				}
			}

		}, search.settings.debounce || 500))

		window.addEventListener('keydown', function (e) {

			if (e.key === 'Escape') {
				document.querySelectorAll('input[name="q"]').forEach(input => {
					input.value = ''
					search.query = ''
					input.setAttribute('value', '')
				})
			}

		})
	},

	query: (term, topic = 'Search') => {
		Util.events.dispatch('Search:query', { query: term })

		search.loading = true;
		search.query = term;
		return new Promise((res, rej) => {
			// const url = Search.url()
			fetch(Search.url())
				.then(response => response.json())
				.then(data => Search.map(data))
				.then(data => {
					search.results = data
					search.results = { ...data }
					search.loading = false
					Util.events.dispatch('Search:results', { data: data })
					res(data)
					Header.updateHeaderOffset()

					Alpine.nextTick(() => {
						if (Search.empty()) {
							search.results = false
						}
					})

					// Util.events.dispatch('Search:results', {data:data})
					// res(data)
				})
				.catch(error => {
					search.loading = false
					console.error("Search:query error", error)
					rej(error);
				})
		})
	},

	url: () => {
		// conditional check for filters
		const searchContext = { ...search, filters: search.filters || {} }

		return new Function(...Object.keys(searchContext), `return \`${search.settings.remote_url}\`;`)(...Object.values(searchContext))
		// return new Function(...Object.keys(search), `return \`${search.settings.remote_url}\`;`)(...Object.values(search))
	},

	clear: () => {
		search.results = false;
		search.form = false;
		search.query = '';
		search.loading = false
		Util.events.dispatch('Search:clear', {})
	},

	empty: () => {
		const results = search.results
		return !results || (!results.suggestions.length && !results.products.length && !results.collections.length)
	},

	map: (data, map) => {

		map = map || search.settings.map || false;

		if (!map) return data;

		return Util.map(data, map)

	},

	search_redirects: () => {
		document.getElementById('SearchModalInput')?.addEventListener('submit', function (evt) {
			evt.preventDefault();
			let header_search_query = document.querySelector('#SearchModalInput input[type="search"]').value
			console.log('header_search_query', header_search_query)
			if (header_search_query.trim().length > 2 && Object.keys(window.search_redirect).length > 0) {
				header_search_query = header_search_query.trim().toLowerCase();
				let search_redirect_json = window.search_redirect
				if (window.search_redirect[header_search_query] != undefined) {
					window.location.href = window.search_redirect[header_search_query];
				} else {
					document.getElementById('SearchModalInput').submit()
				}
			} else {
				document.getElementById('SearchModalInput').submit()
			}
		})
	}
}

// Search.init()

window.addEventListener('DOMContentLoaded', e => {
	Search.init()
	Search.search_redirects()
})