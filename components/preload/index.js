import { listen, prefetch } from "quicklink";

const Preload = {
	watch: () => {
		listen({
			prerender:true,
			ignores:[
				(uri, elem) => {
					return !elem.matches('a[prefetch]')
				}
			]
		})
	},
	fetch: (what) => {
		prefetch(what)
	}
}

window.Preload = Preload

window.addEventListener('DOMContentLoaded', Preload.watch)
window.addEventListener('scroll',Util.debounce(e=>{Preload.watch()},500))
 
