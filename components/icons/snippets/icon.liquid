{% liquid

  assign icon_mapping = settings.icon_mapping | downcase | newline_to_br
  assign icon = icon | downcase | strip
  if icon_mapping contains icon 
    assign icon_map = icon_mapping | split:'<br />' 
    for map in icon_map 
      assign parts = map | remove:'<br />' | strip | split: ':' 
      if parts[0] == icon     
        assign icon = parts[1] | handle
        break 
      endif 
    endfor 
  endif 
%}
<svg 
  class="icon {{ classes }} {{ class }} icon-{{ icon }}"
  style="{{ style }}"
  width="{{ width | default: 24 }}"
  height="{{ height | default: 24 }}"
  fill="{{ fill | default: 'none' }}"
  stroke="{{ stroke | default: 'currentColor' }}"
  stroke-width="{{ strokeWidth | default: settings.icon_stroke_width }}"
  stroke-linecap="{{ linecap | default: 'round' }}"
  stroke-linejoin="{{ linejoin | default: 'round' }}"
  >
  <use xlink:href="#icon-def-{{ icon }}"/>
</svg>
<span class="sr-only">{{ sr | default: icon }}</span>
