## Icon Rendering Liquid Snippet

The provided Liquid snippet is designed to render an icon using SVG markup. It allows for customization of the icon's appearance and attributes. Below is a detailed technical documentation of how the snippet works and how to use it.

### Usage

To render an icon using the Liquid snippet, you can use the following code:

```liquid
{% render 'icon', icon: 'plus', width: 24, height: 24 %}
```

This will render an SVG icon element representing the "plus" icon, with a width and height of 24 pixels.

### Liquid Variables and Logic

1. **`icon_mapping`**: This variable stores a mapping of icon names to their corresponding SVG definitions. It is used to match the specified `icon` value with its corresponding definition.

2. **`icon`**: This variable stores the name of the desired icon. It is used to identify which SVG definition should be used for rendering.

3. **Icon Mapping Logic**: The snippet includes logic to match the `icon` value with its corresponding SVG definition using the `icon_mapping` variable. It iterates through each line in the `icon_mapping` using a loop, splits each line to extract the icon name and SVG definition, and compares it with the provided `icon` value. If a match is found, the `icon` value is updated to the corresponding SVG definition's handle.

### SVG Attributes

The SVG element is customized with various attributes:

- **`class`**: Defines the CSS classes to apply to the SVG element.
- **`style`**: Defines inline CSS styles for the SVG element.
- **`width`** and **`height`**: Define the width and height of the SVG icon.
- **`fill`**: Sets the fill color for the SVG icon. Defaults to `'none'`.
- **`stroke`**: Sets the stroke color for the SVG icon. Defaults to `'currentColor'`.
- **`stroke-width`**: Sets the width of the stroke. Defaults to the `icon_stroke_width` setting value.
- **`stroke-linecap`**: Sets the stroke linecap style. Defaults to `'round'`.
- **`stroke-linejoin`**: Sets the stroke linejoin style. Defaults to `'round'`.

### `<use>` Element

The `<use>` element with an `xlink:href` attribute is used to reference the SVG symbol. It points to the SVG symbol with the ID `icon-def-{{ icon }}`, which is expected to be defined elsewhere in the document.

### Screen Reader Support

A `<span>` element with the class `sr-only` is included to provide screen reader support. It contains the label of the icon, which defaults to the `icon` value.

---

Overall, the provided Liquid snippet allows for the dynamic rendering of SVG icons with customizable attributes and screen reader support. It's a versatile tool for adding iconography to a webpage while ensuring accessibility for all users.