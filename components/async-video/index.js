document.addEventListener('alpine:init', () => {
  Alpine.data('asyncVideo', (options) => ({
    // State
    shouldLoad: false,
    isVisible: false,
    isHovered: false,
    isPlaying: false,
    timeoutId: null,
    isMobileView: false,
    hasMobileVersion: false,
    config: options,

    initVideo() {
      this.hasMobileVersion = !!this.sourceMobile;
      this.isMobileView = window.innerWidth < 1023 

      window.addEventListener('resize', () => {
        const _isMobileView = window.innerWidth < 1023 
        if (this.isMobileView !== _isMobileView) {
          this.isMobileView = _isMobileView;
          if (this.hasMobileVersion) {
            this.shouldLoad = false;
            this.initializeLoading();
          }
        }
      });

      this.initializeLoading();
    },

    initializeLoading() {
      switch (this.config.loadingMethod) {
        case 'scroll':
          const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                this.isVisible = true;
                this.shouldLoad = true;
                observer.disconnect();
              }
            });
          }, {
            root: null,
            rootMargin: '32px',
            threshold: 0.1
          });
          
          const parentElement = this.$el.parentElement;
          if (parentElement && parentElement.classList.contains('content-item__media')) {
            const sectionBlock = parentElement.closest('.content-item');
            if (sectionBlock) {
              observer.observe(sectionBlock);
            } else {
              observer.observe(this.$el);
            }
          } else {
            observer.observe(this.$el);
          }

          break;
          
        case 'hover':
          this.$el.addEventListener('mouseenter', () => {
            if (!this.shouldLoad) {
              this.isHovered = true;
              this.shouldLoad = true;
            }
          });
          break;
          
        case 'time':
          this.timeoutId = setTimeout(() => {
            this.shouldLoad = true;
          }, this.config.loadingDelay);
          break;
          
        default:
          this.shouldLoad = true;
      }

      this.$cleanup = () => {
        if (this.timeoutId) {
          clearTimeout(this.timeoutId);
        }
      };
    },

    playVideo() {
      const video = window.innerWidth >= 1024 
        ? this.$refs.desktopVideo 
        : this.$refs.mobileVideo
        
      if (video) video.play()
    },

    pauseVideo() {
      const video = window.innerWidth >= 1024 
        ? this.$refs.desktopVideo 
        : this.$refs.mobileVideo
        
      if (video) video.pause()
    },

    handlePlay(event) {
      this.isPlaying = true
      event.target.setAttribute('playing', true)
    },

    handlePause(event) {
      this.isPlaying = false
      event.target.setAttribute('playing', false)
    },

    handleEnded(event) {
      this.isPlaying = false
    },

    destroy() {
      if (this.timeoutId) {
        clearTimeout(this.timeoutId)
      }
    }
  }))
})
