module.exports = {
  "ConditionalDisplay": [
    {
      "type": "header",
      "content": "Display Settings"
    }, 
    {
      "type": "checkbox",
      "id": "show_desktop",
      "label": "Show desktop",
      "default": true
    }, 
    {
      "type": "checkbox",
      "id": "show_mobile",
      "label": "Show mobile",
      "default": true
    },
    {
      "type": "text",
      "id": "start_date",
      "label": "Display Start Date"
    },
    {
      "type": "text",
      "id": "end_date",
      "label": "Display End Date"
    },
    {
      "type": "liquid",
      "label": "Liquid Logic",
      "id": "show_liquid",
      "info": "Insert any liquid logic that returns a value to display the section"
    },
    {
      "type": "textarea",
      "label": "Javascript Logic",
      "id": "show_js",
      "info": "Insert any javascript logic that evaluates to true to display the section"
    },
    {
      "type": "checkbox",
      "label": "Load Section Asynchonously on Scroll",
      "id": "async"
    }
  ]
}
