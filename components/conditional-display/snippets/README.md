## Liquid Snippets for Conditional Rendering

The provided Liquid code snippets demonstrate how to conditionally render or display elements based on various conditions. These snippets can be used within your Shopify theme to dynamically control the visibility and behavior of elements.

### Conditional Classes based on Settings

```liquid
{%- liquid 
  if settings.show_mobile and settings.show_desktop == false
    assign classes = "lg:hidden"
  endif

  if settings.show_desktop and settings.show_mobile == false
    assign classes = "hidden lg:block"
  endif

  echo classes
-%}
```

This Liquid snippet evaluates the `settings.show_mobile` and `settings.show_desktop` variables to determine the classes that should be applied to an element. It assigns classes such as "lg:hidden" for mobile-only display or "hidden lg:block" for desktop-only display. The assigned classes are then output using the `echo` tag.

### JavaScript Condition based on Liquid Variable

```liquid
{%- if settings.show_js != blank -%}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const liquidCondition = eval('{{ settings.show_js }}')

    if (liquidCondition) document.querySelector('.section-{{ id }}').style.display = 'none';
  })
</script>
{%- endif -%}
```

This snippet checks if the `settings.show_js` variable is not blank. If it's not blank, it evaluates the JavaScript condition provided by the Liquid variable and hides the corresponding section if the condition is true. This allows you to conditionally control the visibility of a section using JavaScript based on Liquid variables.

### Conditional Display based on Date and Settings

```liquid
{%- liquid 
  assign compare_format = '%Y-%m-%d %H:%M:%S'
  assign current_date = 'now' | date: compare_format
  assign start_date = settings.start_date | date: compare_format
  assign end_date = settings.end_date | date: compare_format
  assign hide = false

  if start_date != blank and start_date > current_date
    assign hide = true
  endif

  if end_date != blank and end_date < current_date
    assign hide = true
  endif

  if settings.show_desktop == false and settings.show_mobile == false 
    assign hide = true
  endif

  if settings.show_liquid == ''
    assign hide = true
  endif

  if hide
    echo 'hide'
  else
    echo 'show'
  endif

  if settings.async
    unless content_for_header contains section.id
      echo 'hide async' 
    endunless
  endif
  
-%}
```

In this snippet, the Liquid code evaluates various conditions to determine whether an element should be hidden or shown. It considers date conditions based on `start_date` and `end_date` settings, as well as conditions related to `show_desktop`, `show_mobile`, and `show_liquid` settings. Depending on these conditions, it assigns the `hide` variable. If `hide` is true, it outputs 'hide', otherwise 'show'. Additionally, if the `async` setting is true and the `content_for_header` does not contain the section ID, it outputs 'hide async'.

### Usage

To utilize these snippets within your Shopify theme, insert them into appropriate Liquid templates, sections, or snippets. Customize the conditions and variables according to your specific use case and theme settings. These snippets provide a powerful way to dynamically control the visibility and behavior of elements on your Shopify store.