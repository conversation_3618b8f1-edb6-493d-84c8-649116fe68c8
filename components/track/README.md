# User Tracking Controller Documentation

## Introduction

The "User Tracking Controller" is responsible for tracking user-related events and activities on a webpage, such as page views, placed orders, and marketing signups. It facilitates event handling, customization, and data manipulation before and after dispatching events.

### Usage

The User Tracking Controller code is intended to be implemented within a JavaScript file in the Shopify theme. It provides a structured approach to tracking user actions and allows for customization through hooks and event callbacks.

## Event Configuration

The User Tracking Controller allows for configuration of various user-related events, each with specific hooks and event callbacks.

### `events` Object

- **Description:** Contains configuration for different user-related events, along with hooks and event callbacks.
- **Structure:**
  ```javascript
  events: {
    eventName1: {
      hooks: [async (data) => { /* Manipulate data before event */ }],
      eventCallback: (e) => { /* Event callback after hooks */ }
    },
    eventName2: {
      eventCallback: (e) => { /* Event callback after hooks */ }
    },
    // ...more event configurations...
  }
  ```

## Event Handling

The User Tracking Controller provides methods to trigger and handle different user-related events.

### `event` Method

- **Description:** Triggers a specified user-related event by dispatching it after running hooks.
- **Parameters:**
  - `event` (string): The name of the event to trigger.
  - `data` (object): Data associated with the event (optional).
- **Implementation:**
  ```javascript
  Track.event = (event, data) => {
    // Manipulate data using hooks and dispatch the event
  };
  ```

## Initialization

The User Tracking Controller is initialized when the DOM content is loaded.

### `init` Method

- **Description:** Initializes the User Tracking Controller by registering hooks, attaching event listeners, and dispatching the 'Track:Ready' event.
- **Implementation:**
  ```javascript
  Track.init = () => {
    // Register hooks and attach event listeners for configured events
    Util.events.dispatch('Track:Ready');
  };
  ```

## Hook Processing

Hooks are used to manipulate data before user-related events are dispatched.

### `hooks` Object

- **Description:** Registers and processes hooks for different user-related events.
- **Implementation:**
  ```javascript
  Util.hooks.register(`Track:${event}`, Track.events[event].hookCallback);
  ```

## Event Dispatching

The User Tracking Controller dispatches events based on different user actions.

### Event Dispatch

- **Description:** Dispatches user-related events after hooks are processed.
- **Implementation:**
  ```javascript
  Util.events.dispatch(`Track:${event}`, data);
  ```

## Conclusion

The "User Tracking Controller" facilitates the tracking of user-related events and activities on a webpage. By providing a structured configuration for events, hooks for data manipulation, event callbacks for handling, and an initialization process, it enhances the ability to monitor and analyze user interactions. This controller contributes to a comprehensive tracking system that assists in understanding user behavior and engagement on a Shopify theme-based website.