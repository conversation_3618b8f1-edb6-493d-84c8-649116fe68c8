import Util from '../@utilities/events'
const Track = {

  events: {
    pageView: { 
      // The hook callback is used to manipulate data before the event is dispatched
      hooks: [async (data) => {
        return data
      }],
      // The event callback is used called after the hooks run and the event is displatched
      eventCallback: (e) => {}
    },
    orderPlaced: { 
      eventCallback: (e) => {}
    },
    marketingSignup: { 
      eventCallback: (e) => {}
    },
  },

  event: (event, data) => {

    Util.hooks.process(`Track:${event}`, data).then( data => {
      Util.events.dispatch(`Track:${event}`, data)
    })

  },
  init: () => {
    for (let event in Track.events) {
      
      Util.hooks.register(`Track:${event}`, Track.events[event].hookCallback)
      window.addEventListener(`Track:${event}`, Track.events[event].eventCallback)
    }

    Util.events.dispatch('Track:Ready')
  } 
  
}

window.addEventListener('DOMContentLoaded', Track.init)

window.Track = Track

export default Track
