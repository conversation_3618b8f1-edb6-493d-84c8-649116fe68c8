.product-finder {
	display: block;

	@media (min-width: 1024px) {
		display: flex;
	}

	@media (max-width: 1024px) {
		display: block;
	}
	&__quiz {
		position: relative;
		overflow: hidden;
		padding: 72px 0 36px;
		@media (min-width: 1024px) {
			position: sticky;
			top: 0px;
			padding: 0;
			height: 100vh;
			width: 36%;
		}
		text-align: center;
		display: flex;
		flex-direction: column;
		background: transparent linear-gradient(0deg, #f5ead8 0%, #b0dfe1 100%) 0% 0% no-repeat padding-box;
	}
	&__decoration {
		position: absolute;
		width: 100%;
		height: 80px;
		bottom: -40px;
		@media (max-width: 1024px) {
			height: 72px;
			bottom: -36px;
		}
		background: url(https://cdn.shopify.com/s/files/1/0015/9229/5523/files/olukai_product_finder_decoration.svg?v=1659121768) center top no-repeat;
	}
	&__quiz-header {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		z-index: 2;
	}
	&__quiz-progress {
		background: rgba(255, 255, 255, 0.5);
	}
	&__quiz-progress-bar {
		height: 10px;
		background-color: var(--content-color);
		transition: width 300ms ease-in-out;
	}
	&__quiz-navigation {
		display: flex;
		justify-content: space-between;
		nav {
			position: relative;
			width: 50%;
			margin: 1.5rem 0 0 1rem;
			button {
				position: absolute;
				top: 0;
				left: 0;
				display: flex;
				align-items: center;
				color: var(--content-color);
				font-family: Olukai-Bold, Arial, Helvetica, sans-serif;
				font-weight: bold;
				font-size: 13px;
				text-transform: uppercase;
				span {
					margin-top: 1px;
				}
			}
		}
	}
	&__quiz-pagination {
		display: flex;
		justify-content: end;
		color: var(--content-color);
		font-family: Olukai-Bold, Arial, Helvetica, sans-serif;
		font-weight: bold;
		font-size: 13px;
		margin: 1.5rem 2rem 0 0;
		padding: 5px;
		&-number {
			margin: 0 1px;
		}
	}
	&__quiz-panes {
		display: block;
		background: transparent !important;
	}
	&__quiz-pane {
		height: 100%;
		width: 100%;
		padding: 0 2rem;
		position: absolute;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		background: transparent;
		&.active {
			@media (max-width: 1024px) {
				position: relative;
				padding: 36px 2rem 48px;
			}
		}
	}
	&__question,
	&__prompt {
		color: var(--content-color);
		font-family: Olukai-Bold, Arial, Helvetica, sans-serif;
		font-size: 18px;
		@media (min-width: 1024px) {
			font-size: 21px;
		}
		font-weight: bold;
		margin-bottom: 14px;
	}

	&__instruction {
		color: var(--content-color);
		font-size: 12px;
		margin-bottom: 12px;
	}

	&__options {
		width: 100%;
		display: grid;
		grid-template-columns: 1fr 1fr;
		@media (max-width: 1024px) {
			grid-template-columns: 1fr;
		}
		&--compact {
			grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
		}
		grid-template-rows: auto;
		grid-gap: 8px;

		input[type='checkbox'] {
			display: none;
		}

		label {
			background: #fff;
			border: 1px solid #dddddd;
			border-radius: 5px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: var(--content-color);
			font-family: Olukai-Bold, Arial, Helvetica, sans-serif;
			text-transform: uppercase;
			font-weight: bold;
			font-size: 13px;
			height: 45px;

			&:hover {
				border: 2px solid var(--content-color);
			}
		}

		input[type='checkbox']:checked + label {
			background: var(--content-color);
			color: #fff;
		}

		input[type='checkbox']:disabled + label,
		input[type='checkbox'][disabled] + label {
			display: none;
		}
	}

	&__disclaimer {
		color: #6f6e6e;
		font-size: 12px;
		margin-bottom: 12px;
		max-width: 75%;
		line-height: 1.5;
	}

	&__restart {
		cursor: pointer;
		color: var(--content-color);
		font-family: Olukai-Bold, Arial, Helvetica, sans-serif;
		font-size: 15px;
		letter-spacing: 0.6px;
		text-decoration: underline;
		margin-bottom: 12px;
		display: inline-block;
		@media (min-width: 1024px) {
			margin-top: 10vh;
		}
	}

	form {
		width: 100%;
		margin-bottom: 2rem;

		input[type='email'] {
			width: 100%;
			text-indent: 1rem;
			margin-bottom: 12px;
			font-size: 15px;
			padding: 12px 0;
			border-radius: 3px;
			&:placeholder {
				color: #797979;
			}
		}

		button {
			display: flex;
			width: 100%;
			text-transform: uppercase;
			height: 45px;
			border-radius: 5px;
			align-items: center;
			justify-content: center;
			color: #fff;
			background-color: var(--content-color);
			font-family: Olukai-Bold, Arial, Helvetica, sans-serif;
			font-size: 15px;
			letter-spacing: 0.6px;
			&[disabled] {
				opacity: 0.8;
			}
		}

		p {
			display: none;
		}

		&.submitted {
			input,
			button {
				display: none;
			}
			p {
				display: block;
			}
		}
	}
	&__products {
		@media (min-width: 1024px) {
			width: 64%;
		}
		.swiper-button {
			margin: 0;
		}
	}
	&__product-grid {
		display: none;
		grid-template-rows: auto 1fr;
		grid-template-columns: 1fr 1fr;
		grid-gap: 20px;
		padding: 20px;
		@media (min-width: 1024px) {
			grid-template-columns: 1fr 1fr 1fr;
			grid-gap: 24px;
			padding: 55px;
			padding-bottom: 75vh;
		}
		.product-tile {
			&__image-container {
				background: #f9f3ea;
				border-bottom: 0;
			}
			&__title {
				font-size: 12px;
				line-height: 22px;
				color: #797979;
			}
			&__type {
				font-size: 12px;
				color: #797979;
			}
		}
	}
	&__selection-header {
		display: none;
		padding: 26px 55px 0px 55px;
	}
	&__back {
		border: 1px solid #381300;
		border-radius: 1000px;
		font-size: 13px;
		text-transform: uppercase;
		padding: 8px 12px;
		display: flex;
		align-items: center;
		@media (max-width: 1024px) {
			background: #042c4b;
			color: #fff;
		}
		&:hover {
			@media (min-width: 1024px) {
				background: #042c4b;
				color: #fff;
			}
		}
		svg {
			margin-right: 6px;
		}
		span {
			margin-bottom: -1px;
		}
	}
	&__recommendation-header {
		display: none;
	}
	&__product-detail {
		display: none;
	}

	&[data-state='grid'] {
		.product-finder__product-grid {
			display: grid;
		}
	}

	&__sticky-header {
		display: none;
		position: sticky;
		top: 0;
		z-index: 6;
		background: #fff;
		padding: 0px 20px 0 20px;
		height: 55px;
		align-items: center;
		justify-content: space-between;
		box-shadow: 0px 5px 5px 0px rgba(173, 173, 173, 0.5);
		nav {
			height: 16px;
		}
	}

	&[data-state='recommend'] {
		.product-finder__recommendation-header {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 55px;
			text-transform: uppercase;
		}
		.product-finder__product-detail {
			display: block;
		}
	}

	&[data-state='select'] {
		.product-finder__sticky-header {
			@media (max-width: 1024px) {
				display: flex;
			}
		}
		.product-finder__selection-header {
			display: flex;
			@media (max-width: 1024px) {
				justify-content: center;
			}
		}
		.product-finder__product-detail {
			display: block;
		}
	}

	.transitional-element {
		opacity: 0;
		pointer-events: none;
		visibility: hidden;
		transform: translateY(1rem);
		transition: all 300ms ease-in-out;
		&.active {
			opacity: 1;
			pointer-events: all;
			visibility: visible;
			transform: translateY(0rem);
		}
	}
	.product-summary__row {
		margin: 0;
		padding: 22px;
		.desktop {
			&:hover {
				.swiper-button {
					opacity: 1;
					visibility: visible;
					&.swiper-button-prev {
						left: 15px;
					}
					&.swiper-button-next {
						right: 15px;
					}
				}
			}
		}
		&-toggle {
			padding: 0 22px 12px 22px !important;
			> span:first-of-type {
				font-size: 15px;
				line-height: 18px;
				display: inline-block;
			}
			> span:last-of-type {
				span:first-child {
					display: inline-block;
				}
				span:last-child {
					display: none;
				}
			}
			&.active {
				> span:last-of-type {
					span:first-child {
						display: none;
					}
					span:last-child {
						display: inline-block;
					}
				}
			}
		}
		&--bottom {
			border-top: none;
			@media (max-width: 1024px) {
				flex-direction: column;
			}
		}
		.product-fit-guide__header-link {
			display: none;
		}
		> article {
			@media (max-width: 1024px) {
				padding: 0;
			}
		}
		.product-fit-guide {
			padding-bottom: 20px;
		}
	}
	.product-bundle {
		&__header {
			background: #eeece1;
			position: sticky;
			top: 0;
			z-index: 10;
			display: flex;
			padding: 22px;
			align-items: center;
			@media (max-width: 1024px) {
				padding: 0;
				flex-wrap: wrap;
			}
			h1 {
				font-size: 18px;
				letter-spacing: -0.38px;
				line-height: 33px;
			}
			> div {
				font-size: 13px;
				letter-spacing: 0px;
				line-height: 15px;
				margin-top: 4px;
				@media (max-width: 1024px) {
					margin: 10px 0 0;
				}
				&:nth-child(1),
				&:nth-child(2) {
					@media (max-width: 1024px) {
						padding: 0 22px;
					}
				}
			}
		}
		&__price {
			font-size: 13px;
			letter-spacing: 0;
			line-height: 17px;
			@media (min-width: 1024px) {
				font-size: 20px;
				letter-spacing: -0.02px;
				line-height: 24px;
			}
		}
		&__value {
			color: #e36662;
			font-size: 13px;
			letter-spacing: 0px;
			line-height: 15px;
			margin-top: 4px;
		}
		&__actions {
			margin-left: 20px;
			@media (max-width: 1024px) {
				width: 100%;
				flex-shrink: 0;
			}
		}
		&__buy,
		&__select-prompt {
			border-radius: 4px;
			max-width: 100%;
			width: 124px;
			text-transform: uppercase;

			@media (max-width: 1024px) {
				padding: 0;
				width: 100%;
				border-radius: 0;
				height: 46px;
			}

			@media (min-width: 1024px) {
				font-size: 15px;
				height: 50px;
				letter-spacing: 1.5px;
				max-width: 100%;
				width: 250px;
			}

			&--disabled {
				pointer-events: none;
				opacity: 0.6;
			}
		}
		&__buy {
		}
		&__select-prompt {
			pointer-events: none;
			opacity: 0.6;
		}
	}

  [product-detail] .product-summary {
    @apply p-8;

    &__image {
      @apply bg-light mix-blend-multiply;
    }
  }
}

.product-bundle ~ form.product__info,
.product-bundle ~ .product__row--shipping-sku {
	display: none;
}

.product-finder__parent + .product-summary {
  @apply hidden;
}

