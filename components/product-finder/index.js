import './product-finder.scss'
const productFinder = {

  currentState: 'grid',
  tags: [],
  
  init: () => {
    collection.filters = {};
    productFinder.id = document.querySelector('[product-finder]') .getAttribute('product-finder')
    window.trackLocation = productFinder.id
    productFinder.render();
    collection.fill().then(()=>{
      if(productFinder.currentState=='grid') productFinder.render(false, 0, false); 
    });    
  },

  render: (clear=true, segment=0, transition=true) => {

    return new Promise((resolve, reject)=>{

      // if(clear) document.querySelector('[product-grid]').innerHTML = '';

      productFinder.tags = []
      const temp = document.createElement('div');

      if (!Alpine) return false
      Alpine.store('productFinder', { products: collection.products
        .filter( (p, i)=>{
          if(!!segment && i<segment) return false;
          if(!p.available || p.type =='PLP Banner') return false;
          for (let set in collection.filters){
            if(
              collection.filters[set].length &&
              p.tags.filter(t=>collection.filters[set].includes(t)).length === 0
            ){ return false }
          }
          return true;
        }).filter(p=>{
          p.tags.forEach(tag=>{if(!productFinder.tags.includes(tag)){productFinder.tags.push(tag)}})
          return true;
        })
      })

      while(temp.firstChild) {
        document.querySelector('[product-grid]').appendChild(temp.firstChild);
      }

      if(transition){
        window.document.documentElement.classList.remove('scrolling--up')
        window.document.documentElement.classList.remove('scrolling--down')
      }
      
      document.querySelectorAll('.product-tile a').forEach(a=>{
        a.addEventListener('click', e => {
          e.preventDefault()
          productFinder.scrollTo = window.scrollY
          productFinder.detail(a.href)
        })
      })
      resolve(true);
    })

  },

  filter: (key, val, event) => {
    
    if(!collection.filters[key]) collection.filters[key] = []
    if(collection.filters[key].includes(val)){
      collection.filters[key] = collection.filters[key].filter(i => i !== val)
    } else {
      collection.filters[key].push(val)
    }
    queueMicrotask(()=>{
      productFinder.track();
      productFinder.question(key)
    })

  },

  clear: key => {
    delete collection.filters[key];
    document.querySelectorAll(`[filter-set="${key}"] input[type="checkbox"]`).forEach(el=>{el.checked = false})
    productFinder.render()
  },

  question: key => {
    const pane = document.querySelector(`[filter-set="${key}"]`)
    if(pane.dataset.limit < collection.filters[key].length){
      let remove = collection.filters[key].shift();
      document.querySelector(`[filter-quiz] input[data-key="${key}"][data-value="${remove}"]`).checked = false;
    }
    if(pane.dataset.limit == collection.filters[key].length){
      productFinder.pane(Number(pane.dataset.index)+1)
    }
  },

  pane: i => {
    productFinder.scrollTo = false;
    document.querySelector('[filter-quiz]').style.setProperty('--current-pane', i);
    document.querySelectorAll('[product-finder] [data-index].active').forEach(el=>el.classList.remove('active'));
    document.querySelectorAll(`[product-finder] [data-index="${i}"]`).forEach(el=>el.classList.add('active'));
    if(i==document.querySelectorAll('[filter-set]').length) {
      productFinder.recommend()
    } else {
      productFinder.recommended = false
      productFinder.state('grid')
      productFinder.render()
    }
    productFinder.constrain()
  },

  constrain: () => {
    document.querySelectorAll('[filter-quiz] input[type="checkbox"]').forEach(el=>{
      el.disabled = false
      if (!!el.dataset.constraint && !Object.values(collection.filters).flat().filter(v=>el.dataset.constraint.includes(v)).length){
        el.disabled = true
      } 
      if (!!el.dataset.available && !productFinder.tags.includes(el.dataset.value)){
        el.disabled = true
      }
    })
  },

  recommend: () => {
    productFinder.recommended = collection.products
    .find(p=>{
      for (let set in collection.filters){
        if(
          collection.filters[set].length &&
          p.tags.filter(t=>collection.filters[set].includes(t)).length === 0
        ){ return false }
      }
      return true;
    });
    productFinder.detail(productFinder.recommended.handle)
    productFinder.state('recommend')
    productFinder.track()
  },

  detail: (handle) => {
    return new Promise((resolve, reject)=>{
      const detail = document.querySelector('[product-detail]')
      detail.classList.remove('loaded')
      detail.innerHTML = '';
      if(handle){
        // let pathname = window.location.pathname

        // if (pathname[pathname.length - 1] != '/') pathname += '/'
        productFinder.state('select');
        detail.classList.add('loading')
        fetch(`${ window.location.pathname }?section_id=${window.productSummary}&product=${handle.split('/').reverse()[0]}`).then(r=>r.text()).then(html=>{
          Util.setInnerHTML(detail, html)
          setTimeout(()=>{detail.classList.add('loaded')},10)
          setTimeout(()=>{detail.classList.remove('loading')},310)
          // productSummary.init()
          Util.events.dispatch('Templates:render')
          resolve(handle)
        })
        
      } else {
        productFinder.state('grid')
        productFinder.render().then(x=>{
          resolve(false)  
        })
      }
    })
  },

  state: state => {
    
    productFinder.previousState = productFinder.currentState
    productFinder.currentState = state

    productFinder.uiState()
  
  },

  uiState: () => {
    const results = document.querySelector('[product-finder] [results]')
    results.style.minHeight = results.offsetHeight+'px';
    setTimeout(()=>{results.style.minHeight = null},1000)

    document.querySelector('[product-finder]').dataset.state = productFinder.currentState

    

    if(productFinder.previousState == 'grid' && productFinder.currentState == 'grid'){
      if(window.outerWidth > 1024){
        productFinder.scrollTop();
      } else {
        // no scroll
        productFinder.scrollTop();
      }
    }

    if(productFinder.previousState == 'grid' && productFinder.currentState == 'select'){
      if(window.outerWidth > 1024){
        productFinder.scrollTop('auto');   
      } else {
        document.querySelector('[results]').scrollIntoView()
      }
    }

    if(productFinder.previousState == 'select' && productFinder.currentState == 'grid'){
      if(productFinder.scrollTo){
        window.scrollTo({top:productFinder.scrollTo})
        productFinder.scrollTo = false
      } else {
        productFinder.scrollTop()
      }
    }

    if(productFinder.currentState == 'recommend'){
      const tracking = Object.fromEntries(Object.entries(collection.filters).map(e=>{
        if(!e[1][0]) return false;
        e[1] = e[1][0].split(':').reverse()[0]
        if(!isNaN(e[1])) e[1] = Number(e[1])
        return e;
      }))

      const currentVariant = productFinder.recommended.variants.find(v=>v.option2==tracking.size)

      let recommendedItem = productFinder.recommended

      let variantsListSkus = []
      let variantsListTitles = []

      recommendedItem.variants.forEach(variant => {
        variantsListSkus.push(variant.sku)
        variantsListTitles.push(variant.title)
      })

      recommendedItem.name = recommendedItem.title.split(' - ')[0]
      // exponea.shopify.product.type = this.product.type
      // exponea.shopify.product.price = this.product.price / 100
      recommendedItem.vendor = recommendedItem.vendor
      // exponea.shopify.product.collection = null
      recommendedItem.variant_id = currentVariant?.id
      recommendedItem.sku = currentVariant?.sku
      recommendedItem.title = currentVariant?.title
      recommendedItem.option1 = currentVariant?.option1
      recommendedItem.option2 = currentVariant?.option2
      if (variantsListSkus.length) {
        recommendedItem.variants_list_skus = variantsListSkus
      }
      if (variantsListTitles.length) {
        recommendedItem.variants_list_titles = variantsListTitles
      }

      recommendedItem.item_category_generc = ''
      recommendedItem.item_gender = ''
      recommendedItem.item_material = ''
      recommendedItem.item_style = ''
      if (recommendedItem.tags.length) {
        recommendedItem.tags.forEach((tag) => {
          if (tag.includes('cat:')) {
            recommendedItem.item_category_generc = tag.split('cat:')[1]
          }
          if (tag.includes('gender:')) {
            recommendedItem.item_gender = tag.split('gender:')[1]
          }
          if (tag.includes('material:')) {
            recommendedItem.item_material = tag.split('material:')[1]
          }
          if (tag.includes('style:')) {
            recommendedItem.item_style = tag.split('style:')[1]
          }
        })
        recommendedItem.tags = recommendedItem.tags.flat(1)
      }
      recommendedItem.available = recommendedItem.available

      if(typeof segmentTrack !== 'undefined'){
        segmentTrack.product({
          product: { ...recommendedItem }
        })
      }

      productFinder.scrollTop()
    }
    
  },
  scrollTop: (behavior='smooth') => {
    if(window.outerWidth > 1024){
      window.scrollTo({top:(window.scrollY >= 101) ? 101 : 0, behavior:behavior})
    } else {
      window.scrollTo({top:(window.scrollY >= 101) ? 141 : 0, behavior:behavior})
    }
  },

  reset: () => {

    document.querySelectorAll(`[filter-quiz] input[type="checkbox"]`).forEach(el=>el.checked = false);
    collection.filters={}
    productFinder.render()
    productFinder.pane(1)
    productFinder.state('grid')
    if(document.querySelector(".submitted")){ 
      var element = document.querySelector(".submitted");
      element.classList.remove("submitted");
    }
  },

  track: () => {
    if(!collection.filters || Object.entries(collection.filters).length===0) return;
    const tracking = Object.fromEntries(Object.entries(collection.filters).map(e=>{
      if(!e[1][0]) return false;
      e[1] = e[1][0].split(':').reverse()[0]
      if(!isNaN(e[1])) e[1] = Number(e[1])
      return e;
    }))
    const tracking_klaviyo = Object.fromEntries(Object.entries(collection.filters).map(e=>{
      if(!e[1][0]) return false;
      e[1] = e[1][0].split(':').reverse()[0]
      if(!isNaN(e[1])) e[1] = Number(e[1])
      return e;
    }))
    if(!!productFinder.recommended) {
      tracking.product_id = productFinder.recommended.id
      tracking.sku = productFinder.recommended.variants.find(v=>v.option2==tracking.size).sku
      tracking_klaviyo.ProductID = productFinder.recommended.id
      tracking_klaviyo.ProductName = productFinder.recommended.name
      tracking_klaviyo.SKU = productFinder.recommended.variants.find(v=>v.option2==tracking.size).sku
    }    
  
    window.tracking = tracking_klaviyo;
  },
  showCalculation: () => {
    document.querySelector(".size_plus_calculation .product-finder__question").style.display = "none";
    document.querySelector(".size_plus_calculation .product-finder__instruction").style.display = "none";
    document.querySelector(".size_plus_calculation .product-finder__options").style.display = "none";
    document.querySelector(".rec-calc-size").style.display = "none";
    document.querySelector('.cal_form #size-number').value = "";
    document.querySelector(".finding-perfect-fit-wrap").classList.add("active");
    setTimeout(function () {
      var onClick_val = document.querySelector(".product-finder__quiz-navigation nav .transitional-element.active").getAttribute("onclick");
      document.querySelector(".product-finder__quiz-navigation nav .transitional-element.active").setAttribute("onclick", onClick_val.replace('productFinder.clearCalculation()', '') + " productFinder.clearCalculation()");
    }, 1000);
  },
  clearCalculation: () => {
    document.querySelector(".finding-perfect-fit-wrap").classList.remove("active");
    document.querySelector(".rec-calc-size").style.display = "none";
    document.querySelector(".cal_error").style.display = "none";
    document.querySelector(".size_plus_calculation .product-finder__question").style.display = "block";
    document.querySelector(".size_plus_calculation .product-finder__instruction").style.display = "block";
    document.querySelector(".size_plus_calculation .product-finder__options").style.display = "block";
    document.querySelector('.cal_form #size-number').value = "";
    document.querySelector(".cal_form #size-number").classList.remove("error");
  },
  clear_measurement_style: () => {
    document.querySelector(".finding-perfect-fit-wrap").classList.remove("active");
    document.querySelector(".size_plus_calculation .product-finder__question").style.display = "block";
    document.querySelector(".size_plus_calculation .product-finder__instruction").style.display = "block";
    document.querySelector(".finding-perfect-fit.active .product-finder__options").style.display = "block";
  },
  headSizeInputChange: () => {
    const cal_input = document.querySelector('.cal_form #size-number');
    const cal_button = document.querySelector('.cal_form button');
    const cal_select = document.querySelector('.cal_form #measure-size');
    document.querySelector(".rec-calc-size").style.display = "none";

    cal_button.disabled = true;

    const cal_size = cal_input.value;
    if(cal_size !== '' && cal_size !== 'undefined'){
      document.querySelector(".cal_error").style.display = "none";
      document.querySelector(".cal_form #size-number").classList.remove("error");
      cal_button.disabled = false;  // Make button enabled
    }else {      
      document.querySelector(".cal_form #size-number").classList.add("error");
    }
  },
  calculate: (key, val, event) => {
    if (!collection.filters[key]) collection.filters[key] = []
    if (collection.filters[key].includes(val)) {
      collection.filters[key] = collection.filters[key].filter(i => i !== val)
    } else {
      collection.filters[key].push(val)
    }

    productFinder.recommended = collection.products.find(p => {
      for (let set in collection.filters) {
        if (collection.filters[set].length && p.tags.filter(t => collection.filters[set].includes(t)).length === 0) { return false }
      }
      return true;
    });
    setTimeout(() => {
      if (typeof productFinder.recommended !== 'undefined') {
        //productFinder.state('grid')
        document.querySelector(".rec-calc-size .availability_error").style.display = "none";
        document.querySelector(".rec-calc-size label").style.display = "block";
        document.querySelector(".rec-calc-size").style.display = "block";

        productFinder.clear('size');
      } else {
        document.querySelector(".rec-calc-size label").style.display = "none";
        document.querySelector(".rec-calc-size").style.display = "block";
        document.querySelector(".rec-calc-size .availability_error").style.display = "block";
        //productFinder.recommended = false
        productFinder.state('grid')
        //productFinder.render()
        productFinder.clear('size');
      }
      //productFinder.constrain()
    }, 500) 
  },
  cal_measurement: (cal_size, cal_scale) => {
    let size_result = '';
    for (let i = 0; i < window.measurement[cal_scale].length; i++) {
      const sizeRange = Object.keys(window.measurement[cal_scale][i])[0];
      if (sizeRange.includes('-')) {
        const [min, max] = sizeRange.split('-');
        if (cal_size >= parseFloat(min) && cal_size <= parseFloat(max)) {
          size_result = window.measurement[cal_scale][i][sizeRange];
          document.querySelector(".cal_form #size-number").classList.remove("error");
          document.querySelector(".cal_error").style.display = "none";
          document.querySelector(".rec-calc-size input").setAttribute("data-value", size_result);
          document.querySelector(".rec-calc-size input").setAttribute("id", "size__" + size_result);
          document.querySelector(".rec-calc-size input").setAttribute("onchange", " productFinder.filter('size','" + size_result + "',event);clear_measurement_style();");

          document.querySelector(".rec-calc-size label").setAttribute("for", "size__" + size_result);

          productFinder.calculate("size", size_result, event)
        } else {
        }
      }
    }
    if (size_result == '' ) {
      size_result = "Please re-measure ";
      document.querySelector(".rec-calc-size").style.display = "none";
      document.querySelector(".cal_error").innerHTML = size_result;
      document.querySelector(".cal_form #size-number").classList.add("error");
      document.querySelector(".cal_error").style.display = "block";
    }

    document.querySelector(".rec-calc-size .calRecommendedSize").innerHTML = size_result;
  },
  calButtonClick: () => {
    const cal_input = document.querySelector('.cal_form #size-number');
    const cal_button = document.querySelector('.cal_form button');
    const cal_select = document.querySelector('.cal_form #measure-size');
    const cal_size = cal_input.value;

    // Get the select element and its selected value    
    const cal_scale = cal_select.value;
    productFinder.cal_measurement(cal_size, cal_scale);
  }
}
window.productFinder = productFinder
window.addEventListener('DOMContentLoaded', productFinder.init)
