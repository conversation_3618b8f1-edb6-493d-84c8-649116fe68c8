window.quickadd = {
  product:false,
  variant:false,
  openedFrom:false,
  success:false,
  plpBackInStockEnable:window.plpBackInStockEnable
}


const QuickAdd = {
  
  open:(product, variant, event, config) => {

    return new Promise(res=>{
      QuickAdd.reset().then( () => {

        Util.events.dispatch('QuickAdd:open', {product:product, variant:variant, config:config})

        if (event) this.openedFrom = event.target || false

        let handle = product
          if (typeof product == 'object') {
            handle = product.handle
          }

        QuickAdd.config = config

        fetch(`${Shopify.routes.root}products/${handle}?view=item-data`)
          .then(r => r.json())
          .then(data => {
            QuickAdd.initializeCurrentSelections(data)

            // data.variants = data.variants.filter(v => v.option1 == data.variants[0].option1)
            quickadd.product = data

            // quickadd.product.variants = data.swatches.map(s=>s.variants).flat()

            // group variants by option
            quickadd.groupedOptions = QuickAdd.groupVariantsByOptions(data.variants)

            quickadd.config = config || {}

            Util.events.dispatch('QuickAdd:opened', quickadd)
            
            if(!!variant) return QuickAdd.select(variant)
            
            Modal.open('quickadd')
            res(quickadd);

        })
      });
    })
  },

  close:() => {
  
    Modal.close()
  
    if (!!quickadd.openedFrom) quickadd.openedFrom.focus()

    QuickAdd.reset()
  },

  reset: () => {
    return new Promise(res=>{
      quickadd.product = false
      quickadd.variant = false
      quickadd.openedFrom = false
      quickadd.success = false
      quickadd.purchaseable = false 
      quickadd.config = {}
      res(quickadd);
    })
  },

  select:(variant_id) => {

    Util.events.dispatch('QuickAdd:select', quickadd)

    quickadd.variant = quickadd.product.variants.find(v=>v.id==variant_id);

    if(!!quickadd.config.onSelect) {

      quickadd.config.onSelect(quickadd)
    
    } else {

      QuickAdd.buy();

    }

    if(!!quickadd.config.success) {

      quickadd.success = true
      Modal.open('quickadd')

    } else {
      if(!quickadd.config.hold) {
        QuickAdd.close()
      }
    }

    Util.events.dispatch('QuickAdd:selected', quickadd)

  },

  buy: () => {

    Util.events.dispatch('QuickAdd:buy', quickadd)

    // variant = quickad
    const item = {
      id: quickadd.variant.id,
      quantity: 1,
      properties: {
        '_source': 'quickadd',
        '_compare_at_price': quickadd.variant.compare_at_price,
        '_upc': quickadd.variant.barcode,
        '_tags': quickadd.product.tags
      }
    }
    Cart.add(item)  

  },

  currentSelections: {},

  initializeCurrentSelections: function(product) {
    this.currentSelections = {}
    
    // dynamically sets up sates for available product options
    product.options_with_values.forEach((option, index) => {
      this.currentSelections[`option${index + 1}`] = null
    })
    // the first index should be selected by default
    this.currentSelections[`option1`] = product.options_with_values[0].values[0]
  },
  

  variantAvailable: function (variants, optionName, value, selectedOptions) {
    return variants.some(variant => {
      if (variant[optionName] === value) {
        for (const key in selectedOptions) {
          if (selectedOptions[key] !== null && key !== optionName && variant[key] !== selectedOptions[key]) {
            return false;
          }
        }
        return variant.available;
      }
      return false;
    });
  },

  selectVariant: function(variants, selectedOptions) {
    // update current selection for the given optionName

    // fint variant that matches all current selections
    const selectedVariant = variants.find(variant => {
        return Object.keys(selectedOptions).every(key => {
            return variant[key] === selectedOptions[key]
        }) && (variant.available || quickadd.plpBackInStockEnable)
    })

    // if matching variant is found, select it
    if (selectedVariant) {
      this.select(selectedVariant.id)
    }
  },

  groupVariantsByOptions: (variants) => {
    let groupedOptions = {}

    variants.forEach(variant => {
      variant.options.forEach((optionValue, index) => {
        const optionName = `option${index + 1}`
        if (!groupedOptions[optionName]) {
          groupedOptions[optionName] = new Set()
        }
        groupedOptions[optionName].add(optionValue)
      })
    })

    // Convert Sets to Arrays
    Object.keys(groupedOptions).forEach(key => {
      groupedOptions[key] = Array.from(groupedOptions[key])
    })

    return groupedOptions
  }

}

window.QuickAdd = QuickAdd
