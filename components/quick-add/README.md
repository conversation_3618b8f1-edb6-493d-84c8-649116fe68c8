# QuickAdd Controller Documentation

## Introduction

The `QuickAdd` controller provides functionality for quickly adding products to the cart in a Shopify custom theme framework. It allows users to open a modal with product information and variants for quick selection and purchase. This controller is responsible for handling the opening, closing, and interaction with the quick add modal.

### Global Object

A global object named `quickadd` is used to store the state and data related to the quick add functionality. The object has the following properties:

- `product`: Represents the selected product information.
- `variant`: Represents the selected product variant information.
- `openedFrom`: Represents the DOM element from which the quick add modal was opened.
- `success`: Represents the success message for quick add actions.
- `config`: Contains configuration options for the quick add modal.

## Methods

### `open(product, variant, event, config)`

This method opens the quick add modal and fetches the product data from the server.

- `product` (mixed): The product handle or object to be added.
- `variant` (number|string|null): The variant ID or option value to be pre-selected.
- `event` (Event|null): The event that triggered the quick add action.
- `config` (object|null): Configuration options for the quick add modal.

The method dispatches a custom event `QuickAdd:open` and fetches product data from the server. If a variant is provided, it calls the `select` method.

### `close()`

This method closes the quick add modal and resets the state.

The method dispatches a custom event `QuickAdd:close`, focuses on the previously opened element, and then calls the `reset` method.

### `reset()`

This method resets the quick add state to its initial values.

The method resets the `quickadd` object's properties to their default values.

### `select(variant_id)`

This method selects a product variant for purchase.

- `variant_id` (number): The ID of the selected variant.

The method dispatches a custom event `QuickAdd:select`, sets the selected variant, and triggers different actions based on the configuration. If no configuration action is specified, it triggers the `buy` action.

### `Enhanced Events`

The `QuickAdd` controller dispatches the following custom events during its operations:

- `QuickAdd:open`: Dispatched when the quick add modal is opened.
- `QuickAdd:close`: Dispatched when the quick add modal is closed.
- `QuickAdd:select`: Dispatched when a variant is selected.
- `QuickAdd:buy`: Dispatched when the user proceeds to buy the selected variant.

## Usage

1. Include the `QuickAdd` controller script in your Shopify theme.
2. Use the `QuickAdd.open` method to open the quick add modal for a specific product.
3. Provide the necessary event, variant, and configuration data.
4. Handle custom events dispatched by the `QuickAdd` controller to perform additional actions.

### Example

```javascript
// Open the quick add modal for a product
QuickAdd.open(productHandle, variantId, event, config);

// Handle custom events dispatched by the QuickAdd controller
window.addEventListener('QuickAdd:open', (event) => {
    // Handle modal open event
});
```

## Conclusion

The `QuickAdd` controller streamlines the process of adding products to the cart in a Shopify custom theme framework. By providing methods for opening, closing, and selecting variants, it enhances the user experience and facilitates quick purchases. Custom events and configuration options make it adaptable to different use cases.