.quick-add {
  &__field-button {
    @apply max-w-full;
  }
  &__field-button-img {
    @apply hidden;
  }
}
.plp-notify-me-when-available {
  text-align: center;
}
@media only screen and (max-width: 767px) {
    .plp-notify-me-when-available {
      font-size: 13px !important;
      white-space: normal !important;
      padding: 16px 14px !important;
      text-align: center;
    }
  }
@media only screen and (min-width:1024px) and (max-width:1277px){
  .plp-notify-me-when-available {
    padding-left: 10px !important;
    padding-right: 10px !important;
    min-height:48px !important;
  }
}
@media only screen and (min-width:1124px) and (max-width:1277px){
  .plp-notify-me-when-available {
    font-size: 13px !important;
  }
}
@media only screen and (min-width:1024px) and (max-width:1123px){
  .plp-notify-me-when-available {
    font-size: 11px !important;
  }
}
