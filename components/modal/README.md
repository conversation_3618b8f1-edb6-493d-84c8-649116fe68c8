## Modal Controller

The provided JavaScript code defines a `Modal` controller object that manages the behavior of modals in a web application. This controller provides methods to open, close, and manage modals, as well as handle modal history and activation. Additionally, the provided CSS code includes styles for modal presentation and scroll locking.

### Usage

To use the `Modal` controller, you can call its methods to open or close modals. For example:

```javascript
Modal.open('myModal');  // Opens a modal with the handle 'myModal'
Modal.close();          // Closes the currently open modal
```

### Methods

#### `open(topic, history=true)`

Opens a modal with the specified `topic` handle. If `history` is set to `true`, the modal's handle will be pushed to the modal history stack. If a modal is already open, it will be closed before opening the new modal.

#### `close()`

Closes the currently open modal.

#### `history.push(topic)`

Pushes the given `topic` handle to the modal history stack, ensuring that it's not duplicated in the stack.

#### `history.back()`

Navigates back in the modal history stack and opens the previous modal.

### Event Listeners

- The code listens for the `hashchange` event and checks if the URL contains a hash starting with "#modal:". If such a hash is detected, it opens the modal corresponding to the topic in the hash.

### Data and State

- The `window.modal` object is used to store information about the active modal and the modal history.
- The `active` property stores the currently active modal topic (if any).
- The `history` property is an array that keeps track of the modal history.

### CSS Styles

The provided CSS code includes styling for the modal presentation and scroll locking. The modals can have various positions such as right-aligned or centered. Additionally, there's an underlay style that adjusts the modal position to accommodate a fixed header bar.

```css
/* Scroll Locking 
  ========================================================================== */
@media only screen and (max-width: 1023px) {
  html:has(.header-bar--main.active .header-menu--main details[open]  ),
  html:has(.modal[open]) {
    overflow:hidden;
    height:100%;
    height:100dvh;
  }
}

@media only screen and (min-width: 1024px) {
  html:has(.header-menu--main details[open] ),
  html:has(.modal[open]) {
    overflow:hidden;
    height:100%;
    height:100dvh;

  }
}

.modal {
  
  position:fixed;
  margin:0;

  &[open] {
  }
  &--right {
    right:0; 
    left:auto;
    @apply lg:w-1/3 bg-white shadow h-screen;
    &[open] {

    }
  }

  &--center {
    top:50%; 
    left:50%;
    transform:translate(-50%, -50%);
    &[open] {

    }
  }

  &--underlay {
    &.modal--right {
      top:var(--header-offset);
      height:calc(100dvh - var(--header-offset));  
    }
  }

  &-overlay {

    background:rgba(0,0,0,0.8);
    opacity:0;
    pointer-events:none;
    transition: opacity 300ms ease;
    html:has(.modal[open])  & {
      opacity:1;
      pointer-events:all;
    }
  }
}
```

Please note that the CSS code applies different styles to the modals based on their positioning and whether they are open or not.

---

Please remember that while the provided code offers a solid foundation for modal management and presentation, its exact integration with your application will depend on your specific requirements, HTML structure, and design preferences. You might need to adjust the code to fit seamlessly into your existing codebase and styling practices.