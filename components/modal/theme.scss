.modal {
  
  position:fixed;
  margin:0;

  &[open] {
  }
  &--right {
    right:0; 
    left:auto;
    @apply w-full lg:w-1/3 bg-white shadow h-screen;
    max-width:100vw;
    max-height:100dvh;
    &[open] {
      transform: translateX(0%);
    }
  }

  &--left {
    right:0; 
    left:auto;
    @apply w-full lg:w-1/3 bg-white shadow h-screen;
    transform: translateX(-100%);
    max-width:100vw;
    max-height:100dvh;
    &[open] {
      transform: translateX(0%);
    }
  }

  &--center {
    top:50%; 
    left:50%;
    transform:translate(-50%, -50%);
    &[open] {

    }
  }

  &--underlay {
    &.modal--right {
      // top:var(--header-offset);
      // height:calc(100vh - var(--header-offset));  
      top:var(--header-bottom);
      height:calc(100vh - var(--header-bottom));  
    }
  }

  &-overlay {

    background:rgba(0,0,0,0.8);
    opacity:0;
    pointer-events:none;
    transition: opacity 300ms ease;
    html:has(.modal[open]) & {
      opacity:1;
      pointer-events:all;
    }
    html:has(.header-bar__block--menu details[open]) & {
      @media only screen and (min-width: 1024px) {
        opacity:1;
        pointer-events:all;
        z-index:30;
      } 
    }
  }

  &--beside-popup img{
    margin-top:10px;
    margin-bottom:10px;
  }
}

.max-md\:modal {
  &--left {
    @media only screen and (max-width: 1023px) {
      right:0; 
      left:auto;
      bottom:0;
      top:0;
      @apply w-full lg:w-1/3 bg-white shadow h-screen;
      transform: translateX(-100%);
      max-width:100vw;
      max-height:100dvh;
    }
    &[open] {
      transform: translateX(0%);
    }
  }
}

/* Size Guide
========================================================================== */

#size-chart-html {
  table {
      td {
          @media (max-width: 767px) {
              padding: 0.5rem;
          }
          &:not(:first-child) {
              p {
                  @media (max-width: 767px) {
                      font-size: 12px;
                  }
              }
          }
      }
  }
}
