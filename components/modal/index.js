const Modal = {

  open: (topic, history=true) => {
    Modal.close()
    if(!!topic){
      Util.events.dispatch('Modal:open', {topic:topic})
      document.querySelectorAll(`[data-modal="${ topic.handle() }"]`).forEach(el=>{
        if (['DIALOG','DETAILS'].includes(el.nodeName)) {
          el.open = true
        } else {
          el.setAttribute('open',true)
        }
        modal.active = topic
        if(history)Modal.history.push(topic) 
      })
    }
  
  },
  close: () => {

    Util.events.dispatch('Modal:close')
    document.querySelectorAll(`[data-modal][open]`).forEach(el=>{
      if (['DIALOG','DETAILS'].includes(el.nodeName)) {
        el.open = false
        // if(quickadd?.product) {
        //   QuickAdd.reset()
        // }
      } else {
       el.removeAttribute('open')
      }
    })
  },
  toggle: topic => {
    if (document.querySelector(`[data-modal="${ topic.handle() }"]`).open){
      Modal.close(topic)
    } else {
      Modal.open(topic)
    }
  },
  history: {
    push: topic => {
      if([...modal.history].reverse()[0] != topic)
        modal.history.push(topic)
    },
    back: () => {
      modal.history.pop()
      Modal.open([...modal.history].reverse()[0], false)
    }
  }

}

window.addEventListener('hashchange', e=>{
  if (document.location.hash.includes('#modal:')) {
    Modal.open(document.location.hash.replace('#modal:',''))
  }
})

window.modal = {
  active:false,
  history:[]
}

window.Modal = Modal;
export default Modal;
