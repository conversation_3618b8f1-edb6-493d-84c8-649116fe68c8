## Offers Controller

The provided JavaScript code defines an `Offers` controller object that manages the presentation of offers, gifts, and other content based on conditions and events that occur on the site.

### Usage

The `Offers` controller initializes when the DOM content is loaded and starts managing the presentation of offers based on conditions and events. It provides methods to accept, decline, and revoke offers.

```javascript
Offers.init();  // Initialize the Offers controller

// To accept an offer
Offers.accept('offer_key');

// To decline an offer
Offers.decline('offer_key');
```

### Data and State

- The `accepted`, `declined`, and `presented` arrays store the keys of offers that have been accepted, declined, and presented, respectively.

### Methods

#### `init()`

Initializes the `Offers` controller. It retrieves data from storage, checks eligibility, and runs the presentation of offers.

#### `clear()`

Clears stored offer data from both local and session storage.

#### `run()`

Runs the logic for determining which offers are eligible and should be presented. It checks conditions, actions, and filters to determine eligibility and decides which offer to present.

Certainly! When the `Offers.run()` method is executed, several key processes take place to determine which offers are eligible for presentation:

1. **Loop Through Offers:** The method starts by iterating through each offer defined in the `offers` array. For each offer, it performs the following steps:

2. **Check Eligibility:** The method evaluates the `conditions` defined for the offer using the `Util.express()` function. This function allows the use of custom expressions that determine if an offer is eligible based on various factors, such as user information, cart content, or other contextual data. If the offer's conditions evaluate to `true`, the offer is marked as eligible.

3. **Set Message Content:** Depending on whether the offer is eligible or not, the method determines the message content that should be presented to the user. If eligible, the offer's `success` message is selected; otherwise, the `approach` message is chosen.

4. **Calculate Progress (Optional):** If the offer defines `progress_numerator` and `progress_denominator`, the method calculates the offer's progress as a percentage. This progress might represent the completion of certain actions or requirements related to the offer.

5. **Determine Actionability:** Based on eligibility and other factors, the method determines if an offer is actionable. An actionable offer is one that can be presented to the user and interacted with, typically to accept or decline.

6. **Present Offer:** The method identifies the first actionable offer that meets the criteria defined in the `action_filter`. This filter is another expression that specifies additional conditions for an offer to be presented.

7. **Dispatch Event and Execute Actions:** If an offer is found to be actionable, the method executes the presentation process. It waits for a specified delay (if any) using the `Util.wait()` function and then proceeds to present the offer.

8. **Update Storage and Execute Actions:** When presenting an offer, the method updates the `presented` status for the offer in storage. It also dispatches an event `Offer:present` to indicate that an offer is being presented. The associated actions defined in the offer are then executed. If the offer has the `automatic` flag set, it's automatically accepted.

9. **Handle Accepted and Declined Offers:** After offer presentation, the method checks for offers that were accepted but are no longer eligible. These offers are revoked, and their accepted status is removed from storage.

In summary, the `Offers.run()` method evaluates each offer's eligibility, determines if it should be presented based on conditions and filters, presents an actionable offer to the user, executes associated actions, and handles the acceptance or revocation of offers. This process ensures that users are presented with relevant offers based on their behavior and contextual information, enhancing their experience on the website.

#### `present(offer)`

Presents the specified offer by executing associated actions and dispatching events. If the `automatic` property is set, the offer is automatically accepted.

#### `decline(key)`

Declines the offer with the specified key and updates the necessary properties and storage.

#### `accept(key)`

Accepts the offer with the specified key and updates the necessary properties and storage.

#### `revoke(key)`

Revokes an accepted offer, executing the associated revoke actions if specified.

### Event Listeners

- The controller listens for various customer-related events to trigger the `run` method and update offer presentation based on customer interactions.

### Initialization

- The `DOMContentLoaded` event listener initializes the `Offers` controller when the DOM content is loaded.

### Export

The controller is exported as the default export, making it accessible to other parts of the application.

```javascript
export default Offers;
```

Please note that the `Offers` controller relies on certain conditions, actions, and events to determine offer eligibility and behavior. Integration of this controller with your application will require careful consideration of your specific use case and the events or conditions that trigger offer presentation, acceptance, and decline.