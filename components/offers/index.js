const Offers = {

	accepted: [],
	declined: [],

	init: () => {
		const accepted = [
			...Util.storage.get('local','Offers:accepted') || [],
			...Util.storage.get('session','Offers:accepted') || [],
		]

		const declined = [
			...Util.storage.get('local','Offers:declined') || [],
			...Util.storage.get('session','Offers:declined') || [],
		]

		const presented = [
			...Util.storage.get('local','Offers:presented') || [],
			...Util.storage.get('session','Offers:presented') || [],
		]

		offers.map(offer => {
			
			offer.accepted = accepted.includes(offer.key)
			offer.declined = declined.includes(offer.key)
			offer.presented = presented.includes(offer.key)
		})

		Offers.run()

	},

	clear: () => {
		['local','session'].forEach(storage=>{
			['accepted','declined','presented'].forEach(evt=>{
				Util.storage.unset('local',`Offers:${evt}`)
			})
		})
	},

	run: () => {

		return new Promise((res, rej) => {

			offers.map(offer=>{
				
				offer.eligible = Util.express((offer.conditions || 'true'), offer)

				if(offer.eligible){
					offer.message = Util.literal(offer.success,offer)
				} else {
					offer.message = Util.literal(offer.approach,offer)
				}

				if(!!offer.progress_numerator && !!offer.progress_denominator)
					try{
						offer.progress = `${eval(Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card') ? (- item.price * item.quantity) : 0)) + cart.total_price) / eval(offer.progress_denominator) * 100}%`
					} catch(err) { }

				if(offer.eligible && !offer.accepted && !offer.declined){
					offer.actionable = true
				} else {
					offer.actionable = false
				}
			})

			const actionableOffer = offers.find(offer => offer.actionable && Util.express(offer.action_filter || 'true', offer))
			
			Offers.present(offers.find(offer=>offer.actionable && Util.express(offer.action_filter || 'true', offer)))

			offers
			.filter(offer => {
				const shouldRevoke = offer.accepted && !offer.eligible;

				return shouldRevoke;
			})
			.forEach(offer => {
				Offers.revoke(offer.key)
			})

			Util.events.dispatch('Offers:update')

		})
	},

	present: offer => {

		if(!offer) return false;

		Util.wait((offer.delay || 0)*1000).then(()=>{

			offer.presented = true;
			
			Util.storage.push(offer.storage,'Offers:presented', offer.key)

			if(Util.events.dispatch('Offer:present', offer)){

				Util.express(offer.actions, offer)
				Util.events.dispatch('Offer:presented', offer)

				if(offer.automatic) Offers.accept(offer.key)
			
			}

		})

	},

	decline: key => {
		
		const offer = offers.find(o=>o.title==key||o.key==key)
		offer.declined = true
		Offers.declined.push(offer.key)
		Util.storage.push(offer.storage,'Offers:declined', offer.key)
		Util.events.dispatch('Offer:declined', offer)
		Offers.run()

	},

	accept: key => {
		
		const offer = offers.find(o=>o.title==key||o.key==key)
		if (!offer) {
			console.error(`Offer not found for key: ${key}`)
			return
		}
		offer.accepted = true;
		offer.revoked = false;
		Offers.accepted.push(offer.key)
		Util.storage.push(offer.storage,'Offers:accepted', offer.key)
		Util.events.dispatch('Offer:accepted', offer)
		// Offers.run() 

	},

	revoke: key => {
		const offer = offers.find(o=>o.title==key||o.key==key)

		offer.accepted = false;
		offer.revoked = true;
		Util.storage.pop(offer.storage,'Offers:accepted', offer.key)
		Util.events.dispatch('Offer:revoked', offer)

		if(!offer.revoke) return false;
		
		Util.express(offer.revoke, offer)

	}
		
}

window.offers = window.offers || []
window.Offers = Offers
window.addEventListener('DOMContentLoaded', Offers.init);

[
	'Cart:set',
	'Customer:set','Customer:logout','Customer:login','Customer:authenticated','Customer:register','Customer:registered','Customer:formSubmit','Customer:request','Customer:update','Customer:identify','Customer:subscribe','Customer:unsubscribe','Customer:profileUpdate','Customer:profileEdit'
].forEach(evt=>{
	window.addEventListener(evt,Util.debounce(e=>{
		Offers.run()
		
	},40))
})

window.addEventListener('Cart:itemRemoved', e => {
	// run offers on cart change
	Offers.run()
})
window.addEventListener('Cart:changed', e => {
	// run offers on cart change
	Offers.run()
})

export default Offers
 
