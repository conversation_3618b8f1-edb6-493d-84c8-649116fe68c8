window.Focus = {
  history: [],
  init() {
    document.addEventListener("focus", (event) => {
      Focus.pushState(document.activeElement);
    }, true);
  },
  pushState(element) {
    if (this.history[this.history.length - 1] !== element) {
      this.history.push(element);
    }
  },
  back() {
    const reversedHistory = this.history.slice().reverse();

    for (let i = 0; i < reversedHistory.length; i++) {
      const currentElement = reversedHistory[i];
      if (window.Util.visible(currentElement, false)) {
        currentElement.focus();
        currentElement.scrollIntoViewIfNeeded();
        this.history.splice(this.history.length - 1 - i, 1); 
        break;
      }
    }
  },
};

document.addEventListener("DOMContentLoaded", window.Focus.init);
