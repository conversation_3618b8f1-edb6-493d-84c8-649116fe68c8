### Focus Controller

The `Focus` controller is implemented as an object with methods and properties to manage the focus history.

- **`history`:** This property is an array that stores the history of focused elements.

- **`init()`:** This method initializes the focus controller by adding a global event listener to the document's `focus` event. When an element gains focus, the `pushState` method is called to add it to the history.

- **`pushState(element)`:** This method is responsible for pushing a focused element to the history array. It only adds the element to the history if it's different from the last element that was focused.

- **`back()`:** This method simulates a "back" functionality for the focus history. It iterates through the reversed history array and looks for a visible element to focus. Once it finds a visible element, it focuses on it, scrolls it into view if needed, and removes it from the history.

### Event Listeners

- **`DOMContentLoaded`:** This event listener triggers the initialization of the `Focus` controller once the DOM content has loaded. It ensures that the focus tracking starts only after the page structure is ready.

- **`focus`:** This event listener is added to the `document` and captures all `focus` events in the capture phase (via the `useCapture` parameter of `addEventListener`). When an element gains focus, the `pushState` method is called to add it to the history.

### How the Controller Works

1. When the DOM content is loaded, the `DOMContentLoaded` event triggers the `init` method, which sets up the `focus` event listener.

2. Whenever an element gains focus anywhere in the document, the `focus` event listener captures it and calls the `pushState` method to add the focused element to the history.

3. The `pushState` method checks if the last element in the history is the same as the currently focused element. If they are different, the focused element is added to the history.

4. The `back` method is used to navigate backward in the focus history. It iterates through the reversed history and focuses on the first visible element it encounters. Once focused, the element is scrolled into view if necessary, and it is removed from the history to prevent focusing on it repeatedly.

This controller can be helpful in scenarios where you want to provide users with an easy way to navigate through the focus states of different elements on a page, improving accessibility and user experience.