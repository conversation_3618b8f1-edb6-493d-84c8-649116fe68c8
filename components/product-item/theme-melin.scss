.product-item {
  @apply relative;

  &__images {
    @apply relative mb-2 border-b /*border-[#d3c7c1] bg-[#f5f5f5]*/;

    img {
      @apply object-cover; // temp fix for dev store product images being a non-square aspect ratio;
    }
  }

  &__title-price-wrap{
    display: flex;
    justify-content: space-between;
    align-items: baseline;
  }

	&__meta {
		@apply py-2.5 lg:pt-[7px] grid-cols-[1fr_auto] gap-x-2;
    & > * {
      @apply basis-full;
    }
	}

  &__title {
    @apply text-base m-0 mb-2 leading-normal basis-full mr-2;

    .section--content-carousel & {
      @apply lg:text-base text-sm;
    }
  }

  &__subtitle,
  &__style,
  &__type {
    @apply font-body text-xs tracking-[0.4px] text-[#77706c] capitalize font-normal mb-1 order-3 col-start-1 leading-normal;
  }

	&__prices {
		@apply m-0 whitespace-nowrap flex gap-x-1;
	}

  &__price,
  &__compare-at-price {
    @apply text-[14px] font-body tracking-[0.4px] mb-2.5 leading-normal;
  }

  &__price_with_compare {
    color: #a00;
  }

  &__swatch {
    @apply bg-[#F5F5F5] border border-transparent active:border-primary mb-[1px] lg:mb-0;
  }

  &__swatch-button {
    @apply bg-white rounded-full w-12 h-12 -ml-12 shadow shrink-0 items-center justify-center inline-flex opacity-0 lg:group-hover:opacity-100 z-[1];

    &--prev {
      @apply left-2;
    }

    &--next {
      @apply right-2;
    }

    svg {
      width: 20px;
      stroke-width: 3px;
      stroke: #021327;
    }
  }

  &__quick-add.button {
    @apply rounded-none border-[#e4e4e4];
    @apply max-lg:bg-transparent max-lg:mt-2 max-lg:mr-2;

    svg:first-child,
    span {
      @apply hidden lg:block;
    }
    svg {
      @apply w-16 h-16 lg:w-3 lg:h-3;
      stroke-width: 3px;
    }
    span {
      @apply text-xs tracking-[1.2px] font-body;
    }

    span + svg {
      @apply lg:hidden ml-0;
    }
  }

  .review-snippet {
    @apply mt-0 order-4 col-start-1;

    .ruk-rating-snippet-count {
      @apply text-[10px] tracking-[0.4px] ml-1;
    }
  }
}

