.product-item {
  @apply relative;

  &__images {
    @apply bg-white relative mb-2 aspect-w-4 aspect-h-3;
  }

  &__title-price-wrap{
    display: flex;
    justify-content: space-between;
    align-items: baseline;
  }

	&__meta {
		@apply py-2.5 lg:pt-[7px] grid-cols-[1fr_auto] gap-x-2;
    & > * {
      @apply basis-full;
    }
	}

  &__title {
    @apply text-base m-0 mb-2 basis-3/5 leading-normal;

    .section--content-carousel & {
      @apply text-base;
    }
  }

  &__subtitle,
  &__style,
  &__type {
    @apply font-body text-xs tracking-[0.4px] text-[#77706c] capitalize font-normal mb-1 order-3 col-start-1 leading-normal;
  }

	&__prices {
		@apply m-0 whitespace-nowrap flex gap-x-1;
	}

  &__price,
  &__compare-at-price {
    @apply text-[14px] font-body tracking-[0.4px] mb-2 leading-normal;
  }

  &__quick-add.button {
    @apply rounded-none max-lg:bg-transparent lg:bg-white lg:text-black lg:border-[#d9d9d9] max-lg:mt-2 max-lg:mr-2 h-10;

    svg:first-child,
    span {
      @apply hidden lg:block;
    }
    svg {
      @apply w-16 h-16 lg:w-3 lg:h-3;
      stroke-width: 3px;
    }
    span {
      @apply text-xs tracking-[1.2px] font-body;
    }

    span + svg {
      @apply lg:hidden ml-0;
    }
  }

  .review-snippet {
    @apply mt-0 order-4 col-start-1;

    .ruk-rating-snippet-count {
      @apply text-[10px] tracking-[0.4px] ml-1;
    }
  }
}



// ======== ALTERNATE STYLING TO MATCH LIVE SITE ========



// .product-item {
//   @apply relative;

//   &__images {
//     @apply relative mb-2 bg-[#f5f5f5] aspect-w-2 aspect-h-1;
//   }

// 	&__meta {
// 		@apply py-2.5 lg:pt-[7px] grid grid-cols-1 justify-self-center text-center gap-x-2;
//     & > * {
//       @apply basis-full;
//     }
// 	}

//   &__title {
//     @apply text-[18px] m-0 mb-1 basis-3/5 leading-normal;

//     .section--content-carousel & {
//       @apply text-base;
//     }
//   }

//   &__subtitle,
//   &__style,
//   &__type {
//     @apply font-body text-base capitalize font-normal mb-1 order-3 col-start-1 leading-normal;
//   }
  

// 	&__prices {
// 		@apply m-0 whitespace-nowrap flex gap-x-1 justify-center  row-start-4;
    
// 	}

//   &__price,
//   &__compare-at-price {
//     @apply text-[14px] text-dark font-subheading mb-2 leading-normal;
//   }

//   body[product-item*="Model"] & {
//     .product-item__images {
//       .opacity-0 {
//         opacity:1;
//       }
//       .opacity-100 {
//         opacity:0;
//       }
//     }
//     &:hover {
//       .product-item__images {
//         .opacity-0 {
//           opacity:0;
//         }
//         .opacity-100 {
//           opacity:100;
//         }
//       }
//     }
//   }

//   &__quick-add.button {
//     @apply rounded-none max-lg:bg-transparent lg:bg-white lg:text-black lg:border-[#d9d9d9] max-lg:mt-2 max-lg:mr-2 h-10;
    
//     svg:first-child,
//     span {
//       @apply hidden lg:block;
//     }
//     svg {
//       @apply w-16 h-16 lg:w-3 lg:h-3;
//       stroke-width: 3px;
//     }
//     span {
//       @apply text-xs tracking-[1.2px] font-body;
//     }
    
//     span + svg {
//       @apply lg:hidden ml-0;
//     }
//     }
    
//     .review-snippet {
//       @apply mt-0 order-4 col-start-1;
    
//       .ruk-rating-snippet-count {
//         @apply text-[10px] tracking-[0.4px] ml-1;
//       }
//     }
// }
