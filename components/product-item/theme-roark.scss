.product-item {
  @apply relative;

  &__images {
    @apply relative mb-2 border-b border-[#d3c7c1];

    img {
      @apply object-contain;
    }
  }

  &__title-price-wrap{
    display: flex;
    justify-content: space-between;
    align-items: baseline;
  }

	&__meta {
		@apply py-2.5 lg:pt-[7px] grid-cols-[1fr_auto] gap-x-2;
    & > * {
      @apply basis-full;
    }
	}

  &__title {
    @apply text-base m-0 basis-3/5;

    .section--content-carousel & {
      @apply text-base;
    }
  }

  &__subtitle,
  &__style,
  &__type {
    @apply font-body text-xs tracking-[0.4px] text-[#77706c] capitalize font-normal mb-[3px] order-3 col-start-1;
  }

	&__prices {
		@apply m-0 whitespace-nowrap flex gap-x-1;
	}

  &__price,
  &__compare-at-price {
    @apply text-[14px] font-body tracking-[0.4px];
  }

&__price_with_compare {
  color: red;
}

  &__quick-add.button {
    @apply rounded-none border-[#e4e4e4];
    @apply max-lg:bg-transparent max-lg:mt-2 max-lg:mr-2;

    svg:first-child,
    span {
      @apply hidden lg:block;
    }
    svg {
      @apply w-16 h-16 lg:w-3 lg:h-3;
      stroke-width: 3px;
    }
    span {
      @apply text-xs tracking-[1.2px] font-body;
    }

    span + svg {
      @apply lg:hidden ml-0;
    }
  }

  .review-snippet {
    @apply mt-2 order-4 col-start-1;

    .ruk-rating-snippet-count {
      @apply text-[10px] tracking-[0.4px] ml-1;
    }
  }
}

