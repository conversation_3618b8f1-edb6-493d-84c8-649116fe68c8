const Redirects = {

	init: () => {

		window.redirects = window.redirects || []

		window.redirects.push(...(Util.storage.get('local', 'redirects')||[]))
		window.redirects.push(...(Util.storage.get('session', 'redirects')||[]))
		
		// Redirects.run()
	},

	run: () => {

		return new Promise((res, rej) => {
			const redirects = [...window.redirects]
			const redirect = redirects.find(redirect=>eval(redirect.conditions))
          	if (!redirect || !redirect.destination || Shopify.designMode) return false
          	
          	return window.location.href = redirect.destination
		})

	},

	push: redirect => {
		const redirects = [...window.redirects]

		const index = redirects.findIndex(r => r.key === redirect.key)
		// key already exists so replace
		// index !== -1 ? console.log('Redirects:push - replace') : console.log('Redirects:push - add to array')
		const newRedirects = index !== -1 ? redirects.map((r, i) => i === index ? redirect : r) : [...redirects, redirect]
		

		if (!!redirect.persist) {
			Util.storage.set(redirect.persist, 'redirects',newRedirects)
			
			window.redirects = newRedirects
		}
		// Redirects.replace()
	},

	replace: () => {
		const redirects = [...window.redirects]
		// preemptive link replacement
		redirects.forEach(redirect => {
			if (redirect.conditions && redirect.conditions.includes('document.location')) {
				const pathMatch = redirect.conditions.match(/'([^']+)'/)
				const pathname = pathMatch ? pathMatch[1] : null
		
				if (pathname) {
					const links = document.querySelectorAll(`a[href="${pathname}"]`)
					links.forEach(link => {
					  link.setAttribute('href', redirect.destination)
					})
				}
			}
		})
	},

	// store redirects for checkout
	checkout: () => {
		const redirects = [...window.redirects]
		const checkoutRedirects = redirects.filter(redirect => redirect.persist === 'session')
		Util.storage.set('session', 'redirects', checkoutRedirects)
	},

	// retrieve redirects in checkout
	checkoutRedirects: () => {
		return Util.storage.get('session', 'redirects')
	}
}

window.Redirects = Redirects
// window.redirects = window.redirects || []
// Redirects.init()

export default Redirects
