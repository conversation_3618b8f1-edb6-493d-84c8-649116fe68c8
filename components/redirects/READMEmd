# Redirects Engine/Controller Documentation

## Introduction

The `Redirects` engine/controller manages the redirection functionality in a Shopify custom theme framework. It allows developers to define and manage redirects based on conditions and destinations. Redirects can be triggered based on specific conditions, and they are processed when the `Redirects.run()` method is called.

### Global Object

A global object named `redirects` is used to store the redirection rules and configuration. The object is initialized with any persisted redirects stored in local and session storage.

## Methods

### `init()`

This method initializes the `Redirects` engine by fetching persisted redirects from storage and prepares the redirection functionality.

The method retrieves redirects from local and session storage and calls the `run()` method to process the redirects.

### `run()`

This method processes the redirects based on their conditions and triggers redirection if the conditions are met.

The method searches for a redirect that matches the conditions and triggers redirection if the destination is defined and the current page is not in Shopify design mode.

### `push(redirect)`

This method adds a new redirect rule to the global `redirects` object and stores it in local or session storage if specified.

- `redirect` (object): The redirect object containing key, conditions, and destination.

The method checks if the redirect key is unique and pushes the redirect object to the `redirects` array. If persistence is specified in the redirect object, it stores the redirect in local or session storage.

## Usage

1. Include the `Redirects` engine/controller script in your Shopify theme.
2. Use the `Redirects.push` method to add new redirect rules to the `redirects` object.
3. Define conditions and destinations for each redirect.
4. Call the `Redirects.run` method to process redirects based on conditions.

### Example

```javascript
// Push a new redirect rule
Redirects.push({
  key: 'example-rule',
  conditions: 'window.location.pathname === "/old-url"',
  destination: '/new-url',
  persist: 'local'
});

// Initialize and run the Redirects engine
Redirects.init();
Redirects.run();
```

## Conclusion

The `Redirects` engine/controller provides a convenient way to manage and execute redirects based on conditions and destinations in a Shopify custom theme framework. By allowing the addition of custom redirect rules and processing them when needed, it enhances the user experience and helps in efficiently managing page redirections.