const Bloomreach = {

  interval: 1440000,
  cookie: '__exponea_etc__',
  
  source_map: {
    '/account/register':'Loyalty Registration Opt-in'
  },

  api: (endpoint, data={}) => {

    return new Promise(res => {

      data.store_name = store.name; 
      if (Util.cookies.get(Bloomreach.cookie)) data.cookie = Util.cookies.get(Bloomreach.cookie);
      if (customer.identity.email) data.email = customer.identity.email;
      if (customer.identity.first_name) data.first_name = customer.identity.first_name;
      if (customer.identity.last_name) data.last_name = customer.identity.last_name;
      if (Bloomreach.source_map[document.location.pathname]) data.source = Bloomreach.source_map[document.location.pathname];

      fetch(`https://norwi2iw70.execute-api.us-west-2.amazonaws.com/exponea/${endpoint}`, {
        method:'POST',
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data)
      }).then(response => response.json())
        .then(r => {
          res(!!r.response ? r.response : r.result || false)
      })
      
    })

  },

  status: () => {
    Bloomreach.api('get', {}).then(profile=>{
      
      /* Customer data assignments */

      Customer.peripheral('bloomreach', profile)
    })
  }

}
window.Bloomreach = Bloomreach;



window.addEventListener('DOMContentLoaded', e => {
  try {
    if(
      (
        customer.peripherals.bloomreach && 
        Date.now() - customer.peripherals.bloomreach.updated > Bloomreach.interval
      ) || 
      (
        !!customer.identity.email &&
        !customer.peripherals.bloomreach 
      ) || 
      (
        !customer.identity.email &&
        !customer.peripherals.bloomreach &&
        Util.cookies.get(Bloomreach.cookie)
      )
    ) Bloomreach.status();
  } catch(err){}
})


window.addEventListener('Customer:subscribe', e => {
  console.log('Customer:subscribe -> Bloomreach', e.detail)
})

window.addEventListener('Customer:unsubscribe', e => {
  console.log('Customer:unsubscribe -> Bloomreach', e.detail)
})

window.addEventListener('Customer:identify', e => {
  console.log('Customer:identify -> Bloomreach', e.detail)
})

window.addEventListener('Customer:profileUpdate', e => {
  console.log('Customer:profileUpdate -> Bloomreach', e.detail)
})

window.addEventListener('Customer:profileEdit', e => {
  console.log('Customer:profileEdit -> Bloomreach', e.detail)
})





