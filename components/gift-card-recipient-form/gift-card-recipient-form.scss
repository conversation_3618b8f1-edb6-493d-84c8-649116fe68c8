.product-form  {
    .customer {
        width: 100%;
    }
	.recipient-form {
			.field {
				position: relative;  
				&__input {
				  padding: 12px 12px 12px 0;				  
			  
				  &:focus + .field__label,
				  &:not(:placeholder-shown) + .field__label {
					top: .275rem;
					left: .75rem;
					font-size: 11px;
				  }
				}
			  
				&__label {
				  position: absolute;
				  left: 12px;
				  top: 12px;
				  transition: 0.2s ease all;
				  pointer-events: none;
				  color: lightgrey;
				  font-size: 14px;
				}
			label {
			padding-bottom: 10px;
			color: rgb(156 163 175);
			}
		}
		.form__message {
				font-size: 12px;
				line-height: 18px;				
		}
		    display: block;
			position: relative;
			max-width: 45rem;
			.error-message::first-letter {
				text-transform: capitalize;
			}     
			.visually-hidden {
				position: absolute !important;
				overflow: hidden;
				width: 1px;
				height: 1px;
				margin: -1px;
				padding: 0;
				border: 0;
				clip: rect(0 0 0 0);
				word-wrap: normal !important;
		}
			 .icon-checkmark {
			  visibility: hidden;
			  position: absolute;
			  left: 0;
			  z-index: 5;
			  top: 0.25rem;
			  width: 10px;
			  height: 9px;
				margin-top: 0 !important;
				margin-right: 0;
				transform: scale(.75);
			}
			> input[type='checkbox']:checked + label .icon-checkmark {
				  visibility: visible;
			}
			> input[type='checkbox']:checked ~ .recipient-fields {
			  display: block;
			  animation: animateMenuOpen var(--duration-default) ease;
			}
			 > input[type='checkbox']:not(:checked, :disabled) ~ .recipient-fields {
			  display: none;
			}
			.js .recipient-fields, .js .recipient-email-label.required, label.form__label  {
			  display: none;
			}
			input[type='checkbox'] {
				position: absolute;
				width: 1.6rem;
				height: 1.6rem;
				margin: var(--recipient-checkbox-margin-top) 0;
				top: 0;
				left: 0;
				z-index: -1;
				appearance: none;
				-webkit-appearance: none;
			}
		.recipient-checkbox {
		font-size: 1rem;
		  flex-grow: 1;
		  font-size: 1rem;
		  display: flex;
		  word-break: break-word;
		  align-items: flex-start;
		  max-width: inherit;
		  position: relative;
		  cursor: pointer;
		& > svg {
		margin-top: .25rem;
		  margin-right: .5rem;
		  flex-shrink: 0;
			width : 1rem;
			height : 1rem;
		}
		}
		.recipient-fields {
			margin-top : 1.5rem;
			&__field {
				margin : 0 0 1rem 0;
				.field__input.invalid {
					--tw-border-opacity: 1;
					border-color: rgb(239 68 68 / var(--tw-border-opacity));
					border-width: 2px;
				}
				&:last-child {
					.field__label {
						top: .275rem;
						left: .75rem;
						font-size: 11px;
					}
				}
				input,
				textarea{
					padding: .25rem .75rem;
					padding-top: 1.25rem;
				}
				textarea {
				height : 4rem
				}
				span.error-msg {
					font-size: 12px;
					color: rgb(239 68 68) ;
					font-weight: 500;
				}
		}
			 hr {
				display: none; 
			}
		}        
	}
}
.cart-item__line-item {
    word-break: break-all;
		&.emailField {
			word-break: break-word;
	}
}

