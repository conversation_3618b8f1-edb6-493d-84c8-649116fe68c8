const Segmentation = {
  init: () => {
    const cartSegments = cart.attributes.segments;

    // const cartSegments = Object.fromEntries(
    // 	Object.entries(cart.attributes).filter(a=>a[0].startsWith('segment__')).map(a=>[a[0].split('__')[1],a[1]])
    // );

    Customer.update({
      segmentation: {
        ...customer.segmentation,
        ...(cartSegments || {}),
      },
    })
      .then((c) => {
        Segmentation.windowState();
      })
      .catch((err) => {
        console.error("Error updating customer segments on init:", err);
      });
  },

  segment: (key, value, config = {}) => {
    const segment = Object.fromEntries([[key, value]]);

    return new Promise((res, rej) => {
      Util.events.dispatch("Customer:segment", segment);

      // Shopify.analytics.publish( "segment", segment);

      customer.segmentation =
        customer.segmentation || cart.attributes.segments || {};

      if (
        !!customer.segmentation[key] &&
        (customer.segmentation[key] == value ||
          Util.equal(customer.segmentation[key], value))
      ) {
        res(false);
        return;
      }

      customer.segmentation[key] = value;

      Segmentation.windowState();

      Customer.update({ segmentation: customer.segmentation })
        .then((c) => {
          let _a = {
            segments: {
              ...(cart.attributes.segments || {}),
              ...customer.segmentation,
            },
          };

          Cart.attributes(_a)
            .then(() => {
              Util.events.dispatch("Customer:segmented", segment);

              if (config.reload) {
                window.location.reload();
              }

              res(customer.segmentation);
            })
            .catch((err) => {
              console.error("Error setting cart attributes:", err);
            });
        })
        .catch((err) => {
          console.error("Error updating customer segments:", err);
        });
    });
  },

  windowState: () => {
    document.documentElement.classList.add(
      ...Object.entries(customer.segmentation)
        .map((e) =>
          `segment-${e[0]} ${typeof e[1] == "string" ? `segment-${e[0]}--${e[1]}` : ``}`.split(
            " ",
          ),
        )
        .flat(),
    );
  },

  reset: () => {
    // Clear cart attributes
    const attributes = cart.attributes;
    const segments = customer.segmentation;
    const updatedAttributes = {};

    return new Promise((res, rej) => {
      for (let key in attributes) {
        if (key.startsWith("segment") || key === "segments") {
          updatedAttributes[key] = null;
        }
      }

      // Clear localStorage
      const customer = JSON.parse(localStorage.getItem("customer") || "{}");
      if (customer.segmentation) {
        customer.segmentation = {};
        localStorage.setItem("customer", JSON.stringify(customer));
      }

      // Update cart attributes
      Cart.attributes(updatedAttributes)
        .then(() => {
          res(Object.keys(segments).length > 0);
        })
        .catch((err) => {
          console.error("Error clearing segmentation data:", err);
        });
    });
  },
};

/*
Usage

Basic:
Customer.segment('test', 'A')

Reload on application of Segment
Customer.segment('test', 'A').then(t=>if(t){window.location.reload()})

Reload by config
Customer.segment('test', 'A', {reload:true})

Observe an applied Segment (first time)
window.addEventListener('Customer:segmented', e=>{
	console.log(e.detail.info)
})

Observe an attempt to apply a Segment (may already be applied)
window.addEventListener('Customer:segment', e=>{
	console.log(e.detail.info)
})

Observe an applied segment during initialization
window.addEventListener('Customer:update', e=>{
	if(!!e.detail.info.updates.segmentation) {
		console.log(e.detail.info.updates.segmentation)
	}
})

*/

Customer.segment = Segmentation.segment;
Customer.resetSegments = Segmentation.reset;
Segmentation.init();

export default Segmentation;
