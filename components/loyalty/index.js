const Loyalty = {

	init:() => {
		
		if(!customer.account || !customer.account.tags || !customer.account.tags.includes('loyalty_member')) return;

		customer.identity.loyalty = {
			tier_key:customer.account.tags.find(t=>t.includes('tier_'))?.split('_')[1],
			points:{
				available:0,
				pending:0,
				cumulative:0	
			}
		}

		Loyalty.update()

	},

	update: () => {

		if(!customer.account || !customer.account.tags.includes('loyalty_member')) return;

		customer.identity.loyalty.tier = loyalty.tiers.find(t=>t.key==customer.identity.loyalty.tier_key)
		
		customer.identity.loyalty.step = loyalty.tiers[loyalty.tiers.findIndex(t=>t.key==customer.identity.loyalty.tier_key)+1];
		if(!!customer.identity.loyalty.step) {
			customer.identity.loyalty.step.balance = customer.identity.loyalty.step.points - customer.identity.loyalty.points.cumulative;
		}

		Util.events.dispatch('Loyalty:update')

	}

}
window.Loyalty = Loyalty;
Loyalty.init();

window.addEventListener('DOMContentLoaded', e=>Loyalty.update)