## Form Controller Overview

By Default, we utilize form validations with native HTML5 form validations. There is some additional javascript added to add invalid input styles on `focusout`.

This is done by adding a `visited` class to any inputs that have been focused.

Submit can be blocked by adding the HTML5 `required` attribute, the `pattern` attribute, or any other HTML5 validation attributes to the input. There's an overview of these attributes below.

## Field Styles

Base level global styles are included by nesting a label and input in a wrapping element with the `field` class added to it.

```
<div class="field w-full">
	<label for="email">Email</label>
	<input id="email" name="email" type="email" required>
</div>
```

## Floating Label Styles

Base level global styles are included by nesting a label and input in a wrapping element with the `field--floating-label` class added to it.

```
<div class="field field--floating-label w-full">
	<label for="email">Email</label>
	<input id="email" name="email" type="email" required>
</div>
```

##  The `error-message` attribute

If an input needs an additional error message to display on error below the input, you can add it in the `error-message` attribute on the input. The form controller will build out the supporting element and styles from that.

```

<input id="gmail" name="gmail"  type="text" pattern="(.*)@gmail.com" required error-message="Please enter a gmail address.">
```

## HTML5 Form Validations

HTML5 introduced a set of new attributes for form elements that allow you to perform client-side form validation without the need for JavaScript. These attributes include:

1.  `required`: This attribute specifies that a form field must be filled out before submitting the form. If the user tries to submit the form without filling out the required field, a validation error message will appear.
    
2.  `type`: This attribute specifies the type of input expected from the user. For example, `type="email"` will validate that the user enters a valid email address.
    
3.  `min` and `max`: These attributes specify the minimum and maximum values allowed for a form field. For example, `min="0"` and `max="100"` could be used to validate a field that only accepts numbers between 0 and 100.
    
4.  `pattern`: This attribute allows you to specify a regular expression pattern that the input must match. For example, `pattern="[A-Za-z]{3}"` could be used to validate that the user enters a three-letter word.
    

To use these attributes for form validation, you simply need to add them to the form elements in your HTML code. When the user submits the form, the browser will automatically validate the input and display any validation errors. You can also style these error messages using CSS to make them more visible to the user.


## HTML5 Input Types

1.  text
2.  password
3.  datetime
4.  datetime-local
5.  date
6.  month
7.  time
8.  week
9.  number
10.  email
11.  search
12.  tel
13.  url
14.  color
15.  range
16.  checkbox
17.  radio
18.  file
19.  submit
20.  reset
21.  image
22.  button

## Example forms

The code below is an example of the default field styles:

```
<section class="py-24">
	<div class="container mx-auto max-w-screen-sm">
		<form action="/">
			<div class="flex space-x-4 w-full">
				<div class="field w-full">
					<label for="f_name">First Name</label>
					<input id="f_name" name="f_name" type="text" required>
				</div>
				<div class="field w-full">
					<label for="l_name">Last Name</label>
					<input id="l_name" name="l_name" type="text" placeholder="Last Name" required>
				</div>
			</div>
			<div class="field w-full">
				<label for="email">Email</label>
				<input id="email" name="email" type="email" required>
			</div>
			<div class="field w-full">
				<label for="gmail">Gmail</label>
				<input id="gmail" name="gmail"  type="text" pattern="(.*)@gmail.com" required error-message="Please enter a gmail address.">
			</div>
			<div class="field w-full">
				<label for="cookies">How many cookies would you like?</label>
				<input id="cookies" name="cookies"  type="number" min="1" max="100" required>
			</div>
			<input class="button" type="submit" placeholder="Submit">
		</form>
	</div>
</section>
```


The code below is an example of the default floating label styles:

```
<section class="py-24">
	<div class="container mx-auto max-w-screen-sm">
		<form action="/">
			<div class="flex space-x-4 w-full">
				<div class="floating-label w-full">
					<label for="f_name">First Name</label>
					<input id="f_name" name="f_name" type="text" required>
				</div>
				<div class="floating-label w-full">
					<label for="l_name">Last Name</label>
					<input id="l_name" name="l_name" type="text" placeholder="Last Name" required>
				</div>
			</div>
			<div class="floating-label w-full">
				<label for="email">Email</label>
				<input id="email" name="email" type="email" required>
			</div>
			<div class=" floating-label w-full">
			 <label for="gmail">Gmail</label>
				<input id="gmail" name="gmail"  type="text" pattern="(.*)@gmail.com" required error-message="Please enter a gmail address.">
			</div>
			<div class="floating-label w-full">
				<label for="cookies">How many cookies would you like?</label>
				<input id="cookies" name="cookies"  type="number" min="1" max="100" required>
			</div>
			<input class="button" type="submit" placeholder="Submit">
		</form>
	</div>
</section>


```
