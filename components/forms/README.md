### Forms Controller

The `Forms` controller is implemented as an object with methods to enhance form interactions.

- **`init()`:** This method initializes the `Forms` controller by attaching various event listeners and handlers to different elements in the forms.

- **`handleSubmitTracking(el)`:** This method adds a click event listener to form buttons and submit inputs. When clicked, the parent form of the clicked element is marked as "submit-attempted" by adding a class. This can be useful for styling purposes or to track form submission attempts.

- **`handleVisitTracking(el)`:** This method adds a focusout event listener to input elements. When an input loses focus, it checks if the input has a non-empty value. If the value is not empty, the input is marked as "visited" by adding a class. This can be used to apply styles to visited inputs.

- **`createErrorMessages(el)`:** This method adds error messages to form elements. It looks for elements with the `error-message` attribute and appends an error message element after the input element. If an error message element already exists next to the input, it updates its inner text with the provided error message.

- **`handleFloatingLabel(el)`:** This method enhances the behavior of floating labels for form inputs. It finds the input element and label element within a container element. It sets the input's placeholder attribute to the label's text and adds event listeners to handle the appearance of the floating label based on input interactions. It also handles error-related styling based on input validation.

### How the Controller Works

1. When the DOM content is loaded, the `Forms` controller's `init` method is called to set up the necessary event listeners and handlers for form interactions.

2. The `handleSubmitTracking` method adds a click event listener to form buttons and submit inputs. When these elements are clicked, the controller adds the class "submit-attempted" to the closest parent form. This class can be used for styling or tracking purposes.

3. The `handleVisitTracking` method adds a focusout event listener to input elements. When an input loses focus, the controller checks if the input's value is non-empty. If it is, the input is marked as "visited" by adding the class "visited". This class can be used to apply styles to visited inputs.

4. The `createErrorMessages` method adds error messages to form elements. It looks for elements with the `error-message` attribute and adds or updates error message elements accordingly.

5. The `handleFloatingLabel` method enhances the behavior of floating labels for form inputs. It sets the input's placeholder attribute to the label's text and adds event listeners to manage the appearance of the floating label based on input interactions and error conditions.

By using the `Forms` controller, you can improve the user experience of your HTML forms by adding features like tracking submission attempts, styling visited inputs, displaying error messages, and enhancing the behavior of floating labels.