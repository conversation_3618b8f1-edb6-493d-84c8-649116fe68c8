const Forms = {
  init() {
    document.querySelectorAll("form button, form [type=submit], form [type=button]").forEach(Forms.handleSubmitTracking)
    document.querySelectorAll("input").forEach(Forms.handleVisitTracking)
    document.querySelectorAll("[error-message]").forEach(Forms.createErrorMessages)
  },

  handleSubmitTracking(el) {
    el.addEventListener('click', (e) => {
      e.target.closest('form').classList.add('submit-attempted')
      if(Util.form.valid(e.target.closest('form'))) {
        // let emailFieldValue = el.closest('form').querySelector('[name="customer[email]"], [name="email"], [name="phone"]').value;        
        // const referrerUrl = window.location.href;       

        // localStorage.setItem('emailFieldValue', emailFieldValue);
        // localStorage.setItem('referrerUrl', referrerUrl);
        e.target.closest('form').classList.add('submitted')
      }
    })
  },

  handleVisitTracking(el) {
    el.addEventListener('focusout', (e) => {
      if (e.target.value == '') return false
      e.target.classList.add('visited')
    })
  },

  createErrorMessages(el) {
    
    const message = el.getAttribute('error-message')

    if (!!message && !!el.nextElementSibling && el.nextElementSibling.classList.contains('error-message')) return el.nextElementSibling.innerText = message
    if (!!message) el.insertAdjacentHTML('afterend', `<p class="error-message">${message}</p>`)
  }

}

Forms.init()

window.Forms = Forms

export default Forms

