const Header = {
  lastScroll: window.scrollY,
  headerMain: null, 

  init: () => {
    Header.checkForTop()
    Header.headerMain = document.querySelector('.header-bar--main')
    Header.initMutationObserver()
  },

  checkForTop: () => {
    if (window.scrollY <= 100) {
      document.body.classList.add('page-scroll--top')
    } else {
      document.body.classList.remove('page-scroll--top')
    }
  },
  checkDirection: () => {

    const maxScrollPosition = document.documentElement.scrollHeight - window.innerHeight;
    if (window.scrollY > Header.lastScroll) {
      document.body.classList.remove('page-scroll--up')
      document.body.classList.add('page-scroll--down')
    } else if (window.scrollY < Header.lastScroll && window.scrollY < (maxScrollPosition - 100) ) {
      document.body.classList.remove('page-scroll--down')
      document.body.classList.add('page-scroll--up')
    }

},

  handleRedirect: () => {
    // intersection of menu and redirects
    const activeMenu = window.menu.find(menu => window.redirects.some(redirect => redirect.title === menu.redirect))
    if (activeMenu) {
      const targetDetailsId = `details [id='${activeMenu.teleport.substring(1)}']`
      const targetDetails = document.querySelector(targetDetailsId).closest('details')
  
      if (targetDetails) {

        if (targetDetails.group) {
          window.Details.close(targetDetails.group)
        }
  
        if (!targetDetails.open) {
          targetDetails.querySelector('summary').click()
        //   Util.events.dispatch('Details:toggle', targetDetails)
        }
        
      }
    }
  },

  initMutationObserver: () => {
    if (Header.headerMain) {
      const observer = new MutationObserver(Header.mutationObserverCallback)
      observer.observe(Header.headerMain, { attributes: true })
    }
  },

  mutationObserverCallback: (mutationsList, observer) => {
    for (const mutation of mutationsList) {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        if (Header.headerMain && Header.headerMain.classList.contains('active')) {
          Header.handleRedirect()
        }
      }
    }
  },

  updateHeaderOffset: () => {
    const lastHeaderBar = document.querySelector('.header-bar:last-of-type')
    if (lastHeaderBar) {
      const headerBottom = lastHeaderBar.getBoundingClientRect().bottom
      document.documentElement.style.setProperty('--header-bottom', `${headerBottom}px`)
      // const viewportHeight = window.innerHeight
      // const offset = Math.max(0, viewportHeight - headerBottom)
      // document.documentElement.style.setProperty('--header-offset', `${offset}px`)
    }
  }
}

window.addEventListener('scroll', Util.throttle(e => {
  Header.checkDirection()
  Header.checkForTop()
  Header.lastScroll = window.scrollY
  Header.updateHeaderOffset()
}, 10))


window.addEventListener('click', () => {
  Header.updateHeaderOffset()
})

window.addEventListener('scroll', Util.debounce(e => {
  Header.checkForTop()
}, 10))

window.addEventListener('load', e => {
  // Header.handleRedirect()
  const detailsElements = document.querySelector('.header-bar--main').querySelectorAll('details')
  detailsElements.forEach(details => {
    details.addEventListener('toggle', e => {
      e.preventDefault()
      if (details.hasAttribute('open')) {
        // Header.handleRedirect()
      }
    })
  })
  Header.updateHeaderOffset()
})

document.getElementById('header_search').addEventListener("focus", (event) => {
  document.querySelectorAll('.search-suggestions-slides').forEach(element => {
    element.style.display = 'none';
  });
});

window.Header = Header
Header.init()
