{% assign ns = 'giftcard' %}
<div class="{{ ns }}__container">
  <div class="{{ ns }}__block {{ ns }}__block--code">

      {% if section.settings.logo %}
        <div class="icon--logo">
          <a href="{{ shop.url }}" target="_blank">
            <img src="{{ section.settings.logo | image_url }}" alt="{{ section.settings.logo.alt }}" />
          </a>
        </div>
      {% endif %}

    <h2 class="{{ ns }}__title {{ ns }}__title--h2 {{ ns }}__title--page">
      {{ 'gift_cards.issued.title' | t | upcase }}
    </h2>
    {% unless gift_card.enabled %}
      <span class="{{ ns }}__text {{ ns }}__text--disabled">{{ 'gift_cards.issued.disabled' | t }}</span>
    {% endunless %}
    <div class="{{ ns }}__image-container">
      {% assign formatted_initial_value = gift_card.initial_value
        | money_without_trailing_zeros: gift_card.currency
      %}
      <h2 class="{{ ns }}__title {{ ns }}__title--h2 {{ ns }}__title--value">{{ formatted_initial_value }}</h2>
      <div class="gift-card__image-wrapper">
        {% if section.settings.image %}
          <img
            src="{{ section.settings.image | img_url: '950x555' }}"
            alt=""
            class="gift-card__image"
            height="{{ 570 | divided_by: 1.5 }}"
            width="570"
            loading="lazy"
          >
        {% else %}
          <img
            src="{{ 'gift-card/card.svg' | shopify_asset_url }}"
            alt=""
            class="gift-card__image"
            height="{{ 570 | divided_by: 1.5 }}"
            width="570"
            loading="lazy"
          >
        {% endif %}
      </div>
      {%- assign code_size = gift_card.code | format_code | size -%}
      <span class="{{ ns }}__code">
        <p class="{{ ns }}__text {{ ns }}__text--how-to-use">{{ 'gift_cards.issued.redeem' | t }}</p>
        <span id="GiftCardDigits" class="{{ ns }}__text {{ ns }}__text--code">
          {{- gift_card.code | format_code -}}
        </span>
      </span>
    </div>
  </div>
  <div class="{{ ns }}__block {{ ns }}__block--left">
    {% assign formatted_current_balance = gift_card.balance | money %}
    <p class="{{ ns }}__text {{ ns }}__text--balance">Balance: {{ formatted_current_balance }}</p>
    {%- assign gift_card_expiry_date = gift_card.expires_on | date: '%b %d, %Y' -%}
    {% if gift_card.expired and gift_card.enabled %}
      <span class="{{ ns }}__tag {{ ns }}__text">
        {{- 'gift_cards.issued.expired' | t: expiry: gift_card_expiry_date -}}
      </span>
    {% endif %}
    {% if gift_card.expired != true and gift_card.expires_on and gift_card.enabled %}
      <span class="{{ ns }}__tag {{ ns }}__tag--active {{ ns }}__text">
        {{- 'gift_cards.issued.active' | t: expiry: gift_card_expiry_date -}}
      </span>
    {% endif %}    
    <div class="{{ ns }}__buttons">
      <a href="{{ shop.url }}" class="{{ ns }}__button" target="_blank">
        {{ 'gift_cards.issued.shop_link' | t }}
      </a>

      <a href="#" id="PrintGiftCard" class="{{ ns }}__print" onclick="window.print();">
        {{ 'gift_cards.issued.print' | t }}
      </a>
    </div>

    <div class="{{ ns }}__buttonss">
      {% for block in section.blocks %}
          <a href="{{ block.settings.link_url }}" class="{{ ns }}__button" target="_blank">
            {{ block.settings.link_text }}
          </a>
      {% endfor %}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Gift Card Landing",
  "class": "shopify-section--melin-e-gift-card",
  "settings": [    
    {
      "id": "logo",
      "label": "Site Logo",
      "type": "image_picker"
    },
    {
      "id": "image",
      "label": "Image (561px * 360px)",
      "type": "image_picker"
    }
  ],
  "blocks": [
    {
      "type": "link",
      "name": "Link",
      "settings": [
        {
          "type": "url",
          "id": "link_url",
          "label": "Link URL"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "Link Text"
        }
      ]
    }
  ],
  "max_blocks": 2
}
{% endschema %}