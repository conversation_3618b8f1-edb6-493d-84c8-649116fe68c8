name: Add Release Notes Comment

on:
  pull_request:
    branches:
      - staging
    types:
      - opened

jobs:
  add-release-notes-comment:
    runs-on: ubuntu-latest

    if: github.base_ref == 'main' && github.head_ref == 'staging'

    steps:
      - name: Add a release notes comment
        uses: peter-evans/create-or-update-comment@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          body: |
            ## Changes going into production: MM/DD
            - Description:
              - Asana:
              - Requested by:
              - Applicable to:
              - Documentation:
          issue-number: ${{ github.event.pull_request.number }}
