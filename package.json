{"name": "archipelago-caffeinated-theme", "author": "alohaolukai", "private": true, "version": "1.0.0", "description": " ", "scripts": {"serve": "yarn brand serve", "serve2": "cd theme && shopify2 theme serve", "watch": "concurrently -c \"bg<PERSON>yan.bold,bgMagenta.bold\" \"yarn:watch-*\"", "watch-assets": "webpack --watch --mode development", "watch-schemas": "schema-util --watch", "develop": "concurrently -c \"bg<PERSON>yan.bold,bgMagenta.bold,bg<PERSON>reen.bold\" \"pnpm:watch\" \" pnpm run serve\"", "build": "concurrently -c \"bg<PERSON>yan.bold,bgMagenta.bold\" \"yarn:build-*\"", "build-assets": "webpack --mode production", "build-schemas": "schema-util", "build-dev": "webpack --mode development", "brand": "node ./brands/cli.js", "pull": "shopify theme pull --path ./theme -o templates/*.json -o config/settings_data.json", "templates": "cd theme && shopify theme pull -o \"templates/*.json\"", "templates2": "cd theme && shopify2 theme pull -o \"templates/*.json\"", "settings": "cd theme && shopify theme pull -o \"config/settings_data.json\"", "settings2": "cd theme && shopify2 theme pull -o \"config/settings_data.json\""}, "license": "ISC", "devDependencies": {"@babel/core": "^7.15.0", "@babel/preset-env": "^7.15.0", "@fullhuman/postcss-purgecss": "^5.0.0", "@tailwindcss/aspect-ratio": "^0.2.1", "@tailwindcss/line-clamp": "^0.4.0", "babel-loader": "^8.2.2", "concurrently": "^7.6.0", "css-loader": "^6.2.0", "cssnano": "^5.0.8", "dotenv": "^16.0.0", "glob": "^7.1.7", "inquirer": "^8.2.2", "mini-css-extract-plugin": "^2.2.0", "postcss": "^8.4.16", "postcss-loader": "^7.0.1", "postcss-nested": "^6.0.0", "postcss-simple-vars": "^7.0.1", "regenerator-runtime": "^0.13.9", "sass-loader": "^13.2.1", "schema-util": "bitbucket:sltwtr-creative-agency/shopify-schema-utilities#bfefa0d", "webpack": "^5.50.0", "webpack-cli": "^4.8.0", "webpack-merge-and-include-globally": "^2.3.4"}, "dependencies": {"@alpinejs/intersect": "^3.14.1", "@alpinejs/mask": "^3.12.3", "@tailwindcss/container-queries": "^0.1.1", "alpinejs": "^3.10.3", "quicklink": "^2.3.0", "sass": "^1.59.3", "swiper": ">=9.1.1", "tailwindcss": "^3.0.23"}}