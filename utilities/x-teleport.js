
import Alpine from 'alpinejs'

import { onlyDuring<PERSON>lone, skipDuringClone } from "alpinejs/src/clone"
// import { directive } from "alpinejs/src/directives"
// import { addInitSelector, initTree } from "alpinejs/src/lifecycle"
// import { mutateDom } from "alpinejs/src/mutation"
// import { addScopeToNode } from "alpinejs/src/scope"
// import { warn } from "alpinejs/src/utils/warn"

let teleportContainerDuringClone = document.createElement('div')

document.addEventListener('alpine:init', () => {

  // return;

  Alpine.directive('teleport', (el, { modifiers, expression }, { cleanup }) => {
      if (el.tagName.toLowerCase() !== 'template') warn('x-teleport can only be used on a <template> tag', el)

      let target = skipDuringClone(() => {
          return document.querySelector(expression)
      }, () => {
          return teleportContainerDuringClone
      })()

      if (! target) warn(`Cannot find x-teleport element for selector: "${expression}"`)

      let clone = el.content.cloneNode(true).firstElementChild

      // Add reference to element on <template x-teleport, and visa versa.
      el._x_teleport = clone
      clone._x_teleportBack = el

      // Forward event listeners:
      if (el._x_forwardEvents) {
          el._x_forwardEvents.forEach(eventName => {
              clone.addEventListener(eventName, e => {
                  e.stopPropagation()

                  el.dispatchEvent(new e.constructor(e.type, e))
              })
          })
      }

      addScopeToNode(clone, {}, el)

      mutateDom(() => {
          if (modifiers.includes('before')) {
              // insert element after the target
              target.before(clone)
          } else if (modifiers.includes('after')) {
              // insert element after the target
              target.after(clone)
          } else if (modifiers.includes('prepend')) {
              // insert element before the target
              target.parentNode.insertBefore(clone, target)
          } else if (modifiers.includes('append')) {
              // insert element after the target
              target.parentNode.insertBefore(clone, target.nextSibling)
          } else {
              // origin
              target.appendChild(clone)
          }

          initTree(clone)

          clone._x_ignore = true
      })

      cleanup(() => clone.remove())
  })

})