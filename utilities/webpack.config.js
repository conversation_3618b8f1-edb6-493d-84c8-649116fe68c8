const path = require("path");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const glob = require("glob");
const tailwindcss = require("tailwindcss");
const purgecss = require("@fullhuman/postcss-purgecss");
const SymSyncPlugin = require("./symsync");
const docsFiles = require("./docs");
const MergeIntoSingleFilePlugin = require("webpack-merge-and-include-globally");

const projectDir = path.join(__dirname, '..')

module.exports = async (env, argv) => ({
  entry: glob.sync(path.join(projectDir, "components", "**", "index.js")).reduce(
    (acc, path) => {
      const entry_split = path.split("/").reverse();
      let entry = entry_split[1];

      acc[entry] = path;
      return acc;
    },
    { main: path.join(projectDir, "components", "main.js") }
  ),
  output: {
    path: path.join(projectDir, "theme"),
    filename: path.join(".", "assets", "[name].js"),
  },
  cache: true,
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /(node_modules)/,
        use: {
          loader: "babel-loader",
          options: {
            presets: ["@babel/preset-env"],
          },
        },
      },
      {
        test: /\.(sa|sc|c)ss$/i,
        use: [
          MiniCssExtractPlugin.loader,
          "css-loader",
          "sass-loader",
          {
            loader: "postcss-loader",
            options: {
              postcssOptions: {
                plugins: [
                  require('postcss-nested'),
                  tailwindcss,
                  argv.mode === "production"
                    ? require("cssnano")({ preset: "default" })
                    : null,
                ],
                minimize: true,
              },
            },
          },
        ],
      },
    ],
  },
  optimization: {
    runtimeChunk: "single",
    splitChunks: {
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: "vendors",
          chunks: "all",
        },
      },
    },
  },
  plugins: [
    new SymSyncPlugin({
      //debug: true,
      origin: path.join("components", "**", "*.liquid"),
      destination: path.join("theme", "[parentDir]", "[file]"),
      host: 'destination'
    }),
    new MiniCssExtractPlugin({
      filename: path.join(".", "assets", "[name].css"),
    }),
    ...(argv.mode === "production" ? [new MergeIntoSingleFilePlugin({
      files: {
        "./assets/README.md": await docsFiles
      }
    })] : []),
    // new BundleAnalyzerPlugin(),
  ],
});
