<!DOCTYPE html>
<html 
  class="no-js" 
  lang="{{ request.locale.iso_code }}" 
  {%- if request.design_mode %}design-mode{%- endif -%}
>
  <head>
    {%- render 'code' zone:'head' position:'start' -%}

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>

    {%- if settings.favicon != blank -%}
      <link rel="icon" type="image/png" href="{{ settings.favicon | img_url: '32x32' }}">
      <link rel="icon" type="image/png" href="{{ settings.favicon | img_url: '16x16' }}">
      <link rel="shortcut icon" type="image/png" href="{{ settings.favicon | img_url: '32x32' }}"/>
      <link rel="apple-touch-icon" href="{{ settings.favicon | img_url: '32x32' }}">
      <link rel="apple-touch-icon" sizes="76x76" href="{{ settings.favicon | img_url: '76x76' }}">
      <link rel="apple-touch-icon" sizes="120x120" href="{{ settings.favicon | img_url: '120x120' }}">
      <link rel="apple-touch-icon" sizes="152x152" href="{{ settings.favicon | img_url: '152x152' }}">
    {%- endif -%}

    {%- unless settings.type_header_font.system? and settings.type_body_font.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}

    <title>
      {{ page_title }}
      {%- if current_tags %} &ndash; tagged "{{ current_tags | join: ', ' }}"{% endif -%}
      {%- if current_page != 1 %} &ndash; Page {{ current_page }}{% endif -%}
      {%- unless page_title contains shop.name %} &ndash; {{ shop.name }}{% endunless -%}
    </title>

    {%- if page_description -%}
      <meta name="description" content="{{ page_description | escape }}">
    {%- endif -%}

    {%- render 'meta-tags' -%}

    {%- render 'theme-styles' -%}

    {{ 'main.css' | asset_url | stylesheet_tag }}
    
    <!-- do not defer to ensure availability for inline use -->
    <script src="{{ '@utilities.js' | asset_url }}"></script>
    <script src="{{ 'redirects.js' | asset_url }}"></script>

    <script src="{{ 'vendors.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'runtime.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'main.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'custom.js' | asset_url }}" defer="defer"></script>

    <script>
      window.store = {
        name:{{ settings.store_name | json }},
        brand:{{ settings.brand_name | json }},
        domain:{{ shop.permanent_domain | json }}
      }

      window.$=q=>document.querySelector(q)
      window.$$=q=>document.querySelectorAll(q)

      window.customer = {%- render 'customer-data' -%};

      window.cart = {{ cart | json }};
    </script>

    {%- render 'script-blocker' -%}

    <!-- CONTENT FOR HEADER -->
    {%- if settings.split_content_for_header != blank -%}
      {%- capture contentforheader -%}
        {{ content_for_header }}
      {%- endcapture -%}
      {{ contentforheader | split: settings.split_content_for_header | first }}
    {%- else -%}
      {{ content_for_header }}
    {%- endif -%}
    <!-- /CONTENT FOR HEADER -->

    <script>document.documentElement.className = document.documentElement.className.replace('no-js', 'js');</script>

    {%- render 'code' zone:'head' position:'end' -%}

    {%- section 'customer-segments' -%}
  </head>
  <body id="{{ page_title | handle }}" class="template-{{ template.name | handle }} template-{{ template | split: '.' | last }}">
    
    {%- render 'code' zone:'body' position:'start' -%}

    {%- render 'accessibility-skip' -%}
    {%- render 'accessibility-statement' -%}
    
    {%- section 'geolocation' -%}

    {%- if settings.brand_name contains 'roark' -%}
      {%- render 'roark-icons' -%}
    {%- elsif settings.brand_name contains 'melin' -%}
      {%- render 'melin-icons' -%}
    {%- elsif settings.brand_name contains 'kaenon' -%}
      {%- render 'kaenon-icons' -%}
    {%- elsif settings.brand_name contains 'olukai' -%}
      {%- render 'olukai-icons' -%}
    {%- endif -%}
      {%- render 'amble-icons' -%}
    {%- render 'icons' -%}

    {%- if template == 'index' -%}
      <h1 class="p-0 m-0 sr-only">{{ shop.name }}</h1>
    {%- endif -%}
    
    {%- if settings.header_group -%}
      {%- sections 'headers' -%}
    {%- else -%}
      {%- section 'header' -%}
      {%- section 'mega-menu' -%}
    {%- endif -%}

    <main id="MainContent" role="main" tabindex="0">
      {{ content_for_layout }}
    </main>

    <div id="stickyBottom"></div>
    
    {%- if settings.header_group -%}
      {%- sections 'footers' -%}
    {%- else -%}
      {%- section 'newsletter' -%}
      {%- section 'footer' -%}
    {%- endif -%}

    {%- section 'search-modal' -%}
    {%- section 'slider-cart' -%}
    {%- section 'loyalty' -%}
    {%- section 'offers' -%}

    {%- render 'quick-add' -%}
    {%- render 'modal-overlay' -%}
    {%- render 'payment-modal' -%}
    {%- render 'account-modals' -%}
    {%- render 'page-view-trigger' -%}

    {%- unless request.design_mode -%} 
      {%- render 'cookie-compliance' -%}
    {%- endunless -%} 

    {%- if settings.reamaze_lightbox -%}
      {%- render 'reamaze-lightbox' -%}
    {%- endif -%}

    {%- if settings.product_item_enhanced_data -%}
      {%- render 'product-item-enhancements' -%}
    {%- endif -%}

    {%- section 'product-badges' -%}
    
    {%- if settings.enable_custom_meta_title -%}
      {%- render 'custom-meta-title' -%}
    {%- endif -%}

    {%- render 'code' zone:'body' position:'end' -%}

    {%- render 'peripherals-map' -%}
    {%- render 'theme-scripts' -%}
  </body>
</html>
