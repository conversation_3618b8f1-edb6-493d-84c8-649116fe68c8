{% layout none %}
{% assign ns = 'giftcard' %}
<!DOCTYPE html>
<html 
  class="no-js" 
  lang="{{ request.locale.iso_code }}" 
  {% if request.design_mode %}design-mode{% endif %}
>
  <head>
    <script>window.store = {
      name:{{ settings.store_name | json }},
      brand:{{ settings.brand_name | json }},
      domain:{{ shop.permanent_domain | json }}
    }</script>

    {% render 'code' zone:'head' position:'start' %}

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>

    {%- if settings.favicon != blank -%}
      <link rel="icon" type="image/png" href="{{ settings.favicon | img_url: '32x32' }}">
    {%- endif -%}

    {%- unless settings.type_header_font.system? and settings.type_body_font.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}

    <title>
      {{ page_title }}
      {%- if current_tags %} &ndash; tagged "{{ current_tags | join: ', ' }}"{% endif -%}
      {%- if current_page != 1 %} &ndash; Page {{ current_page }}{% endif -%}
      {%- unless page_title contains shop.name %} &ndash; {{ shop.name }}{% endunless -%}
    </title>

    {% if page_description %}
      <meta name="description" content="{{ page_description | escape }}">
    {% endif %}

    {% render 'meta-tags' %}
  
    <!-- do not defer to ensure availability for inline use -->
    <script src="{{ '@utilities.js' | asset_url }}"></script>
    
    <script src="{{ 'redirects.js' | asset_url }}"></script>

    <script src="{{ 'vendors.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'runtime.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'main.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'custom.js' | asset_url }}" defer="defer"></script>

    <script>
      window.customer = {% render 'customer-data' %};
    </script> 

    <script defer>
      window.cart = {{ cart | json }};
    </script>
    {% render 'script-blocker' %}
    {% render 'peripherals-map' %}

    <!-- CONTENT FOR HEADER -->
    {% if settings.split_content_for_header != blank %}
      {% capture contentforheader %}
      {{ content_for_header }}
      {% endcapture %}
      {{ contentforheader | split: settings.split_content_for_header | first }}
    {% else %}
      {{ content_for_header }}
    {% endif %}
    <!-- /CONTENT FOR HEADER -->

    {% render 'theme-styles' %}

    {{ 'main.css' | asset_url | stylesheet_tag }}

    <script>document.documentElement.className = document.documentElement.className.replace('no-js', 'js');</script>

    {% render 'code' zone:'head' position:'end' %}

  </head>
  <body>
    <header class="gift-card__title">
      {% if settings.currency_code_enabled %}
        {%- assign gift_card_balance = gift_card.balance | money_with_currency -%}
      {% else %}
        {%- assign gift_card_balance = gift_card.balance | money -%}
      {% endif %}
      {%- if gift_card.balance != gift_card.initial_value -%}
        <p class="gift-card__label caption-large">
          {{ 'gift_cards.issued.remaining_html' | t: balance: gift_card_balance }}
        </p>
      {%- endif -%}
    </header>
    <main role="main" id="MainContent" class="main main--{{ page_title | handle }}" style="background: #f9f9f9;">
      <div class="{{ ns }} {% if gift_card.expired or gift_card.enabled != true %} {{ ns }}--disabled{% endif %}">
        {% section 'giftcard' %}
      </div>
    </main>
    <div hidden>
      <span id="a11y-new-window-message">{{ 'accessibility.link_messages.new_window' | t }}</span>
    </div>
  </body>
</html>
<script>
  var string = { qrImageAlt: {{ 'gift_cards.issued.qr_image_alt' | t | json }} };
  document.addEventListener('DOMContentLoaded', function() {
   new QRCode( document.querySelector('.gift-card__qr-code'), {
    text: document.querySelector('.gift-card__qr-code').dataset.identifier,
    width: 120,
    height: 120,
    imageAltText: string.qrImageAlt
    });
  });

  var template = document.getElementsByTagName("template")[0];
  var clonedTemplate = template.content.cloneNode(true);

  var isMessageDisplayed = false
  document
  .querySelector('.gift-card__copy-link')
  .addEventListener('click', () => {
    navigator.clipboard.writeText(document.querySelector('.gift-card__number').value).then(function () {
      if (!isMessageDisplayed) {
        document.querySelector('.gift-card__copy-success').appendChild(clonedTemplate);
        isMessageDisplayed = true
      }
    });
  });
</script>