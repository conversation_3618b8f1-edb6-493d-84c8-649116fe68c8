<section class="register relative {% if settings.create_an_account_image != blank %}bg-image{% endif %}">
  {% if settings.create_an_account_image != blank %}
    {% render 'image' with image: settings.create_an_account_image, class:"register__background absolute inset-0 w-full h-full object-cover object-center" %}
  {% endif %}

  <div class="register__content relative">
    <div class="register__form">
      <h1 class="register__title type-headline type--sm mt-0">{{ 'customer.register.title' | t }}</h1>
      <p class="register__subtitle type-subline mt-0">{{ settings.create_an_account_paragraph_1 }}</p>

      {% comment %} Register Form {% endcomment %}
      {% form 'create_customer' %}
        <input class="hidden" type="invisible" name="return_to" value="/account"/>
        {%- if form.errors -%}
          <ul>
            {%- for field in form.errors -%}
              <li>
                {%- if field == 'form' -%}
                  {{ form.errors.messages[field] }}
                {%- else -%}
                  <a href="#RegisterForm-{{ field }}">
                    {{ form.errors.translated_fields[field] | capitalize }}
                    {{ form.errors.messages[field] }}
                  </a>
                {%- endif -%}
              </li>
            {%- endfor -%}
          </ul>
        {%- endif -%}

        <div class="register__names">
          <div class="floating-label w-full lg:flex-1">
            <label for="RegisterForm-FirstName">{{ 'customer.register.first_name' | t }}</label>
            <input
              type="text"
              name="customer[first_name]"
              id="RegisterForm-FirstName"
              class="field__input w-full"
              {% if form.first_name %}
                value="{{ form.first_name }}"
              {% endif %}
              autocomplete="given-name"
              placeholder="{{ 'customer.register.first_name' | t }}"
            >
          </div>

          <div class="floating-label w-full lg:flex-1">
            <label for="RegisterForm-LastName">
              {{ 'customer.register.last_name' | t }}
            </label>
            <input
              type="text"
              name="customer[last_name]"
              id="RegisterForm-LastName"
              class="field__input w-full"
              {% if form.last_name %}
                value="{{ form.last_name }}"
              {% endif %}
              autocomplete="family-name"
              placeholder="{{ 'customer.register.last_name' | t }}"
            >
          </div>
        </div>

        <div class="floating-label">
          <label for="RegisterForm-email">{{ 'customer.register.email' | t }}</label>
          <input
            type="email"
            name="customer[email]"
            id="RegisterForm-email"
            class="field__input w-full"
            {% if form.email %}
              value="{{ form.email }}"
            {% endif %}
            spellcheck="false"
            autocapitalize="off"
            autocomplete="email"
            aria-required="true"
            {% if form.errors contains 'email' %}
              aria-invalid="true"
              aria-describedby="RegisterForm-email-error"
            {% endif %}
            placeholder="{{ 'customer.register.email' | t }}"
            required
          >
        </div>

        <div class="floating-label">
            <label for="RegisterForm-password">{{ 'customer.register.password' | t }}</label>
            <input
              type="password"
              name="customer[password]"
              id="RegisterForm-password"
              class="field__input w-full"
              aria-required="true"
              {% if form.errors contains 'password' %}
                aria-invalid="true"
                aria-describedby="RegisterForm-password-error"
              {% endif %}
              placeholder="{{ 'customer.register.password' | t }}"
              required
            >
        </div>

        <div class="field">
          <label class="field__checkbox" for="create_an_account_sign_up_agreement">
            <input type="checkbox" id="create_an_account_sign_up_agreement" value="create_an_account_sign_up_agreement">
            <span>
              {{ 'customer.register.checkbox' | t }}
            </span>
          </label>
        </div>

        <div class="floating-label mt-8">
          <label for="RegisterForm-phone" class="form--label">
            {{ 'customer.register.phone' | t }}
          </label>
          <input type="tel"
            name="customer[note][phone]"
            id="RegisterForm-phone"
            pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}"
            class="field__input"
            {% if form.errors contains 'phone' %}
              aria-invalid="true"
              aria-describedby="RegisterForm-phone-error"
            {% endif %}
            placeholder="{{ 'customer.register.phone' | t }}"
            required
          >
        </div>

        <div class="rewards my-4">
          {%
            render 'content-item' 
            item_classes:'rewards__header',
            text_item_1_element:'p',
            text_item_1_text: settings.rewards_callout_message,
            text_item_1_classes:'rewards__message',
            image: settings.rewards_callout_image,
            image_mobile: settings.rewards_callout_image_mobile,
            media_classes:'rewards__logo',
            settings:settings
          %}
        
          <div class="field">
            <label class="field__checkbox" for="rewards_register">
              <input type="checkbox" id="rewards_register" value="rewards_register">
              <span class="flex flex-col">
                <span class="font-bold">I want to join.</span>
                By joining, you will be subscribed to marketing and acknowledge that you have read OluKai's privacy policy and agree to the Holoholo Club Terms & Conditions.
              </span>
            </label>
          </div>
        </div>
        
        <input type="submit" value="{{ 'customer.register.submit' | t }}" class="button button--primary w-full">
      {% endform %}
    </div>

    {% comment %} Callout {% endcomment %}
    {% if settings.create_title != blank or settings.create_subtitle != blank or settings.create_link_title != blank %}
      <aside class="callout" {% if settings.create_text_color != blank %} style="color: {{ settings.create_text_color }};"{% endif %}>
        <h4 class="callout__title type-headline type--sm mt-0" {% if settings.create_text_color != blank %} style="color: {{ settings.create_text_color }}"{% endif %}>
          {{ settings.create_title }}
        </h4>

        <p class="callout__subtitle type-subline mt-0" {% if settings.create_text_color != blank %}style="color: {{ settings.create_text_color }}"{% endif %}>{{ settings.create_subtitle }}</p>

        <a class="callout__button button button--primary" href="{{ settings.create_url }}">
          {{- settings.create_link_title -}}
        </a>
      </div>
    {% endif %}
  </div>
</section>
