<section class="flex flex-col text-center lg:h-75v items-center justify-center">
  <div class="account__header">
    <h1 class="type--secondary m-0">{{ 'customer.activate_account.title' | t }}</h1>
    <p class="lg:ml-auto">{{ 'customer.activate_account.subtext' | t }}</p>
  </div>

  <div class="max-w-md w-full p-4 lg:p-0">
    {% form 'activate_customer_password' %}
      {{ form.errors | default_errors }}

      <div class="field mt-4 relative">
        <label for="CustomerPassword" class="floating-label">
          {{ 'customer.activate_account.password' | t }}
        </label>
        <input type="password"
          name="customer[password]"
          id="CustomerPassword"
          class="w-full"
          floating-label
          placeholder="{{ 'customer.activate_account.password' | t }}">
      </div>

      <div class="field mt-4 relative">
        <label for="CustomerPasswordConfirmation" class="floating-label">
          {{ 'customer.activate_account.password_confirm' | t }}
        </label>
        <input type="password"
          name="customer[password_confirmation]"
          id="CustomerPasswordConfirmation"
          class="w-full"
          floating-label
          placeholder="{{ 'customer.activate_account.password_confirm' | t }}">
      </div>

      <div class="flex flex-row mt-4">
        <input type="submit" class="btn btn--primary mr-4" value="{{ 'customer.activate_account.submit' | t }}">
        <input type="submit" class="btn btn--secondary" name="decline" value="{{ 'customer.activate_account.cancel' | t }}">
      </div>
      
    {% endform %}
  </div>
</section>