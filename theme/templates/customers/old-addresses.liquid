<script src="{{ 'customer.js' | asset_url }}" defer></script>

<section class="account" x-data="{ selected: 'deliveryAddresses'  }">
  <div class="account__container  container--narrow">
    {% render 'account-menu' classes: 'flex lg:hidden' %}
    <div class="account__header">
      <h1 class="account__title type-headline m-0">{{ customer.name }}</h1>
    </div>
    <div class="flex flex-wrap flex-row">
      {% render 'account-menu' classes: 'hidden lg:block' %}
      <div class="lg:w-4/5 w-full lg:p-8 p-4">
        <div id="addressInformation">
          {% paginate customer.addresses by 5 %}
              
              {% unless customer.addresses.size == 0 %}
                <h2 class="type--subline mt-0 mb-8">{{ 'customer.addresses.title' | t }}</h2>
              {% endunless %}

              <div class="flex flex-wrap">

                {% assign count = 0 %}

                {% for address in customer.addresses %}

                  {% assign count = count | plus: 1 %}

                  <div data-address class="address w-full p-4">

                    <div class="flex flex-row {% if count > 1 %}py-4 border-t border-gray-200{% endif %}"> 

                      <div class="flex-grow text-gray">
                        {% if address == customer.default_address %}
                          <p class="mb-4 text-black"><strong>{{ 'customer.addresses.default' | t }}</strong></p>
                        {% else %}
                          <p class="mb-4 text-black"><strong>Address {{ count }}</strong></p>
                        {% endif %}
                        <div>
                          {{ address | format_address }}
                        </div>
                      </div>
                      <div class="lg:mt-8 mt-4">
                        <button class="btn btn--link" type="button" data-address-toggle>
                          {{ 'customer.addresses.edit' | t }}
                        </button>
                        <form class="inline-block ml-4" data-address-delete-form method="post" action="/account/addresses/{{ address.id }}" data-confirm-message="{{ 'customer.addresses.delete_confirm' | t }}">
                          <input type="invisible" class="sr-only" name="_method" value="delete"/>
                          <button class="btn btn--link" type="submit">
                            {{ 'customer.addresses.delete' | t }}
                          </button>
                        </form>
                      </div>

                      <div data-address-form-{{count}} class="">
                        <button 
                          class="modal-close p-3 icon bg-transparent black border-none absolute top-4 right-4"
                          neptune-engage='[
                            {
                              "targets": [
                                {
                                  selector:html
                                  attributes:[{
                                    att:data-active-modal 
                                    set:_remove
                                  }]
                                },
                                {
                                  "selector": "[data-address-form-{{count}}]",
                                  "classes": {
                                    "toggle": "active"
                                  }
                                }
                              ]
                            }
                          ]'>
                          {% if settings.icon_display_icon %}
                            {% render 'icon' icon:'close' %}
                          {% else %}
                            <h2 class="m-0">CLOSE</h2>
                          {% endif %}
                        </button>
                        <div class="flex flex-col">
                          <div class="p-8">
                            {% form 'customer_address', address %}

                            <h4 class="type--subline mt-0 mb-8">{{ 'customer.addresses.edit_address' | t }}</h4>
                            <div data-address-fields class="address-form modal--tight-inner ">
                              <div class="field mt-4 relative">
                                <label class="form--label" for="AddressFirstName_{{ form.id }}">
                                  {{ 'customer.addresses.first_name' | t }}
                                </label>
                                <input type="text"
                                  name="address[first_name]"
                                  id="AddressFirstName_{{ form.id }}"
                                  class="w-full form--input"
                                  value="{{ form.first_name }}"
                                  floating-label
                                  autocapitalize="words">

                              </div>
                              <div class="field mt-4 relative">
                                <label class="form--label" for="AddressLastName_{{ form.id }}">
                                  {{ 'customer.addresses.last_name' | t }}
                                </label>
                                <input type="text"
                                  name="address[last_name]"
                                  id="AddressLastName_{{ form.id }}"
                                  class="w-full form--input"
                                  value="{{ form.last_name }}"
                                  floating-label
                                  autocapitalize="words">

                              </div>
                              <div class="field mt-4 relative">
                                <label class="form--label" for="AddressCompany_{{ form.id }}">
                                  {{ 'customer.addresses.company' | t }}
                                </label>
                                <input type="text"
                                  name="address[company]"
                                  id="AddressCompany_{{ form.id }}"
                                  class="w-full form--input"
                                  value="{{ form.company }}"
                                  floating-label
                                  autocapitalize="words">

                              </div>
                              <div class="field mt-4 relative">
                                <label class="form--label" for="AddressAddress1_{{ form.id }}">
                                  {{ 'customer.addresses.address1' | t }}
                                </label>
                                <input type="text"
                                  name="address[address1]"
                                  class="w-full form--input"
                                  id="AddressAddress1_{{ form.id }}"
                                  value="{{ form.address1 }}"
                                  floating-label
                                  autocapitalize="words">

                              </div>
                              <div class="field mt-4 relative">
                                <label class="form--label" for="AddressAddress2_{{ form.id }}">
                                  {{ 'customer.addresses.address2' | t }}
                                </label>
                                <input type="text"
                                  name="address[address2]"
                                  id="AddressAddress2_{{ form.id }}"
                                  class="w-full form--input"
                                  value="{{ form.address2 }}"
                                  floating-label
                                  autocapitalize="words">

                              </div>
                              <div class="field mt-4 relative">
                                <label class="form--label" for="AddressCity_{{ form.id }}">
                                  {{ 'customer.addresses.city' | t }}
                                </label>
                                <input type="text"
                                  name="address[city]"
                                  id="AddressCity_{{ form.id }}"
                                  class="w-full form--input"
                                  value="{{ form.city }}"
                                  floating-label
                                  autocapitalize="words">

                              </div>
                              <div class="field mt-4 relative" >
                                
                                <label class="form--label" for="AddressCountry_{{ form.id }}">
                                  {{ 'customer.addresses.country' | t }}
                                </label>
                                <select
                                  name="address[country]"
                                  id="AddressCountry_{{ form.id }}"
                                  class="address-country-option w-full block"
                                  data-form-id="{{ form.id }}"
                                  floating-label
                                  data-default="{{ form.country }}"
                                  onchange="setCountry(this.value)"
                                  aria-live="polite"neptune-liquid="topic:Country">
                                  {% raw %}
                                  {% assign selected = {% endraw %}"{{form.country}}"{% raw %}%}
                                  {% for country in Shopify.countries %}
                                  {% if country.country == selected %}
                                    <option value="{{country.country}}" selected>{{country.country}}</option>
                                  {% else %}
                                    <option value="{{country.country}}">{{country.country}}</option>
                                  {% endif %}
                                  {% endfor %}
                                  {% endraw %}
                                </select>

                                <script defer>window.addEventListener('DOMContentLoaded', function(){
                                  setCountry('{{form.country}}')
                                })</script>

                              </div>

                              <div class="field mt-4 relative">
                                <label class="form--label" for="AddressProvince_{{ form.id }}">
                                  {{ 'customer.addresses.province' | t }}
                                </label>
                                <select
                                  name="address[province]"
                                  id="AddressProvince_{{ form.id }}"
                                  class="w-full form--input block"
                                  floating-label
                                  data-default="{{ form.province }}"
                                  aria-live="polite"neptune-liquid="topic:Province">
                                  {% raw %}
                                  {% assign selected = {% endraw %}"{{form.province}}"{% raw %}%}
                                  {% for province in Shopify.provinces %}
                                  {% if province == selected %}
                                    <option value="{{province}}" selected>{{province}}</option>
                                    {% else %}
                                    <option value="{{province}}">{{province}}</option>
                                  {% endif %}
                                  {% endfor %}
                                  {% endraw %}
                                </select>
                              </div>

                              <div class="field mt-4 relative">
                                <label class="form--label" for="AddressZip_{{ form.id }}">
                                  {{ 'customer.addresses.zip' | t }}
                                </label>
                                <input type="text"
                                  name="address[zip]"
                                  id="AddressZip_{{ form.id }}"
                                  class="w-full form--input"
                                  value="{{ form.zip }}"
                                  floating-label
                                  autocapitalize="characters">
                              </div>
                              <div class="field mt-4 relative">
                                <label class="form--label" for="AddressPhone_{{ form.id }}">
                                  {{ 'customer.addresses.phone' | t }}
                                </label>
                                <input type="tel"
                                  name="address[phone]"
                                  id="AddressPhone_{{ form.id }}"
                                  class="w-full form--input"
                                  value="{{ form.phone }}"
                                  floating-label
                                  pattern="[0-9\-]*">
                              </div>
                            </div>

                            <div class="field mt-4 relative">
                              
                              <label class="" for="address_default_address_{{ form.id }}">
                                {{ form.set_as_default_checkbox }}
                                <span>
                                  {% render 'icon' icon: 'plus' width:16 height:16 strokeWidth:1 %}
                                </span> 
                                <span class="capitalize">
                                  {{ 'customer.addresses.set_default' | t }}
                                </span>
                              </label>
                            </div>

                            <div class="field mt-4 relative flex justify-start">
                              <button class="btn btn--primary" type="submit">{{ 'customer.addresses.update' | t }}</button>
                              <button class="btn btn--secondary ml-2" type="button" data-address-toggle data-form-id="{{ form.id }}" neptune-engage='[
                                {
                                  "targets": [
                                    {
                                      selector:html
                                      attributes:[{
                                        att:data-active-modal 
                                        set:_remove
                                      }]
                                    },
                                    {
                                      "selector": "[data-address-form-{{count}}]",
                                      "classes": {
                                        "toggle": "active"
                                      }
                                    }
                                  ]
                                }
                              ]'>
                                {{ 'customer.addresses.cancel' | t }}
                              </button>
                            </div>
                            

                            {% endform %}
                          </div>
                        </div>
                      </div>

                    </div>
                  </div>
                {% endfor %}
              </div>

              <div class="inline-block" data-address>
                <button class="btn bg-primary btn--rounded" type="button" neptune-engage='[
                  {
                    "targets": [
                      {
                        selector:html
                        attributes:[{
                          att:data-active-modal 
                          set:newAddress
                        }]
                      },
                      {
                        "selector":"[data-add-address-form]",
                        "classes":{
                          "toggle":"active"
                        }
                      }
                    ]
                  }
                ]'>
                  {{ 'customer.addresses.add_new' | t }}
                </button>
                <div data-add-address-form class=">
                  <button 
                    class="modal-close p-2 icon bg-transparent text-black border-none absolute top-4 right-4"
                    neptune-engage='[
                      {
                        "targets": [
                          {
                            "selector": "[data-add-address-form]",
                            "classes": {
                              "toggle": "active"
                            }
                          },
                          {
                            selector:html
                            attributes:[{
                              att:data-active-modal 
                              set:_remove
                            }]
                          }
                        ]
                      }
                    ]'>
                    {% render 'icon' icon:'close' %}
                  </button>
                  <div class="flex flex-col">
                    <div class="p-8 bg-white">
                    {% form 'customer_address', customer.new_address %}
                      <h2 class="type--subline mt-0 mb-8">{{ 'customer.addresses.add_new' | t }}</h2>

                      <div data-address-fields class="address-form">
                        <div class="field mt-4 relative">
                          <label class="form--label" for="AddressFirstNameNew">
                            {{ 'customer.addresses.first_name' | t }}
                          </label>
                          <input type="text"
                            name="address[first_name]"
                            id="AddressFirstNameNew"
                            floating-label
                            class="w-full form--input"
                            value="{{ form.first_name }}"
                            floating-label
                            autocapitalize="words">
                        </div>
                        <div class="field mt-4 relative">
                          <label class="form--label" for="AddressLastNameNew">
                            {{ 'customer.addresses.last_name' | t }}
                          </label>
                          <input type="text"
                            name="address[last_name]"
                            id="AddressLastNameNew"
                            class="w-full form--input"
                            value="{{ form.last_name }}"
                            floating-label
                            autocapitalize="words">
                        </div>
                        <div class="field mt-4 relative">
                          <label class="form--label" for="AddressCompanyNew">
                            {{ 'customer.addresses.company' | t }}
                          </label>
                          <input type="text"
                            name="address[company]"
                            id="AddressCompanyNew"
                            class="w-full form--input"
                            value="{{ form.company }}"
                            floating-label
                            autocapitalize="words">
                        </div>
                        <div class="field mt-4 relative">
                          <label class="form--label" for="AddressAddress1New">
                            {{ 'customer.addresses.address1' | t }}
                          </label>
                          <input type="text"
                            name="address[address1]"
                            id="AddressAddress1New"
                            class="w-full form--input"
                            value="{{ form.address1 }}"
                            floating-label
                            autocapitalize="words">
                        </div>
                        <div class="field mt-4 relative">
                          <label class="form--label" for="AddressAddress2New">
                            {{ 'customer.addresses.address2' | t }}
                          </label>
                          <input type="text"
                            name="address[address2]"
                            id="AddressAddress2New"
                            class="w-full form--input"
                            value="{{ form.address2 }}"
                            floating-label
                            autocapitalize="words">
                        </div>
                        <div class="field mt-4 relative">
                          <label class="form--label" for="AddressCityNew">
                            {{ 'customer.addresses.city' | t }}
                          </label>
                          <input type="text"
                            name="address[city]"
                            id="AddressCityNew"
                            class="w-full form--input"
                            value="{{ form.city }}"
                            floating-label
                            autocapitalize="words">
                        </div>
                        <div class="field mt-4 relative">
                          <label class="form--label" for="AddressCountryNew">
                            {{ 'customer.addresses.country' | t }}
                          </label>
                          <select
                            name="address[country]"
                            id="AddressCountryNew"
                            class="w-full block"
                            floating-label
                            data-default="{{ form.country }}"
                            onchange="setCountry(this.value)"
                            aria-live="polite"neptune-liquid="topic:Country">
                            {% raw %}
                            {% assign selected = {% endraw %}"{{form.country}}"{% raw %}%}
                            {% for country in Shopify.countries %}
                            {% if country.country == selected %}
                              <option value="{{country.country}}" selected>{{country.country}}</option>
                            {% else %}
                              <option value="{{country.country}}">{{country.country}}</option>
                            {% endif %}
                            {% endfor %}
                            {% endraw %}
                            >
                          </select>
                          <script defer>window.addEventListener('DOMContentLoaded', function(){
                            setCountry('{{form.country}}')
                          })</script>
                        </div>
                        <div class="field mt-4 relative">
                          <label class="form--label" for="AddressProvinceNew">
                            {{ 'customer.addresses.province' | t }}
                          </label>
                          <select
                            name="address[province]"
                            id="AddressProvinceNew"
                            class="w-full block"
                            floating-label
                            data-default="{{ form.province }}"
                            aria-live="polite"neptune-liquid="topic:Province">
                            {% raw %}
                            {% assign selected = {% endraw %}"{{form.province}}"{% raw %}%}
                            {% for province in Shopify.provinces %}
                            {% if province == selected %}
                              <option value="{{province}}" selected>{{province}}</option>
                              {% else %}
                              <option value="{{province}}">{{province}}</option>
                            {% endif %}
                            {% endfor %}
                            {% endraw %}>
                          </select>
                        </div>
                        <div class="field mt-4 relative">
                          <label class="form--label" for="AddressZipNew">
                            {{ 'customer.addresses.zip' | t }}
                          </label>
                          <input type="text"
                            name="address[zip]"
                            id="AddressZipNew"
                            class="w-full form--input"
                            value="{{ form.zip }}"
                            floating-label
                            autocapitalize="characters">
                        </div>
                        <div class="field mt-4 relative">
                          <label class="form--label" for="AddressPhoneNew">
                            {{ 'customer.addresses.phone' | t }}
                          </label>
                          <input type="tel"
                            name="address[phone]"
                            id="AddressPhoneNew"
                            class="w-full form--input"
                            value="{{ form.phone }}"
                            floating-label
                            pattern="[0-9\-]*">
                        </div>
                      </div>
                      <div class="field mt-4">
                        <label for="address_default_address_new">
                          {{ form.set_as_default_checkbox }}
                          <span>
                            {% render 'icon' icon: 'plus' width:16 height:16 strokeWidth:1 %}
                          </span>
                          <span class="capitalize">
                            {{ 'customer.addresses.set_default' | t }}
                          </span>
                        </label>
                      </div>
                      <div class="field mt-4 relative flex justify-start">
                        <button class="btn btn--primary" type="submit">{{ 'customer.addresses.add' | t }}</button>
                        <button class="btn btn--secondary ml-2" type="button" neptune-engage='[
                          {
                            "targets": [
                              {
                                "selector": "[data-add-address-form]",
                                "classes": {
                                  "toggle": "active"
                                }
                              },
                              {
                                selector:html
                                attributes:[{
                                  att:data-active-modal 
                                  set:_remove
                                }]
                              }
                            ]
                          }
                        ]'>
                          {{ 'customer.addresses.cancel' | t }}
                        </button>
                      </div>
                    {% endform %}
                    </div>
                  </div>
                </div>
              </div>  

              {% if paginate.pages > 1 %}
                {% include 'pagination' %}
              {% endif %}

            {% endpaginate %}
          </div>
        </div>
    </div>
  </div>
</section>