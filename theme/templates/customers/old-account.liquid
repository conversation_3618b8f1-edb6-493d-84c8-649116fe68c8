<section class="account" x-data="{ active: window.location.hash ? window.location.hash.replace('#', '') : 'accountInformation', selected: window.location.hash ? window.location.hash.replace('#', '') : 'accountInformation'  }">
  <div class="account__container  container--narrow">
    {% render 'account-menu' classes: 'flex lg:hidden' %}
    <div class="account__header">
      <h1 class="account__title type-headline m-0">{{ customer.name }}</h1>
    </div>
    <div class="account__content flex flex-wrap flex-row">
      {% render 'account-menu' classes: 'hidden lg:block' %}
      <div id="accountInformation" class="account-information block w-full lg:w-4/5 lg:order-1 order-2" x-show="active == 'accountInformation'">
        <div class="bg-white border border-[#f5e9d8] rounded-[3px] p-4 flex flex-row">
          <div class="flex-grow">
            <p class="type--subline mt-0 mb-8">{{ customer.first_name }} {{ customer.last_name }}</p>
            {{ customer.email }}
            {{ customer.default_address | format_address }}
          </div>
          <div class="ml-auto text-right flex items-center">
            <a href="/account/addresses" class="button button--link">Edit</a>
          </div>


        
            {% comment %}
              
              TESTING CUSTOMER PROFILE GETTING AND SETTING INFO
              
              <div x-data="{
              customer: Alpine.reactive(window.customer),
              newProfile: { first_name: '', last_name: '' },
              addNewProfile: async function(event) {
                  event.preventDefault();
                  const newProfile = Util.form.values(event.target);
                  const addedProfile = await Customer.profile.add(newProfile);
                  if (addedProfile) {
                      this.customer.profiles.push(addedProfile);
                  }
              }
          }">
              <!-- Existing profiles -->
              <div x-show="customer.profiles.length > 0">
                  <h2>Existing profiles</h2>
                  <ul>
                      <template x-for="(profile, index) in customer.profiles" :key="index">
                          <li>
                              <h3 x-text="profile.first_name"></h3>
                              <p x-text="profile.last_name"></p>
                          </li>
                      </template>
                  </ul>
              </div>
          
              <!-- Form to add a new profile -->
              <div>
                  <h2>Add new profile</h2>
                  <form @submit="addNewProfile($event)">
                      {% render 'field' name: 'first_name' type: 'text' placeholder: 'First' x-model="newProfile.first_name" %}
                      {% render 'field' name: 'last_name' type: 'text' placeholder: 'Last' x-model="newProfile.last_name" %}
                      <button type="submit">Add Profile</button>
                  </form>
              </div>        
          </div> {% endcomment %}
          
        </div>
      </div>

      <div id="orderHistory" class="order-history w-full lg:w-4/5 lg:order-1 order-2 " x-show="active == 'orderHistory'">
        <div>
          <h2 class="type--subline mt-0 mb-8">{{ 'customer.orders.title' | t }}</h2>
          {% paginate customer.orders by 20 %}
            {% if customer.orders.size != 0 %}
              <div class="w-full my-3 overflow-x-auto">
                {% for order in customer.orders %}
                  <div class="flex lg:flex-row flex-col border-t border-gray-200 py-8">
                    <div class="flex-grow">
                      <p class="m-0 mb-4 font-bold text-xs tracking-wider tracking-widest uppercase"><span>{{ order.fulfillment_status_label }}</span></p>
                      <a class="block link text-black font-semibold" href="{{ order.customer_url }}">{{ order.name }}</a>
                      <p class="mb-0"><span>{{ 'customer.orders.date' | t }}: {{ order.created_at | date: "%B %d, %Y" }}</span></p>
                      <p class="mb-0"><span>{{ 'customer.orders.payment_status' | t }}: {{ order.financial_status_label }}</span></p>
                      {% if order.total_price > 0 %}
                      <p class="mb-0"><span>{{ 'customer.orders.total' | t }}: {{ order.total_price | money }}</span></p>
                      {% endif %}
                    </div>
                    <div class="lg:ml-auto mt-4 lg:mt-0 flex flex-col justify-center">
                      <a href="{{ order.customer_url }}" class="button button--primary lg:block mb-4">View Order</a>
                      {% if order.fulfillment_status_label contains 'Unfulfilled' %}
                        <a class="button button--link" target="_blank" href="https://shopify-order-edit.herokuapp.com/order-editor/kswiss-us.myshopify.com/{{order.id}}">Edit or Cancel</a>
                      {% endif %}
                      <a class="button button--link" target="_blank" href="https://shopify-order-edit.herokuapp.com/order-editor/reorder/kswiss-us.myshopify.com/{{order.id}}">Reorder</a>
                    </div>
                  </div>
                {% endfor %}
              </div>
            {% else %}
              <p class="my-4">{{ 'customer.orders.none' | t }}</p>
            {% endif %}
            {% if paginate.pages > 1 %}
              {% include 'pagination' %}
            {% endif %}
          {% endpaginate %}
        </div>
      </div>

      <div id="wishlist" x-data class="w-full lg:w-4/5 lg:order-1 order-2 " x-show="active == 'wishlist'">
          <div class="flex">
            {% render 'icon' icon:'heart' width:settings.icon_size %}
            
            <template x-if="wishlist">
              <span class="nav-tools__count" x-show="wishlist.lists[0].items.length > 0" x-text="wishlist.lists[0].items.length + ' ' + 'items'"></span>
            </template>
          </div>

          {% render 'wishlist' %}

      </div>
    </div>
  </div>
</section>