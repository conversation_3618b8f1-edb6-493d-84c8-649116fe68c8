{% comment %}
  The data-label attributes on <td> elements are mobile-friendly
  helpers used for responsive-table labels
{% endcomment %}

<section class="container account__container">
  <div class="account__header">
    <h1 class="type--secondary m-0">{{ 'customer.account.title' | t }}</h1>
    <p class="lg:ml-auto">Welcome, {{ customer.first_name }}</p>
  </div>
  <div class="flex flex-wrap flex-row">
    {% include 'account-menu' %}
    <div class="block w-full lg:w-4/5 lg:order-1 order-2">
      <div class="lg:flex flex-row">
        <div class="lg:p-8 p-4 flex-grow">

          <h2 class="type--subline mt-0 mb-8">{{ 'customer.order.title' | t: name: order.name }}</h2>
          <p class="m-0"><span>{{ 'customer.order.fulfillment_status' | t }}:</span> {{ order.fulfillment_status_label }}</p>
          <p class="m-0"><span>{{ 'customer.order.payment_status' | t }}:</span> {{ order.financial_status_label }}</p>
          <p class="mb-4"><span>{{ 'customer.order.date' | t: }}</span> {{ order.created_at | date: "%B %d, %Y" }}</p>

          {% if order.cancelled %}
            {%- assign cancelled_at = order.cancelled_at | date: "%B %d, %Y" -%}
            <p>{{ 'customer.order.cancelled' | t: date: cancelled_at }}</p>
            <p>{{ 'customer.order.cancelled_reason' | t: reason: order.cancel_reason }}</p>
          {% endif %}

          <div class="w-full overflow-x-auto lg:pr-8 lg:py-8 py-4">

            {% for line_item in order.line_items %}

              {% unless line_item.properties._bundle == 'component' %}

              <div class="flex flex-row py-8 border-t border-light">
                <div class="lg:w-1/4 w-full">
                  <div class="product-item__image-wrapper">
                    <div class="product-image__wrapper aspect-w-1 aspect-h-1 w-full">
                      {% render 'image' with image: line_item.image, image_class: "product-item__image" %}
                    </div>
                  </div>
                </div>
                <div class="flex-grow pl-8 lg:w-3/4 w-full">

                  {% assign lineTitle = line_item.title | remove: product.type | split: ' | ' %}
                  <h2 class="product-item__title mt-0 mb-4">{{ lineTitle[1] }}</h2>
                  {% for option in line_item.options_with_values %}
                    {% unless option.name == "Title" %}
                      <p class="leading-normal text-sm mb-0"><span>{{ option.name }}:</span> {{ option.value }}</p>
                    {% endunless %}
                  {% endfor %}

                  {% assign price = item.final_price %}

                  {% if line_item.properties._bundle == 'primary' %}
                    {% assign reverse_items = order.line_items | reverse %}
                    {% assign showIncludes = false %} 
                    {% for subitem in reverse_items %}{% if subitem.properties._bundle == 'component' and subitem.properties._instance == line_item.properties._instance %} {% assign showIncludes = true %} {% endif %} {% endfor %} 
                    <h4 class="font-normal mt-2 {% if showIncludes == true %}db{% else %}dn{% endif %}">Includes</h4>
                    {% for subitem in reverse_items %}
                      {% if subitem.properties._bundle == 'component' and subitem.properties._instance == line_item.properties._instance %}
                        
                        <p class="mb-0"><span>{{subitem.title}} - {{subitem.final_price | money }}</span></p>
                        {% assign price = price | plus: subitem.final_price %}

                      {% endif %}
                    {% endfor %}

                  {% endif %}

                  <p class="mb-0 text-sm"><span>{{ 'customer.order.sku' | t }}:</span {{ line_item.sku }}></p>
                  <p class="mb-0 text-sm"><span>{{ 'customer.order.quantity' | t }}:</span> {{ line_item.quantity }}</p>
                  <p class="mb-0 text-sm"><span>{{ 'customer.order.total' | t }}:</span> {{ line_item.quantity | times: line_item.price | money }}</p>
                  
                  {% if line_item.fulfillment %}
                    <div class="">
                      <div>{{ 'customer.order.fulfilled_at' | t }}: </div>
                      <div>{{ line_item.fulfillment.created_at | date: "%B %d, %Y" }}</div>
                    </div>

                      {% if line_item.fulfillment.tracking_url %}
                        <div class="flex">
                          <div>{{ 'customer.order.tracking_url' | t }}: </div>
                          <div>
                            <a href="{{ line_item.fulfillment.tracking_url }}">
                              {{ 'customer.order.track_shipment' | t }}
                            </a>
                          </div>
                        </div>
                      {% endif %}

                      <div class="flex">
                        <div>{{ 'customer.order.tracking_company' | t }}: </div>
                        <div>{{ line_item.fulfillment.tracking_company }}</div>
                      </div>

                      {% if line_item.fulfillment.tracking_number %}
                        <div class="flex">
                          <div>{{ 'customer.order.tracking_number' | t }}: </div>
                          <div>{{ line_item.fulfillment.tracking_number }}</div>
                        </div>
                      {% endif %}
                      
                  {% endif %}

                </div>
              </div>

              {% endunless %}

            {% endfor %}
          </div>

        </div>
        <div class="w-full lg:w-1/3 lg:border-l lg:border-t-0 border-light border-t border-l-0">

          {% if order.line_items_subtotal_price > 0 %}

          <div class="lg:p-8 p-4">
            <h3 class="type--subline mt-0">{{ 'customer.order.summary' | t }}</h3>

            <div>
              <div class="flex">
                <div class="pr-4 text-left">{{ 'customer.order.subtotal' | t }}: </div>
                <div class="pr-4 text-right ml-auto" data-label="{{ 'customer.order.subtotal' | t }}">{{ order.line_items_subtotal_price | money }}</div>
              </div>

              {% for discount in order.discounts %}
                <div class="flex">
                  <div class="py-1 pr-4 text-left">{{ discount.code }} {{ 'customer.order.discount' | t }}</div>
                  <div class="py-1 pr-4 text-right ml-auto" data-label="{{ 'customer.order.discount' | t }}">{{ discount.savings | money }}</div>
                </div>
              {% endfor %}

              {% for shipping_method in order.shipping_methods %}
                <div class="flex">
                  <div class="py-1 pr-3 text-left">{{ 'customer.order.shipping' | t }} ({{ shipping_method.title }})</div>
                  <div class="py-1 pr-3 text-right ml-auto" data-label="{{ 'customer.order.shipping' | t }} ({{ shipping_method.title }})">{{ shipping_method.price | money }}</div>
                </div>
              {% endfor %}

              {% for tax_line in order.tax_lines %}
                <div class="flex">
                  <div class="py-1 pr-3 text-left">{{ 'customer.order.tax' | t }} ({{ tax_line.title }} {{ tax_line.rate | times: 100 }}%)</div>
                  <div class="py-1 pr-3 text-right ml-auto" data-label="{{ 'customer.order.tax' | t }} ({{ tax_line.title }} {{ tax_line.rate | times: 100 }}%)">{{ tax_line.price | money }}</div>
                </div>
              {% endfor %}

              <div class="flex">
                <div class="py-1 pr-3 text-left">{{ 'customer.order.total' | t }}</div>
                <div class="py-1 pr-3 text-right ml-auto" data-label="{{ 'customer.order.total' | t }}">{{ order.total_price | money }} {{ order.currency }}</div>
              </div>
            </div>
          </div>

          {% endif %}

          <div class="border-t border-light lg:p-8 p-4">
            <h3 class="type--subline mt-0">{{ 'customer.order.billing_address' | t }}</h3>

            {{ order.billing_address | format_address }}
          </div>
          <div class="border-t border-light lg:p-8 p-4">
            <h3 class="type--subline mt-0">{{ 'customer.order.shipping_address' | t }}</h3>

            {{ order.shipping_address | format_address }}
          </div>

        </div>
      </div>
    </div>
  </div>
</section>
