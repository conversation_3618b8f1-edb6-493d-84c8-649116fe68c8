<section class="reset relative {% if settings.reset_image != blank %}bg-image{% endif %}">
  {% if settings.reset_image != blank %}
    {% render 'image' with image: settings.reset_image, class:"reset__background absolute inset-0 w-full h-full object-cover object-center" %}
  {% endif %}

  <div class="reset__content relative">
    <div class="reset__form">
      <h1 class="reset__title type-headline type--sm m-0">{{ 'customer.reset_password.title' | t }}</h1>
      
      {% comment %} Reset Form {% endcomment %}
      {% form 'reset_customer_password' %}
        <p class="reset__subtitle">{{ 'customer.reset_password.subtext' | t }} {{ email }}</p>
        <div class="form-errors">{{ form.errors | default_errors }}</div>

        <div class="floating-label">
          <label for="ResetPassword" class="form--label">{{ 'customer.reset_password.password' | t }}</label>
          <input type="password"
            name="customer[password]"
            id="ResetPassword"
            class="field__input w-full {% if form.errors contains 'password' %}input-error{% endif %}"
            placeholder="{{ 'customer.reset_password.password' | t }}">
        </div>

        <div class="floating-label">
          <label for="PasswordConfirmation" class="clip">
            {{ 'customer.reset_password.password_confirm' | t }}
          </label>
          <input type="password"
            name="customer[password_confirmation]"
            id="PasswordConfirmation"
            class="field__input w-full {% if form.errors contains 'password_confirmation' %}input-error{% endif %}"
            placeholder="{{ 'customer.reset_password.password_confirm' | t }}">
        </div>

        <div class="mt-4 relative">
          <input type="submit" class="btn btn--primary w-full" value="{{ 'customer.reset_password.submit' | t }}">
        </div>
      {% endform %}
    </div>

    {% comment %} Callout {% endcomment %}
    {% if settings.reset_title != blank or settings.reset_subtitle != blank or settings.reset_link_title != blank %}
      <aside class="callout" {% if settings.reset_text_color != blank %} style="color: {{ settings.reset_text_color }};"{% endif %}>
        <h4 class="callout__title type-headline type--sm mt-0" {% if settings.reset_text_color != blank %} style="color: {{ settings.reset_text_color }}"{% endif %}>
          {{ settings.reset_title }}
        </h4>

        <p class="callout__subtitle type-subline mt-0" {% if settings.reset_text_color != blank %}style="color: {{ settings.reset_text_color }}"{% endif %}>{{ settings.reset_subtitle }}</p>

        <a class="callout__button button button--primary" href="{{ settings.reset_url }}">
          {{- settings.reset_link_title -}}
        </a>
      </div>
    {% endif %}
  </div>
</section>
