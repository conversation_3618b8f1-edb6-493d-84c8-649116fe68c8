<section class="login relative {% if settings.sign_in_image != blank %}bg-image{% endif %}" x-data="{ showLogin: true }">
  {% if settings.sign_in_image != blank %}
    {% render 'image' with image: settings.sign_in_image, class:"login__background absolute inset-0 w-full h-full object-cover object-center" %}
  {% endif %}

  <div class="login__content relative">
    {% comment %} Password Recover Form {% endcomment %}
    <div class="recover" x-show="!showLogin" x-cloak>
      <div class="recover__form">
        <h2 class="recover__title type-headline type--sm mt-0">{{ 'customer.recover_password.title' | t }}</h2>
        <p class="recover__subtitle type-subline mt-0">{{ 'customer.recover_password.subtext' | t }}</p>

        {% form 'recover_customer_password' %}
          {% assign recover_success = form.posted_successfully? %}
          <div class="form-errors">{{ form.errors | default_errors }}</div>

          <div class="floating-label">
            <label for="RecoverEmail">{{ 'customer.recover_password.email' | t }}</label>
            <input
              type="email"
              value=""
              name="email"
              id="RecoverEmail"
              class="field__input w-full"
              autocorrect="off"
              autocapitalize="off"
              autocomplete="email"
              {% if form.errors %}
                aria-invalid="true"
                aria-describedby="RecoverEmail-email-error"
                autofocus
              {% endif %}
              placeholder="{{ 'customer.recover_password.email' | t }}"
            >
          </div> 
          
          <input type="submit" class="button button--primary w-full" value="{{ 'customer.recover_password.submit' | t }}">

          <div class="recover__link-container flex justify-center mt-4">
            <button
              type="button"
              class="recover__link link text-sm flex items-center gap-1"
              @click.prevent="showLogin = true"
            >
              {% render 'icon' icon: 'chevron-left' width:16 height:16 strokeWidth:2 %}
              {{ 'customer.recover_password.cancel' | t }}
            </button>
          </div>
        
        {% endform %}
      </div>
    </div>

    {% comment %} Login Form {% endcomment %}
    <div class="login__form"  x-show="showLogin">
      <h1 class="login__title type-headline type--sm mt-0">{{ 'customer.login.title' | t }}</h1>
      {% comment %} Form Success {% endcomment %}
      {% if recover_success %}
        <div class="form-success">{{ 'customer.recover_password.success' | t }}</div>
      {% endif %}

      {% form 'customer_login' %}
        <div class="form-errors">{{ form.errors | default_errors }}</div>

        <div class="floating-label">
          <label for="CustomerEmail">
            {{ 'customer.login.email' | t }}
          </label>
          <input
            type="email"
            name="customer[email]"
            id="CustomerEmail"
            class="field__input w-full"
            autocomplete="email"
            autocorrect="off"
            autocapitalize="off"
            {% if form.errors contains 'form' %}
              aria-invalid="true"
            {% endif %}
            placeholder="{{ 'customer.login.email' | t }}"
          >
        </div>
        
        {% if form.password_needed %}
          <div class="floating-label">
            <label for="CustomerPassword">
              {{ 'customer.login.password' | t }}
            </label>
            <input
              type="password"
              value=""
              name="customer[password]"
              id="CustomerPassword"
              class="field__input w-full"
              autocomplete="current-password"
              {% if form.errors contains 'form' %}
                aria-invalid="true"
              {% endif %}
              placeholder="{{ 'customer.login.password' | t }}"
            >
          </div>
        {% endif %}

        <div class="login__link-container flex justify-end mb-6">
          <a class="login__link create-account-link text-sm px-1" href="/account/register">
            {{ 'layout.customer.create_account' | t }}
          </a>
          
          {% if form.password_needed %}
            <a
              href="#recover"
              class="login__link recover-account-link text-sm px-1"
              @click.prevent="showLogin = false"
              data-recover-toggle
            >
              {{- 'customer.login.forgot_password' | t -}}
            </a>
          {% endif %}
        </div>

        <input type="submit" class="login__submit button button--primary w-full" value="{{ 'customer.login.sign_in' | t }}">
      {% endform %}
      
      {% comment %} Guest Login {% endcomment %}
      {% if shop.checkout.guest_login %}
        <h2 class="type--secondary mb-2 mt-0">{{ 'customer.login.guest_title' | t }}</h2>
        {% form 'guest_login' %}
          <input type="submit" class="btn btn--primary" value="{{ 'customer.login.guest_continue' | t }}">
        {% endform %}
      {% endif %}
    </div>

    {% comment %} Callout {% endcomment %}
    {% if settings.login_title != blank or settings.login_subtitle != blank or settings.login_link_title != blank %}
      <aside class="callout" {% if settings.login_text_color != blank %} style="color: {{ settings.login_text_color }};"{% endif %}>
        <h4 class="callout__title type-headline type--sm mt-0" {% if settings.login_text_color != blank %} style="color: {{ settings.login_text_color }}"{% endif %}>
          {{ settings.login_title }}
        </h4>

        <p class="callout__subtitle type-subline mt-0" {% if settings.login_text_color != blank %}style="color: {{ settings.login_text_color }}"{% endif %}>{{ settings.login_subtitle }}</p>

        <div class="rewards my-4">
          {%
            render 'content-item' 
            item_classes:'rewards__header',
            text_item_1_element:'p',
            text_item_1_text: settings.rewards_callout_message,
            text_item_1_classes:'rewards__message',
            image: settings.rewards_callout_image,
            image_mobile: settings.rewards_callout_image_mobile,
            media_classes:'rewards__logo',
            settings:settings
          %}
        </div>

        <a class="callout__button button button--primary" href="{{ settings.login_url }}">
          {{- settings.login_link_title -}}
        </a>
      </div>
    {% endif %}
  </aside>
</section>
