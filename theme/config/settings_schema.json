[{"name": "theme_info", "theme_name": "Archipelago Brands Theme", "theme_version": "1.0", "theme_author": "SLTWTR", "theme_documentation_url": "https://help.shopify.com/manual/online-store/themes/os20/themes-by-shopify/dawn", "theme_support_url": "https://sltwtr.com/"}, {"name": "Brand", "settings": [{"type": "text", "label": "Brand Name", "id": "brand_name", "info": "This is the variable for sourcing the proper brand assets in the theme."}, {"type": "text", "label": "Store Name", "id": "store_name", "info": "This is the variable for identifying the store with 3rd party integrations."}]}, {"name": "t:settings_schema.styles.name", "settings": [{"type": "header", "content": "t:settings_schema.styles.settings.header__1.content"}, {"type": "select", "id": "sale_badge_color_scheme", "options": [{"value": "accent-1", "label": "t:settings_schema.styles.settings.sale_badge_color_scheme.options__2.label"}, {"value": "accent-2", "label": "t:settings_schema.styles.settings.sale_badge_color_scheme.options__3.label"}, {"value": "background-2", "label": "t:settings_schema.styles.settings.sale_badge_color_scheme.options__1.label"}], "default": "accent-2", "label": "t:settings_schema.styles.settings.sale_badge_color_scheme.label"}, {"type": "select", "id": "sold_out_badge_color_scheme", "options": [{"value": "background-1", "label": "t:settings_schema.styles.settings.sold_out_badge_color_scheme.options__1.label"}, {"value": "inverse", "label": "t:settings_schema.styles.settings.sold_out_badge_color_scheme.options__2.label"}], "default": "inverse", "label": "t:settings_schema.styles.settings.sold_out_badge_color_scheme.label"}, {"type": "header", "content": "t:settings_schema.styles.settings.header__2.content"}, {"type": "select", "id": "accent_icons", "options": [{"value": "accent-1", "label": "t:settings_schema.styles.settings.accent_icons.options__1.label"}, {"value": "accent-2", "label": "t:settings_schema.styles.settings.accent_icons.options__2.label"}, {"value": "outline-button", "label": "t:settings_schema.styles.settings.accent_icons.options__3.label"}, {"value": "text", "label": "t:settings_schema.styles.settings.accent_icons.options__4.label"}], "default": "text", "label": "t:settings_schema.styles.settings.accent_icons.label"}]}, {"name": "t:settings_schema.layout.name", "settings": [{"type": "select", "id": "page_width", "options": [{"value": "1200", "label": "t:settings_schema.layout.settings.page_width.options__1.label"}, {"value": "1600", "label": "t:settings_schema.layout.settings.page_width.options__2.label"}], "default": "1600", "label": "t:settings_schema.layout.settings.page_width.label"}]}, {"name": "Header", "settings": [{"type": "checkbox", "label": "Header Group", "id": "header_group", "info": "Allows multiple header sections"}]}, {"name": "Footer", "settings": [{"type": "checkbox", "label": "Footer Group", "id": "footer_group", "info": "Allows multiple header sections"}]}, {"name": "Account", "settings": [{"type": "header", "content": "Account Navigation"}, {"type": "text", "label": "Returns URL", "id": "returns_url"}, {"type": "text", "label": "Rewards URL", "id": "rewards_url"}, {"type": "text", "label": "Wishlist URL", "id": "wishlist_url"}, {"type": "header", "content": "Create account"}, {"type": "image_picker", "id": "create_an_account_image", "label": "Image"}, {"type": "color", "id": "create_text_color", "label": "Text color", "default": "#ffffff"}, {"type": "text", "id": "create_title", "label": "Title"}, {"type": "text", "id": "create_subtitle", "label": "Subtitle"}, {"type": "text", "id": "create_link_title", "label": "Link title"}, {"type": "url", "id": "create_url", "label": "Link"}, {"type": "header", "content": "<PERSON><PERSON>"}, {"type": "image_picker", "id": "sign_in_image", "label": "Image"}, {"type": "color", "id": "login_text_color", "label": "Text color", "default": "#ffffff"}, {"type": "text", "id": "login_title", "label": "Title"}, {"type": "text", "id": "login_subtitle", "label": "Subtitle"}, {"type": "text", "id": "login_link_title", "label": "Link title"}, {"type": "url", "id": "login_url", "label": "Link"}, {"type": "header", "content": "Reset Password"}, {"type": "image_picker", "id": "reset_image", "label": "Image"}, {"type": "color", "id": "reset_text_color", "label": "Text color", "default": "#ffffff"}, {"type": "text", "id": "reset_title", "label": "Title"}, {"type": "text", "id": "reset_subtitle", "label": "Subtitle"}, {"type": "text", "id": "reset_link_title", "label": "Link title"}, {"type": "url", "id": "reset_url", "label": "Link"}, {"type": "header", "content": "<PERSON><PERSON><PERSON> Callout"}, {"type": "image_picker", "id": "rewards_callout_image", "label": "Logo (Desktop)"}, {"type": "image_picker", "id": "rewards_callout_image_mobile", "label": "Logo (Mobile)"}, {"type": "textarea", "id": "rewards_callout_message", "label": "Message"}]}, {"name": "Mega Menus", "settings": [{"type": "number", "id": "mega_menus", "label": "Number of Mega Menu Sections", "default": 1}]}, {"name": "t:settings_schema.favicon.name", "settings": [{"type": "image_picker", "id": "favicon", "label": "t:settings_schema.favicon.settings.favicon.label", "info": "t:settings_schema.favicon.settings.favicon.info"}]}, {"name": "Social media", "settings": [{"type": "header", "content": "Social sharing options"}, {"type": "checkbox", "id": "share_facebook", "label": "Share on Facebook", "default": true}, {"type": "checkbox", "id": "share_twitter", "label": "Share on Twitter", "default": true}, {"type": "checkbox", "id": "share_pinterest", "label": "Share on Pinterest", "default": true}, {"type": "header", "content": "Sharing links"}, {"type": "text", "id": "social_facebook_link", "label": "Facebook link"}, {"type": "text", "id": "social_instagram_link", "label": "Instagram link"}, {"type": "text", "id": "social_pinterest_link", "label": "Pinterest link"}, {"type": "text", "id": "social_twitter_link", "label": "Twitter link"}, {"type": "text", "id": "social_youtube_link", "label": "Youtube link"}, {"type": "text", "id": "social_snapchat_link", "label": "Snapchat link"}]}, {"name": "Meta tags", "settings": [{"type": "checkbox", "id": "enable_custom_meta_title", "label": "Enable Custom Meta Title", "default": false}, {"type": "text", "id": "custom_meta_title", "label": "Meta title"}]}, {"name": "t:settings_schema.currency_format.name", "settings": [{"type": "header", "content": "t:settings_schema.currency_format.settings.content"}, {"type": "paragraph", "content": "t:settings_schema.currency_format.settings.paragraph"}, {"type": "checkbox", "id": "currency_code_enabled", "label": "t:settings_schema.currency_format.settings.currency_code_enabled.label", "default": true}]}, {"name": "t:settings_schema.search_input.name", "settings": [{"type": "header", "content": "t:settings_schema.search_input.settings.header.content"}, {"type": "checkbox", "id": "predictive_search_enabled", "default": true, "label": "t:settings_schema.search_input.settings.predictive_search_enabled.label"}, {"type": "checkbox", "id": "predictive_search_show_vendor", "default": false, "label": "t:settings_schema.search_input.settings.predictive_search_show_vendor.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_vendor.info"}, {"type": "checkbox", "id": "predictive_search_show_price", "default": true, "label": "t:settings_schema.search_input.settings.predictive_search_show_price.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_price.info"}]}, {"name": "Buttons", "settings": [{"type": "header", "content": "Classes"}, {"type": "text", "id": "button_default_classes", "label": "Default <PERSON> Classes", "default": "inline-flex w-auto px-3 py-2 bg-white text-black border border-black rounded-sm m-3 hover:opacity-80 transition:opacity cursor-pointer"}, {"type": "text", "id": "button_primary_classes", "label": "Primary Button Classes", "default": "inline-flex px-3 py-2 bg-black text-white rounded-md"}, {"type": "text", "id": "button_secondary_classes", "label": "Secondary Button Classes", "default": "inline-flex px-3 py-2 bg-white text-black rounded-md"}]}, {"name": "Icons", "settings": [{"type": "header", "content": "Icon Display"}, {"type": "checkbox", "id": "icon_display_text", "label": "Display icons as text"}, {"type": "checkbox", "id": "icon_display_icon", "label": "Display icons as icons"}, {"type": "text", "id": "icon_stroke_width", "label": "Stroke width for all icons", "default": "1"}, {"type": "header", "content": "Icon Mapping"}, {"type": "html", "id": "icon_mapping", "label": "Icon Mapping"}]}, {"name": "Modals", "settings": [{"type": "header", "content": "Modal Overlay Display"}, {"type": "liquid", "id": "modal_overlay_classes", "label": "Modal Overlay Classes", "default": "fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"}, {"type": "liquid", "id": "modal_overlay_attributes", "label": "Modal Overlay Attributes", "default": "x-transition:enter=\"transition ease-out duration-500\" x-transition:enter-start=\"opacity-0\" x-transition:enter-end=\"opacity-100\" x-transition:leave=\"transition ease-in duration-500\" x-transition:leave-start=\"opacity-100\" x-transition:leave-end=\"opacity-0\""}]}, {"name": "Product", "settings": [{"type": "header", "content": "Product Form"}, {"type": "liquid", "id": "strip_from_swatches", "label": "Strip values from Swatches", "default": "Mens,Womens,Big Kids"}, {"type": "checkbox", "label": "Hide signal size variant", "id": "hide_single_size_variant", "default": false}]}, {"name": "Product Item", "settings": [{"type": "header", "content": "Product Item Settings"}, {"type": "liquid", "id": "product_item_title_source", "label": "Product Item Title Source", "default": "product.title.split('-')[0]"}, {"type": "liquid", "id": "product_item_subtitle_source", "label": "Product Item Subtitle Source", "default": "product.title.split('-')[1]"}, {"type": "liquid", "id": "product_item_type_source", "label": "Product Item Type Source", "default": "product.type"}, {"type": "html", "id": "product_option_name_map", "label": "Product Option Name Map", "info": "JSON template only"}, {"type": "header", "content": "Classes"}, {"type": "text", "id": "classes_product_item", "label": "Product Item"}, {"type": "text", "id": "classes_product_item_image_wrapper", "label": "Image"}, {"type": "text", "id": "classes_product_item_title", "label": "Product Item Title"}, {"type": "text", "id": "classes_product_item_subtitle", "label": "Product Item Subtitle"}, {"type": "liquid", "id": "product_item_additional_liquid", "label": "Additional Liquid"}, {"type": "checkbox", "id": "product_item_show_title", "label": "Show Title", "default": true}, {"type": "checkbox", "id": "product_item_show_subtitle", "label": "Show Subtitle", "default": true}, {"type": "checkbox", "id": "product_item_show_type", "label": "Show Product Type", "default": true}, {"type": "checkbox", "id": "product_item_show_price", "label": "Show Price", "default": true}, {"type": "checkbox", "id": "product_item_show_reviews", "label": "Show Review Stars", "default": true}, {"type": "select", "id": "product_item_info_layout", "label": "Info Layout", "options": [{"value": "", "label": "Column"}, {"value": "flex flex-row justify-between", "label": "Spaced Row"}]}, {"type": "select", "id": "product_item_quick_add_position_mobile", "label": "Quick Add But<PERSON> position (Mobile)", "default": "image", "options": [{"label": "Image Container", "value": "image"}, {"label": "Info Container", "value": "info"}]}, {"type": "select", "id": "product_item_quick_add_position_desktop", "label": "Quick Add But<PERSON> position (Desktop)", "default": "image", "options": [{"label": "Image Container", "value": "image"}, {"label": "Info Container", "value": "info"}]}, {"type": "checkbox", "id": "product_item_show_swatches", "label": "Show Swatches"}, {"type": "number", "id": "product_item_swatches_view", "label": "Swatches Per View (Mobile)", "default": 3}, {"type": "number", "id": "product_item_swatches_view_desktop", "label": "Swatches Per View (Desktop)", "default": 5}, {"type": "select", "id": "product_item_swatch_interaction", "label": "User swatch interaction to update product item on desktop", "default": "click", "options": [{"label": "Hover", "value": "hover"}, {"label": "Click", "value": "click"}]}, {"type": "checkbox", "id": "product_item_enhanced_data", "label": "Enhanced Data for Swatches"}, {"type": "text", "id": "product_item_enhanced_data_key", "label": "API key for enhanced data"}, {"type": "select", "id": "product_color_option_type", "label": "Product Color Option Type", "default": "image", "options": [{"label": "Image", "value": "image"}, {"label": "Swatch Color", "value": "swatch"}, {"label": "Swatch Image", "value": "swatch_image"}]}, {"type": "select", "id": "product_color_swatch_page", "label": "Product Color Swatch Position", "default": "both_pdp_plp", "options": [{"label": "Both PDP and PLP", "value": "both_pdp_plp"}, {"label": "Only PLP", "value": "only_plp"}, {"label": "Only PDP", "value": "only_pdp"}]}, {"type": "color", "id": "product_default_swatch_color_1", "label": "Product Default Swatch Color 1", "default": "#000000"}]}, {"name": "PLP Back in Stock", "settings": [{"type": "checkbox", "id": "plp_back_in_stock", "label": "Enable Back in stock on PLP", "default": false}]}, {"name": "Quick Add", "settings": [{"type": "header", "content": "Quick Add Settings"}, {"type": "liquid", "id": "quickadd_title_source", "label": "Quick Add Title Source", "default": "product.title.split('-')[0]"}, {"type": "liquid", "id": "quickadd_subtitle_source", "label": "Quick Add Subtitle Source", "default": "product.title.split('-')[1]"}, {"type": "checkbox", "id": "preselect_variant", "label": "Pre-select <PERSON><PERSON><PERSON>", "default": false}, {"type": "liquid", "id": "quickadd_type_source", "label": "Quick Add Type Source"}, {"type": "checkbox", "id": "quickadd_media", "label": "Show Media Carousel", "default": false}, {"type": "checkbox", "id": "quickadd_siblings", "label": "Show Sibling Selector", "default": false}, {"type": "checkbox", "id": "quickadd_selected_color", "label": "Show Selected Color", "default": true}, {"type": "checkbox", "id": "quickadd_cta", "label": "Show CTA Footer", "default": false}, {"type": "select", "id": "quickadd_carousel_pagination", "label": "Crousel Pagination Type", "default": "bullets", "options": [{"value": "", "label": "None"}, {"value": "bullets", "label": "Bullets"}, {"value": "progressbar", "label": "Progress Bar"}]}, {"type": "number", "id": "quickadd_slides_per_view", "label": "Media Items Per View"}, {"type": "number", "id": "quickadd_slides_per_view_desktop", "label": "Media Items Per View (Desktop)"}, {"type": "checkbox", "id": "quickadd_trigger_full", "label": "Prioritize Quickadd on Mobile", "default": false}, {"type": "checkbox", "label": "Enable/disable videos on quick add", "id": "enable_disable_videos_quick_add", "default": true}, {"type": "text", "label": "Video Media Attributes", "id": "media_item_attr", "info": "Allows direct access to native web video attributes, i.e. 'muted', 'autoplay'.", "default": "muted autoplay loop playsinline controls disablepictureinpicture controlslist=\"nodownload noplaybackrate\""}]}, {"name": "Visitor Segmentation", "settings": [{"type": "number", "label": "Visitor Timeout", "id": "visitor_timeout", "default": 24, "info": "Hours to wait until marking a new visitor as returning."}, {"type": "select", "label": "Data Storage", "id": "visitor_storage", "default": "local", "options": [{"label": "Session Storage (Browser Session/Tab)", "value": "session"}, {"label": "Local Storage (Persistent)", "value": "local"}]}]}, {"name": "Integrations", "settings": [{"type": "header", "content": "Peripherals Mapping"}, {"type": "liquid", "id": "peripherals_map", "label": "Peripherals Map", "info": "A JSON object that maps data from peripherals into local customer data object"}, {"type": "paragraph", "content": "{ PERIPHERAL: { SOURCE:DESTINATION, SOURCE:DESTINATION } }"}, {"type": "paragraph", "content": "SOURCE is the dot path that comes off of the peripheral data to source the value"}, {"type": "paragraph", "content": "DESTINATION is the path to map to on the customer object where the value will be assigned"}]}, {"name": "mParticle", "settings": [{"type": "checkbox", "label": "Connect to mParticle", "id": "mparticle_enable"}, {"type": "text", "id": "mparticle_domain_name", "label": "Domain name"}, {"type": "text", "id": "key_mparticle", "label": "Key For mParticle"}, {"type": "checkbox", "label": "isDevelopmentMode", "id": "is_development_mode", "default": false}, {"type": "select", "id": "logLevel", "label": "Log level", "options": [{"value": "warning", "label": "warning"}, {"value": "verbose", "label": "verbose"}], "default": "warning", "info": "Select log handler"}, {"type": "checkbox", "label": "Enable logout function", "id": "logout_function", "default": false}, {"type": "textarea", "id": "mparticle_config", "label": "<PERSON><PERSON><PERSON><PERSON> config"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": [{"type": "liquid", "id": "klavi<PERSON>_new_registration_check", "label": "Klaviyo Global Config"}]}, {"name": "<PERSON><PERSON><PERSON>", "settings": [{"type": "header", "content": "Enable"}, {"type": "checkbox", "id": "reamaze_lightbox", "label": "Enable/Disable Reamaze Chat Lightbox"}, {"type": "header", "content": "Settings"}, {"type": "text", "id": "reamaze_sso_key", "label": "Reamaze SSO Key", "default": "d8540910f93b0f65da0ee23025ad33b9a74625c732e40dd50053be7101313c0e"}, {"type": "text", "id": "reamaze_account", "label": "<PERSON><PERSON><PERSON> Account", "info": "Name of brand account from reamaze settings"}, {"type": "select", "id": "reamaze_contactMode", "label": "Reamaze Contact Mode", "options": [{"value": "contact", "label": "Contact"}, {"value": "default", "label": "<PERSON><PERSON><PERSON>"}], "default": "default", "info": "Controls what screen displays first on the chat lightbox"}, {"type": "text", "id": "reamaze_enableKb", "label": "Reamaze KB Mode", "info": "KB Mode", "default": "true"}, {"type": "color", "id": "reamaze_widget_color", "label": "Widget Color", "info": "Change the fill color of the Lightbox", "default": "#00213b"}, {"type": "checkbox", "id": "reamaze_gradient", "label": "Enable Gradient Effect", "default": true}, {"type": "text", "id": "reamaze_shoutboxFacesMode", "label": "Shoutbox Faces Mode", "info": "Style to show the Avatare Image", "default": "custom"}, {"type": "image_picker", "id": "reamaze_faces_image", "label": "Faces Image", "info": "1:1 Image for Avatar"}, {"type": "checkbox", "id": "reamaze_shoutboxHeaderLogo", "label": "Shoutbox Header Logo", "default": true}, {"type": "checkbox", "id": "reamaze_faq", "label": "Enable FAQ", "default": true}, {"type": "checkbox", "id": "reamaze_orders", "label": "Enable Orders", "default": true}, {"type": "checkbox", "id": "reamaze_orders_notes", "label": "Enable Order Notes", "default": false}]}, {"name": "Exponea", "settings": [{"type": "checkbox", "label": "Connect to Exponea", "id": "exponea", "default": true}, {"type": "text", "label": "Exponea Target", "id": "exponea_cdt_api_url", "info": "Exponea CDT API URL", "default": "https://wlkadan6pg.execute-api.us-west-2.amazonaws.com/dev"}, {"type": "text", "label": "<PERSON><PERSON>", "id": "exponea_token_id", "info": "Exponea <PERSON>", "default": "614d6164-71c0-11e9-a986-0a580a2037f6"}, {"type": "checkbox", "label": "Enable Experiments", "id": "exponea_experiments_enabled", "default": true}, {"type": "select", "label": "Exponea Experiments Mode", "id": "exponea_experiments_mode", "options": [{"label": "Blocking (sync)", "value": "sync"}, {"label": "Hidden (async)", "value": "async"}]}, {"type": "number", "label": "Exponea Experiments Timeout", "id": "exponea_experiments_timeout", "default": 4000}, {"type": "text", "label": "Exponea Experiments Hide Class", "id": "exponea_experiments_hide_class", "default": "xnpe_async_hide"}]}, {"name": "Cookie Compliance", "settings": [{"type": "checkbox", "id": "cookie_compliance_enabled", "label": "Enable Cookie Compliance", "default": false}, {"type": "text", "id": "cookie_compliance_id", "label": "Setting-ID", "info": "Project ID from the compliance platform"}]}, {"name": "Accessibility Statement", "settings": [{"type": "checkbox", "id": "enable_accessibility_statement", "label": "Enable Accessibility Statement Link in Header", "default": false}, {"type": "url", "id": "accessibility_statement_link", "label": "Accessibility Statement Link"}, {"type": "text", "id": "accessibility_statement_text", "label": "Accessibility Statement Text"}]}, {"name": "Code", "settings": [{"type": "header", "content": "Code Injections"}, {"type": "liquid", "id": "code_head_start", "label": "Start of head"}, {"type": "liquid", "id": "code_head_end", "label": "End of head"}, {"type": "liquid", "id": "code_body_start", "label": "Start of body"}, {"type": "liquid", "id": "code_body_end", "label": "End of body"}, {"type": "header", "content": "Content for Head<PERSON>"}, {"type": "liquid", "id": "split_content_for_header", "label": "Split content_for_header", "default": "<script type=\"module\">"}, {"type": "header", "content": "<PERSON><PERSON><PERSON>"}, {"type": "radio", "id": "block_scripts", "label": "Block Script and Pixel Injections", "default": "always", "options": [{"value": "", "label": "Never"}, {"value": "before", "label": "Before Page Load"}, {"value": "always", "label": "Always"}]}, {"type": "textarea", "id": "script_block_exceptions", "label": "Script Blocker Exceptions", "info": "If populated, scripts injected into the HEAD of the site will be blocked on the designated page type."}, {"type": "header", "content": "Cart Events"}, {"type": "liquid", "id": "script_cart_add", "label": "Add to Cart", "info": "JavaScript to be executed when an item is added to the cart."}, {"type": "liquid", "id": "script_cart_remove", "label": "Remove from Cart", "info": "JavaScript to be executed when an item is removed from the cart."}, {"type": "header", "content": "Customer Events"}, {"type": "liquid", "id": "script_customer_identify", "label": "Customer Identified", "info": "JavaScript to be executed when a Customer is identified. Variables for 'key' and 'value' are avaiable for the identification method"}, {"type": "header", "content": "Form Events"}, {"type": "liquid", "id": "script_form_submit", "label": "Form Submitted", "info": "JavaScript to be executed when a Form is submitted"}, {"type": "header", "content": "Development"}, {"type": "checkbox", "label": "Enable logs", "id": "enable_logs"}]}, {"name": "Checkout - Olukai", "settings": [{"type": "header", "content": "<PERSON><PERSON>"}, {"type": "checkbox", "id": "fraud_filter_express_checkout_enabled", "label": "Hide Express Checkout if cart exceeds $1000", "default": false}, {"type": "header", "content": "Checkout Gift Card Modal"}, {"type": "checkbox", "id": "enable_discount_code_checkout", "label": "Enable Discount code", "default": false}, {"type": "header", "content": "Checkout Gift Card Modal"}, {"type": "radio", "id": "checkout_giftcard_modal_enabled", "label": "Select Mode for Gift Card Modal", "options": [{"value": "standard", "label": "Standard"}, {"value": "vip", "label": "Vip"}], "default": "standard"}, {"type": "text", "id": "giftcard_validation_endpoint", "label": "Gift card validation endpoint", "default": "https://7o5fbudw2d.execute-api.us-west-2.amazonaws.com/olukai-giftcard-validation/resource"}, {"type": "text", "id": "giftcard_code_contains", "label": "Gift card Code Contains"}, {"type": "text", "id": "giftcard_code_starts", "label": "Gift card Code starts"}, {"type": "header", "content": "Checkout Sticky CTA"}, {"type": "checkbox", "id": "checkout_sticky_cta_enabled", "label": "Enable Sticky Checkout <PERSON>", "default": false}, {"type": "header", "content": "Address Validation"}, {"type": "checkbox", "id": "address_validation_enable", "label": "Enable Address Validation"}, {"type": "header", "content": "Estimated Shipping Dates"}, {"type": "checkbox", "id": "estimated_shipping_enabled", "label": "Enable", "default": true}, {"type": "text", "id": "estimated_shipping_delay", "label": "Global Shipping Delay", "info": "in days"}, {"type": "text", "id": "cutoff_time", "label": "Cutoff Time", "placeholder": "11", "info": "24-hr format. ex: 16 = 4 PM"}, {"type": "textarea", "id": "blackout_dates", "label": "Blackout Dates", "placeholder": "1/1/19,4/21/19", "info": "csv dates: '05/21/19' format"}, {"type": "text", "id": "exclude_states", "label": "Exclude Hide Rate", "placeholder": "'HI', 'AK', 'PR', 'VI', 'GU'", "info": "Exclude States from Expedited Hide Function", "default": "'HI', 'AK', 'PR', 'VI', 'GU'"}, {"type": "textarea", "id": "shipping_text_message", "label": "Shipping Method Description", "placeholder": "We do not ship or deliver orders on weekends or major holidays.", "info": "paragraph displayed above the shipping options"}, {"type": "text", "id": "estimated_text_space", "label": "Est. Date Placement L/R", "placeholder": "45", "info": "value in px ex: 45"}, {"type": "textarea", "id": "shipping_text_loading_message", "label": "Preload Message", "placeholder": "Est. Arrival: Checking with the locals. BRB", "info": "displayed when calling api dates"}, {"type": "html", "id": "shipping_map", "label": "Shipping Map", "info": "JSON template only"}, {"type": "header", "content": "Split Profile Feature"}, {"type": "checkbox", "id": "profile_split_enable", "label": "Enable Profile Split", "info": "By enableing this feature we catch split profile events at checkout and merge them into a main profile if found.", "default": false}, {"type": "text", "id": "profile_split_endpoint", "label": "Profile split endpoint ", "default": "https://nkti7n2upk.execute-api.us-west-2.amazonaws.com/default/OlukaiProfileSplit"}, {"type": "header", "content": "Enable/Disable Pixels in checkout"}, {"type": "checkbox", "id": "twitter_pixel_enable", "label": "Enable Twitter Pixel", "default": false}, {"type": "html", "id": "twitter_pixel_code", "label": "Twitter Pixel Code"}, {"type": "checkbox", "id": "canella_media_pixel", "label": "Enable Canella Media Pixel", "default": false}, {"type": "html", "id": "canella_pixel_code", "label": "Canella Pixel Code"}, {"type": "checkbox", "id": "mntn_pixel_enable", "label": "MNTN Pixel", "default": false}, {"type": "html", "id": "mntn_pixel_code", "label": "Mntn Pixel Code"}, {"type": "checkbox", "id": "cybba_pixel_enable", "label": "Enable <PERSON><PERSON>", "default": false}, {"type": "html", "id": "cybba_pixel_code", "label": "Cybba Pixel Code"}, {"type": "text", "id": "gtm_container_id", "label": "Google Tag Manager ID"}, {"type": "text", "id": "google_optimize_id", "label": "Google Optimize ID"}, {"type": "checkbox", "id": "outbrain_pixel_enabled", "label": "Enable Outbrain Pixel", "default": false}, {"type": "text", "id": "outbrain_ob_adv_id", "label": "Outbrain Merchant ID", "default": "0029d2f1ca08c10164c21c8b2e156f04e0"}, {"type": "header", "content": "Sms Legal consent copy"}, {"type": "checkbox", "id": "olu_enable_consent_checkout", "label": "Turn on sms checkout", "default": false}, {"id": "sms_legal_consent_copy1", "info": "Legal copy Header that shows next to checkbox for SMS consent in bold text", "label": "SMS Consent Copy <PERSON>er", "type": "textarea"}, {"id": "sms_legal_consent_copy2", "info": "Legal copy Body that shows next to checkbox for SMS consent in regular text", "label": "SMS Consent Copy Body", "type": "richtext"}]}, {"name": "Checkout - <PERSON><PERSON>", "settings": [{"type": "header", "content": "Checkout Refund Policy URL"}, {"type": "text", "id": "melin_refund_policy_checkout_url", "label": "URL", "default": "https://support.melin.com/kb/returns-438cb584ce8146fa/what-is-your-return-policy-9e151a6e0bc8aa1f"}, {"type": "header", "content": "Checkout Afterpay"}, {"type": "checkbox", "id": "melin_afterpay_enabled", "label": "Enable Afterpay"}, {"type": "header", "content": "Checkout VIP Code Re-Direct"}, {"type": "checkbox", "id": "melin_vip_redirect_modal_enabled", "label": "Enable Redirect Modal"}, {"type": "textarea", "id": "melin_vip_startswith_array", "label": "Starts With Syntax", "info": "For Example: PD1, DF1"}, {"type": "url", "id": "melin_vip_redirect_url", "label": "Goto url", "info": "route the user to vip site"}, {"type": "text", "id": "melin_vip_modal_heading", "label": "<PERSON><PERSON> Heading"}, {"type": "html", "id": "melin_vip_modal_paragraph", "label": "Modal Paragraph", "info": "okay to use html inside like <a>"}, {"type": "color", "id": "melin_vip_modal_button_color", "label": "Button Color"}, {"type": "color", "id": "melin_vip_modal_text_color", "label": "Text Color"}, {"type": "header", "content": "Checkout Site Mode"}, {"type": "radio", "id": "melin_site_mode", "label": "Site Mode", "info": "Standard Site or VIP Site", "default": "standard", "options": [{"value": "standard", "label": "Standard Site"}, {"value": "vip", "label": "VIP Site"}]}, {"type": "header", "content": "Checkout PO Box Blocker"}, {"type": "checkbox", "id": "melin-pobox-blocker_enable", "label": "Enable PO Box blocker", "default": false}, {"type": "text", "id": "melin-pob-header-font", "label": "Heading Font Family", "info": "Do not use \", only use 'single quotes'.", "default": "inherit"}, {"type": "color", "id": "melin-pob-text-color", "label": "Text Color", "default": "#000000"}, {"type": "text", "id": "melin-pob-button-font", "label": "Button Font Family", "info": "Do not use \", only use 'single quotes'.", "default": "inherit"}, {"type": "text", "id": "melin-pob-button-text", "label": "Button Copy", "info": "CLEAR ADDRESS", "default": "CLEAR ADDRESS"}, {"type": "color", "id": "melin-pob-color", "label": "Text Color", "default": "#000000"}, {"type": "color", "id": "melin-pob-background-color", "label": "Background Color", "default": "#FFFFFF"}, {"type": "color", "id": "melin-pob-border-color", "label": "Border Color", "default": "#ffffff"}, {"type": "color", "id": "melin-pob-hover-color", "label": "Hover Text Color", "default": "#ffffff"}, {"type": "color", "id": "melin-pob-hover-background-color", "label": "Hover Background Color", "default": "#000000"}, {"type": "color", "id": "melin-pob-border-hover-color", "label": "Hover Border Color", "default": "#000000"}, {"type": "header", "content": "Checkout Gift Order"}, {"default": false, "id": "melin_enable_gift_order", "label": "Enable Gift Order", "type": "checkbox"}, {"id": "melin_gift_svg", "label": "Path to Checkbox SVG", "type": "text"}, {"type": "header", "content": "Checkout Exponea"}, {"type": "textarea", "id": "melin_exponea_consents", "label": "Consent JSON", "info": "{action: 'accept', category: 'news-and-offers', valid_until: 'unlimited'}", "default": "{action: 'accept', category: 'news-and-offers', valid_until: 'unlimited'}, {action: 'accept', category: 'cart-notifications', valid_until: 'unlimited'}, {action: 'accept', category: 'back-in-stock', valid_until: 'unlimited'}, {action: 'accept', category: 'surveys', valid_until: 'unlimited'}"}, {"type": "textarea", "id": "melin_sms_exponea_consents", "label": "SMS Consent JSON", "info": "{action: 'accept', category: 'news-and-offers', valid_until: 'unlimited'}", "default": "{action: 'accept', category: 'news-and-offers', valid_until: 'unlimited'}, {action: 'accept', category: 'cart-notifications', valid_until: 'unlimited'}, {action: 'accept', category: 'back-in-stock', valid_until: 'unlimited'}, {action: 'accept', category: 'surveys', valid_until: 'unlimited'}"}, {"type": "header", "content": "Checkout Shipping"}, {"type": "text", "id": "melin_free_shipping_amount", "label": "Free Shipping Amount", "info": "This is for free shipping promotions", "default": "150.00"}, {"type": "header", "content": "Checkout Discount Plus"}, {"type": "checkbox", "id": "melin_plus_show_discount_enable", "label": "enable discount Plus", "default": true}, {"type": "header", "content": "Checkout Newsletter"}, {"type": "checkbox", "id": "melin_cn_modal_enabled", "label": "Enable Newsletter Banner"}, {"type": "text", "id": "melin_cn_superscript", "label": "Superscript Text", "info": "The text that is above heading text"}, {"type": "text", "id": "melin_cn_heading", "label": "Heading Text", "info": "Free 2-Day Shipping"}, {"type": "textarea", "id": "melin_cn_description", "label": "Description Text", "info": "Descripive text under heading"}, {"type": "color", "id": "melin_cn_background_color", "label": "Background fill color"}, {"type": "image_picker", "id": "melin_cn_icon", "label": "Banner Icon", "info": "svg or png image"}, {"type": "color", "id": "melin_cn_button_color", "label": "Button Color"}, {"type": "color", "id": "melin_cn_btn_text_color", "label": "Button Text Color"}, {"type": "text", "id": "melin_cn_btn_text", "label": "Button Text", "info": "Join Now"}, {"type": "header", "content": "Address Validation"}, {"type": "checkbox", "id": "melin_address_validation_enable", "label": "Enable Address Validation"}, {"type": "header", "content": "Estimated Shipping Dates"}, {"type": "checkbox", "id": "melin_estimated_shipping_enabled", "label": "Enable", "default": true}, {"type": "text", "id": "melin_estimated_shipping_delay", "label": "Global Shipping Delay", "info": "in days"}, {"type": "text", "id": "melin_cutoff_time", "label": "Cutoff Time", "placeholder": "11", "info": "24-hr format. ex: 16 = 4 PM"}, {"type": "textarea", "id": "melin_blackout_dates", "label": "Blackout Dates", "placeholder": "1/1/19,4/21/19", "info": "csv dates: '05/21/19' format"}, {"type": "text", "id": "melin_exclude_states", "label": "Exclude Hide Rate", "placeholder": "'HI', 'AK', 'PR', 'VI', 'GU'", "info": "Exclude States from Expedited Hide Function", "default": "'HI', 'AK', 'PR', 'VI', 'GU'"}, {"type": "textarea", "id": "melin_shipping_text_message", "label": "Shipping Method Description", "placeholder": "We do not ship or deliver orders on weekends or major holidays.", "info": "paragraph displayed above the shipping options"}, {"type": "textarea", "id": "melin_shipping_address_notice", "label": "Shipping Address Notice", "info": "Paragraph displayed above the shipping address fields"}, {"type": "text", "id": "melin_estimated_text_space", "label": "Est. Date Placement L/R", "placeholder": "45", "info": "value in px ex: 45"}, {"type": "textarea", "id": "melin_shipping_text_loading_message", "label": "Preload Message", "placeholder": "Est. Arrival: Checking with the locals. BRB", "info": "displayed when calling api dates"}, {"type": "html", "id": "melin_shipping_map", "label": "Shipping Map", "info": "JSON template only"}, {"type": "header", "content": "Enable/Disable Pixels in checkout"}, {"type": "text", "id": "melin_gtm_container_id", "label": "Google Tag Manager ID"}, {"type": "text", "id": "melin_google_optimize_id", "label": "Google Optimize ID"}, {"type": "header", "content": "Sms Legal consent copy"}, {"type": "checkbox", "id": "melin_enable_consent_checkout", "label": "Checkout Page"}, {"id": "melin_sms_legal_consent_copy1", "info": "Legal copy Header that shows next to checkbox for SMS consent in bold text", "label": "SMS Consent Copy <PERSON>er", "type": "textarea"}, {"id": "melin_sms_legal_consent_copy2", "info": "Legal copy Body that shows next to checkbox for SMS consent in regular text", "label": "SMS Consent Copy Body", "type": "richtext"}]}, {"name": "Checkout - Ka<PERSON>n", "settings": [{"type": "header", "content": "Checkout Site Mode"}, {"type": "radio", "id": "kaenon_site_mode", "label": "Site Mode", "info": "USA or VIP Site", "options": [{"value": "usa", "label": "USA Site"}, {"value": "vip", "label": "VIP Site"}]}, {"type": "header", "content": "Checkout PO Box Blocker"}, {"type": "checkbox", "id": "kaenon-pobox-blocker_enable", "label": "Enable PO Box blocker", "default": false}, {"type": "text", "id": "kaenon-pob-header-font", "label": "Heading Font Family", "info": "Do not use \", only use 'single quotes'.", "default": "inherit"}, {"type": "color", "id": "kaenon-pob-text-color", "label": "Text Color", "default": "#000000"}, {"type": "text", "id": "kaenon-pob-button-font", "label": "Button Font Family", "info": "Do not use \", only use 'single quotes'.", "default": "inherit"}, {"type": "text", "id": "kaenon-pob-button-text", "label": "Button Copy", "info": "CLEAR ADDRESS", "default": "CLEAR ADDRESS"}, {"type": "color", "id": "kaenon-pob-color", "label": "Text Color", "default": "#000000"}, {"type": "color", "id": "kaenon-pob-background-color", "label": "Background Color", "default": "#FFFFFF"}, {"type": "color", "id": "kaenon-pob-border-color", "label": "Border Color", "default": "#ffffff"}, {"type": "color", "id": "kaenon-pob-hover-color", "label": "Hover Text Color", "default": "#ffffff"}, {"type": "color", "id": "kaenon-pob-hover-background-color", "label": "Hover Background Color", "default": "#000000"}, {"type": "color", "id": "kaenon-pob-border-hover-color", "label": "Hover Border Color", "default": "#000000"}, {"type": "header", "content": "Checkout Exponea"}, {"type": "textarea", "id": "kaenon_exponea_consents", "label": "Consent JSON", "info": "{action: 'accept', category: 'news-and-offers', valid_until: 'unlimited'}", "default": "{action: 'accept', category: 'news-and-offers', valid_until: 'unlimited'}, {action: 'accept', category: 'cart-notifications', valid_until: 'unlimited'}, {action: 'accept', category: 'back-in-stock', valid_until: 'unlimited'}, {action: 'accept', category: 'surveys', valid_until: 'unlimited'}"}, {"type": "textarea", "id": "kaenon_sms_exponea_consents", "label": "SMS Consent JSON", "info": "{action: 'accept', category: 'news-and-offers', valid_until: 'unlimited'}", "default": "{action: 'accept', category: 'news-and-offers', valid_until: 'unlimited'}, {action: 'accept', category: 'cart-notifications', valid_until: 'unlimited'}, {action: 'accept', category: 'back-in-stock', valid_until: 'unlimited'}, {action: 'accept', category: 'surveys', valid_until: 'unlimited'}"}, {"type": "header", "content": "Checkout Discount Plus"}, {"type": "checkbox", "id": "kaenon_plus_show_discount_enable", "label": "enable discount Plus", "default": true}, {"type": "header", "content": "Checkout Newsletter"}, {"type": "checkbox", "id": "kaenon_cn_modal_enabled", "label": "Enable Newsletter Banner"}, {"type": "text", "id": "kaenon_cn_superscript", "label": "Superscript Text", "info": "The text that is above heading text"}, {"type": "text", "id": "kaenon_cn_heading", "label": "Heading Text", "info": "Free 2-Day Shipping"}, {"type": "textarea", "id": "kaenon_cn_description", "label": "Description Text", "info": "Descripive text under heading"}, {"type": "color", "id": "kaenon_cn_background_color", "label": "Background fill color"}, {"type": "image_picker", "id": "kaenon_cn_icon", "label": "Banner Icon", "info": "svg or png image"}, {"type": "color", "id": "kaenon_cn_button_color", "label": "Button Color"}, {"type": "color", "id": "kaenon_cn_btn_text_color", "label": "Button Text Color"}, {"type": "text", "id": "kaenon_cn_btn_text", "label": "Button Text", "info": "Join Now"}, {"type": "header", "content": "Address Validation"}, {"type": "checkbox", "id": "kaenon_address_validation_enable", "label": "Enable Address Validation"}, {"type": "header", "content": "Estimated Shipping Dates"}, {"type": "checkbox", "id": "kaenon_estimated_shipping_enabled", "label": "Enable", "default": true}, {"type": "text", "id": "kaenon_estimated_shipping_delay", "label": "Global Shipping Delay", "info": "in days"}, {"type": "text", "id": "kaenon_cutoff_time", "label": "Cutoff Time", "placeholder": "11", "info": "24-hr format. ex: 16 = 4 PM"}, {"type": "textarea", "id": "kaenon_blackout_dates", "label": "Blackout Dates", "placeholder": "1/1/19,4/21/19", "info": "csv dates: '05/21/19' format"}, {"type": "text", "id": "kaenon_exclude_states", "label": "Exclude Hide Rate", "placeholder": "'HI', 'AK', 'PR', 'VI', 'GU'", "info": "Exclude States from Expedited Hide Function", "default": "'HI', 'AK', 'PR', 'VI', 'GU'"}, {"type": "textarea", "id": "kaenon_shipping_text_message", "label": "Shipping Method Description", "placeholder": "We do not ship or deliver orders on weekends or major holidays.", "info": "paragraph displayed above the shipping options"}, {"type": "textarea", "id": "kaenon_shipping_address_notice", "label": "Shipping Address Notice", "info": "Paragraph displayed above the shipping address fields"}, {"type": "text", "id": "kaenon_estimated_text_space", "label": "Est. Date Placement L/R", "placeholder": "45", "info": "value in px ex: 45"}, {"type": "textarea", "id": "kaenon_shipping_text_loading_message", "label": "Preload Message", "placeholder": "Est. Arrival: Checking with the locals. BRB", "info": "displayed when calling api dates"}, {"type": "html", "id": "kaenon_shipping_map", "label": "Shipping Map", "info": "JSON template only"}, {"type": "header", "content": "Enable/Disable Pixels in checkout"}, {"type": "text", "id": "kaenon_gtm_container_id", "label": "Google Tag Manager ID"}, {"type": "header", "content": "Sms Legal consent copy"}, {"type": "checkbox", "id": "kaenon_enable_consent_checkout", "label": "Checkout Page"}, {"id": "kaenon_sms_legal_consent_copy1", "info": "Legal copy Header that shows next to checkbox for SMS consent in bold text", "label": "SMS Consent Copy <PERSON>er", "type": "textarea"}, {"id": "kaenon_sms_legal_consent_copy2", "info": "Legal copy Body that shows next to checkbox for SMS consent in regular text", "label": "SMS Consent Copy Body", "type": "richtext"}]}, {"name": "Checkout - <PERSON><PERSON><PERSON>", "settings": [{"type": "header", "content": "Checkout Ambassador Initialization"}, {"type": "checkbox", "id": "roark_enable_ambassador", "label": "Enable Refer a Friend", "default": false}, {"type": "text", "id": "roark_ambassador_uid", "label": "UID for Ambassador", "info": "Unique ID for Ambassador"}, {"type": "header", "content": "Checkout Refund Policy URL"}, {"type": "header", "content": "Checkout VIP Code Re-Direct"}, {"type": "checkbox", "id": "roark_vip_redirect_modal_enabled", "label": "Enable Redirect Modal"}, {"type": "textarea", "id": "roark_vip_startswith_array", "label": "Starts With Syntax", "info": "For Example: PD1, DF1"}, {"type": "url", "id": "roark_vip_redirect_url", "label": "Goto url", "info": "route the user to vip site"}, {"type": "text", "id": "roark_vip_modal_heading", "label": "<PERSON><PERSON> Heading"}, {"type": "html", "id": "roark_vip_modal_paragraph", "label": "Modal Paragraph", "info": "okay to use html inside like <a>"}, {"type": "color", "id": "roark_vip_modal_button_color", "label": "Button Color"}, {"type": "color", "id": "roark_vip_modal_text_color", "label": "Text Color"}, {"type": "header", "content": "Checkout Site Mode"}, {"type": "radio", "id": "roark_site_mode", "label": "Site Mode", "info": "Standard Site or VIP Site", "default": "standard", "options": [{"value": "standard", "label": "Standard Site"}, {"value": "vip", "label": "VIP Site"}]}, {"type": "header", "content": "Checkout Exponea"}, {"type": "textarea", "id": "roark_exponea_consents", "label": "Consent JSON", "info": "{action: 'accept', category: 'news-and-offers', valid_until: 'unlimited'}", "default": "{action: 'accept', category: 'news-and-offers', valid_until: 'unlimited'}, {action: 'accept', category: 'cart-notifications', valid_until: 'unlimited'}, {action: 'accept', category: 'back-in-stock', valid_until: 'unlimited'}, {action: 'accept', category: 'surveys', valid_until: 'unlimited'}"}, {"type": "textarea", "id": "roark_sms_exponea_consents", "label": "SMS Consent JSON", "info": "{action: 'accept', category: 'news-and-offers', valid_until: 'unlimited'}", "default": "{action: 'accept', category: 'news-and-offers', valid_until: 'unlimited'}, {action: 'accept', category: 'cart-notifications', valid_until: 'unlimited'}, {action: 'accept', category: 'back-in-stock', valid_until: 'unlimited'}, {action: 'accept', category: 'surveys', valid_until: 'unlimited'}"}, {"type": "header", "content": "Checkout Discount Plus"}, {"type": "checkbox", "id": "roark_plus_show_discount_enable", "label": "enable discount Plus", "default": true}, {"type": "header", "content": "Checkout Newsletter"}, {"type": "checkbox", "id": "roark_cn_modal_enabled", "label": "Enable Newsletter Banner"}, {"type": "text", "id": "roark_cn_superscript", "label": "Superscript Text", "info": "The text that is above heading text"}, {"type": "text", "id": "roark_cn_heading", "label": "Heading Text", "info": "Free 2-Day Shipping"}, {"type": "textarea", "id": "roark_cn_description", "label": "Description Text", "info": "Descripive text under heading"}, {"type": "color", "id": "roark_cn_background_color", "label": "Background fill color"}, {"type": "image_picker", "id": "roark_cn_icon", "label": "Banner Icon", "info": "svg or png image"}, {"type": "color", "id": "roark_cn_button_color", "label": "Button Color"}, {"type": "color", "id": "roark_cn_btn_text_color", "label": "Button Text Color"}, {"type": "text", "id": "roark_cn_btn_text", "label": "Button Text", "info": "Join Now"}, {"type": "header", "content": "Address Validation"}, {"type": "checkbox", "id": "roark_address_validation_enable", "label": "Enable Address Validation"}, {"type": "header", "content": "Estimated Shipping Dates"}, {"type": "checkbox", "id": "roark_estimated_shipping_enabled", "label": "Enable", "default": true}, {"type": "text", "id": "roark_estimated_shipping_delay", "label": "Global Shipping Delay", "info": "in days"}, {"type": "text", "id": "roark_cutoff_time", "label": "Cutoff Time", "placeholder": "11", "info": "24-hr format. ex: 16 = 4 PM"}, {"type": "textarea", "id": "roark_blackout_dates", "label": "Blackout Dates", "placeholder": "1/1/19,4/21/19", "info": "csv dates: '05/21/19' format"}, {"type": "text", "id": "roark_exclude_states", "label": "Exclude Hide Rate", "placeholder": "'HI', 'AK', 'PR', 'VI', 'GU'", "info": "Exclude States from Expedited Hide Function", "default": "'HI', 'AK', 'PR', 'VI', 'GU'"}, {"type": "textarea", "id": "roark_shipping_text_message", "label": "Shipping Method Description", "placeholder": "We do not ship or deliver orders on weekends or major holidays.", "info": "paragraph displayed above the shipping options"}, {"type": "textarea", "id": "roark_shipping_address_notice", "label": "Shipping Address Notice", "info": "Paragraph displayed above the shipping address fields"}, {"type": "text", "id": "roark_estimated_text_space", "label": "Est. Date Placement L/R", "placeholder": "45", "info": "value in px ex: 45"}, {"type": "textarea", "id": "roark_shipping_text_loading_message", "label": "Preload Message", "placeholder": "Est. Arrival: Checking with the locals. BRB", "info": "displayed when calling api dates"}, {"type": "html", "id": "roark_shipping_map", "label": "Shipping Map", "info": "JSON template only"}, {"type": "header", "content": "Enable/Disable Pixels in checkout"}, {"type": "text", "id": "roark_gtm_container_id", "label": "Google Tag Manager ID"}, {"type": "checkbox", "id": "path_2_response_enable", "label": "Path 2 Repsonse", "info": "Enable Path 2 Response", "default": false}, {"type": "text", "id": "roark_google_optimize_id", "label": "Google Optimize ID"}, {"type": "checkbox", "id": "roark_spotify_pixel_enable", "label": "Enable Spotify Pixel", "default": false}, {"type": "header", "content": "Sms Legal consent copy"}, {"type": "checkbox", "id": "roark_enable_consent_checkout", "label": "Checkout Page"}, {"id": "roark_sms_legal_consent_copy1", "info": "Legal copy Header that shows next to checkbox for SMS consent in bold text", "label": "SMS Consent Copy <PERSON>er", "type": "textarea"}, {"id": "roark_sms_legal_consent_copy2", "info": "Legal copy Body that shows next to checkbox for SMS consent in regular text", "label": "SMS Consent Copy Body", "type": "richtext"}]}]