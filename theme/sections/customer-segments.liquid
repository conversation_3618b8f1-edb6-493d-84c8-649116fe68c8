{% schema %}
{
    "name": "User Segments",
    "tag": "section",
    "settings": [],
    "blocks":[
        {
            "type": "segment",
            "name": "Segment",
            "settings": [
                {
                    "type":"text",
                    "id":"key",
                    "label":"Key"
                },
                {
                    "type":"liquid",
                    "id":"conditions",
                    "label":"Conditions"
                },
                {
                    "type":"select",
                    "id":"assignment",
                    "label":"Assignment",
                    "options": [
                        {
                            "label":"Single Value",
                            "value":"single"
                        },
                        {
                            "label":"Multiple Values",
                            "value":"multiple"
                        },
                        {
                            "label":"Random Value",
                            "value":"random"
                        }
                    ]
                },
                {
                    "type":"liquid",
                    "id":"value",
                    "label":"Value"
                },
                {
                    "type":"checkbox",
                    "id":"reload",
                    "label":"Force Refresh or Redirect"
                },
                {
                    "type":"url",
                    "id":"redirect",
                    "label":"Redirect",
                    "info":"Leave blank for refresh"
                }
            ]
        }
    ]
}
{% endschema %}

<!-- Customer segments -->
{% liquid 

    assign _segments = ''
    assign reload = false
    assign redirect = ''

    for block in section.blocks

        unless request.design_mode

            if block.settings.conditions != blank

                assign value = ''

                case block.settings.assignment

                    when 'single'

                        assign value = block.settings.value

                    when 'multiple'

                        assign value = block.settings.value | split: ','

                    when 'random'

                        assign pool = block.settings.value | split: ','
                        assign max  = pool.size
                        assign index = "now" | date: "%N" | modulo: max
                        assign value = pool[index]

                endcase

                if value != blank 

                    unless cart.attributes.segments[block.settings.key] == value

                        assign key = block.settings.key | json 
                        assign val = value | json
                        assign _segments = _segments | append: ',' | append: key | append: ':' | append: val 

                        if block.settings.reload
                            assign reload = true
                            if block.settings.redirect
                                assign redirect = block.settings.redirect
                            endif
                        endif

                    endunless

                endif

            endif

        endunless

    endfor 

    if _segments != blank 
        assign segments = _segments | remove_first: ',' | prepend: '{' | append: '}'
    endif

%}
{% if segments != blank %}
<script>
    document.addEventListener('DOMContentLoaded', () => {
        Promise.allSettled(
            Object.entries({{ segments }}).map(([key, value]) => 
                Customer.segment(key, value)
            )
        ).then(results => {
            const failures = results.filter(r => r.status === 'rejected');
            if (failures.length) {
                console.error('Failed to set segments:', failures);
            }
            if (results.some(r => r.status === 'fulfilled')) {
                {% if reload %}
                    {% if redirect %}
                        window.location = {{ redirect | json }}
                    {% else %}
                        window.location.reload()
                    {% endif %}
                {% endif %}
            }
        });
    });
</script>
{% if reload %}<style>body {display:none!important}</style>{% endif %}
{% endif %}