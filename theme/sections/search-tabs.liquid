{% liquid
  
  paginate search.results by 2000
    assign product_count = 0
    assign article_count = 0
    assign page_count = 0

    for result in search.results
      case result.object_type
        when 'product'
          assign product_count = product_count | plus: 1
        when 'article'
          assign article_count = article_count | plus: 1
        when 'page'
          assign page_count = page_count | plus: 1
      endcase
    endfor
  endpaginate 

%}

<nav class="container bg-white lg:flex lg:justify-start tabs">
  <ul class="list flex tabs start">
    
    {% if product_count > 0 %}
    <li>
      <a data-search-tab="products" class="tab-title block cursor-pointer" onclick='
        _n.qs(`.shopify-section[id*="__filters"]`).style.display=`block`;
        _n.qs(`.shopify-section[id*="__search-product-grid"]`).style.display=`block`;
        _n.qs(`.shopify-section[id*="__search-articles"]`).style.display=`none`;
        _n.qs(`.shopify-section[id*="__search-pages"]`).style.display=`none`;
        '>
        Products <span class="ml-1 inline-block hidden">({{ product_count }})</span>
      </a>
    </li>
    {% endif %}
    
    {% if article_count > 0 %}
    <li>
      <a data-search-tab="articles" class="tab-title block cursor-pointer" onclick='
        _n.qs(`.shopify-section[id*="__filters"]`).style.display=`none`;
        _n.qs(`.shopify-section[id*="__search-product-grid"]`).style.display=`none`;
        _n.qs(`.shopify-section[id*="__search-articles"]`).style.display=`block`;
        _n.qs(`.shopify-section[id*="__search-pages"]`).style.display=`none`;
        '>
        Articles <span class="ml-1 inline-block hidden">({{ article_count }})</span>
      </a>
    </li>
    {% endif %}
    
    {% if page_count > 0 %}
    <li>
      <a data-search-tab="pages" class="tab-title block cursor-pointer" onclick='
        _n.qs(`.shopify-section[id*="__filters"]`).style.display=`none`;
        _n.qs(`.shopify-section[id*="__search-product-grid"]`).style.display=`none`;
        _n.qs(`.shopify-section[id*="__search-articles"]`).style.display=`none`;
        _n.qs(`.shopify-section[id*="__search-pages"]`).style.display=`block`;
        '>
        Pages <span class="ml-1 inline-block hidden">({{ page_count }})</span>
      </a>
    </li>
    {% endif %}
    
  </ul>
</nav>

{% style %}

  .shopify-section[id*="__search-no-results"] { display:none; }
  
{% if product_count == 0 %}
  .shopify-section[id*="__search-product-grid"] { display:none; }
  .shopify-section[id*="__filters"] { display:none; }
{% endif %}

{% if product_count > 0 %}
  .shopify-section[id*="__search-articles"] { display:none; }
{% endif %}

{% if product_count > 0 or article_count > 0 or page_count == 0 %}
  .shopify-section[id*="__search-pages"] { display:none; }
{% endif %}

{% if article_count == 0 %}
  .shopify-section[id*="__search-articles"] { display:none; }
{% endif %}

{% if article_count == 0 and product_count == 0 and page_count == 0 %}
  .shopify-section[id*="__search-no-results"] { display:block; }
{% endif %}


{% endstyle %}

<script type="text/javascript">
  window.addEventListener('DOMContentLoaded', function(){
    _n.qs(`[data-search-tab="${_n.urlparams.get('tab')}"]`)?.click()
    Collection.init()
  })
</script>

{% schema %}
  {
    "name": "Search Tabs",
    "settings": [
    ],
    "blocks": [
    ]
  }
{% endschema %}