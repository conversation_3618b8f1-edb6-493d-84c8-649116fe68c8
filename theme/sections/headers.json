{"type": "header", "name": "Headers", "sections": {"flex_yWBg34": {"type": "flex", "blocks": {"frame_67Ct4x": {"type": "frame", "settings": {"title": "⤷ Announcement Banner", "article_class_visibility": "", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "w-full", "article_class_width_desktop": "lg:w-1/3", "article_class_height": "h-auto", "article_class_height_desktop": "lg:h-auto", "article_style_background_color": "rgba(0,0,0,0)", "article_style_background": "", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-row", "article_class_layout": "layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "@include Spacing prop:py", "article_class_horizontal_padding": "px-0", "article_class_gap": "@include Spacing prop:gap", "article_class_vertical_padding_desktop": "lg:py-0", "article_class_horizontal_padding_desktop": "lg:px-0", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "rounded-md overflow-hidden pointer-events-auto {% if request.page_type=='product' %}hidden lg:hidden{% endif %}", "article_attr_x_data": "", "article_attr_x_if": ""}}, "image_G9wxPk": {"type": "image", "disabled": true, "settings": {"image": "shopify://shop_images/topography_light.png", "media_class_position": "absolute inset-0 h-full w-full object-cover", "media_class_custom": ""}}, "announcements_X3cHQm": {"type": "announcements", "settings": {"title": "⤷ Announcements", "column_class_width": "w-full", "column_class_width_desktop": "lg:w-full", "column_classes": "", "interval": 3}}, "announcement_94nRKt": {"type": "announcement", "settings": {"inclusion": "1", "inclusion_js": "", "text": "Free shipping on Orders over $100", "text_desktop": "", "svg": "", "image_size": "", "link": ""}}, "announcement_p6kiQB": {"type": "announcement", "settings": {"inclusion": "1", "inclusion_js": "", "text": "Free shipping on Orders over $100", "text_desktop": "", "svg": "", "image_size": "", "link": ""}}, "announcements_end_Kkqnkg": {"type": "announcements-end", "settings": {"title": "⤶ Announcements End"}}}, "block_order": ["frame_67Ct4x", "image_G9wxPk", "announcements_X3cHQm", "announcement_94nRKt", "announcement_p6kiQB", "announcements_end_Kkqnkg"], "custom_css": ["{position: absolute; top: 0; left: 0; width: 100%; pointer-events: none; z-index: 41;}"], "settings": {"wrapper_style_background_color": "", "wrapper_style_background": "", "container_class_container": "w-full", "section_class_height": "h-auto", "section_class_height_desktop": "lg:h-auto", "section_class_display": "flex", "section_class_display_desktop": "lg:flex", "section_class_direction": "flex-row", "section_class_layout": "layout-top", "section_class_layout_spacing": "layout-space-packed", "section_class_vertical_padding": "py-3xl", "section_class_horizontal_padding": "px-lg", "section_class_gap": "@include Spacing prop:gap", "section_class_vertical_padding_desktop": "lg:py-sm", "section_class_horizontal_padding_desktop": "lg:px-sm", "section_class_gap_desktop": "@include SpacingDesktop prop:gap"}}, "flex_MFHj8P": {"type": "flex", "blocks": {"liquid_DpNkwm": {"type": "liquid", "settings": {"title": "Logo", "liquid": "<a href=\"/\">\n<svg style=\"fill:#CCC;   filter: contrast(10); pointer-events:auto;\" xmlns=\"http://www.w3.org/2000/svg\" width=\"96px\" height=\"36px\" viewBox=\"0 0 399.331 103.9\">\n  <g id=\"logo\" transform=\"translate(-1.2 -1)\">\n    <g id=\"Group_1\" data-name=\"Group 1\">\n      <path id=\"Path_1\" data-name=\"Path 1\" d=\"M15.5,14.8c5.6,0,13.7-.3,18.8,6,2.5,3.1,4,7.7,4,13.4V40a19.647,19.647,0,0,1-4,12.1A13.569,13.569,0,0,1,28.5,56l12,35.3H29.2l-10.6-33H12.1v33H1.2V14.8ZM12.1,48.5h6.2A9.191,9.191,0,0,0,25.1,46c2.2-2.1,2.3-3.9,2.3-6.9V33.6a10.167,10.167,0,0,0-2.5-7c-2.2-2.2-4.8-2-7.1-2H12.1V48.5Z\"></path>\n      <path id=\"Path_2\" data-name=\"Path 2\" d=\"M58.7,34.9c0-5.8.4-11.4,5.7-16.3,3.3-3.1,7.8-4.9,13.5-4.9a19.5,19.5,0,0,1,13.5,4.9c5.3,4.9,5.7,10.5,5.7,16.3V71.2c0,5.8-.4,11.4-5.7,16.3-3.3,3.1-7.8,4.9-13.5,4.9a19.5,19.5,0,0,1-13.5-4.9c-5.3-4.9-5.7-10.5-5.7-16.3Zm27.4-1.6c0-2.3-.2-4.6-2.3-6.7a9.011,9.011,0,0,0-11.8,0c-2.1,2.1-2.3,4.4-2.3,6.7V72.9c0,2.3.2,4.6,2.3,6.7a9.011,9.011,0,0,0,11.8,0c2.1-2.1,2.3-4.4,2.3-6.7Z\"></path>\n      <path id=\"Path_3\" data-name=\"Path 3\" d=\"M146.2,91.4,142.6,75h-14L125,91.4H114.1l16.7-76.6h9.6l16.7,76.6ZM135.4,36.1l-5.7,29.1h11.6Z\"></path>\n      <path id=\"Path_4\" data-name=\"Path 4\" d=\"M191.1,14.8c5.6,0,13.7-.3,18.8,6,2.5,3.1,4,7.7,4,13.4V40a19.647,19.647,0,0,1-4,12.1,13.569,13.569,0,0,1-5.8,3.9l12,35.3H204.7l-10.6-33h-6.5v33H176.7V14.8Zm-3.4,33.7h6.2a9.191,9.191,0,0,0,6.8-2.5c2.2-2.1,2.3-3.9,2.3-6.9V33.6a10.167,10.167,0,0,0-2.5-7c-2.2-2.2-4.8-2-7.1-2h-5.7Z\"></path>\n      <path id=\"Path_5\" data-name=\"Path 5\" d=\"M246.6,14.8V51.7L264,14.8h11.5L260,46.2l18.4,45.2H266.5l-13-34.2L246.7,69V91.4H235.8V14.8Z\"></path>\n    </g>\n    <g id=\"Group_2\" data-name=\"Group 2\">\n      <path id=\"Path_6\" data-name=\"Path 6\" d=\"M345.4,104.9l-.1-37.5L313,104.9H302.2c-.8,0-1.8-.4-1.2-1.6.3-.3,19.7-22.7,39.1-45.2l15.4,17.3.1,29.5Z\"></path>\n      <circle id=\"Ellipse_1\" data-name=\"Ellipse 1\" cx=\"8.9\" cy=\"8.9\" r=\"8.9\" transform=\"translate(304 42.2)\"></circle>\n      <circle id=\"Ellipse_2\" data-name=\"Ellipse 2\" cx=\"8.9\" cy=\"8.9\" r=\"8.9\" transform=\"translate(380 41.9)\"></circle>\n      <path id=\"Path_7\" data-name=\"Path 7\" d=\"M400.2,103.3c-.3-.4-22.2-25.7-43-49.8l.1-.1L320.2,10.5h25.2v21l16,17.2,38.7-45A1.611,1.611,0,0,0,399,1H302.4a1.645,1.645,0,0,0-1.1,2.7l87,101.1h10.8C399.8,104.8,400.7,104.4,400.2,103.3ZM381.1,10.5s-11.4,13.2-25.5,29.6V10.6Z\"></path>\n    </g>\n  </g>\n</svg></a>"}}, "liquid_AXDEwy": {"type": "liquid", "settings": {"title": "Left Placement", "liquid": "{% if request.page_type=='product' %}\n<style>\n@media(min-width:1025px){\n#shopify-section-{{ section.id }} {width:auto!important;}\n}\n</style>\n{% endif %}"}}}, "block_order": ["liquid_DpNkwm", "liquid_AXDEwy"], "custom_css": ["{position: fixed; width: 100%; z-index: 42; pointer-events: none; mix-blend-mode: difference;}"], "settings": {"wrapper_style_background_color": "", "wrapper_style_background": "", "container_class_container": "w-full", "section_class_height": "h-auto", "section_class_height_desktop": "lg:h-auto", "section_class_display": "flex", "section_class_display_desktop": "lg:flex", "section_class_direction": "flex-col", "section_class_layout": "layout-center layout-top", "section_class_layout_spacing": "layout-space-packed", "section_class_vertical_padding": "py-sm", "section_class_horizontal_padding": "px-xl", "section_class_gap": "@include Spacing prop:gap", "section_class_vertical_padding_desktop": "lg:py-lg", "section_class_horizontal_padding_desktop": "lg:px-2xl", "section_class_gap_desktop": "@include SpacingDesktop prop:gap"}}, "flex_LDzP8k": {"type": "flex", "blocks": {"liquid_qTCdMe": {"type": "liquid", "settings": {"title": "Menus", "liquid": "<style>\n.menu-lists > ul {display: none;}.menus[data-active-menu=\"mens\"] .menu-lists > ul.link-list--mens {\n  display: flex;\n}.menus[data-active-menu=\"womens\"] .menu-lists > ul.link-list--womens {\n  display: flex;\n}.menus[data-active-menu=\"run\"] .menu-lists > ul.link-list--run {\n  display: flex;\n}.menus[data-active-menu=\"explore\"] .menu-lists > ul.link-list--explore {\n  display: flex;\n}.menus[data-active-menu=\"main\"] .menu-lists > ul.link-list--main {\n  display: flex;\n}\n</style>"}}, "frame_YFF9hp": {"type": "frame", "settings": {"title": "⤷ Container", "article_class_visibility": "", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "w-full", "article_class_width_desktop": "lg:w-2/5", "article_class_height": "h-dvh", "article_class_height_desktop": "lg:h-screen", "article_style_background_color": "", "article_style_background": "", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-col", "article_class_layout": "layout-bottom", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "py-sm", "article_class_horizontal_padding": "px-sm", "article_class_gap": "gap-sm", "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "lg:self-end lg:flex-col-reverse rounded-buttons frosty-bottom", "article_attr_x_data": "", "article_attr_x_if": ""}}, "frame_PyPFpV": {"type": "frame", "settings": {"title": "⤷ Menus", "article_class_visibility": "", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "w-full", "article_class_width_desktop": "lg:w-full", "article_class_height": "h-full", "article_class_height_desktop": "lg:h-full", "article_style_background_color": "rgba(0,0,0,0)", "article_style_background": "", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-row", "article_class_layout": "layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "@include Spacing prop:py", "article_class_horizontal_padding": "@include Spacing prop:px", "article_class_gap": "@include Spacing prop:gap", "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "", "article_attr_x_data": "", "article_attr_x_if": ""}}, "frame_HLnrbf": {"type": "frame", "settings": {"title": "⤷ Frame", "article_class_visibility": "", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "w-full", "article_class_width_desktop": "lg:w-full", "article_class_height": "h-full", "article_class_height_desktop": "lg:h-full", "article_style_background_color": "#ffffff", "article_style_background": "", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-col", "article_class_layout": "layout-middle", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "py-3xl", "article_class_horizontal_padding": "px-0", "article_class_gap": "@include Spacing prop:gap", "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "article_class_horizontal_padding_desktop": "lg:px-0", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "menus rounded-md pointer-events-auto relative", "article_attr_x_data": "", "article_attr_x_if": ""}}, "spacer_3F93Kx": {"type": "spacer", "settings": {"spacer_class_vertical_spacing": "pb-2xl", "spacer_class_horizontal_spacing": "@include Spacing prop:pr", "spacer_class_vertical_spacing_desktop": "lg:pb-0", "spacer_class_horizontal_spacing_desktop": "@include SpacingDesktop prop:pr"}}, "frame_DUCbNU": {"type": "frame", "settings": {"title": "⤷ Frame", "article_class_visibility": "", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "w-full", "article_class_width_desktop": "lg:w-full", "article_class_height": "h-auto", "article_class_height_desktop": "lg:h-auto", "article_style_background_color": "", "article_style_background": "", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-col", "article_class_layout": "layout-left layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "@include Spacing prop:py", "article_class_horizontal_padding": "@include Spacing prop:px", "article_class_gap": "@include Spacing prop:gap", "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "", "article_class_overflow": "", "article_class_custom_classes": "menu-lists", "article_attr_x_data": "", "article_attr_x_if": ""}}, "linklist_eUV7dG": {"type": "linklist", "settings": {"link_list": "main", "linklist_class_format": "link-list--collapsable link-list--panels", "link_style_color": "#000000", "linklist_class_display": "flex", "linklist_class_display_desktop": "lg:flex", "linklist_class_direction": "flex-col", "linklist_class_layout": "layout-middle", "linklist_class_layout_spacing": "layout-space-packed", "linklist_class_vertical_padding": "@include Spacing prop:py", "linklist_class_horizontal_padding": "px-2xl", "linklist_class_gap": "@include Spacing prop:gap", "linklist_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "linklist_class_horizontal_padding_desktop": "lg:px-2xl", "linklist_class_gap_desktop": "@include SpacingDesktop prop:gap"}}, "linklist_VKHKap": {"type": "linklist", "settings": {"link_list": "mens", "linklist_class_format": "link-list--collapsable link-list--panels", "link_style_color": "#000000", "linklist_class_display": "flex", "linklist_class_display_desktop": "lg:flex", "linklist_class_direction": "flex-col", "linklist_class_layout": "layout-middle", "linklist_class_layout_spacing": "layout-space-packed", "linklist_class_vertical_padding": "@include Spacing prop:py", "linklist_class_horizontal_padding": "px-2xl", "linklist_class_gap": "@include Spacing prop:gap", "linklist_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "linklist_class_horizontal_padding_desktop": "lg:px-2xl", "linklist_class_gap_desktop": "@include SpacingDesktop prop:gap"}}, "break_rcaNBz": {"type": "break", "settings": {"title": "⤶ Frame End"}}, "linklist_LUftDN": {"type": "linklist", "settings": {"link_list": "footer-customer-service", "linklist_class_format": "link-list--collapsable link-list--panels", "link_style_color": "#aeaeae", "linklist_class_display": "flex", "linklist_class_display_desktop": "lg:flex", "linklist_class_direction": "flex-col", "linklist_class_layout": "layout-middle", "linklist_class_layout_spacing": "layout-space-packed", "linklist_class_vertical_padding": "py-md", "linklist_class_horizontal_padding": "px-2xl", "linklist_class_gap": "@include Spacing prop:gap", "linklist_class_vertical_padding_desktop": "lg:py-md", "linklist_class_horizontal_padding_desktop": "lg:px-2xl", "linklist_class_gap_desktop": "@include SpacingDesktop prop:gap"}}, "break_YNacKH": {"type": "break", "settings": {"title": "⤶ Frame Break"}}, "break_RAEGnh": {"type": "break", "settings": {"title": "⤶ Menus End"}}, "frame_Mtj7rW": {"type": "frame", "settings": {"title": "⤷ Nav Bar", "article_class_visibility": "", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "w-auto", "article_class_width_desktop": "lg:w-full", "article_class_height": "h-auto", "article_class_height_desktop": "lg:h-auto", "article_style_background_color": "rgba(0,0,0,0)", "article_style_background": "", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-row", "article_class_layout": "layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "py-0", "article_class_horizontal_padding": "px-0", "article_class_gap": "gap-xs", "article_class_vertical_padding_desktop": "lg:py-0", "article_class_horizontal_padding_desktop": "lg:px-0", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "pointer-events-auto relative group navbar", "article_attr_x_data": "", "article_attr_x_if": ""}}, "frame_YE3PnF": {"type": "frame", "settings": {"title": "⤷ Green", "article_class_visibility": "", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "w-auto", "article_class_width_desktop": "lg:w-auto", "article_class_height": "h-full", "article_class_height_desktop": "lg:h-full", "article_style_background_color": "#3f4b40", "article_style_background": "", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-row", "article_class_layout": "layout-center layout-middle", "article_class_layout_spacing": "layout-space-between", "article_class_vertical_padding": "py-0", "article_class_horizontal_padding": "@include Spacing prop:px", "article_class_gap": "@include Spacing prop:gap", "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "article_class_horizontal_padding_desktop": "lg:px-xl", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "rounded-md text-white main-navbar shrink grow", "article_attr_x_data": "", "article_attr_x_if": ""}}, "frame_mQ6z4n": {"type": "frame", "settings": {"title": "⤷ Mobile", "article_class_visibility": "lg:hidden", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "w-auto", "article_class_width_desktop": "lg:w-auto", "article_class_height": "h-auto", "article_class_height_desktop": "lg:h-auto", "article_style_background_color": "", "article_style_background": "", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-row", "article_class_layout": "layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "@include Spacing prop:py", "article_class_horizontal_padding": "@include Spacing prop:px", "article_class_gap": "@include Spacing prop:gap", "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "", "article_attr_x_data": "", "article_attr_x_if": ""}}, "button_D6qgfz": {"type": "button", "settings": {"style": "button--primary", "button_text": "", "leading_icon": "hamburger", "trailing_icon": "", "link": "", "onclick": "Menu.toggle('main'); document.querySelector('.navbar').classList.toggle('active')", "form_validity": false, "button_class_custom_classes": "group-active:hidden", "button_attr_x_data": "", "button_attr_x_show": "", "button_attr_x_text": "", "button_attr_QQclass": "", "disabled": false}}, "button_Fp7LtX": {"type": "button", "settings": {"style": "button--primary", "button_text": "", "leading_icon": "arrow-left", "trailing_icon": "", "link": "", "onclick": "Menu.toggle('main'); document.querySelector('.navbar').classList.toggle('active')", "form_validity": false, "button_class_custom_classes": "hidden group-active:block", "button_attr_x_data": "", "button_attr_x_show": "", "button_attr_x_text": "", "button_attr_QQclass": "", "disabled": false}}, "break_9X3bUd": {"type": "break", "settings": {"title": "⤶ Mobile End"}}, "frame_rwHDDA": {"type": "frame", "settings": {"title": "⤷ Desktop", "article_class_visibility": "max-lg:hidden", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "w-full", "article_class_width_desktop": "lg:w-full", "article_class_height": "h-auto", "article_class_height_desktop": "lg:h-auto", "article_style_background_color": "", "article_style_background": "", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-row", "article_class_layout": "layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "@include Spacing prop:py", "article_class_horizontal_padding": "@include Spacing prop:px", "article_class_gap": "@include Spacing prop:gap", "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "", "article_attr_x_data": "", "article_attr_x_if": ""}}, "button_AE8G7Q": {"type": "button", "settings": {"style": "@include ButtonStyle", "button_text": "Mens", "leading_icon": "", "trailing_icon": "", "link": "", "onclick": "Menu.toggle('mens')", "form_validity": false, "button_class_custom_classes": "", "button_attr_x_data": "", "button_attr_x_show": "", "button_attr_x_text": "", "button_attr_QQclass": "", "disabled": false}}, "button_K9QMtW": {"type": "button", "settings": {"style": "@include ButtonStyle", "button_text": "Womens", "leading_icon": "", "trailing_icon": "", "link": "", "onclick": "Menu.toggle('womens')", "form_validity": false, "button_class_custom_classes": "", "button_attr_x_data": "", "button_attr_x_show": "", "button_attr_x_text": "", "button_attr_QQclass": "", "disabled": false}}, "button_xfkMnq": {"type": "button", "settings": {"style": "@include ButtonStyle", "button_text": "Run Amok", "leading_icon": "", "trailing_icon": "", "link": "", "onclick": "<PERSON>u.toggle('run')", "form_validity": false, "button_class_custom_classes": "", "button_attr_x_data": "", "button_attr_x_show": "", "button_attr_x_text": "", "button_attr_QQclass": "", "disabled": false}}, "button_yaHmyM": {"type": "button", "settings": {"style": "@include ButtonStyle", "button_text": "Explore", "leading_icon": "", "trailing_icon": "", "link": "", "onclick": "<PERSON>u.toggle('explore')", "form_validity": false, "button_class_custom_classes": "", "button_attr_x_data": "", "button_attr_x_show": "", "button_attr_x_text": "", "button_attr_QQclass": "", "disabled": false}}, "break_V7BHQJ": {"type": "break", "settings": {"title": "⤶ Desktop End"}}, "frame_rRymiF": {"type": "frame", "settings": {"title": "⤷ Tools", "article_class_visibility": "", "inclusion_liquid": "", "inclusion_js": "", "article_class_width": "w-auto", "article_class_width_desktop": "lg:w-auto", "article_class_height": "h-auto", "article_class_height_desktop": "lg:h-auto", "article_style_background_color": "", "article_style_background": "", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-row", "article_class_layout": "layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "@include Spacing prop:py", "article_class_horizontal_padding": "px-md", "article_class_gap": "@include Spacing prop:gap", "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "{% if request.page_type=='product' %}hidden{% endif %}\ngroup-active:flex", "article_attr_x_data": "", "article_attr_x_if": ""}}, "nav_tools_8r7UXJ": {"type": "nav-tools", "settings": {"column_classes": "", "button_classes": "items-center ml-4 text-xs", "search": true, "wishlist": false, "account": true, "geo": false, "cart": true, "icon_size": 20, "display": "icons", "greeting": ""}}, "break_iB9aNH": {"type": "break", "settings": {"title": "⤶ Tools End"}}, "break_xqLjwN": {"type": "break", "settings": {"title": "⤶ Green End"}}, "frame_MDkpiz": {"type": "frame", "settings": {"title": "⤷ PDP", "article_class_visibility": "lg:hidden", "inclusion_liquid": "{% if request.page_type=='product' %}1{% endif %}", "inclusion_js": "", "article_class_width": "w-full", "article_class_width_desktop": "lg:w-full", "article_class_height": "h-auto", "article_class_height_desktop": "lg:h-auto", "article_style_background_color": "", "article_style_background": "", "article_class_display": "grid grid-cols-1", "article_class_display_desktop": "lg:grid lg:grid-cols-1", "article_class_direction": "flex-row", "article_class_layout": "layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "@include Spacing prop:py", "article_class_horizontal_padding": "px-0", "article_class_gap": "@include Spacing prop:gap", "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "grow group-active:hidden", "article_attr_x_data": "", "article_attr_x_if": ""}}, "product_actions_kj3BM3": {"type": "product_actions", "settings": {"sticky_block_class_layout": "layout-left"}}, "button_wxNzaK": {"type": "button", "settings": {"style": "button--dark", "button_text": "Sold Out", "leading_icon": "", "trailing_icon": "", "link": "", "onclick": "", "form_validity": false, "button_class_custom_classes": "button--disabled", "button_attr_x_data": "", "button_attr_x_show": "product.variant && \n!product.variant.available && \nproduct.selected_options.length == product.options.length", "button_attr_x_text": "", "button_attr_QQclass": "", "disabled": true}}, "button_Wz4Td7": {"type": "button", "settings": {"style": "button--dark", "button_text": "Please Select Options", "leading_icon": "", "trailing_icon": "", "link": "", "onclick": "", "form_validity": false, "button_class_custom_classes": "button--disabled", "button_attr_x_data": "", "button_attr_x_show": "!product.variant && product.selected_options.length < product.options.length", "button_attr_x_text": "", "button_attr_QQclass": "", "disabled": true}}, "button_py7cej": {"type": "button", "settings": {"style": "button--dark", "button_text": "Add to Cart", "leading_icon": "", "trailing_icon": "", "link": "", "onclick": "Cart.add({id:variant.id,_compare_at_price:variant.compare_at_price,_upc:variant.upc,_source:'PDP'})", "form_validity": false, "button_class_custom_classes": "", "button_attr_x_data": "", "button_attr_x_show": "product.variant && \nproduct.variant.available && \nproduct.selected_options.length == product.options.length", "button_attr_x_text": "", "button_attr_QQclass": "", "disabled": false}}, "product_actions_close_WULkrP": {"type": "product_actions_close", "settings": {}}, "break_cHTgDV": {"type": "break", "settings": {"title": "⤶ PDP End"}}, "frame_K7VUVY": {"type": "frame", "settings": {"title": "⤷ PLP", "article_class_visibility": "lg:hidden", "inclusion_liquid": "{% if request.page_type=='collection' %}1{% endif %}", "inclusion_js": "", "article_class_width": "w-full", "article_class_width_desktop": "lg:w-full", "article_class_height": "h-auto", "article_class_height_desktop": "lg:h-auto", "article_style_background_color": "", "article_style_background": "", "article_class_display": "grid grid-cols-1", "article_class_display_desktop": "lg:grid lg:grid-cols-1", "article_class_direction": "flex-row", "article_class_layout": "layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "@include Spacing prop:py", "article_class_horizontal_padding": "px-0", "article_class_gap": "@include Spacing prop:gap", "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "grow group-active:hidden", "article_attr_x_data": "", "article_attr_x_if": ""}}, "button_AjLqVr": {"type": "button", "settings": {"style": "button--dark", "button_text": "Filter", "leading_icon": "", "trailing_icon": "", "link": "", "onclick": "Modal.toggle('filters')", "form_validity": false, "button_class_custom_classes": "", "button_attr_x_data": "", "button_attr_x_show": "", "button_attr_x_text": "", "button_attr_QQclass": "", "disabled": false}}, "break_zi6tqy": {"type": "break", "settings": {"title": "⤶ PLP End"}}, "break_39Agfg": {"type": "break", "settings": {"title": "⤶ Nav Bar End"}}, "break_bGVLwd": {"type": "break", "settings": {"title": "⤶ Container End"}}}, "block_order": ["liquid_qTCdMe", "frame_YFF9hp", "frame_PyPFpV", "frame_HLnrbf", "spacer_3F93Kx", "frame_DUCbNU", "linklist_eUV7dG", "linklist_VKHKap", "break_rcaNBz", "linklist_LUftDN", "break_YNacKH", "break_RAEGnh", "frame_Mtj7rW", "frame_YE3PnF", "frame_mQ6z4n", "button_D6qgfz", "button_Fp7LtX", "break_9X3bUd", "frame_rwHDDA", "button_AE8G7Q", "button_K9QMtW", "button_xfkMnq", "button_yaHmyM", "break_V7BHQJ", "frame_rRymiF", "nav_tools_8r7UXJ", "break_iB9aNH", "break_xqLjwN", "frame_MDkpiz", "product_actions_kj3BM3", "button_wxNzaK", "button_Wz4Td7", "button_py7cej", "product_actions_close_WULkrP", "break_cHTgDV", "frame_K7VUVY", "button_AjLqVr", "break_zi6tqy", "break_39Agfg", "break_bGVLwd"], "custom_css": ["{position: fixed; inset: 0; pointer-events: none; z-index: 40;}", ".pointer-events-auto {pointer-events: auto;}", ".menus {display: none;}", ".menus[data-active-menu] {display: flex;}"], "settings": {"wrapper_style_background_color": "rgba(0,0,0,0)", "wrapper_style_background": "", "container_class_container": "w-full", "section_class_height": "h-auto", "section_class_height_desktop": "lg:h-auto", "section_class_display": "flex", "section_class_display_desktop": "lg:flex", "section_class_direction": "flex-col", "section_class_layout": "layout-top", "section_class_layout_spacing": "layout-space-between", "section_class_vertical_padding": "@include Spacing prop:py", "section_class_horizontal_padding": "@include Spacing prop:px", "section_class_gap": "@include Spacing prop:gap", "section_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "section_class_horizontal_padding_desktop": "lg:px-0", "section_class_gap_desktop": "@include SpacingDesktop prop:gap"}}}, "order": ["flex_yWBg34", "flex_MFHj8P", "flex_LDzP8k"]}