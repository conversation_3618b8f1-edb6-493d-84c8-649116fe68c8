{{ 'product-finder.css' | asset_url | stylesheet_tag }}
{% include 'product-finder' %}
{{ 'product-finder.js' | asset_url | script_tag }}

{% schema %}
{
	"name": "Product Finder",
	"class": "product-finder__parent",
	"settings": [
		{
			"id": "tracking_id",
			"label": "Tracking ID",
			"type": "text",
			"info": "Enter tracking id e.g 'shoe_finder'"
		},
		{
			"id": "recommendation_header",
			"label": "Recommendation Header Text",
			"type": "text",
			"default": "We Recommend"
		},
		{
			"id": "recommendation_header_bg_color",
			"label": "Recommendation Header Background Color",
			"type": "color",
			"default": "#55c7b5"
		},
		{
			"id": "recommendation_header_text_color",
			"label": "Recommendation Header Text Color",
			"type": "color",
			"default": "#FFFFFF"
		},
		{
			"id": "back_to_results",
			"label": "Back Button",
			"type": "text",
			"default": "Back to Results"
		},
		{
			"id": "content_color",
			"type": "color",
			"label": "Content Color",
			"default": "#042C4B"
		},
		{
			"id": "no_results",
			"label": "No Results Message",
			"type": "text",
			"default": "No Recommended Products Available.",
			"info": "Leave blank for no message."
		},
		{
			"type": "paragraph",
			"content": "@include ProductItem"
		},
		{
			"type": "header",
			"content": "@global: Product Item Settings"
		},
		{
			"id": "product_item_settings",
			"label": "@global: Override Global Product Item Settings",
			"type": "checkbox"
		},
		{
			"type": "liquid",
			"id": "product_item_title_source",
			"label": "@global: Product Item Title Source",
			"default": "product.title.split('-')[0]"
		},
		{
			"type": "liquid",
			"id": "product_item_subtitle_source",
			"label": "@global: Product Item Subtitle Source",
			"default": "product.title.split('-')[1]"
		},
		{
			"type": "liquid",
			"id": "product_item_type_source",
			"label": "@global: Product Item Type Source",
			"default": "product.type"
		},
		{
			"type": "header",
			"content": "@global: Classes"
		},
		{
			"type": "text",
			"id": "classes_product_item",
			"label": "@global: Product Item"
		},
		{
			"type": "text",
			"id": "classes_product_item_image_wrapper",
			"label": "@global: Image"
		},
		{
			"type": "text",
			"id": "classes_product_item_title",
			"label": "@global: Product Item Title"
		},
		{
			"type": "text",
			"id": "classes_product_item_subtitle",
			"label": "@global: Product Item Subtitle"
		},
		{
			"type": "liquid",
			"id": "product_item_additional_liquid",
			"label": "@global: Additional Liquid"
		},
		{
			"type": "checkbox",
			"id": "product_item_show_title",
			"label": "@global: Show Title",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "product_item_show_subtitle",
			"label": "@global: Show Subtitle",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "product_item_show_type",
			"label": "@global: Show Product Type",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "product_item_show_price",
			"label": "@global: Show Price",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "product_item_show_reviews",
			"label": "@global: Show Review Stars",
			"default": true
		},
		{
			"type": "select",
			"id": "product_item_info_layout",
			"label": "@global: Info Layout",
			"options": [
				{
					"value": "",
					"label": "Column"
				},
				{
					"value": "flex flex-row justify-between",
					"label": "Spaced Row"
				}
			]
		},
		{
			"type": "select",
			"id": "product_item_quick_add_position_mobile",
			"label": "@global: Quick Add Button position (Mobile)",
			"default": "image",
			"options": [
				{
					"label": "Image Container",
					"value": "image"
				},
				{
					"label": "Info Container",
					"value": "info"
				}
			]
		},
		{
			"type": "select",
			"id": "product_item_quick_add_position_desktop",
			"label": "@global: Quick Add Button position (Desktop)",
			"default": "image",
			"options": [
				{
					"label": "Image Container",
					"value": "image"
				},
				{
					"label": "Info Container",
					"value": "info"
				}
			]
		},
		{
			"type": "select",
			"id": "product_item_show_button",
			"label": "@global: Show/Hide Button",
			"default": "hide",
			"options": [
				{
					"label": "Show",
					"value": "show"
				},
				{
					"label": "Hide",
					"value": "hide"
				}
			]
		},
		{
			"type": "checkbox",
			"id": "product_item_variant_selector",
			"label": "@global: Include Variant Form",
			"default": false
		},
		{
			"type": "checkbox",
			"id": "product_item_show_swatches",
			"label": "@global: Show Swatches"
		},
		{
			"type": "checkbox",
			"id": "product_item_show_oos_siblings",
			"label": "@global: Show Out of Stock Siblings",
			"default": false
		},
		{
			"type": "number",
			"id": "product_item_swatches_view",
			"label": "@global: Swatches Per View (Mobile)",
			"default": 3
		},
		{
			"type": "number",
			"id": "product_item_swatches_view_desktop",
			"label": "@global: Swatches Per View (Desktop)",
			"default": 5
		},
		{
			"type": "select",
			"id": "product_item_swatch_interaction",
			"label": "@global: User swatch interaction to update product item on desktop",
			"default": "click",
			"options": [
				{
					"label": "Hover",
					"value": "hover"
				},
				{
					"label": "Click",
					"value": "click"
				}
			]
		},
		{
			"type": "select",
			"id": "product_color_option_type",
			"label": "@global: Product Color Option Type",
			"default": "admin_setting",
			"options": [
				{
					"label": "Theme Setting",
					"value": "admin_setting"
				},
				{
					"label": "Image",
					"value": "image"
				},
				{
					"label": "Swatch Color",
					"value": "swatch"
				},
				{
					"label": "Swatch Image",
					"value": "swatch_image"
				}
			]
		}
	],
	"blocks": [
		{
			"type": "question",
			"name": "Question",
			"settings": [
				{
					"id": "title",
					"label": "Title",
					"type": "text",
					"info": "Appears in navigational elements"
				},
				{
					"id": "id",
					"label": "Tracking ID",
					"type": "text",
					"info": "Will be passed to Exponea tracking"
				},
				{
					"id": "question",
					"label": "Question",
					"type": "text"
				},
				{
					"id": "options",
					"label": "Options",
					"type": "textarea",
					"info": "Map of values, one per line with map of Label > Tag (constaints). \nExample:WOMEN > gender:women\nExample:5 (gender:women)"
				},
				{
					"type": "checkbox",
					"id": "constrain_to_available",
					"label": "Only Show Available",
					"default": true
				},
				{
					"type": "text",
					"id": "instruction",
					"label": "Selection Instruction",
					"default": "Please select one"
				},
				{
					"type": "text",
					"id": "increment",
					"label": "Selection Increment",
					"default": "Please select {{balance}} more"
				},
				{
					"type": "number",
					"id": "limit",
					"label": "Option Limit",
					"default": 1
				},
				{
					"type": "select",
					"id": "grid",
					"label": "Option Grid Layout",
					"default": "wide",
					"options": [
						{
							"value": "wide",
							"label": "Wide"
						},
						{
							"value": "compact",
							"label": "Compact"
						}
					]
				}
			]
		},
		{
			"type": "size_plus_calculation",
			"name": "Size + Calculation",
			"settings": [
				{
					"id": "title",
					"label": "Title",
					"type": "text",
					"info": "Appears in navigational elements"
				},
				{
					"id": "id",
					"label": "Tracking ID",
					"type": "text",
					"info": "Will be passed to Exponea tracking"
				},
				{
					"id": "question",
					"label": "Question",
					"type": "text"
				},
				{
					"id": "options",
					"label": "Options",
					"type": "textarea",
					"info": "Map of values, one per line with map of Label > Tag (constaints). \nExample:WOMEN > gender:women\nExample:5 (gender:women)"
				},
				{
					"type": "checkbox",
					"id": "constrain_to_available",
					"label": "Only Show Available",
					"default": true
				},
				{
					"type": "text",
					"id": "instruction",
					"label": "Selection Instruction",
					"default": "Please select one"
				},
				{
					"type": "text",
					"id": "increment",
					"label": "Selection Increment",
					"default": "Please select {{ balance }} more"
				},
				{
					"type": "number",
					"id": "limit",
					"label": "Option Limit",
					"default": 1
				},
				{
					"type": "select",
					"id": "grid",
					"label": "Option Grid Layout",
					"default": "wide",
					"options": [
						{
							"value": "wide",
							"label": "Wide"
						},
						{
							"value": "compact",
							"label": "Compact"
						}
					]
				},
				{
					"id": "style_class",
					"label": "Style Class",
					"type": "text",
					"default": "initial-finder-slide"
				},
				{
					"type": "header",
					"content": "Calculation"
				},
				{
					"id": "finding_fit_title",
					"label": "Title",
					"type": "text",
					"default": "Finding Your Perfect Fit"
				},
				{
					"id": "finding_fit_info",
					"label": "Info",
					"type": "textarea",
					"default": "To start you will need a soft measuring tape."
				},
				{
					"id": "cal_measurement",
					"label": "Measurements JSON",
					"type": "textarea",
					"info": "Give value in JSON format.\nExample: {'cm': [{'56-58': 'Small'},{'58.5-60': 'Classic'}],'in': [{'22-22.8': 'Small'}]}"
				}
			]
		},
		{
			"type": "question_icon",
			"name": "Question with icon",
			"settings": [
				{
					"id": "title",
					"label": "Title",
					"type": "text",
					"info": "Appears in navigational elements"
				},
				{
					"id": "id",
					"label": "Tracking ID",
					"type": "text",
					"info": "Will be passed to Exponea tracking"
				},
				{
					"id": "question",
					"label": "Question",
					"type": "text"
				},
				{
					"id": "options",
					"label": "Options",
					"type": "textarea",
					"info": "Map of values, one per line with map of Label > Tag (constaints). \nExample:WOMEN > gender:women\nExample:5 (gender:women)"
				},
				{
					"id": "option_icon1",
					"label": "Icon 1",
					"type": "image_picker"
				},
				{
					"id": "option_icon2",
					"label": "Icon 2",
					"type": "image_picker"
				},
				{
					"id": "option_icon3",
					"label": "Icon 3",
					"type": "image_picker"
				},
				{
					"id": "option_icon4",
					"label": "Icon 4",
					"type": "image_picker"
				},
				{
					"type": "checkbox",
					"id": "constrain_to_available",
					"label": "Only Show Available",
					"default": true
				},
				{
					"type": "text",
					"id": "instruction",
					"label": "Selection Instruction",
					"default": "Please select one"
				},
				{
					"type": "text",
					"id": "increment",
					"label": "Selection Increment",
					"default": "Please select {{ balance }} more"
				},
				{
					"type": "number",
					"id": "limit",
					"label": "Option Limit",
					"default": 1
				},
				{
					"type": "select",
					"id": "grid",
					"label": "Option Grid Layout",
					"default": "wide",
					"options": [
						{
							"value": "wide",
							"label": "Wide"
						},
						{
							"value": "compact",
							"label": "Compact"
						}
					]
				},
				{
					"id": "style_class",
					"label": "Style Class",
					"type": "text",
					"default": "initial-finder-slide"
				}
			]
		},
		{
			"type": "initial-slide",
			"name": "Initial Slide",
			"settings": [
				{
					"id": "initial_slide_title",
					"label": "Title",
					"type": "text",
					"default": "Perfect fit finder"
				},
				{
					"id": "initial_slide_copy",
					"label": "Initial Slide Copy",
					"type": "text",
					"default": "We labored over perfecting our fits and wanted to help inform your decision to ensure the one that will be yours."
				},
				{
					"id": "initial_slide_continue",
					"label": "Continue Button",
					"type": "text",
					"default": "Continue"
				},
				{
					"id": "style_class",
					"label": "Style Class",
					"type": "text",
					"default": "initial-finder-slide"
				}
			]
		},
		{
			"type": "recommendation",
			"name": "Recommendation",
			"settings": [
				{
					"type": "checkbox",
					"id": "show_email_form",
					"label": "Show email form",
					"default": true
				},
				{
					"id": "prompt",
					"label": "Prompt",
					"type": "text",
					"default": "Want to see more recommendations?"
				},
				{
					"id": "success",
					"label": "Success message",
					"type": "text",
					"default": "Thank you. We'll send you some good stuff."
				},
				{
					"id": "placeholder",
					"label": "Email Placeholder",
					"type": "text",
					"default": "Enter Email Address"
				},
				{
					"type": "liquid",
					"id": "additional_script_liquid",
					"label": "Javascript Logic Inclusion on Form Submission"
				},
				{
					"id": "submit",
					"label": "Submit Button",
					"type": "text",
					"default": "Submit"
				},
				{
					"id": "disclaimer",
					"label": "Disclaimer",
					"type": "text",
					"default": "By submitting this form, you agree to sign up for our email marketing newsletter"
				},
				{
					"id": "restart",
					"label": "Restart Link",
					"type": "text",
					"default": "RESTART QUIZ"
				},
				{
					"id": "style_class",
					"label": "Style Class",
					"type": "text",
					"default": "initial-finder-slide"
				},
				{
					"id": "klaviyo_source_name",
					"label": "Klaviyo Source Name",
					"type": "text",
					"default": "-"
				},
				{
					"id": "klaviyo_public_api",
					"label": "Klaviyo Public Api",
					"type": "text",
					"default": "-"
				},
				{
					"id": "klaviyo_email_list_id",
					"label": "Klaviyo Product Finder List id",
					"type": "text",
					"default": "-"
				},
				{
					"id": "klaviyo_news_list_id",
					"label": "Klaviyo Newsletter List id",
					"type": "text",
					"default": "-"
				}
			]
		}
	]
}
{% endschema %}
