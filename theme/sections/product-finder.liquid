{{ 'product-finder.css' | asset_url | stylesheet_tag }}
{% include 'product-finder' %}
{{ 'product-finder.js' | asset_url | script_tag }}

{% schema %}
{
	"name": "Product Finder",
	"class": "product-finder__parent",
	"settings": [
		{
			"id": "tracking_id",
			"label": "Tracking ID",
			"type": "text",
			"info": "Enter tracking id e.g 'shoe_finder'"
		},
		{
			"id": "recommendation_header",
			"label": "Recommendation Header Text",
			"type": "text",
			"default": "We Recommend"
		},
		{
			"id": "recommendation_header_bg_color",
			"label": "Recommendation Header Background Color",
			"type": "color",
			"default": "#55c7b5"
		},
		{
			"id": "recommendation_header_text_color",
			"label": "Recommendation Header Text Color",
			"type": "color",
			"default": "#FFFFFF"
		},
		{
			"id": "back_to_results",
			"label": "Back Button",
			"type": "text",
			"default": "Back to Results"
		},
		{
			"id": "content_color",
			"type": "color",
			"label": "Content Color",
			"default": "#042C4B"
		},
		{
			"id": "no_results",
			"label": "No Results Message",
			"type": "text",
			"default": "No Recommended Products Available.",
			"info": "Leave blank for no message."
		},
		{
			"type": "paragraph",
			"content": "@include ProductItem"
		}
	],
	"blocks": [
		{
			"type": "question",
			"name": "Question",
			"settings": [
				{
					"id": "title",
					"label": "Title",
					"type": "text",
					"info": "Appears in navigational elements"
				},
				{
					"id": "id",
					"label": "Tracking ID",
					"type": "text",
					"info": "Will be passed to Exponea tracking"
				},
				{
					"id": "question",
					"label": "Question",
					"type": "text"
				},
				{
					"id": "options",
					"label": "Options",
					"type": "textarea",
					"info": "Map of values, one per line with map of Label > Tag (constaints). \nExample:WOMEN > gender:women\nExample:5 (gender:women)"
				},
				{
					"type": "checkbox",
					"id": "constrain_to_available",
					"label": "Only Show Available",
					"default": true
				},
				{
					"type": "text",
					"id": "instruction",
					"label": "Selection Instruction",
					"default": "Please select one"
				},
				{
					"type": "text",
					"id": "increment",
					"label": "Selection Increment",
					"default": "Please select {{balance}} more"
				},
				{
					"type": "number",
					"id": "limit",
					"label": "Option Limit",
					"default": 1
				},
				{
					"type": "select",
					"id": "grid",
					"label": "Option Grid Layout",
					"default": "wide",
					"options": [
						{
							"value": "wide",
							"label": "Wide"
						},
						{
							"value": "compact",
							"label": "Compact"
						}
					]
				}
			]
		},
		{
			"type": "size_plus_calculation",
			"name": "Size + Calculation",
			"settings": [
				{
					"id": "title",
					"label": "Title",
					"type": "text",
					"info": "Appears in navigational elements"
				},
				{
					"id": "id",
					"label": "Tracking ID",
					"type": "text",
					"info": "Will be passed to Exponea tracking"
				},
				{
					"id": "question",
					"label": "Question",
					"type": "text"
				},
				{
					"id": "options",
					"label": "Options",
					"type": "textarea",
					"info": "Map of values, one per line with map of Label > Tag (constaints). \nExample:WOMEN > gender:women\nExample:5 (gender:women)"
				},
				{
					"type": "checkbox",
					"id": "constrain_to_available",
					"label": "Only Show Available",
					"default": true
				},
				{
					"type": "text",
					"id": "instruction",
					"label": "Selection Instruction",
					"default": "Please select one"
				},
				{
					"type": "text",
					"id": "increment",
					"label": "Selection Increment",
					"default": "Please select {{ balance }} more"
				},
				{
					"type": "number",
					"id": "limit",
					"label": "Option Limit",
					"default": 1
				},
				{
					"type": "select",
					"id": "grid",
					"label": "Option Grid Layout",
					"default": "wide",
					"options": [
						{
							"value": "wide",
							"label": "Wide"
						},
						{
							"value": "compact",
							"label": "Compact"
						}
					]
				},
				{
					"id": "style_class",
					"label": "Style Class",
					"type": "text",
					"default": "initial-finder-slide"
				},
				{
					"type": "header",
					"content": "Calculation"
				},
				{
					"id": "finding_fit_title",
					"label": "Title",
					"type": "text",
					"default": "Finding Your Perfect Fit"
				},
				{
					"id": "finding_fit_info",
					"label": "Info",
					"type": "textarea",
					"default": "To start you will need a soft measuring tape."
				},
				{
					"id": "cal_measurement",
					"label": "Measurements JSON",
					"type": "textarea",
					"info": "Give value in JSON format.\nExample: {'cm': [{'56-58': 'Small'},{'58.5-60': 'Classic'}],'in': [{'22-22.8': 'Small'}]}"
				}
			]
		},
		{
			"type": "question_icon",
			"name": "Question with icon",
			"settings": [
				{
					"id": "title",
					"label": "Title",
					"type": "text",
					"info": "Appears in navigational elements"
				},
				{
					"id": "id",
					"label": "Tracking ID",
					"type": "text",
					"info": "Will be passed to Exponea tracking"
				},
				{
					"id": "question",
					"label": "Question",
					"type": "text"
				},
				{
					"id": "options",
					"label": "Options",
					"type": "textarea",
					"info": "Map of values, one per line with map of Label > Tag (constaints). \nExample:WOMEN > gender:women\nExample:5 (gender:women)"
				},
				{
					"id": "option_icon1",
					"label": "Icon 1",
					"type": "image_picker"
				},
				{
					"id": "option_icon2",
					"label": "Icon 2",
					"type": "image_picker"
				},
				{
					"id": "option_icon3",
					"label": "Icon 3",
					"type": "image_picker"
				},
				{
					"id": "option_icon4",
					"label": "Icon 4",
					"type": "image_picker"
				},
				{
					"type": "checkbox",
					"id": "constrain_to_available",
					"label": "Only Show Available",
					"default": true
				},
				{
					"type": "text",
					"id": "instruction",
					"label": "Selection Instruction",
					"default": "Please select one"
				},
				{
					"type": "text",
					"id": "increment",
					"label": "Selection Increment",
					"default": "Please select {{ balance }} more"
				},
				{
					"type": "number",
					"id": "limit",
					"label": "Option Limit",
					"default": 1
				},
				{
					"type": "select",
					"id": "grid",
					"label": "Option Grid Layout",
					"default": "wide",
					"options": [
						{
							"value": "wide",
							"label": "Wide"
						},
						{
							"value": "compact",
							"label": "Compact"
						}
					]
				},
				{
					"id": "style_class",
					"label": "Style Class",
					"type": "text",
					"default": "initial-finder-slide"
				}
			]
		},
		{
			"type": "initial-slide",
			"name": "Initial Slide",
			"settings": [
				{
					"id": "initial_slide_title",
					"label": "Title",
					"type": "text",
					"default": "Perfect fit finder"
				},
				{
					"id": "initial_slide_copy",
					"label": "Initial Slide Copy",
					"type": "text",
					"default": "We labored over perfecting our fits and wanted to help inform your decision to ensure the one that will be yours."
				},
				{
					"id": "initial_slide_continue",
					"label": "Continue Button",
					"type": "text",
					"default": "Continue"
				},
				{
					"id": "style_class",
					"label": "Style Class",
					"type": "text",
					"default": "initial-finder-slide"
				}
			]
		},
		{
			"type": "recommendation",
			"name": "Recommendation",
			"settings": [
				{
					"type": "checkbox",
					"id": "show_email_form",
					"label": "Show email form",
					"default": true
				},
				{
					"id": "prompt",
					"label": "Prompt",
					"type": "text",
					"default": "Want to see more recommendations?"
				},
				{
					"id": "success",
					"label": "Success message",
					"type": "text",
					"default": "Thank you. We'll send you some good stuff."
				},
				{
					"id": "placeholder",
					"label": "Email Placeholder",
					"type": "text",
					"default": "Enter Email Address"
				},
				{
					"type": "liquid",
					"id": "additional_script_liquid",
					"label": "Javascript Logic Inclusion on Form Submission"
				},
				{
					"id": "submit",
					"label": "Submit Button",
					"type": "text",
					"default": "Submit"
				},
				{
					"id": "disclaimer",
					"label": "Disclaimer",
					"type": "text",
					"default": "By submitting this form, you agree to sign up for our email marketing newsletter"
				},
				{
					"id": "restart",
					"label": "Restart Link",
					"type": "text",
					"default": "RESTART QUIZ"
				},
				{
					"id": "style_class",
					"label": "Style Class",
					"type": "text",
					"default": "initial-finder-slide"
				},
				{
					"id": "klaviyo_source_name",
					"label": "Klaviyo Source Name",
					"type": "text",
					"default": "-"
				},
				{
					"id": "klaviyo_public_api",
					"label": "Klaviyo Public Api",
					"type": "text",
					"default": "-"
				},
				{
					"id": "klaviyo_email_list_id",
					"label": "Klaviyo Product Finder List id",
					"type": "text",
					"default": "-"
				},
				{
					"id": "klaviyo_news_list_id",
					"label": "Klaviyo Newsletter List id",
					"type": "text",
					"default": "-"
				}
			]
		}
	]
}
{% endschema %}
