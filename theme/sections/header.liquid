{% if section.settings.unwrap %}{% unless request.design_mode %}</div>{% endunless %}{% endif %}

{% for bar in section.blocks %}
{% assign barIndex = forloop.index %}
{% unless bar.type == 'bar' %}{% continue %}{% endunless %}
{%- liquid
  
  if bar.settings.transparency_paths != blank
    assign enabled_paths = bar.settings.transparency_paths | newline_to_br | split: '<br />'
    if enabled_paths contains request.path
      assign transparency_class = 'header-bar--transparent-at-top'
    endif
  endif
-%}

<header class="header-bar relative group {{ bar.settings.classes }} {% render 'class-settings' prefix:'bar_class' settings:bar.settings %} {{ transparency_class }}" style="{% render 'style-settings' prefix:'bar_style' settings:bar.settings %} --transparent-text-color: {{ bar.settings.bar_transparent_text_color }};">
  
  <div class="header-bar__container flex flex-wrap lg:flex-nowrap {{ bar.settings.container_classes }}">
    
    {% for block in section.blocks offset:barIndex %}

      {% assign blockIndex = barIndex | plus:forloop.index %}

      {% if block.type == 'bar' %}{% break %}{% endif %}

      <span {{ block.shopify_attributes }} class="header-bar__block header-bar__block--{{block.type | handle}} h-full flex items-center {% render 'class-settings' settings:block.settings prefix:'column_class' %} {{ block.settings.classes }}">

      {% case block.type %}

        {% when 'menu' %} 

          {% render 'header-nav' settings:block.settings id:block.id %}

        {% when 'logo' %}

            {% render 'header-logo' settings:block.settings %}

        {% when 'liquid' %}

            {{ block.settings.liquid }}

        {% when 'search' %}

            {% render 'header-search' settings:block.settings %}

        {% when 'nav-tools' %}

            {% render 'header-tools' settings:block.settings %}

        {% when 'announcements' %}

            {% render 'announcements' settings:block.settings blocks:section.blocks index:blockIndex %}

        {% when 'menu-toggle' %}

            {% render 'header-menu-toggle' settings:block.settings %}

      {% endcase %}

      </span>

    {% endfor %}
      
  </div>

  {% if bar.settings.sticky_stuff %}
  <div id="stickyTop"></div>
  {% endif %}
  <script src="{{ 'header.js' | asset_url }}"></script> 
</header>

{% endfor %}
{% if section.settings.unwrap %}{% unless request.design_mode %}<div>{% endunless %}{% endif %}


{% schema %}
{
	"name": "Header",
	"settings": [
		{
			"id": "unwrap",
			"label": "Unwrap Header Bars from Section Element",
			"type": "checkbox",
			"default": true,
			"info": "In order to make any individual Header Bar sticky to the top of the viewport, the bars must be \"unwrapped\" from the main section. Bars are then treated an individual sections."
		}
	],
	"blocks": [
		{
			"type": "bar",
			"name": "⤷ Bar",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤷ Bar"
				},
				{
					"id": "bar_class_role",
					"label": "Role",
					"type": "select",
					"options": [
						{
							"label": "Main",
							"value": "header-bar--main"
						},
						{
							"label": "Promo",
							"value": "header-bar--main-promo"
						}
					]
				},
				{
					"id": "bar_class_position",
					"label": "position",
					"type": "select",
					"options": [
						{
							"label": "Sticky",
							"value": "sticky top-0"
						},
						{
							"label": "Scroll",
							"value": ""
						}
					]
				},
				{
					"type": "color",
					"id": "bar_style_background_color",
					"label": "Bar Background Color"
				},
				{
					"type": "color",
					"id": "bar_style_color",
					"label": "Bar Text Color"
				},
				{
					"type": "header",
					"content": "Transparent Bar"
				},
				{
					"id": "transparency_paths",
					"label": "Request Paths to enable transparency",
					"type": "textarea",
					"info": "Add paths to a new line to make this bar transparent. Use \"/\" for the home page."
				},
				{
					"type": "color",
					"id": "bar_transparent_text_color",
					"label": "Transparent Bar Text Color"
				}
			]
		},
		{
			"type": "bar-end",
			"name": "⤶ Bar End",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤶ Bar End"
				}
			]
		},
		{
			"type": "menu-toggle",
			"name": "Menu Toggle",
			"settings": [
				{
					"id": "column_classes",
					"label": "Column Classes",
					"type": "text"
				},
				{
					"id": "onclick",
					"label": "Click Event",
					"type": "liquid"
				}
			]
		},
		{
			"type": "logo",
			"name": "Logo",
			"settings": [
				{
					"id": "column_classes",
					"label": "Column Classes",
					"type": "text"
				},
				{
					"id": "logo",
					"label": "Logo SVG",
					"type": "liquid"
				}
			]
		},
		{
			"type": "liquid",
			"name": "Custom Liquid",
			"settings": [
				{
					"id": "column_classes",
					"label": "Column Classes",
					"type": "text"
				},
				{
					"id": "logo",
					"label": "Custom Liquid",
					"type": "liquid"
				}
			]
		},
		{
			"type": "menu",
			"name": "Menu",
			"settings": [
				{
					"id": "column_classes",
					"label": "Column Classes",
					"type": "text"
				},
				{
					"id": "classes_format",
					"label": "Format",
					"type": "select",
					"options": [
						{
							"value": "flex-row",
							"label": "Inline / Tabs"
						},
						{
							"value": "flex-col",
							"label": "List / Accordion"
						}
					]
				},
				{
					"type": "link_list",
					"id": "menu",
					"label": "Menu",
					"info": "Select main menu"
				},
				{
					"type": "textarea",
					"id": "hide_mobile",
					"label": "Hide on Mobile",
					"default": "explore"
				},
				{
					"type": "textarea",
					"id": "hide_desktop",
					"label": "Hide on Desktop"
				},
				{
					"type": "checkbox",
					"id": "mega_menu_hover",
					"label": "Hover to show Mega Menu",
					"default": false
				}
			]
		},
		{
			"type": "search",
			"name": "Search",
			"settings": [
				{
					"id": "column_classes",
					"label": "Column Classes",
					"type": "text"
				},
				{
					"id": "input_focus",
					"label": "On Focus",
					"type": "text"
				},
				{
					"type": "liquid",
					"id": "search_suggestions",
					"label": "Search Terms Suggestions",
					"info": "comma separated"
				},
				{
					"type": "number",
					"id": "search_terms_animation_speed",
					"label": "Search Terms Speed",
					"default": 20,
					"info": "Duration of transition between slides (in seconds)"
				},
				{
					"type": "number",
					"id": "search_terms_inbetween_distance",
					"label": "Search Terms Inbetween Distance",
					"default": 20,
					"info": "Distance between slides in px."
				},
				{
					"type": "number",
					"id": "search_terms_position",
					"label": "Search Terms Position",
					"default": 75,
					"info": "Position from left in px. (Desktop)"
				},
				{
					"type": "number",
					"id": "search_terms_position_tablet",
					"label": "Search Terms Position for tablet",
					"default": 90,
					"info": "Position from left in px. (tablet)"
				},
				{
					"type": "number",
					"id": "search_terms_position_mobile",
					"label": "Search Terms Position for Mobile",
					"default": 90,
					"info": "Position from left in px. (Mobile)"
				},
				{
					"type": "text",
					"id": "search_terms_classes",
					"label": "Search Terms classes",
					"default": "text-[16px] lg:text-[12px] text-inherit"
				},
				{
					"type": "color",
					"id": "search_terms_color",
					"label": "Color",
					"default": "#000000"
				},
				{
					"type": "number",
					"id": "search_terms_color_opacity",
					"label": "Text color opacity",
					"default": 50,
					"info": "example: 45 will be .45 opacity."
				},
				{
					"type": "number",
					"id": "search_terms_animation_delay",
					"label": "Search Terms Animation Delay",
					"default": 2000,
					"info": "Delay between transitions (in ms)"
				}
			]
		},
		{
			"type": "spacer",
			"name": "Spacer",
			"settings": [
				{
					"id": "column_classes",
					"label": "Column Classes",
					"type": "text"
				}
			]
		},
		{
			"type": "nav-tools",
			"name": "Nav Tools",
			"settings": [
				{
					"id": "column_classes",
					"label": "Column Classes",
					"type": "text"
				},
				{
					"id": "button_classes",
					"label": "Button Classes",
					"type": "text",
					"default": "items-center ml-4 text-xs"
				},
				{
					"id": "search",
					"label": "Search",
					"type": "checkbox",
					"default": true
				},
				{
					"id": "wishlist",
					"label": "Wishlist",
					"type": "checkbox",
					"default": true
				},
				{
					"id": "account",
					"label": "Account",
					"type": "checkbox",
					"default": true
				},
				{
					"id": "geo",
					"label": "Geolocation",
					"type": "checkbox",
					"default": true
				},
				{
					"id": "cart",
					"label": "Cart",
					"type": "checkbox",
					"default": true
				},
				{
					"id": "icon_size",
					"label": "Icon Size",
					"type": "number",
					"default": 20
				},
				{
					"id": "display",
					"label": "Display",
					"type": "radio",
					"options": [
						{
							"value": "icons",
							"label": "Icons"
						},
						{
							"value": "text",
							"label": "Text"
						},
						{
							"value": "both",
							"label": "Both"
						}
					],
					"default": "icons"
				},
				{
					"id": "greeting",
					"label": "Greeting",
					"type": "liquid"
				}
			]
		},
		{
			"type": "announcements",
			"name": "Announcements",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤷ Announcements"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:column_class"
				},
				{
					"id": "column_classes",
					"label": "Column Classes",
					"type": "text"
				},
				{
					"id": "interval",
					"label": "Autoplay Timer",
					"type": "number",
					"default": 5
				}
			]
		},
		{
			"type": "announcements-end",
			"name": "⤶ Announcements End",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤶ Announcements End"
				}
			]
		},
		{
			"type": "announcement",
			"name": "Announcement",
			"settings": [
				{
					"id": "inclusion",
					"label": "Liquid Inclusion Logic",
					"type": "liquid",
					"default": "1"
				},
				{
					"id": "inclusion_js",
					"label": "Javascript Inclusion Logic",
					"type": "liquid"
				},
				{
					"id": "text",
					"label": "Announcement Text",
					"type": "text"
				},
				{
					"id": "text_desktop",
					"label": "Desktop Announcment Text",
					"type": "text"
				},
				{
					"id": "image",
					"label": "Announcement Logo",
					"type": "image_picker"
				},
				{
					"id": "svg",
					"label": "Announcement Logo SVG",
					"type": "html",
					"info": "for adding an svg"
				},
				{
					"id": "image_size",
					"label": "Announcement Logo Size",
					"type": "text",
					"info": "set dimensions, example: 100x100"
				},
				{
					"id": "link",
					"label": "Announcement Link",
					"type": "url"
				}
			]
		}
	]
}
{% endschema %}

<script type="application/ld+json">
{
  "@context": "http://schema.org",
  "@type": "Organization",
  "name": "{{ shop.name }}",
  {% if section.settings.logo %}
    {% assign image_size = section.settings.logo.width | append:'x' %}
    "logo": "https:{{ section.settings.logo | img_url: image_size }}",
  {% endif %}
  "sameAs": [
    "{{ settings.social_twitter_link }}",
    "{{ settings.social_facebook_link }}",
    "{{ settings.social_pinterest_link }}",
    "{{ settings.social_instagram_link }}",
    "{{ settings.social_tumblr_link }}",
    "{{ settings.social_snapchat_link }}",
    "{{ settings.social_youtube_link }}",
    "{{ settings.social_vimeo_link }}"
  ],
  "url": "{{ shop.url }}{{ page.url }}"
}
</script>

{% if template.name == 'index' %}
  <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": "{{ shop.name }}",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "{{ shop.url }}/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      },
      "url": "{{ shop.url }}{{ page.url }}"
    }
  </script>
{% endif %}
