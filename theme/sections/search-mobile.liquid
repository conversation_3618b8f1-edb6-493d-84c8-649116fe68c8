{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<form action="/search" method="get" role="search" class="flex relative container bg-light-gray br-pill items-center justify-center w-full group modal-search-form">
  <label for="headerSearch" class="invisible hidden">
    Search
  </label>
  <input 
  type="search" 
  name="q" 
  id="headerSearch" 
  value="" 
  placeholder="Search" 
  class="focus:border-0 focus:outline-none h-16 lg:h-24 lg:mt-0 pl-10 shadow-none ui-autocomplete-input w-full" 
  autocomplete="off" 
  autocorrect="off" 
  autocapitalize="off" 
  aria-label="Search" 
  aria-autocomplete="list" 
  onkeydown="this.typingTimer = this.typingTimer || {}; clearTimeout(this.typingTimer);" 
  onkeyup="clearTimeout(this.typingTimer); this.typingTimer = setTimeout(()=>{Neptune.liquid.load('SearchResults','url:/search/suggest.json?resources[type]=product&q='+this.value+'');Neptune.liquid.load('SearchCollections','url:/search/suggest.json?resources[type]=collection&q='+this.value+'');_n.qs('.search-results').classList.remove('hidden')},1000)"
  >
  <input type="submit" value="go" class="sr-only">
  <button class="h-full border-none cursor-pointer text-center absolute animate left-0 top-0 px-2">
    <span class="icon text-black flex flex-col justify-center items-center mha" style="max-width: 38px;">
      {% render 'icon' icon:'search' %}
    </span>
    <span class="icon-fallback-text sr-only">Search</span>
  </button>
  <button class="" neptune-engage="{preventDefault:true,targets:[
    {selector:body,attributes:[{att:data-active-modal,set:_remove}]},
    {selector:.search-results,classes:add:hidden}
    ]}">
      {% render 'icon' icon:'x-circle' %}
  </button>           
</form>


{% schema %}
  {
    "name": "Search Mobile",
    "settings": [
    ],
    "blocks": [
    ]
  }
{% endschema %}