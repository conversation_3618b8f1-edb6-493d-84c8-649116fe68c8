{% liquid 
  assign section_type = 'authentication'
%}

<section 
  class="section section--{{ section_type }} {% render 'class-settings' prefix:'wrapper_class' settings:section.settings %} relative" 
  style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}"> 

  {% if section.settings.wrapper_bg_image %}
    <img src="{{ section.settings.wrapper_bg_image | image_url }}" alt="{{ section.settings.wrapper_bg_image.alt }}" class="section__media absolute inset-0 w-full h-full object-cover" />
  {% endif %}

  <main class="section__container relative {% render 'class-settings' prefix:'container_class', settings:section.settings %}">

    {% render 'flex-nested-blocks' blocks:section.blocks offset:0 %}

  </main>

</section>

{% schema %}
{
	"name": "Authentication",
	"class": "shopify-section--authentication",
	"settings": [
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		}
	],
	"blocks": [
		{
			"type": "frame",
			"name": "⤷ Frame",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:article_class"
				},
				{
					"type": "paragraph",
					"content": "@include LogicInclusion"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "color",
					"id": "article_style_background_color",
					"label": "Frame Background Color"
				},
				{
					"type": "color_background",
					"id": "article_style_background",
					"label": "Frame Background Gradient"
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:article_class, label_prefix:Frame "
				}
			]
		},
		{
			"type": "break",
			"name": "⤶ Frame Break",
			"settings": []
		},
		{
			"type": "dropdown-menu",
			"name": "Dropdown Menu",
			"settings": [
				{
					"type": "text",
					"id": "label",
					"label": "*Label"
				},
				{
					"type": "liquid",
					"id": "model",
					"label": "Data source/model"
				},
				{
					"type": "textarea",
					"id": "options",
					"label": "Options",
					"info": "value:Label",
					"default": "featured:Featured\nbest-selling:Best Selling\nprice-ascending:Lowest Price"
				},
				{
					"type": "liquid",
					"id": "onchange",
					"label": "on Change",
					"info": "JavaScript"
				}
			]
		},
		{
			"type": "toggle-switch",
			"name": "Toggle Switch",
			"settings": [
				{
					"type": "liquid",
					"id": "model",
					"label": "Data source/model"
				},
				{
					"type": "textarea",
					"id": "options",
					"label": "Toggle Switch Options",
					"info": "value:Selected|Unselected",
					"default": "Product:View on Product,Model:View on Model"
				},
				{
					"type": "liquid",
					"id": "onchange",
					"label": "on Change",
					"info": "JavaScript"
				},
				{
					"type": "select",
					"id": "toggle_interaction",
					"label": "Toggle Interaction",
					"default": "click",
					"options": [
						{
							"value": "hover",
							"label": "Hover"
						},
						{
							"value": "click",
							"label": "Click"
						}
					]
				}
			]
		},
		{
			"type": "title",
			"name": "Title",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include Text id_prefix:title, label_prefix:Title"
				}
			]
		},
		{
			"type": "button",
			"name": "Button",
			"settings": [
				{
					"type": "select",
					"id": "style",
					"label": "Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "text",
					"id": "button_text",
					"label": "Button Text"
				},
				{
					"type": "text",
					"id": "leading_icon",
					"label": "Leading Icon"
				},
				{
					"type": "text",
					"id": "trailing_icon",
					"label": "Trailing Icon"
				},
				{
					"type": "url",
					"id": "link",
					"label": "Link"
				},
				{
					"type": "liquid",
					"id": "onclick",
					"label": "Click Event",
					"info": "JavaScript onclick event, overrides native functionality."
				},
				{
					"type": "select",
					"id": "onclick_type",
					"label": "Type of Click Event",
					"options": [
						{
							"value": "x-data @",
							"label": "Alpine"
						},
						{
							"value": "on",
							"label": "Native"
						}
					],
					"default": "x-data @"
				},
				{
					"type": "checkbox",
					"id": "form_validity",
					"label": "Require Form Validity",
					"info": "Disables button until all fields in enclosing form are valid"
				}
			]
		},
		{
			"name": "⤷ Form",
			"type": "form",
			"settings": [
				{
					"type": "select",
					"label": "Form Type",
					"id": "shopify",
					"default": "",
					"options": [
						{
							"value": "",
							"label": "Custom"
						},
						{
							"value": "activate_customer_password",
							"label": "activate_customer_password"
						},
						{
							"value": "cart",
							"label": "cart"
						},
						{
							"value": "contact",
							"label": "contact"
						},
						{
							"value": "create_customer",
							"label": "create customer"
						},
						{
							"value": "currency",
							"label": "currency"
						},
						{
							"value": "customer",
							"label": "customer"
						},
						{
							"value": "customer_address",
							"label": "customer address"
						},
						{
							"value": "customer_login",
							"label": "customer login"
						},
						{
							"value": "guest_login",
							"label": "guest login"
						},
						{
							"value": "localization",
							"label": "localization"
						},
						{
							"value": "new_comment",
							"label": "new comment"
						},
						{
							"value": "product",
							"label": "product"
						},
						{
							"value": "recover_customer_password",
							"label": "recover customer password"
						},
						{
							"value": "reset_customer_password",
							"label": "reset customer password"
						},
						{
							"value": "storefront_password",
							"label": "storefront password"
						}
					]
				},
				{
					"type": "text",
					"label": "Form Action",
					"id": "form_action"
				},
				{
					"type": "select",
					"label": "Form Method",
					"id": "form_method",
					"default": "POST",
					"options": [
						{
							"value": "POST",
							"label": "POST"
						},
						{
							"value": "GET",
							"label": "GET"
						}
					]
				},
				{
					"type": "textarea",
					"label": "Hidden Fields",
					"id": "hidden_fields",
					"info": "Lines with name:value"
				},
				{
					"type": "liquid",
					"label": "Form onsubmit",
					"id": "form_onsubmit"
				},
				{
					"type": "checkbox",
					"label": "Block Default HTTP Submit",
					"id": "prevent_default"
				}
			]
		},
		{
			"name": "⤶ Form End",
			"type": "form-end",
			"settings": []
		},
		{
			"name": "Form Errors",
			"type": "form-errors",
			"settings": []
		},
		{
			"name": "Text field",
			"type": "text-field",
			"settings": [
				{
					"type": "text",
					"label": "Field Label",
					"id": "label"
				},
				{
					"type": "text",
					"label": "Field Name",
					"id": "name"
				},
				{
					"type": "select",
					"label": "Field Type",
					"id": "type",
					"default": "text",
					"options": [
						{
							"value": "text",
							"label": "Text"
						},
						{
							"value": "textarea",
							"label": "Textarea"
						},
						{
							"value": "password",
							"label": "Password"
						},
						{
							"value": "email",
							"label": "Email"
						},
						{
							"value": "date",
							"label": "Date"
						}
					]
				},
				{
					"type": "select",
					"label": "Label Style",
					"id": "field_class_label_style",
					"default": "field--floating-label",
					"options": [
						{
							"value": "field--floating-label",
							"label": "Floating"
						},
						{
							"value": "",
							"label": "Stacked"
						}
					]
				},
				{
					"type": "text",
					"label": "Placeholder Text",
					"id": "placeholder"
				},
				{
					"type": "text",
					"label": "Value Mask",
					"id": "mask",
					"info": "https://alpinejs.dev/plugins/mask"
				},
				{
					"type": "text",
					"label": "Validation Regex Pattern",
					"id": "pattern"
				},
				{
					"type": "checkbox",
					"label": "Required",
					"id": "required",
					"default": false
				},
				{
					"type": "richtext",
					"label": "Field Description",
					"id": "description"
				},
				{
					"type": "select",
					"label": "Justify Description Text",
					"id": "description_class_justify",
					"options": [
						{
							"value": "text-left",
							"label": "Left"
						},
						{
							"value": "text-right",
							"label": "Right"
						},
						{
							"value": "text-center",
							"label": "Center"
						}
					]
				}
			]
		},
		{
			"name": "Multi-Select",
			"type": "multiselect",
			"settings": [
				{
					"type": "text",
					"label": "Field Label",
					"id": "label"
				},
				{
					"type": "text",
					"label": "Field Name",
					"id": "name"
				},
				{
					"type": "select",
					"label": "Field Type",
					"id": "type",
					"default": "select",
					"options": [
						{
							"value": "select",
							"label": "Dropdown Select"
						},
						{
							"value": "radio",
							"label": "Radio Buttons"
						},
						{
							"value": "checkboxes",
							"label": "Checkboxes"
						}
					]
				},
				{
					"type": "select",
					"label": "Label Style",
					"id": "field_class_label_style",
					"default": "field--floating-label",
					"options": [
						{
							"value": "field--floating-label",
							"label": "Floating"
						},
						{
							"value": "",
							"label": "Stacked"
						}
					]
				},
				{
					"type": "textarea",
					"label": "Options",
					"id": "options"
				},
				{
					"type": "text",
					"label": "Placeholder Text",
					"id": "placeholder"
				},
				{
					"type": "checkbox",
					"label": "Required",
					"id": "required",
					"default": false
				}
			]
		},
		{
			"name": "Checkbox",
			"type": "checkbox",
			"settings": [
				{
					"type": "text",
					"label": "Field Label",
					"id": "label"
				},
				{
					"type": "text",
					"label": "Field Name",
					"id": "name"
				},
				{
					"type": "text",
					"label": "Checkbox Label",
					"id": "input_label"
				},
				{
					"type": "text",
					"label": "Description",
					"id": "description"
				},
				{
					"type": "checkbox",
					"label": "Checked by Default",
					"id": "checked"
				},
				{
					"type": "checkbox",
					"label": "Required to be Checked",
					"id": "required",
					"default": false
				},
				{
					"type": "select",
					"label": "Alignment",
					"id": "label_class_align",
					"default": "items-start",
					"options": [
						{
							"value": "items-start",
							"label": "Top"
						},
						{
							"value": "items-center",
							"label": "Center"
						},
						{
							"value": "items-end",
							"label": "Bottom"
						}
					]
				}
			]
		},
		{
			"name": "Multi Option Field",
			"type": "multi-options",
			"settings": [
				{
					"type": "text",
					"label": "Field Label",
					"id": "field_label"
				},
				{
					"type": "text",
					"label": "Field Name",
					"id": "field_name"
				},
				{
					"type": "select",
					"label": "Field Type",
					"id": "field_type",
					"default": "checkbox",
					"options": [
						{
							"value": "checkbox",
							"label": "Checkbox"
						},
						{
							"value": "radio",
							"label": "Radio"
						},
						{
							"value": "select",
							"label": "Select"
						}
					]
				},
				{
					"type": "textarea",
					"label": "Options",
					"id": "field_options",
					"info": "Enter options for checkbox or radio fields, separated by line breaks."
				},
				{
					"type": "text",
					"label": "Default Value",
					"id": "default_value"
				},
				{
					"type": "checkbox",
					"label": "Required",
					"id": "field_required",
					"default": false
				}
			]
		},
		{
			"name": "Divider",
			"type": "divider",
			"settings": [
				{
					"type": "color",
					"id": "divider_style_border_color",
					"label": "Divider Color"
				}
			]
		},
		{
			"type": "image",
			"name": "Image",
			"settings": [
				{
					"type": "image_picker",
					"label": "Image",
					"id": "image"
				},
				{
					"type": "image_picker",
					"label": "Image (Desktop)",
					"id": "image_desktop"
				},
				{
					"type": "select",
					"label": "Image Position",
					"id": "media_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full w-full"
						}
					]
				}
			]
		},
		{
			"type": "video",
			"name": "Video",
			"settings": [
				{
					"type": "text",
					"label": "Video URL",
					"id": "video"
				},
				{
					"type": "select",
					"label": "Video Position",
					"id": "media_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full w-full"
						}
					]
				}
			]
		},
		{
			"type": "custom_liquid",
			"name": "Liquid",
			"limit": 4,
			"settings": [
				{
					"type": "liquid",
					"id": "liquid",
					"label": "Liquid Block"
				}
			]
		},
		{
			"name": "Accordion",
			"type": "accordion",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:accordion_detail_class"
				},
				{
					"type": "paragraph",
					"content": "@include Accordion"
				}
			]
		}
	]
}
{% endschema %}
