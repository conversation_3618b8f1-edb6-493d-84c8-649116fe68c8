{%- liquid 
  if content_for_header contains 'product='
    assign product_handle = content_for_header | split: 'product=' | last | url_decode | split: '?' | first | split: '"' | first
    assign product = all_products[product_handle]
  endif
  if section.settings.product != blank
    assign product = section.settings.product
  endif
-%} 

<script> 
  window.productSummary = {{ section.id | json }} 
</script>
 
{% unless template == 'collection.finder' %}
{% render 'flex-nested-blocks' blocks:section.blocks offset:0 %}
{% endunless %}

{% render 'product-data' product:product, script_tag:true include_media:true  include_variants:true include_inventory:true preselect_variant:false history_state:true, include:'media' %}

<div class="grid lg:grid-cols-2 gap-4">

  {{ product.images[0] | image_url: width: 500 | image_tag: class: 'product-summary__image' }}

  <div class="product-summary__buy-box flex flex-col gap-md">
  {%- for block in section.blocks -%}
      
      <div class="product-essentials__block product-essentials__{{ block.type | replace: '_','-' }}">
      {%- case block.type -%}
          {%- when 'custom_liquid' -%}
            {{- block.settings.liquid -}}

          {%- when 'header' -%}
            {%- render 'product-title-lockup' product:product, settings:block.settings, title_element:'h2'%}

          {%- when 'quick_add_button' -%}
            {%- liquid
            assign attributes = 'onclick="event.preventDefault(); QuickAdd.open(window.products[`' | append: product.handle | append: '`], null, event);"'
              render 'button' tag:'button' attributes:attributes style:'button--primary button--large' content:'Add to Cart' class:'w-full justify-center' 
            -%}

          {%- when 'learn_more_button' -%}
            {%- assign attributes = 'href="/products/' | append: product.handle | append: '"' -%}
            {%- render 'button' 
              style: 'pop' 
              tag:'a' 
              content: 'Learn More' 
              class: 'quick-add__info-button w-full py-[24px_!important]' 
              attributes: attributes
            -%}

          {%- when 'fit_guide' -%}
            
            {% if block.settings.override_product != blank %}
              {% assign fit_guide_product = block.settings.override_product %}
            {% else %}
              {% assign fit_guide_product = product %}
            {% endif %}

            {% render 'fit-guide' settings:block.settings, product:fit_guide_product, block:block %}
            

            {%- when 'promo' -%}
              {% if block.settings.tag == blank %}
                <a href="{{ block.settings.link_link }}" class="promo">
                  {% if block.settings.icon %}
                    <div class="promo__icon-wrapper">
                      <div class="promo__icon">
                        <img src="{{ block.settings.icon }}" alt="{{ block.settings.headline }}" />
                      </div>
                    </div>
                  {% endif %}
                  {% if block.settings.headline %}
                    <div class="promo__content">
                      <h2 class="promo__headline">{{ block.settings.headline }}</h2>
                      {% if block.settings.headline %}
                        <span class="promo__description"> - {{ block.settings.description }}</span>
                      {% endif %}
                    </div>
                  {% endif %}
                </a>
              {% else %}
                {% assign productTags = product.tags | join: ', ' %}
                {% if productTags contains block.settings.tag %}
                  <a href="{{ block.settings.link_link }}" class="promo promo__tag">
                    {% if block.settings.icon %}
                      <div class="promo__icon-wrapper">
                        <div class="promo__icon">
                          <img src="{{ block.settings.icon }}" alt="{{ block.settings.headline }}" />
                        </div>
                      </div>
                    {% endif %}
                    {% if block.settings.headline %}
                      <div class="promo__content">
                        <span class="promo__headline">{{ block.settings.headline }}</span>
                        {% if block.settings.headline %}
                          <span class="promo__description"> - {{ block.settings.description }}</span>
                        {% endif %}
                      </div>
                    {% endif %}
                  </a>
                {% endif %}
              {% endif %}

          {%- when 'horizontal_line' -%}
            <hr class="my-6" />

      {% endcase %}
    </div>

  {%- endfor -%}
  </div>
</div>
{%- for block in section.blocks -%}
  {%- case block.type -%}
      {%- when 'product_description' -%}
        <hr class="border-t-1 border-solid"  style="border-color:#707070;" />
        <div class="grid lg:grid-cols-2 gap-4">
          <div class="product-essentials__block product-essentials__{{ block.type | replace: '_','-' }}">
            <article class="product-summary__enunciation">
                <header class="product-header">
                  <h2 class="product-header__title">About {{ product.title | split: ' - ' | first }}</h2>
                </header>
                <div class="product__enunciation" data-product-story="">
                  {{ product.description }}
                </div>
            </article>
          </div>
        </div>
  {% endcase %}
{%- endfor -%}

{% schema %}
{
  "name": "Product Summary",
  "tag": "section",
  "class": "product-summary",
  "settings": [
    {
      "type": "product",
      "id": "product",
      "label": "Product Source"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "header",
      "name": "Product Header",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        },
        {
          "type": "liquid",
          "id": "title_source",
          "label": "Product Title Source",
          "info": "Liquid to extract the product title",
          "default": "{{ product.title|split:'-'|first }}"
        },
				{
					"type": "checkbox",
					"id": "price",
					"label": "Display Price",
					"default": true
				},
				{
					"type": "checkbox",
					"id": "type",
					"label": "Display Product Type",
					"default": true
				},
				{
					"type": "checkbox",
					"id": "reviews",
					"label": "Display Review Summary",
					"default": true
				}
      ]
    },
    {
      "type": "custom_liquid",
      "name": "Liquid",
      "limit": 4,
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid Block"
        }
      ]
    },
    {
      "type": "fit_guide",
      "name": "Fit Guide",
      "settings": [
        {
          "type": "paragraph",
          "content": "This feature is powered by tags. On a product the tag format follows: \n\"{Category}:{value}\""
        },
        {
          "type": "paragraph",
          "content": "The product tag can use the option value or a numerical value. For Example: \"size:small\" or \"width:50\" "
        },
        {
          "type": "textarea",
          "label": "Categories",
          "id": "categories"
        },
				{
					"type": "checkbox",
					"label": "Make Collapsable",
					"id": "collapse"
				}
      ]
    },
    {
      "type": "quick_add_button",
      "name": "Quick Add Button"
    },
    {
      "type": "learn_more_button",
      "name": "Learn More Button"
    },
    {
      "type": "product_description",
      "name": "Product Description"
    }
  ]
}
{% endschema %}
