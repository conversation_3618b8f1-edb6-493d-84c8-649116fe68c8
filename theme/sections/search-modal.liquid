<aside class="" x-data>

  <template x-if="$store.search && !!$store.search.form && $store.search.form == 'SearchModalInput'">

    <dialog data-modal="search" :class="`search-results modal modal--right h-screen modal--underlay fixed overflow-scroll`" open x-init="Header.updateHeaderOffset()">

      <div>

        <header class="search-results__header">
          {% comment %}<p>{{ 'general.search.search' | t }}</p>{% endcomment %}
          <p x-text="$store.search.query"></p>
        </header>

        <template x-if="$store.search.results">

          <main>

            <template x-if="$store.search.results && $store.search.results.suggestions && $store.search.results.suggestions.length">
              {% render 'search-results' type:'suggestions' settings:section.settings %}
            </template>
            
            <template x-if="$store.search.results && $store.search.results.products && $store.search.results.products.length">
              {% render 'search-results' type:'products' settings:section.settings %}
            </template>
            
            <template x-if="$store.search.results && $store.search.results.collections && $store.search.results.collections.length">
              {% if localization.country.currency.iso_code == shop.currency  %}
                {% render 'search-results' type:'collections' settings:section.settings %}
              {% elsif section.settings.hide_collections_nonprimary_market == 'show' %}
                {% render 'search-results' type:'collections' settings:section.settings %}
              {% endif %}}
            </template>

            <template x-if="Search.empty()">
              <div class="mt-8 ml-8">
                <p>Sorry, nothing found for "<span x-text="$store.search.query"></span>"</p>
              </div>
            </template>
            
          </main>

        </template>
        <template x-if="!$store.search.results">
        {% if section.settings.show_search_suggestions %}
          <div class="search-results__type search-results__type--suggestions overflow-y-auto lg:mt-[50px]">
            <p class="search-results__type-label">{{ section.settings.search_suggestions_heading_label }}</p>
            {% if section.settings.search_suggestions %}
              {% assign search_suggestions_array = section.settings.search_suggestions | split: "," %}
              {% for search_suggestion in search_suggestions_array %}
                <article class="search-results__item search-results__item--suggestion search-result-item">
                  <a class="flex" href="/search?q={{ search_suggestion }}">
                    <p>{{ search_suggestion }}</p>
                  </a>
                </article>
              {% endfor %}
            {% endif %}
            {% if section.settings.product_suggestions_heading_label %}
              <p class="search-results__type-label">{{ section.settings.product_suggestions_heading_label }}</p>
            {% endif %}
            {% if section.settings.product_suggestions_type == 'bestsellers' %}
              {% for product in collections['boost-all'].products limit:10 %}
                <article class="search-results__item search-results__item--product search-result-item">
                  <a href="{{ product.url }}" class="flex">
                    <img src="{{ product.featured_image | img_url: '100x100' }}" alt="{{ product.featured_image.alt }}" class="w-1/6 object-contain" />
                    <div class="w-full">
                      <div class="flex flex-row justify-between">
                        <p class="my-0 search-result-item__title">{{ product.title }}</p>
                      </div>
                      <p class="my-0 search-result-item__type">{{ product.type }}</p>
                      <p class="my-0 search-result-item__sku">{{ product.first_available_variant.sku | split: "|" | first }}</p>
                      <div>
                        {% if product.type != 'Gift Card' %}
                          <span class="my-0 search-result-item__price {% if product.first_available_variant.compare_at_price > product.first_available_variant.price %}product-item__price_with_compare{% endif %}">{{ product.first_available_variant.price | money }}</span>
                          {% if product.first_available_variant.compare_at_price > product.first_available_variant.price %}
                          <s class="product-item__compare-at-price type-item" >{{ product.first_available_variant.compare_at_price | money }}</s>{% endif %}
                        {% endif %}
                      </div>
                    </div>
                  </a>
                </article>
              {% endfor %}
            {% else %}
              {% for product in section.settings.product_list %}
                <article class="search-results__item search-results__item--product search-result-item">
                  <a href="{{ product.url }}" class="flex">
                    <img src="{{ product.featured_image | img_url: '100x100' }}" alt="{{ product.featured_image.alt }}" class="w-1/6 object-contain" />
                    <div class="w-full">
                      <div class="flex flex-row justify-between">
                        <p class="my-0 search-result-item__title">{{ product.title }}</p>
                      </div>
                      <p class="my-0 search-result-item__type">{{ product.type }}</p>
                      <p class="my-0 search-result-item__sku">{{ product.first_available_variant.sku | split: "|" | first }}</p>
                      <div>
                        {% if product.type != 'Gift Card' %}
                          <span class="my-0 search-result-item__price {% if product.first_available_variant.compare_at_price > product.first_available_variant.price %}product-item__price_with_compare{% endif %}">{{ product.first_available_variant.price | money }}</span>
                          {% if product.first_available_variant.compare_at_price > product.first_available_variant.price %}
                          <s class="product-item__compare-at-price type-item" >{{ product.first_available_variant.compare_at_price | money }}</s>{% endif %}
                        {% endif %}
                      </div>
                    </div>
                  </a>
                </article>
              {% endfor %}
            {% endif %}
          </div>
        {% endif %}
        </template>

        {% if section.settings.form %}
        <footer>
          <form action="/search" id="SearchModalInput">
            <div class="field -floating-label w-full">
              <label for="SearchModalQ" class="">Search</label>
              {% render 'icon' icon:'search' %}
              <input id="SearchModalQ" type="search" placeholder="{{ 'general.search.placeholder' | t }}" name="q">
            </div>
          </form>
        </footer>
        {% endif %}

      </div>

    </dialog>

  </template>

</aside>

<script defer src="{{ 'search-modal.js' | asset_url }}"></script>
<script>
  window.search_redirect = {
    {% if section.settings.search_redirects %}
      {% assign redirects_array = section.settings.search_redirects_map | newline_to_br | strip_newlines | split: '<br />' %}
      {% for redirect in redirects_array %}
        {% assign search_redirect_term = redirect | split: ':' | first %}
        {% assign search_redirect_link = redirect | split: ':' | last %}
        {% if search_redirect_term != '' and search_redirect_link != '' %}
          "{{ search_redirect_term | downcase }}": "{{ search_redirect_link }}"{% unless forloop.last %},{% endunless %}
        {% endif %}
      {% endfor %}
    {% endif %}
  }
</script>

<script>
  window.search = {
    settings: {{ section.settings | json }}
  };
  {% for block in section.blocks %}

    {% case block.type %}

      {% when 'data-remote' %}
        search.settings.remote_url = '{{ block.settings.remote_url }}';
        search.settings.map = {{ block.settings.map }};

      {% when 'data-boost' %}

        {% render 'boost-search-settings' %}

    {% endcase %}
  {% endfor %}
  window.addEventListener('Search:results', ()=>{
  	if(search.form=='SearchModalInput')
  	Modal.open('Search')
  })
</script>

{% schema %}
{
  "name": "Search",
  "tag": "aside",
  "max_blocks": 1,
  "settings": [
    {
      "type":"checkbox",
      "id":"form",
      "label":"Include Search Input in Modal",
      "default":false 
    },
    {
      "type":"number",
      "id":"debounce",
      "label":"Milliseconds delay from keypress in search field to execute search",
      "default":300
    },
    {
      "type":"number",
      "id":"limit",
      "label":"Products per Page",
      "default":9
    },
    {
      "type":"text",
      "id":"product_grid_classes",
      "label":"Product Grid Classes",
      "default":"w-3/4 p-5 mt-6 grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3 xl:gap-x-8"
    },
    {
      "type":"text",
      "id":"product_filters_classes",
      "label":"Product Filters Classes",
      "default":"p-5 w-1/4 sticky top-24"
    }, 
    {
      "type":"radio",
      "id":"paging",
      "label":"Collection Paging",
      "default":"scroll",
      "options": [
        {
          "value":"scroll",
          "label":"Scroll-Triggered Auto Loading"
        }
      ]
    },
    {
      "type":"textarea",
      "id":"sort",
      "label":"Sort Options",
      "info":"value:Label",
      "default":"featured:Featured\nbest-selling:Best Selling\nprice-ascending:Lowest Price"
    },
    {
      "type": "header",
      "content": "Search Results Item"
    },
    {
      "type":"liquid",
      "id":"search_item_title_logic",
      "label":"Product Title",
      "info":"JavaScript logic that will output the item's title.",
      "default":"product.title"
    },
    {
      "type":"liquid",
      "id":"search_item_type_logic",
      "label":"Product Type",
      "info":"JavaScript logic that will output the item's type.",
      "default":"product.type"
    },
    {
      "type":"liquid",
      "id":"search_item_sku_logic",
      "label":"Product SKU",
      "info":"JavaScript logic that will output the item's SKU.",
      "default":"product.sku.split('|').at(0)"
    },
    {
      "type": "header",
      "content": "Static Preload Items"
    },
    {
      "type": "checkbox",
      "id": "show_search_suggestions",
      "label": "Show Search Suggestions",
      "default": false
    },
    {
      "type": "select",
      "id": "hide_collections_nonprimary_market",
      "label": "Hide collections in search modal for non-primary market",
      "options": [
        {
          "value": "show",
          "label": "Show"
        },
        {
          "value": "hide",
          "label": "Hide"
        }
      ],
      "default": "show"
    },
    {
      "type": "textarea",
      "id": "search_suggestions",
      "label": "Search Suggestions",
      "default": "backpack,bless up,hat,bag"
    },
    {
      "type": "text",
      "id": "search_suggestions_heading_label",
      "label": "Search Suggestions Heading Label",
      "default": "Suggestions"
    },
    {
      "type": "select",
      "id": "product_suggestions_type",
      "label": "Product Suggestion Type",
      "options": [
        {
          "value": "bestsellers",
          "label": "Bestseller"
        },
        {
          "value": "manually",
          "label": "Manually"
        }
      ],
      "default": "bestsellers"
    },
    {
      "type": "product_list",
      "id": "product_list",
      "label": "Products",
      "limit": 12
    },
    {
      "type": "text",
      "id": "product_suggestions_heading_label",
      "label": "Product Suggestions Heading Label",
      "default": "Trending Products"
    },
    {
      "type": "header",
      "content": "Search Redirects"
    },
    {
      "type": "checkbox",
      "id": "search_redirects",
      "label": "Enable Search Redirects",
      "default": false
    },
    {
      "type": "textarea",
      "id": "search_redirects_map",
      "label": "Redirects Map"
    }
  ],
  "blocks": [
    {
      "type": "data-boost",
      "name": "BoostCommerce Data",
      "limit": 1
    },
    {
      "type": "data-remote",
      "name": "Open Data",
      "limit": 1,
      "settings":[
        {
          "id":"remote_url",
          "label": "Remote URL",
          "type":"liquid"
        },
        {
          "id":"map",
          "label": "Data Map",
          "type":"liquid"
        }
      ]
    }
  ]
}
{% endschema %}
