{% liquid
	assign section_type = 'video'
%} 

<section 
	class="section section--{{ section_type }} relative {% render 'class-settings' prefix:'wrapper_class' settings:section.settings %}" 
	style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}">

	{% if section.settings.wrapper_bg_image %}
		<img src="{{ section.settings.wrapper_bg_image | image_url }}" alt="{{ section.settings.wrapper_bg_image.alt }}" class="section__media absolute inset-0 w-full h-full object-cover" />
	{% endif %}
	
	<main 
		class="section__container relative {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
		style="{% render 'style-settings' prefix:'container_styles' settings:section.settings %}">

    {% render 'async-video'
      settings: section.settings
    %}

	</main>

	<style>
		button.play-pause-hide {
			opacity:0;
			pointer-events:none;
		}
	</style>

</section>


 

{% schema %}
{
	"name": "Video",
	"tag": "section",
	"class": "shopify-section--video",
	"settings": [
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "liquid",
			"id": "source",
			"label": "Video Source"
		},
		{
			"type": "liquid",
			"id": "source_mobile",
			"label": "Video Source for mobile"
		},
		{
			"type": "select",
			"id": "item_aspect_ratio_desktop",
			"label": "Item aspect ratio desktop",
			"options": [
				{
					"value": "Auto",
					"label": "auto"
				},
				{
					"value": "2/1",
					"label": "2:1"
				},
				{
					"value": "16/9",
					"label": "16:9"
				},
				{
					"value": "4/3",
					"label": "4:3"
				},
				{
					"value": "1/1",
					"label": "1:1"
				},
				{
					"value": "3/1",
					"label": "3:1"
				},
				{
					"value": "3/4",
					"label": "3:4"
				},
				{
					"value": "8/30",
					"label": "8:30"
				},
				{
					"value": "9/16",
					"label": "9:16"
				}
			],
			"default": "16/9"
		},
		{
			"type": "select",
			"id": "item_aspect_ratio_mobile",
			"label": "Item aspect ratio mobile",
			"options": [
				{
					"value": "Auto",
					"label": "auto"
				},
				{
					"value": "2/1",
					"label": "2:1"
				},
				{
					"value": "16/9",
					"label": "16:9"
				},
				{
					"value": "4/3",
					"label": "4:3"
				},
				{
					"value": "1/1",
					"label": "1:1"
				},
				{
					"value": "3/1",
					"label": "3:1"
				},
				{
					"value": "3/4",
					"label": "3:4"
				},
				{
					"value": "8/30",
					"label": "8:30"
				},
				{
					"value": "9/16",
					"label": "9:16"
				}
			],
			"default": "9/16"
		},
		{
			"type": "checkbox",
			"id": "autoplay",
			"label": "Autoplay"
		},
		{
			"type": "checkbox",
			"id": "play_button",
			"label": "Play Button"
		},
		{
			"type": "checkbox",
			"id": "controls",
			"label": "Controls"
		},
		{
			"type": "checkbox",
			"id": "muted",
			"label": "Mute"
		},
		{
			"type": "checkbox",
			"id": "loop",
			"label": "Loop"
		},
		{
			"type": "checkbox",
			"id": "playsinline",
			"label": "Plays inline on Mobile"
		},
		{
			"type": "image_picker",
			"id": "poster",
			"label": "Poster Image For Desktop"
		},
		{
			"type": "image_picker",
			"id": "poster_mobile",
			"label": "Poster Image for Mobile"
		},
		{
			"type": "header",
			"content": "Video Defer Settings"
		},
		{
			"type": "select",
			"id": "loading_method",
			"label": "Video Loading Method",
			"options": [
				{
					"value": "immediate",
					"label": "Immediate"
				},
				{
					"value": "scroll",
					"label": "On Scroll"
				},
				{
					"value": "hover",
					"label": "On Hover"
				},
				{
					"value": "time",
					"label": "After Delay"
				}
			],
			"default": "scroll"
		},
		{
			"type": "range",
			"id": "loading_delay",
			"label": "Loading Delay (seconds)",
			"min": 0,
			"max": 10,
			"step": 1,
			"default": 2,
			"info": "Only applies when loading method is After Delay"
		}
	],
	"presets": [
		{
			"name": "Video"
		}
	]
}
{% endschema %}
