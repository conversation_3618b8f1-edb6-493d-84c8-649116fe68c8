<div class="">

  <div class="container text-center">

    <header class="">
      <h1 class="type--page">{{ page.title }}</h1>
      <hr class="hr--small">
    </header>

    <div class="">
      <div class="">
        <div class="rte rte--nomargin rte--indented-images">
          {{ page.content }}
        </div>

        <div class="my-12 mx-auto px-4 max-w-3xl">
          {% form 'contact' %}

            {% comment %}
              Successful message
            {% endcomment %}
            {% if form.posted_successfully? %}
              <p class="note form-success">
                {{ 'templates.contact.form.post_success' | t }}
              </p>
            {% endif %}

            {{ form.errors | default_errors }}

            <div class="">
              <div class="field">
                <label for="ContactFormName" class="hidden-label">{{ 'templates.contact.form.name' | t }}</label>
                <input type="text" id="ContactFormName" class="input-full" name="contact[{{ 'templates.contact.form.name' | t }}]" placeholder="{{ 'templates.contact.form.name' | t }}" autocapitalize="words" value="{% if form.name %}{{ form.name }}{% elsif customer %}{{ customer.name }}{% endif %}">
              </div>

              <div class="field">
                <label for="ContactFormEmail" class="hidden-label">{{ 'templates.contact.form.email' | t }}</label>
                <input type="email" id="ContactFormEmail" class="input-full" name="contact[email]" placeholder="{{ 'templates.contact.form.email' | t }}" autocorrect="off" autocapitalize="off" value="{% if form.email %}{{ form.email }}{% elsif customer %}{{ customer.email }}{% endif %}">
              </div>
            </div>

						<div class="field">
							<label for="ContactFormPhone" class="">{{ 'templates.contact.form.phone' | t }}</label>
							<input type="tel" id="ContactFormPhone" class="" name="contact[{{ 'templates.contact.form.phone' | t }}]" placeholder="{{ 'templates.contact.form.phone' | t }}" pattern="[0-9\-]*" value="{% if form.phone %}{{ form.phone }}{% elsif customer %}{{ customer.phone }}{% endif %}">
						</div>

						<div class="field">
							<label for="ContactFormMessage" class="">{{ 'templates.contact.form.message' | t }}</label>
							<textarea rows="10" id="ContactFormMessage" class="input-full" name="contact[{{ 'templates.contact.form.message' | t }}]" placeholder="{{ 'templates.contact.form.message' | t }}">{% if form.body %}{{ form.body }}{% endif %}</textarea>
						</div>

            <input type="submit" class="btn right" value="{{ 'templates.contact.form.send' | t }}">

          {% endform %}
        </div>
      </div>
    </div>

  </div>

</div>

{% schema %}
  {
    "name": "Contact Us",
    "settings": []
  }
{% endschema %}