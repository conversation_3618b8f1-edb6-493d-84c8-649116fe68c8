{% liquid 
	assign section_type = 'newsletter'
%}
<section 
	class="section section--{{ section_type }} relative {% render 'class-settings' prefix:'wrapper_class' settings:section.settings %}" 
	style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}">

	{% if section.settings.wrapper_bg_image %}
		<img src="{{ section.settings.wrapper_bg_image | image_url }}" alt="{{ section.settings.wrapper_bg_image.alt }}" class="absolute inset-0 object-cover w-full h-full section__media" />
	{% endif %}
	
	<main 
		class="section__container relative {% render 'class-settings' prefix:'container_class' settings:section.settings %} {% render 'class-settings' prefix:'text_alignment_class' settings:section.settings %}" 
		style="{% render 'style-settings' prefix:'container_styles' settings:section.settings %}">

			<!-- text items with text content and format settings -->
      
      {% if section.settings.text_item_1_text != blank or text_item_1_text != blank or text_item_1_attributes %}
      {% assign element = text_item_1_element | default: section.settings.text_item_1_element | default: 'p' %}
      <{{ element }} class="newsletter__title {% render 'class-settings' prefix:'text_item_1_class' settings:section.settings %} {{ text_item_1_classes }}" {{ text_item_1_attributes }} style="{% render 'style-settings' prefix:'text_item_1_style' settings:section.settings %}">{{text_item_1_text | default: section.settings.text_item_1_text}}</{{ element}}>
      {% endif %}
      
      {% if section.settings.text_item_2_text != blank or text_item_2_text != blank or text_item_2_attributes %}
      {% assign element = text_item_2_element | default: section.settings.text_item_2_element | default: 'p' %}
      <{{ element }} class="newsletter__subtitle {% render 'class-settings' prefix:'text_item_2_class' settings:section.settings %} {{ text_item_2_classes }}" {{ text_item_2_attributes }} style="{% render 'style-settings' prefix:'text_item_2_style' settings:section.settings %}">{{text_item_2_text | default: section.settings.text_item_2_text}}</{{ element}}>
      {% endif %}


			<div class="section__mini-forms">

				{% liquid 
					
					for block in section.blocks

						case block.type

							when 'mini-form'
								render 'mini-form' settings:block.settings

						endcase

					endfor 

				%}

			</div>

	</main>

</section>

{% schema %}
{
	"name": "Newsletter",
	"settings": [
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		},
		{
			"type": "header",
			"content": "@global: Wrapper Settings"
		},
		{
			"type": "paragraph",
			"content": "@include BackgroundStyles, @extends:SectionWrapper"
		},
		{
			"type": "color",
			"id": "wrapper_style_background_color",
			"label": "@global:  Background Color"
		},
		{
			"type": "color_background",
			"id": "wrapper_style_background",
			"label": "@global:  Background Gradient"
		},
		{
			"type": "image_picker",
			"label": "@global:  Background Image Mobile",
			"id": "wrapper_bg_image_mob"
		},
		{
			"type": "image_picker",
			"label": "@global:  Background Image",
			"id": "wrapper_bg_image"
		},
		{
			"type": "select",
			"id": "wrapper_class_vertical_padding",
			"label": "@global: Wrapper Vertical Padding",
			"options": [
				{
					"value": "@include Spacing prop:py",
					"label": "Inclusion"
				},
				{
					"value": "py-0",
					"label": "@global: None"
				},
				{
					"value": "py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "py-xs",
					"label": "@global: XS"
				},
				{
					"value": "py-sm",
					"label": "@global: SM"
				},
				{
					"value": "py-md",
					"label": "@global: MD"
				},
				{
					"value": "py-lg",
					"label": "@global: LG"
				},
				{
					"value": "py-xl",
					"label": "@global: XL"
				},
				{
					"value": "py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "py-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "wrapper_class_horizontal_padding",
			"label": "@global: Wrapper Horizontal Padding",
			"options": [
				{
					"value": "@include Spacing prop:px",
					"label": "Inclusion"
				},
				{
					"value": "px-0",
					"label": "@global: None"
				},
				{
					"value": "px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "px-xs",
					"label": "@global: XS"
				},
				{
					"value": "px-sm",
					"label": "@global: SM"
				},
				{
					"value": "px-md",
					"label": "@global: MD"
				},
				{
					"value": "px-lg",
					"label": "@global: LG"
				},
				{
					"value": "px-xl",
					"label": "@global: XL"
				},
				{
					"value": "px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "px-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "wrapper_class_vertical_padding_desktop",
			"label": "@global: Wrapper Vertical Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:py",
					"label": "Inclusion"
				},
				{
					"value": "lg:py-0",
					"label": "@global: None"
				},
				{
					"value": "lg:py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:py-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:py-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:py-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:py-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:py-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:py-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:py-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:py-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:py-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:py-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:py-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "select",
			"id": "wrapper_class_horizontal_padding_desktop",
			"label": "@global: Wrapper Horizontal Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:px",
					"label": "Inclusion"
				},
				{
					"value": "lg:px-0",
					"label": "@global: None"
				},
				{
					"value": "lg:px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:px-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:px-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:px-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:px-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:px-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:px-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:px-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:px-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:px-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:px-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:px-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "header",
			"content": "@global: Container Settings"
		},
		{
			"type": "select",
			"id": "container_class_container",
			"label": "@global:  Container",
			"options": [
				{
					"value": "w-full",
					"label": "Full Screen Width"
				},
				{
					"value": "container",
					"label": "Container Width"
				},
				{
					"value": "container container--wide",
					"label": "Wide Container"
				},
				{
					"value": "container container--narrow",
					"label": "Narrow Container"
				},
				{
					"value": "container container--tight",
					"label": "Very Narrow Container"
				},
				{
					"value": "container--product",
					"label": "Product Container Width"
				},
				{
					"value": "container--wide",
					"label": "Wide Container Width"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_vertical_padding",
			"label": "@global:  Vertical Padding",
			"options": [
				{
					"value": "@include Spacing prop:py",
					"label": "Inclusion"
				},
				{
					"value": "py-0",
					"label": "@global: None"
				},
				{
					"value": "py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "py-xs",
					"label": "@global: XS"
				},
				{
					"value": "py-sm",
					"label": "@global: SM"
				},
				{
					"value": "py-md",
					"label": "@global: MD"
				},
				{
					"value": "py-lg",
					"label": "@global: LG"
				},
				{
					"value": "py-xl",
					"label": "@global: XL"
				},
				{
					"value": "py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "py-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_horizontal_padding",
			"label": "@global:  Horizontal Padding",
			"options": [
				{
					"value": "@include Spacing prop:px",
					"label": "Inclusion"
				},
				{
					"value": "px-0",
					"label": "@global: None"
				},
				{
					"value": "px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "px-xs",
					"label": "@global: XS"
				},
				{
					"value": "px-sm",
					"label": "@global: SM"
				},
				{
					"value": "px-md",
					"label": "@global: MD"
				},
				{
					"value": "px-lg",
					"label": "@global: LG"
				},
				{
					"value": "px-xl",
					"label": "@global: XL"
				},
				{
					"value": "px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "px-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_vertical_padding_desktop",
			"label": "@global:  Vertical Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:py",
					"label": "Inclusion"
				},
				{
					"value": "lg:py-0",
					"label": "@global: None"
				},
				{
					"value": "lg:py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:py-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:py-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:py-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:py-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:py-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:py-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:py-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:py-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:py-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:py-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:py-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_horizontal_padding_desktop",
			"label": "@global:  Horizontal Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:px",
					"label": "Inclusion"
				},
				{
					"value": "lg:px-0",
					"label": "@global: None"
				},
				{
					"value": "lg:px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:px-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:px-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:px-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:px-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:px-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:px-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:px-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:px-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:px-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:px-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:px-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "paragraph",
			"content": "@include Text, id_prefix:text_item_1, label_prefix:Text 1"
		},
		{
			"type": "header",
			"content": "@global: Text 1 Settings"
		},
		{
			"type": "text",
			"id": "text_item_1_text",
			"label": "@global: Text 1 Text"
		},
		{
			"type": "liquid",
			"id": "text_item_1_liquid",
			"label": "@global: Text 1 Text (Liquid)"
		},
		{
			"type": "liquid",
			"id": "text_item_1_attr_x_text",
			"label": "@global: Text 1 Dynamic Text (Alpine)"
		},
		{
			"type": "select",
			"id": "text_item_1_element",
			"label": "@global: Text 1 Element",
			"default": "p",
			"options": [
				{
					"value": "@include TextElement",
					"label": "Inclusion"
				},
				{
					"value": "h1",
					"label": "@global:  Heading 1"
				},
				{
					"value": "h2",
					"label": "@global:  Heading 2"
				},
				{
					"value": "h3",
					"label": "@global:  Heading 3"
				},
				{
					"value": "h4",
					"label": "@global:  Heading 4"
				},
				{
					"value": "h5",
					"label": "@global:  Heading 5"
				},
				{
					"value": "p",
					"label": "@global:  Paragraph"
				},
				{
					"value": "div",
					"label": "@global:  Div"
				}
			]
		},
		{
			"type": "select",
			"id": "text_item_1_class_type_style",
			"label": "@global: Text 1 Type Style",
			"options": [
				{
					"value": "@include TypeStyle",
					"label": "Inclusion"
				},
				{
					"value": "",
					"label": "@global:  Auto"
				},
				{
					"value": "type-body",
					"label": "@global:  Body"
				},
				{
					"value": "type-hero",
					"label": "@global:  Hero"
				},
				{
					"value": "type-eyebrow",
					"label": "@global:  Eyebrow"
				},
				{
					"value": "type-headline",
					"label": "@global:  Headline"
				},
				{
					"value": "type-subline",
					"label": "@global:  Subline"
				},
				{
					"value": "type-micro",
					"label": "@global:  Micro"
				},
				{
					"value": "type-item",
					"label": "@global:  Item Title"
				},
				{
					"value": "type-section",
					"label": "@global:  Section Title"
				}
			]
		},
		{
			"type": "select",
			"id": "text_item_1_class_type_size",
			"label": "@global: Text 1 Type Size",
			"options": [
				{
					"value": "@include TypeSize",
					"label": "Inclusion"
				},
				{
					"value": "",
					"label": "@global:  Default"
				},
				{
					"value": "type--sm",
					"label": "@global:  Smaller"
				},
				{
					"value": "type--lg",
					"label": "@global:  Larger"
				}
			]
		},
		{
			"type": "color",
			"id": "text_item_1_style_color",
			"label": "@global: Text 1 Color"
		},
		{
			"type": "paragraph",
			"content": "@include Text, id_prefix:text_item_2, label_prefix:Text 2"
		},
		{
			"type": "header",
			"content": "@global: Text 2 Settings"
		},
		{
			"type": "text",
			"id": "text_item_2_text",
			"label": "@global: Text 2 Text"
		},
		{
			"type": "liquid",
			"id": "text_item_2_liquid",
			"label": "@global: Text 2 Text (Liquid)"
		},
		{
			"type": "liquid",
			"id": "text_item_2_attr_x_text",
			"label": "@global: Text 2 Dynamic Text (Alpine)"
		},
		{
			"type": "select",
			"id": "text_item_2_element",
			"label": "@global: Text 2 Element",
			"default": "p",
			"options": [
				{
					"value": "@include TextElement",
					"label": "Inclusion"
				},
				{
					"value": "h1",
					"label": "@global:  Heading 1"
				},
				{
					"value": "h2",
					"label": "@global:  Heading 2"
				},
				{
					"value": "h3",
					"label": "@global:  Heading 3"
				},
				{
					"value": "h4",
					"label": "@global:  Heading 4"
				},
				{
					"value": "h5",
					"label": "@global:  Heading 5"
				},
				{
					"value": "p",
					"label": "@global:  Paragraph"
				},
				{
					"value": "div",
					"label": "@global:  Div"
				}
			]
		},
		{
			"type": "select",
			"id": "text_item_2_class_type_style",
			"label": "@global: Text 2 Type Style",
			"options": [
				{
					"value": "@include TypeStyle",
					"label": "Inclusion"
				},
				{
					"value": "",
					"label": "@global:  Auto"
				},
				{
					"value": "type-body",
					"label": "@global:  Body"
				},
				{
					"value": "type-hero",
					"label": "@global:  Hero"
				},
				{
					"value": "type-eyebrow",
					"label": "@global:  Eyebrow"
				},
				{
					"value": "type-headline",
					"label": "@global:  Headline"
				},
				{
					"value": "type-subline",
					"label": "@global:  Subline"
				},
				{
					"value": "type-micro",
					"label": "@global:  Micro"
				},
				{
					"value": "type-item",
					"label": "@global:  Item Title"
				},
				{
					"value": "type-section",
					"label": "@global:  Section Title"
				}
			]
		},
		{
			"type": "select",
			"id": "text_item_2_class_type_size",
			"label": "@global: Text 2 Type Size",
			"options": [
				{
					"value": "@include TypeSize",
					"label": "Inclusion"
				},
				{
					"value": "",
					"label": "@global:  Default"
				},
				{
					"value": "type--sm",
					"label": "@global:  Smaller"
				},
				{
					"value": "type--lg",
					"label": "@global:  Larger"
				}
			]
		},
		{
			"type": "color",
			"id": "text_item_2_style_color",
			"label": "@global: Text 2 Color"
		},
		{
			"type": "paragraph",
			"content": "@include TextAlignment, id_prefix:text_alignment, label_prefix:Text Alignment"
		}
	],
	"blocks": [
		{
			"type": "mini-form",
			"name": "Mini Form",
			"settings": [
				{
					"id": "inclusion_js",
					"label": "Show Mini form",
					"type": "textarea",
					"default": "true"
				},
				{
					"type": "liquid",
					"id": "on_button_click",
					"label": "JavaScript OnButtonClick"
				},
				{
					"type": "select",
					"id": "form_method",
					"label": "Form Method",
					"default": "POST",
					"options": [
						{
							"value": "GET",
							"label": "GET"
						},
						{
							"value": "POST",
							"label": "POST"
						}
					]
				},
				{
					"type": "text",
					"id": "form_action",
					"label": "Form Action"
				},
				{
					"type": "checkbox",
					"id": "prevent_default",
					"label": "Block Default HTTP Submit",
					"default": true
				},
				{
					"type": "liquid",
					"id": "on_submit",
					"label": "JavaScript OnSubmit"
				},
				{
					"type": "select",
					"id": "field_type",
					"label": "Input Field Type",
					"default": "email",
					"options": [
						{
							"value": "email",
							"label": "Email"
						},
						{
							"value": "tel",
							"label": "Tel"
						},
						{
							"value": "text",
							"label": "Text"
						}
					]
				},
				{
					"type": "text",
					"id": "trigger_text",
					"label": "Trigger Text"
				},
				{
					"type": "text",
					"id": "field_name",
					"label": "Field Name",
					"default": "email"
				},
				{
					"type": "text",
					"id": "placeholder_text",
					"label": "Placeholder Text"
				},
				{
					"type": "text",
					"id": "input_pattern",
					"label": "Input RexEx Pattern"
				},
				{
					"type": "text",
					"id": "input_mask",
					"label": "Input Mask"
				},
				{
					"type": "liquid",
					"id": "on_input",
					"label": "JavaScript OnInput"
				},
				{
					"type": "checkbox",
					"label": "Activate email field validation",
					"id": "activate_email_validation",
					"default": false
				},
				{
					"type": "text",
					"id": "error_message",
					"label": "Field Validation Error Text"
				},
				{
					"type": "text",
					"id": "submit_text",
					"label": "Submit Text",
					"default": "Submit"
				},
				{
					"type": "select",
					"id": "button_style",
					"label": "Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "richtext",
					"id": "info_text",
					"label": "Revealed Information"
				},
				{
					"type": "richtext",
					"id": "success_text",
					"label": "Success Message"
				}
			]
		}
	]
}
{% endschema %}
