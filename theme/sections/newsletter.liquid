{% liquid 
	assign section_type = 'newsletter'
%}
<section 
	class="section section--{{ section_type }} relative {% render 'class-settings' prefix:'wrapper_class' settings:section.settings %}" 
	style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}">

	{% if section.settings.wrapper_bg_image %}
		<img src="{{ section.settings.wrapper_bg_image | image_url }}" alt="{{ section.settings.wrapper_bg_image.alt }}" class="absolute inset-0 object-cover w-full h-full section__media" />
	{% endif %}
	
	<main 
		class="section__container relative {% render 'class-settings' prefix:'container_class' settings:section.settings %} {% render 'class-settings' prefix:'text_alignment_class' settings:section.settings %}" 
		style="{% render 'style-settings' prefix:'container_styles' settings:section.settings %}">

			<!-- text items with text content and format settings -->
      
      {% if section.settings.text_item_1_text != blank or text_item_1_text != blank or text_item_1_attributes %}
      {% assign element = text_item_1_element | default: section.settings.text_item_1_element | default: 'p' %}
      <{{ element }} class="newsletter__title {% render 'class-settings' prefix:'text_item_1_class' settings:section.settings %} {{ text_item_1_classes }}" {{ text_item_1_attributes }} style="{% render 'style-settings' prefix:'text_item_1_style' settings:section.settings %}">{{text_item_1_text | default: section.settings.text_item_1_text}}</{{ element}}>
      {% endif %}
      
      {% if section.settings.text_item_2_text != blank or text_item_2_text != blank or text_item_2_attributes %}
      {% assign element = text_item_2_element | default: section.settings.text_item_2_element | default: 'p' %}
      <{{ element }} class="newsletter__subtitle {% render 'class-settings' prefix:'text_item_2_class' settings:section.settings %} {{ text_item_2_classes }}" {{ text_item_2_attributes }} style="{% render 'style-settings' prefix:'text_item_2_style' settings:section.settings %}">{{text_item_2_text | default: section.settings.text_item_2_text}}</{{ element}}>
      {% endif %}


			<div class="section__mini-forms">

				{% liquid 
					
					for block in section.blocks

						case block.type

							when 'mini-form'
								render 'mini-form' settings:block.settings

						endcase

					endfor 

				%}

			</div>

	</main>

</section>

{% schema %}
{
	"name": "Newsletter",
	"settings": [
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "paragraph",
			"content": "@include Text, id_prefix:text_item_1, label_prefix:Text 1"
		},
		{
			"type": "paragraph",
			"content": "@include Text, id_prefix:text_item_2, label_prefix:Text 2"
		},
		{
			"type": "paragraph",
			"content": "@include TextAlignment, id_prefix:text_alignment, label_prefix:Text Alignment"
		}
	],
	"blocks": [
		{
			"type": "mini-form",
			"name": "Mini Form",
			"settings": [
				{
					"id": "inclusion_js",
					"label": "Show Mini form",
					"type": "textarea",
					"default": "true"
				},
				{
					"type": "liquid",
					"id": "on_button_click",
					"label": "JavaScript OnButtonClick"
				},
				{
					"type": "select",
					"id": "form_method",
					"label": "Form Method",
					"default": "POST",
					"options": [
						{
							"value": "GET",
							"label": "GET"
						},
						{
							"value": "POST",
							"label": "POST"
						}
					]
				},
				{
					"type": "text",
					"id": "form_action",
					"label": "Form Action"
				},
				{
					"type": "checkbox",
					"id": "prevent_default",
					"label": "Block Default HTTP Submit",
					"default": true
				},
				{
					"type": "liquid",
					"id": "on_submit",
					"label": "JavaScript OnSubmit"
				},
				{
					"type": "select",
					"id": "field_type",
					"label": "Input Field Type",
					"default": "email",
					"options": [
						{
							"value": "email",
							"label": "Email"
						},
						{
							"value": "tel",
							"label": "Tel"
						},
						{
							"value": "text",
							"label": "Text"
						}
					]
				},
				{
					"type": "text",
					"id": "trigger_text",
					"label": "Trigger Text"
				},
				{
					"type": "text",
					"id": "field_name",
					"label": "Field Name",
					"default": "email"
				},
				{
					"type": "text",
					"id": "placeholder_text",
					"label": "Placeholder Text"
				},
				{
					"type": "text",
					"id": "input_pattern",
					"label": "Input RexEx Pattern"
				},
				{
					"type": "text",
					"id": "input_mask",
					"label": "Input Mask"
				},
				{
					"type": "liquid",
					"id": "on_input",
					"label": "JavaScript OnInput"
				},
				{
					"type": "checkbox",
					"label": "Activate email field validation",
					"id": "activate_email_validation",
					"default": false
				},
				{
					"type": "text",
					"id": "error_message",
					"label": "Field Validation Error Text"
				},
				{
					"type": "text",
					"id": "submit_text",
					"label": "Submit Text",
					"default": "Submit"
				},
				{
					"type": "select",
					"id": "button_style",
					"label": "Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "richtext",
					"id": "info_text",
					"label": "Revealed Information"
				},
				{
					"type": "richtext",
					"id": "success_text",
					"label": "Success Message"
				}
			]
		}
	]
}
{% endschema %}
