{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






{%- assign matches = match | split: ',' -%}
{%- assign tagMatch = false -%}
{%- for matchitem in matches -%}
  {%- assign inclusions = matchitem | split: '+' -%}
  {%- assign passes = true -%}
  {%- for inclusion in inclusions -%}
    {%- assign inc = inclusion | strip -%}
    {%- unless tags contains inc -%}
      {%- assign passes = false -%}
      {%- break -%}
    {%- endunless -%}
  {%- endfor -%}
  {%- if passes -%}{%- assign tagMatch = true -%}{%- break -%}{%- endif -%}
{%- endfor -%}