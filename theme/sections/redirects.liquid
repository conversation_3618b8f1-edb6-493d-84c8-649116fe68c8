<script type="text/javascript">
  window.addEventListener('DOMContentLoaded', () => {
    Redirects.init()
    setTimeout(()=>{
      {% if settings.enable_logs -%}console.log('Redirects - initial', JSON.parse(JSON.stringify(window.redirects))){%- endif %}
      {% for block in section.blocks %}
        {% case block.type %}
          {% when 'redirect' %}
            if({{ block.settings.inclusion_js | default: 'false'}}) {
              {% if settings.enable_logs -%}console.log('Redirects - its in the inclusion'){%- endif %}
              {% if settings.enable_logs -%}console.log('Redirects', {{ block.settings | json }}){%- endif %}
              Redirects.push({{ block.settings | json }})
            }
        {% endcase %}
      {% endfor %}
      {% if settings.enable_logs -%}console.log('Redirects - updated', JSON.parse(JSON.stringify(window.redirects))){%- endif %}

      Redirects.replace()
      Redirects.checkout()

    },100)   
  })
</script> 

{% schema %}
{
  "name": "Redirects",
  "blocks": [
    {
      "type": "redirect",
      "name": "Redirect",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "info": "Internal reference only"
        },
        {
          "type": "text",
          "id": "key",
          "label": "Key *",
          "info": "Required. Shared keys will override each other."
        },
        {
          "type": "liquid",
          "id": "inclusion",
          "label": "Liquid Inclusion Logic *",
          "info": "[Liquid] A value other than blank will register the redirect",
          "default":"1"
        },
        {
          "type": "liquid",
          "id": "inclusion_js",
          "label": "JS Inclusion Logic",
          "info": "[JavaScript] An expression that produces a true value will register the redirect"
        },
        {
          "type": "textarea",
          "id": "conditions",
          "label": "Action Conditions *",
          "info": "[JavaScript] An expression that produces a true value will allow redirect to action"
        },
        {
          "type": "text",
          "id": "destination",
          "label": "Destination *"
        },
        {
          "type": "select",
          "id": "persist",
          "label": "Persist Across",
          "default":"",
          "options": [
            {
              "label":"None",
              "value":""
            },
            {
              "label":"Session",
              "value":"session"
            },
            {
              "label":"Browser",
              "value":"local"
            }
          ]
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Redirects",
      "category": "Advanced",
      "settings": {},
      "blocks": []
    }
  ]
}
{% endschema %}
