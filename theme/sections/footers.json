{"type": "footer", "name": "Footers", "sections": {"flex_xjJAhm": {"type": "flex", "blocks": {"frame_X4ngVg": {"type": "frame", "disabled": true, "settings": {"title": "⤷ Frame", "article_class_visibility": "", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "w-full", "article_class_width_desktop": "lg:w-full", "article_class_height": "h-full", "article_class_height_desktop": "lg:h-full", "article_style_background_color": "", "article_style_background": "linear-gradient(180deg, rgba(24, 92, 36, 0.25), rgba(1, 63, 11, 0.5) 100%)", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-row", "article_class_layout": "layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "@include Spacing prop:py", "article_class_horizontal_padding": "@include Spacing prop:px", "article_class_gap": "@include Spacing prop:gap", "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "", "article_attr_x_data": "", "article_attr_x_if": ""}}}, "block_order": ["frame_X4ngVg"], "custom_css": ["{position: sticky; bottom: 0; top: 0; z-index: -1;}"], "settings": {"wrapper_style_background_color": "", "wrapper_style_background": "", "wrapper_bg_image": "shopify://shop_images/topography_dark.png", "container_class_container": "w-full", "section_class_height": "h-screen", "section_class_height_desktop": "lg:h-screen", "section_class_display": "flex", "section_class_display_desktop": "lg:flex", "section_class_direction": "flex-row", "section_class_layout": "layout-top", "section_class_layout_spacing": "layout-space-packed", "section_class_vertical_padding": "@include Spacing prop:py", "section_class_horizontal_padding": "@include Spacing prop:px", "section_class_gap": "@include Spacing prop:gap", "section_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "section_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "section_class_gap_desktop": "@include SpacingDesktop prop:gap"}}, "flex_fez6Jp": {"type": "flex", "blocks": {"frame_jKQU3R": {"type": "frame", "settings": {"title": "⤷ Frame", "article_class_visibility": "", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "w-full", "article_class_width_desktop": "lg:w-2/5", "article_class_height": "h-auto", "article_class_height_desktop": "lg:h-auto", "article_style_background_color": "#ffffff", "article_style_background": "", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-col", "article_class_layout": "layout-left layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "py-2xl", "article_class_horizontal_padding": "px-md", "article_class_gap": "gap-sm", "article_class_vertical_padding_desktop": "lg:py-6xl", "article_class_horizontal_padding_desktop": "lg:px-4xl", "article_class_gap_desktop": "lg:gap-sm", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "rounded-md", "article_attr_x_data": "", "article_attr_x_if": ""}}, "title_zBpWhF": {"type": "title", "settings": {"title_text": "Get 10% Off", "title_liquid": "", "title_element": "h3", "title_class_type_style": "type-section", "title_class_type_size": "@include TypeSize", "title_style_color": "", "title_class_justification": "text-left", "text_class_wrap": "text-wrap"}}, "divider_ReBdx7": {"type": "divider", "settings": {"divider_style_border_color": "", "divider_class_width": "w-full", "divider_class_width_desktop": "lg:w-3/4"}}, "rich_text_hkELKU": {"type": "rich-text", "settings": {"text_text": "<p>Get access to new releases and the latest updates. Just choose how you want to hear from us. Get two codes: one for Email and one for Text. Codes expire 30 Days after signup.</p>", "text_liquid": "", "text_class_type_style": "@include TypeStyle", "text_class_type_size": "@include TypeSize", "text_style_color": "", "text_class_justification": "text-left", "text_class_measure": "measure", "text_class_wrap": "text-wrap", "text_class_width": "w-full", "text_class_width_desktop": "lg:w-full"}}, "spacer_jnRawA": {"type": "spacer", "settings": {"spacer_class_vertical_spacing": "pb-md", "spacer_class_horizontal_spacing": "@include Spacing prop:pr", "spacer_class_vertical_spacing_desktop": "lg:pb-sm", "spacer_class_horizontal_spacing_desktop": "@include SpacingDesktop prop:pr"}}, "frame_dxYfW9": {"type": "frame", "settings": {"title": "⤷ Frame", "article_class_visibility": "", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "w-full", "article_class_width_desktop": "lg:w-3/5", "article_class_height": "h-auto", "article_class_height_desktop": "lg:h-auto", "article_style_background_color": "", "article_style_background": "", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-col", "article_class_layout": "layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "@include Spacing prop:py", "article_class_horizontal_padding": "@include Spacing prop:px", "article_class_gap": "gap-sm", "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "article_class_gap_desktop": "lg:gap-sm", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "", "article_attr_x_data": "", "article_attr_x_if": ""}}, "mini_form_CenwWm": {"type": "mini-form", "settings": {"on_button_click": "", "form_method": "POST", "form_action": "", "prevent_default": true, "on_submit": "", "field_type": "email", "trigger_text": "Sign Up For Emails", "field_name": "email", "placeholder_text": "Your Email Address", "input_pattern": "", "input_mask": "", "on_input": "", "activate_email_validation": false, "error_message": "", "submit_text": "Submit", "button_style": "button--primary", "info_text": "", "success_text": ""}}, "divider_EEyYDb": {"type": "divider", "settings": {"divider_style_border_color": "#aeaeae", "divider_class_width": "w-full", "divider_class_width_desktop": "lg:w-full"}}, "mini_form_pdNMYk": {"type": "mini-form", "settings": {"on_button_click": "", "form_method": "POST", "form_action": "", "prevent_default": true, "on_submit": "", "field_type": "email", "trigger_text": "Sign Up For Texts", "field_name": "phone", "placeholder_text": "Your Phone Number", "input_pattern": "", "input_mask": "", "on_input": "", "activate_email_validation": false, "error_message": "", "submit_text": "Submit", "button_style": "button--dark", "info_text": "", "success_text": ""}}, "break_B7HyH7": {"type": "break", "settings": {"title": "⤶ Frame End"}}, "break_GaFfj9": {"type": "break", "settings": {"title": "⤶ Frame End"}}, "frame_EFqp4H": {"type": "frame", "settings": {"title": "⤷ Frame", "article_class_visibility": "", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "w-full", "article_class_width_desktop": "lg:w-3/5", "article_class_height": "h-auto", "article_class_height_desktop": "lg:h-auto", "article_style_background_color": "", "article_style_background": "", "article_class_display": "grid grid-cols-1", "article_class_display_desktop": "lg:grid lg:grid-cols-3", "article_class_direction": "flex-row", "article_class_layout": "layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "py-md", "article_class_horizontal_padding": "px-md", "article_class_gap": "gap-sm", "article_class_vertical_padding_desktop": "lg:py-3xl", "article_class_horizontal_padding_desktop": "lg:px-3xl", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "footer-menus", "article_attr_x_data": "", "article_attr_x_if": ""}}, "linklist_fkytFE": {"type": "linklist", "settings": {"link_list": "footer-customer-service", "linklist_class_format": "link-list--basic", "link_style_color": "#ffffff", "linklist_class_display": "flex", "linklist_class_display_desktop": "lg:flex", "linklist_class_direction": "flex-col", "linklist_class_layout": "layout-top", "linklist_class_layout_spacing": "layout-space-packed", "linklist_class_vertical_padding": "@include Spacing prop:py", "linklist_class_horizontal_padding": "@include Spacing prop:px", "linklist_class_gap": "gap-0", "linklist_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "linklist_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "linklist_class_gap_desktop": "@include SpacingDesktop prop:gap"}}, "linklist_RKQMeQ": {"type": "linklist", "settings": {"link_list": "footer-our-company", "linklist_class_format": "link-list--basic", "link_style_color": "#ffffff", "linklist_class_display": "flex", "linklist_class_display_desktop": "lg:flex", "linklist_class_direction": "flex-col", "linklist_class_layout": "layout-top", "linklist_class_layout_spacing": "layout-space-packed", "linklist_class_vertical_padding": "@include Spacing prop:py", "linklist_class_horizontal_padding": "@include Spacing prop:px", "linklist_class_gap": "@include Spacing prop:gap", "linklist_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "linklist_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "linklist_class_gap_desktop": "@include SpacingDesktop prop:gap"}}, "linklist_FbWRJV": {"type": "linklist", "settings": {"link_list": "footer", "linklist_class_format": "link-list--basic", "link_style_color": "#ffffff", "linklist_class_display": "flex", "linklist_class_display_desktop": "lg:flex", "linklist_class_direction": "flex-col", "linklist_class_layout": "layout-top", "linklist_class_layout_spacing": "layout-space-packed", "linklist_class_vertical_padding": "@include Spacing prop:py", "linklist_class_horizontal_padding": "@include Spacing prop:px", "linklist_class_gap": "@include Spacing prop:gap", "linklist_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "linklist_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "linklist_class_gap_desktop": "@include SpacingDesktop prop:gap"}}, "frame_DM7GG6": {"type": "frame", "settings": {"title": "⤷ Frame", "article_class_visibility": "", "inclusion_liquid": "true", "inclusion_js": "", "article_class_width": "col-span-1", "article_class_width_desktop": "lg:col-span-3", "article_class_height": "h-auto", "article_class_height_desktop": "lg:h-auto", "article_style_background_color": "", "article_style_background": "", "article_class_display": "flex", "article_class_display_desktop": "lg:flex", "article_class_direction": "flex-col", "article_class_layout": "layout-top", "article_class_layout_spacing": "layout-space-packed", "article_class_vertical_padding": "@include Spacing prop:py", "article_class_horizontal_padding": "@include Spacing prop:px", "article_class_gap": "@include Spacing prop:gap", "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py", "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px", "article_class_gap_desktop": "@include SpacingDesktop prop:gap", "article_class_position": "relative", "article_class_overflow": "", "article_class_custom_classes": "", "article_attr_x_data": "", "article_attr_x_if": ""}}, "spacer_zxwcEm": {"type": "spacer", "settings": {"spacer_class_vertical_spacing": "pb-md", "spacer_class_horizontal_spacing": "@include Spacing prop:pr", "spacer_class_vertical_spacing_desktop": "lg:pb-2xl", "spacer_class_horizontal_spacing_desktop": "@include SpacingDesktop prop:pr"}}, "divider_68GF8r": {"type": "divider", "settings": {"divider_style_border_color": "#eeeeee", "divider_class_width": "w-full", "divider_class_width_desktop": "lg:w-full"}}, "spacer_diPeEF": {"type": "spacer", "settings": {"spacer_class_vertical_spacing": "pb-md", "spacer_class_horizontal_spacing": "@include Spacing prop:pr", "spacer_class_vertical_spacing_desktop": "lg:pb-2xl", "spacer_class_horizontal_spacing_desktop": "@include SpacingDesktop prop:pr"}}, "rich_text_rjGBgm": {"type": "rich-text", "settings": {"text_text": "<p><strong>Phone:</strong>************<strong><br/>Email:</strong><a href=\"mailto:<EMAIL>\"> <EMAIL></a><br/><br/>© 2024 Roark. All Rights Reserved.<br/><a href=\"https://roark.com/pages/privacy-policy\">Privacy Policy | </a><a href=\"https://roark.com/pages/cookie-policy\">Cookie Policy </a>| <a href=\"https://roark.com/pages/accessibility-statement\">Accessibility </a>| <a href=\"https://roark.com/pages/california-consumer-privacy-act-ccpa\">Do Not Sell </a>| <a href=\"https://roark.com/pages/map-policy\">MAP Policy | </a><a href=\"https://roark.com/pages/terms\">Terms</a></p>", "text_liquid": "", "text_class_type_style": "type-micro", "text_class_type_size": "@include TypeSize", "text_style_color": "#eeeeee", "text_class_justification": "text-right", "text_class_measure": "", "text_class_wrap": "text-wrap", "text_class_width": "w-full", "text_class_width_desktop": "lg:w-full"}}, "spacer_NBUmkX": {"type": "spacer", "settings": {"spacer_class_vertical_spacing": "pb-3xl", "spacer_class_horizontal_spacing": "@include Spacing prop:pr", "spacer_class_vertical_spacing_desktop": "lg:pb-0", "spacer_class_horizontal_spacing_desktop": "@include SpacingDesktop prop:pr"}}, "spacer_kcz4HK": {"type": "spacer", "settings": {"spacer_class_vertical_spacing": "pb-2xl", "spacer_class_horizontal_spacing": "@include Spacing prop:pr", "spacer_class_vertical_spacing_desktop": "lg:pb-0", "spacer_class_horizontal_spacing_desktop": "@include SpacingDesktop prop:pr"}}}, "block_order": ["frame_jKQU3R", "title_zBpWhF", "divider_ReBdx7", "rich_text_hkELKU", "spacer_jnRawA", "frame_dxYfW9", "mini_form_CenwWm", "divider_EEyYDb", "mini_form_pdNMYk", "break_B7HyH7", "break_GaFfj9", "frame_EFqp4H", "linklist_fkytFE", "linklist_RKQMeQ", "linklist_FbWRJV", "frame_DM7GG6", "spacer_zxwcEm", "divider_68GF8r", "spacer_diPeEF", "rich_text_rjGBgm", "spacer_NBUmkX", "spacer_kcz4HK"], "custom_css": ["{margin-top: -50vh;}"], "settings": {"wrapper_style_background_color": "", "wrapper_style_background": "linear-gradient(180deg, rgba(1, 77, 14, 0), rgba(1, 63, 11, 0) 97%)", "container_class_container": "w-full", "section_class_height": "h-auto", "section_class_height_desktop": "lg:h-auto", "section_class_display": "grid grid-cols-1", "section_class_display_desktop": "lg:flex", "section_class_direction": "flex-row", "section_class_layout": "layout-top", "section_class_layout_spacing": "layout-space-packed", "section_class_vertical_padding": "@include Spacing prop:py", "section_class_horizontal_padding": "px-md", "section_class_gap": "@include Spacing prop:gap", "section_class_vertical_padding_desktop": "lg:py-2xl", "section_class_horizontal_padding_desktop": "lg:px-2xl", "section_class_gap_desktop": "@include SpacingDesktop prop:gap"}}}, "order": ["flex_xjJAhm", "flex_fez6Jp"]}