{% comment %} 


This is generated by webpack so don't edit this unless you want to be sad when your work gets overwritten in production.
You can edit this file in ../../components/** 


{% endcomment %}






<div data-modal="search" class="fixed top-0 right-0 bg-white z-50 modal-top animate  w-screen transition-transform ease-in-out duration-300" tabindex="0" neptune-engage="{
  on:keyup,
  which: 27,
  targets:[{
    selector:body,
    attributes:[{
      att:data-active-modal,
      set:_remove
    }]
  },
  { 
    selector:'[data-return-focus]',
    attributes:[{
      att:data-return-focus,
      set:_remove
    }],
    focus:true
  }]
}">
    
<section class="header w-full animate z-30 flex flex-row items-center bg-white text-black py-0 h-auto lg:px-4 pl-2 pr-1 justify-center border-b b--light-gray">
  

  <form action="/search" method="get" role="search" class="flex relative container bg-light-gray br-pill items-center justify-center w-full group modal-search-form">
    <label for="headerSearch" class="invisible hidden">
      Search
    </label>
    <input 
    type="search" 
    name="q" 
    id="headerSearch" 
    value="" 
    placeholder="Search" 
    class="focus:border-0 focus:outline-none h-16 lg:h-24 lg:mt-0 pl-10 shadow-none ui-autocomplete-input w-full" 
    autocomplete="off" 
    autocorrect="off" 
    autocapitalize="off" 
    aria-label="Search" 
    aria-autocomplete="list" 
    onkeydown="this.typingTimer = this.typingTimer || {}; clearTimeout(this.typingTimer);" 
    onkeyup="clearTimeout(this.typingTimer); this.typingTimer = setTimeout(()=>{Neptune.liquid.load('SearchResults','url:/search/suggest.json?resources[type]=product&q='+this.value+'');Neptune.liquid.load('SearchCollections','url:/search/suggest.json?resources[type]=collection&q='+this.value+'');_n.qs('.search-results').classList.remove('hidden')},1000)"
    >
    <input type="submit" value="go" class="sr-only">
    <button class="h-full border-none cursor-pointer text-center absolute animate left-0 top-0 px-2">
      <span class="icon text-black flex flex-col justify-center items-center mha" style="max-width: 38px;">
        {% render 'icon' icon:'search' %}
      </span>
      <span class="icon-fallback-text sr-only">Search</span>
    </button>
    <button class="" neptune-engage="{preventDefault:true,targets:[
      {selector:body,attributes:[{att:data-active-modal,set:_remove}]},
      {selector:.search-results,classes:add:hidden}
      ]}">
        {% render 'icon' icon:'x-circle' %}
    </button>           
  </form>

  <div class="absolute search-results hidden bg-white flex left-0 max-h-main overflow-y-scroll max-h-main right-0 smooth-scroll top-full w-full">
    <div class="lg:w-1/4 p-4 lg:p-6" aria-live="polite"neptune-liquid="{topic:SearchCollections}">
      {% raw %}
        {% if resources and resources.results.collections.size > 0 %}
          <h3 class="link db black lh-copy pb1-l pt4-l pv2 ph4 f5 brand-semibold">Collections</h3>
        {% endif %}
      {% endraw %}
      
      <ul class="list p-5">    
        {% raw %}
        {% if resources and resources.results.collections.size > 0 %}
          {% for collection in resources.results.collections %}
            <li>
              <a class="link db black brand-medium brighten animate lh-copy pv1-l pv2 ph3 ph0-l" tabindex="1" href="{{collection.url}}">{{ collection.title }}</a>
            </li>
          {% endfor %}
        {% else %}
          {% endraw %}
            {% for block in section.blocks %}
              {% if block.settings.query != blank %}
                <li>
                  <a class="link db black brand-medium brighten animate lh-copy pv1-l pv2 ph3 ph0-l" tabindex="1" href="/search?q={{block.settings.query}}">{{block.settings.title}}</a>
                </li>
              {% endif %}
            {% endfor %}
          {% raw %}
        {% endif %}
        {% endraw %}
      </ul>
    </div>
    <div class="w-3/4 p-4 lg:p-6" aria-live="polite"neptune-liquid="{topic:SearchResults}">
        {% raw %}
          {% if resources %}
          <div class="flex flex-wrap">
            {% for product in resources.results.products %}

              <a tabindex="1" href="{{product.url}}" class="w-1/2 lg:w-1/4">

                <div class="aspect-w-1 aspect-h-1 mb-2">
                  <img
                    class=""
                    {{'s'}}rc="{{ product.image }}"
                    alt="{{product.title}}"
                  >
                </div>
                <div>{{product.title}}</div>
                <div>{{product.price | times: 100 | money}}</div>
              </a>
            {% endfor %}
          </div>
          {% endif %}
        {% endraw %}
    </div>
  </div>


</section>

</div>


{% schema %}
  {
    "name": "Search",
    "settings": [
    ],
    "blocks": [
    ]
  }
{% endschema %}