{% schema %}
	{
	  "name": "Globals",
	  "settings": []
	}
{% endschema %}

<div class="w-full p-4 mx-auto lg:p-8 lg:w-1/2">
	<h1 class="mb-8 sr-only">{{ page.title }}</h1>

	<div class="p-4 m-2 overflow-hidden bg-gray-100 rounded-md">
		<h3 class="px-3 py-2 m-0 mb-4 font-bold tracking-wider text-gray-800 bg-gray-200 rounded-md">Colors</h3>
		<div class="flex flex-wrap p-0 m-0 list">

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-gray-300" href="#">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-primary"></span>
				<span data-copy="true">Primary <br/>[utility]-primary</span>
				<span class="block text-sm text-gray-400" data-css-var="--color-primary"></span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-gray-300" href="#">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-secondary"></span>
				<span data-copy="true">Secondary <br/>[utility]-secondary</span>
				<span class="block text-sm text-gray-400" data-css-var="--color-secondary"></span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-gray-300" href="#">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-tertiary"></span>
				<span data-copy="true">Tertiary <br/>[utility]-tertiary</span>
				<span class="block text-sm text-gray-400" data-css-var="--color-tertiary"></span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-gray-300" href="#">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-light"></span>
				<span data-copy="true">Light <br/>[utility]-light</span>
				<span class="block text-sm text-gray-400" data-css-var="--color-light"></span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-gray-300" href="#">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-dark"></span>
				<span data-copy="true">Dark <br/>[utility]-dark</span>
				<span class="block text-sm text-gray-400" data-css-var="--color-dark"></span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-gray-300" href="#">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-pop"></span>
				<span data-copy="true">Pop <br/>[utility]-pop</span>
				<span class="block text-sm text-gray-400" data-css-var="--color-pop"></span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-gray-300" href="#">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-highlight"></span>
				<span data-copy="true">Highlight <br/>[utility]-highlight</span>
				<span class="block text-sm text-gray-400" data-css-var="--color-highlight"></span>
			</a>

			<a class="w-1/4 py-4 mb-2 text-center cursor-pointer hover:bg-gray-300" href="#">
				<span class="block w-16 h-16 mx-auto mb-2 border border-black bg-body"></span>
				<span data-copy="true">Body <br/>[utility]-body</span>
				<span class="block text-sm text-gray-400" data-css-var="--color-body"></span>
			</a>

		</div>
	</div>
	 
	<div class="p-4 m-2 mt-8 overflow-hidden bg-gray-100 rounded-md">
		<h3 class="px-3 py-2 m-0 mb-4 font-bold tracking-wider text-gray-800 bg-gray-200 rounded-md">Text Styles</h3>
		<h1>h1: Heading 1</h1>
		<h2>h2: Heading 2</h2>
		<h3>h3: Heading 3</h3>
		<h4>h4: Heading 4</h4>
		<h5>h5: Heading 5</h5>
		<h6>h6: Heading 6</h6>
		<p class="text-base">Paragraph</p>
		<p class="text-xs">Paragraph XS</p>
		<p class="text-sm">Paragraph SM</p>
		<p class="text-lg">Paragraph LG</p>
	</div>

	<div class="p-4 m-2 mt-8 overflow-hidden bg-gray-100 rounded-md">
		<h3 class="px-3 py-2 m-0 mb-4 font-bold tracking-wider text-gray-800 bg-gray-200 rounded-md">Type Styles</h3>
		<h2 class="mt-4 mb-2 text-xl text-black">Hero</h2>
		<p class="text-xs text-gray-500">
			Description: Hero type styles are used for large text throughout the site, typically in a prominent or attention-grabbing format. Common examples include text overlays on slideshows or images with text overlays, where the messaging is featured in a larger than normal type style.
		</p>
		<p class="type-hero">Hero</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-hero</code>
		<p class="type-hero type--sm">Hero Small</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-hero .type--sm</code>
		<p class="type-hero type--lg">Hero Large</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-hero .type--lg</code>

		<hr>
		<h2 class="mt-4 mb-2 text-xl text-black">Headline</h2>
		<p class="text-xs text-gray-500">
			Description: Headline type styles are used for titles, such as product titles, page titles, article titles, and collection titles. This style is meant to give prominence and hierarchy to key content elements.
		</p>
		<p class="type-headline">Headline</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-headline</code>
		<p class="type-headline type--sm">Headline Small</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-headline .type--sm</code>
		<p class="type-headline type--lg">Headline Large</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-headline .type--lg</code>

		<hr>
		<h2 class="mt-4 mb-2 text-xl text-black">Subline</h2>
		<p class="text-xs text-gray-500">
			Description: Subline type styles are used for text that follows hero or headline type styles. These are typically secondary or supplementary pieces of information that provide context or additional details.
		</p>
		<p class="type-subline">Subline</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-subline</code>
		<p class="type-subline type--sm">Subline Small</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-subline .type--sm</code>
		<p class="type-subline type--lg">Subline Large</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-subline .type--lg</code>

		<hr>
		<h2 class="mt-4 mb-2 text-xl text-black">Item</h2>
		<p class="text-xs text-gray-500">
			Description: Item type styles are used for product items on Product Listing Pages (PLP), recommended products, article items on blog landing pages, and similar contexts. This style is used to give a consistent look and feel to related content elements.
		</p>
		<p class="type-item">Item Base</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-item</code>
		<p class="type-item type--sm">Item Small</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-item .type--sm</code>
		<p class="type-item type--lg">Item Large</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-item .type--lg</code>

		<hr>
		<h2 class="mt-4 mb-2 text-xl text-black">Section</h2>
		<p class="text-xs text-gray-500">
			Description: Section type styles are used for primary titles of sections. For example, a carousel section may have a title like "Men's New Arrivals," or a page featuring brick-and-mortar stores might have a title that says "California Stores."
		</p>
		<p class="type-section">Section</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-section</code>
		<p class="type-section type--sm">Section Small</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-section .type--sm</code>
		<p class="type-section type--lg">Section Large</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-section .type--lg</code>

		<hr>
		<h2 class="mt-4 mb-2 text-xl text-black">Eyebrow</h2>
		<p class="text-xs text-gray-500">
			Description: Eyebrow type styles are used for small text placed above hero or headline text styles. This style is typically used to provide additional context, a category label, or an introduction to the main content.
		</p>
		<p class="type-eyebrow">Eyebrow</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-eyebrow</code>
		<p class="type-eyebrow type--sm">Eyebrow Small</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-eyebrow .type--sm</code>
		<p class="type-eyebrow type--lg">Eyebrow Large</p>
		<code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">.type-eyebrow .type--lg</code>
	</div>

	<div class="p-4 m-2 mt-8 overflow-hidden bg-gray-100 rounded-md">
		<h3 class="px-3 py-2 m-0 mb-4 font-bold tracking-wider text-gray-800 bg-gray-200 rounded-md">Font Families</h3>

		<p class="my-4 text-base font-body" data-css-var="--font-body-family"></p>
		<p class="my-4 text-base font-heading" data-css-var="--font-heading-family"></p>
		<p class="my-4 text-base font-subheading" data-css-var="--font-subheading-family"></p>
		<p class="my-4 text-base font-olukai-bold" data-css-var="--font-olukai-bold"></p>
	</div>

	<div class="p-4 m-2 mt-8 overflow-hidden bg-gray-100 rounded-md">
		<h3 class="px-3 py-2 m-0 mb-4 font-bold tracking-wider text-gray-800 bg-gray-200 rounded-md">Forms & Buttons</h3>

		<form>

			<p class="mt-4 mb-2 underline">Text Input</p>

      {% render 'field' name: 'Full Name' placeholder: 'John Doe' label: 'Full Name' %}
      {% render 'field' name: 'Full Name' style: 'field--floating-label' label: 'Full Name' %}
      {% render 'field' name: 'Email' type: 'email' placeholder: '<EMAIL>' label: 'Email' %}
      {% render 'field' name: 'Email' type: 'email' style: 'field--floating-label' label: 'Email' %}
      {% render 'field' name: 'Phone' type: 'tel' placeholder: '************' attributes: 'pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" required' %}
      {% render 'field' name: 'Phone' type: 'tel' error:'Please enter a valid phone number' attributes: 'pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" required' %}
      {% render 'field' name: 'Password' type: 'password' %}
      {% render 'field' name: 'Password' type: 'password' style: 'field--floating-label' %}
      {% render 'field' name: 'Cookies' label: 'How many cookies would you like?' type: 'number' attributes: 'min="1" max="100"' %}
      {% render 'field' name: 'Cookies' label: 'How many cookies would you like?' type: 'number' attributes: 'min="1" max="100"' style:"field--floating-label" %}
      {% render 'field' name: 'comment' label: 'Tell me about something' type: 'textarea' %}
      {% render 'field' name: 'comment' label: 'Tell me about something' type: 'textarea' style:"field--floating-label" %}
      {% render 'field' type: 'select' name: 'Favorite Color' options: 'Green, Red, Blue' %}
      {% render 'field' type: 'toggle' name: 'Image Type (with paired options)' options: 'Product:View Product, Model:View on Model' %}

      {%- capture selectOptions -%}
        <template x-for="option in options">
          <option x-text="option"></option>
        </template>
      {%- endcapture -%}
      {% render 'field' type: 'select' name: 'Favorite Option' content: selectOptions style: 'field--floating-label' label: "Options" attributes: 'x-data="{options: [`Option 1`, `Option 2`, `Option 3`]}"' %}

      {% render 'popover-menu' label:'Sort By:' %}


      {% render 'field' type: 'checkbox' name: 'Favorite Color' options: 'Green, Red, Blue' %}
      {% render 'field' type: 'radio' name: 'Favorite Animal' options: 'Dog, Cat, Fish' %}
      {% render 'field' type: 'color' name: 'Favorite Color' options: 'Green, Red:#C32033, Blue:#1E3E58' %}
      {% render 'field' type: 'button' name: 'Size' options: '9, 10, 10.5' %}

			<p class="mt-4 mb-2 underline">Quanity Input</p>

			<div class="flex items-center w-1/3 field field-plus-minus">
	      <button onclick="Neptune.cart.changeItem({{forloop.index0}},{{item.quantity | minus: 1}})">{% render 'icon' icon: 'minus' width:16 height:16 strokeWidth:1 %}</button>
	      <input type="text" oninput="Neptune.cart.changeItem({{forloop.index0}},this.value)" value="{{item.quantity}}"></input>
	      <button onclick="Neptune.cart.changeItem({{forloop.index0}},{{item.quantity | plus: 1}})">{% render 'icon' icon: 'plus' width:16 height:16 strokeWidth:1 %}</button>
	    </div>
	  </div>

	  <div class="p-4 m-2 mt-8 overflow-hidden bg-gray-100 rounded-md">
			<h3 class="px-3 py-2 m-0 mb-4 font-bold tracking-wider text-gray-800 bg-gray-200 rounded-md">Tools</h3>

      {% render 'progress' id: 'progress_bar' value: 20 max: 100 options: 'Narrow, Standard, Wide' active_options: 'Standard, Narrow' %}

	    <div class="flex mb-4">
	    	<button class="btn-control">
      		{% render 'icon' icon:'chevron-left' width:30 height:30 %}
    		</button>

		    <button class="btn-control">
		      {% render 'icon' icon:'chevron-right' width:30 height:30 %}
		    </button>

		    <button class="btn-control">
					{% render 'icon' icon:'x' width:30 height:30 %}
				</button>
	    </div>

		</form>

		<div class="relative py-4 mb-4">
			<div class="max-w-xs pagination swiper-pagination-bullets swiper-pagination-horizontal">
				<span class="swiper-pagination-bullet pagination-bullet swiper-pagination-bullet-active active"></span>
				<span class="swiper-pagination-bullet pagination-bullet"></span>
				<span class="swiper-pagination-bullet pagination-bullet"></span>
			</div>
		</div>

		<ul class="mb-4 pagination--page" role="list">
	    <li class="hidden lg:block">
	      <a href="#" class="" aria-label="Page 1">1</a>
	    </li>
	    <li class="hidden lg:block">
	      <span aria-current="page" aria-label="Page 2">2</span>
	    </li>
	    <li class="hidden lg:block">
	    	<a href="#" class="" aria-label="Page 3">3</a>
	    </li>
	    <li class="hidden lg:block">
	      <a href="#" class="" aria-label="Page 4">4</a>
	    </li>
	    <li class="self-end hidden lg:block">
	      <span class="block h-full">…</span>
	    </li>
	    <li class="hidden lg:block">
	      <a href="#" class="" aria-label="Page 16">16</a>
	    </li>
	  </ul>
	</div>

  <div class="p-4 m-2 mt-8 overflow-hidden bg-gray-100 rounded-md">
		<h3 class="px-3 py-2 m-0 mb-4 font-bold tracking-wider text-gray-800 bg-gray-200 rounded-md">Buttons</h3>

   	<div class="flex flex-wrap">
      {%- liquid
        assign buttons = 'primary,secondary,tertiary,light,dark,pop'
        assign buttons = buttons | split: ','
      -%}
      {%- for button in buttons -%}
			<div class="w-full py-2 lg:w-1/2">
        {% render 'button' content:button attributes: 'href="#"' style: button %}
	    </div>
      {%- endfor -%}

			<div class="w-full py-2 lg:w-1/2">
        {% render 'button' content:'Disabled' attributes: 'href="#"' style: 'primary' class:'button--disabled' %}
	    </div>

	    <div class="w-full py-2 lg:w-1/2">
	    	<input type="button" class="button button--primary" value="Button Input" />
	    </div>
	    <div class="w-full py-2 lg:w-1/2">
	    	<button class="button button--w-icon"><span>{% render 'icon' icon: 'account-1' width:20 height:20 %}</span><span>Button w/ Icon</span></button>
	    </div>
	    <div class="py-2 w-/1/4">
	    	<a class="inline-block button button--link">Button Text Only</a>
	    </div>

    </div>

   </div>

   <div class="p-4 m-2 mt-8 overflow-hidden bg-gray-100 rounded-md">
		<h3 class="px-3 py-2 m-0 mb-4 font-bold tracking-wider text-gray-800 bg-gray-200 rounded-md">Accordion</h3>

	  <div class="mb-4">
      <ul 
        class="pl-0 list" 
        x-data="{
          currentIndex: 'none',
          accordions: [
            {
              id: 'Accordion1',
              summary: 'Accordion Title 1',
              details: 'Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.'
            },
            {
              id: 'Accordion2',
              summary: 'Accordion Title 2',
              details: 'Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.'
            }
          ]
        }"
      >
        <template x-for="(accordion, index) in accordions">
          <li class="accordion group" :class="{ 'active': currentIndex == index }">
            <button 
              :id="accordion.id + 'Title'" 
              class="accordion-title"
              :aria-controls="accordion.id + 'Panel'" 
              @click="() => {
                if (currentIndex == index) return currentIndex = 'none'
                return currentIndex = index
              }"
            >
              <span x-text="accordion.summary"></span>
              <span class="flex group-active:hidden accordion-control">
                {% render 'icon', icon: 'plus' width:24 height:24 %}
              </span>
              <span class="hidden group-active:flex accordion-control">
                {% render 'icon', icon: 'minus' width:24 height:24 %}
              </span>
            </button>
            <div 
              :id="accordion.id + 'Panel'" 
              aria-hidden
              :aria-labelledby="accordion.id + 'Title'" 
              role="region" 
              class="accordion-panel "
              :class="{ 'hidden': index != currentIndex }"
             >
              <div>
                <p x-text="accordion.details">Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.</p>
              </div>
            </div>
          </li>
        </template>
      </ul>
	  </div>

	  <p class="mt-12 mb-2 underline">Accordion (Details)</p>

	  <div class="mb-4">
	  	<menu>
	      {% for i in (1..5) %}
	        <details type="accordion" group="test" class="accordion group">
	          <summary class="accordion-title">
	            <span>Accordion Title {{ i }}</span>
	            <span class="flex group-open:hidden accordion-control">
					      {% render 'icon', icon: 'plus' width:24 height:24 %}
					    </span>
					    <span class="hidden group-open:flex accordion-control">
					      {% render 'icon', icon: 'minus' width:24 height:24 %}
					    </span>
	          </summary>
	          <div class="accordion-panel">
	            <p>Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.</p>
				      <p>Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.</p>
	          </div>
	        </details>
	      {% endfor %}
	    </menu>
		</div>
	</div>

	<div class="p-4 m-2 mt-8 overflow-hidden bg-gray-100 rounded-md">
		<h3 class="px-3 py-2 m-0 mb-4 font-bold tracking-wider text-gray-800 bg-gray-200 rounded-md">Mini Form</h3>
		<details class="mini-form no-close">
			<summary class="flex justify-end">
				<span class="absolute w-full pointer-events-none button button--primary"> 
					Sign up for Emails
				</span>
			</summary>
			<div>
				<form action="" class="relative w-full">
					<div class="flex w-full">
						<input type="text" class="w-full" placeholder="Enter your email...">
						<button class="button button--primary">Sign up</button>
					</div>
					<div class="mini-form__info">
						<p>Unlock EARLY access to our exclusive products when you sign up for texts.</p>
						<p>By submitting this form, you agree to receive recurring automated and promotional marketing text messages (e.g. cart reminders) from OluKai at the cell number used when signing up. Reply HELP for help and STOP to cancel. Msg frequency varies. Msg & data rates apply. View Terms & Privacy.</p>
					</div>
				</form>
			</div>
		</details>
	</div>

	<div class="p-4 m-2 mt-8 overflow-hidden bg-gray-100 rounded-md">
		<h3 class="px-3 py-2 m-0 mb-4 font-bold tracking-wider text-gray-800 bg-gray-200 rounded-md">Tabs</h3>

    <div 
      class="mb-4 tabs" 
      aria-live="polite" 
      x-data="{currentTab: 'TabSection-1'}"
    >
      <ul class="" role='tablist'>
        {% for i in (1..3) %}
	        <li>
            <button 
              id="Tab-{{ i }}" 
              class="tab-title" 
              :class="{ 'active': currentTab == $el.getAttribute('aria-controls') }"
              aria-controls="TabSection-{{ i }}" 
              role="tab"
              @click="currentTab = $el.getAttribute('aria-controls')"
            >
	            <span>Tab Title {{ i }}</span>
	          </button>
	        </li>
        {% endfor %}
      </ul>
      <div>
        {% for i in (1..3) %}
        <div id="TabSection-{{ i }}" class="hidden tab-panel active:block" :class="{ 'active': $el.id == currentTab }">
          <div>
            <p>Body {{ i }}</p>
            <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Obcaecati id similique beatae possimus. Rem accusantium harum necessitatibus voluptates consectetur saepe? Voluptate quisquam odio, officia temporibus cumque dolorem quidem nesciunt aperiam.</p>
            <input type="text">
          </div>
        </div>
      	{% endfor %}
      </div>
    </div>

  </div>

  <div class="p-4 m-2 mt-8 overflow-hidden bg-gray-100 rounded-md">
		<h3 class="px-3 py-2 m-0 mb-4 font-bold tracking-wider text-gray-800 bg-gray-200 rounded-md">Typeface Examples</h3>

    <h3 class="underline mb4">Typeface Options</h3>

		<div class="">
			
			<h1 class="block mb-4 type-hero">Hero Font</h1>

			<p class="type--subline"><strong>Pellentesque habitant morbi tristique</strong> senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. <em>Aenean ultricies mi vitae est.</em> Mauris placerat eleifend leo. Quisque sit amet est et sapien ullamcorper pharetra. Vestibulum erat wisi, condimentum sed, <code class="block p-2 mx-0 my-4 text-sm text-red-500 bg-white rounded-md">commodo vitae</code>, ornare sit amet, wisi. Aenean fermentum, elit eget tincidunt condimentum, eros ipsum rutrum orci, sagittis tempus lacus enim ac dui. <a href="#">Donec non enim</a> in turpis pulvinar facilisis. Ut felis.</p>

			<h2 class="mt-8 mb-4 type-headline">Headline Font</h2>

			<p class="type--subline">
				<ol>
				   <li>Lorem ipsum dolor sit amet, consectetuer adipiscing elit.</li>
				   <li>Aliquam tincidunt mauris eu risus.</li>
				</ol>
			</p>

			<blockquote><p class="type--subline">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus magna. Cras in mi at felis aliquet congue. Ut a est eget ligula molestie gravida. Curabitur massa. Donec eleifend, libero at sagittis mollis, tellus est malesuada tellus, at luctus turpis elit sit amet quam. Vivamus pretium ornare est.</p></blockquote>

			<h3>Header Level 3</h3>

			<ul>
			   <li>Lorem ipsum dolor sit amet, consectetuer adipiscing elit.</li>
			   <li>Aliquam tincidunt mauris eu risus.</li>
			</ul>

			<h4>Header Level 4</h4>
			<h5>Header Level 5</h5>
			<h6>Header Level 6</h6>

		</div>

	</div>

	<div class="p-4 m-2 mt-8 overflow-hidden bg-gray-100 rounded-md">
		<h3 class="px-3 py-2 m-0 mb-4 font-bold tracking-wider text-gray-800 bg-gray-200 rounded-md">Icons</h3>

		{% render 'icon-library' %}
	</div>

		<script>
	    document.addEventListener('DOMContentLoaded', () => {
	      const cssVars = document.querySelectorAll('[data-css-var]');

	      cssVars.forEach(el => {
	        const cssVar = el.dataset.cssVar;
	        const value = getComputedStyle(document.documentElement).getPropertyValue(cssVar).trim();
	        el.textContent = value;
	      });
	    });
	  </script>

		<hr class="my-5">

    <h3 class="underline mb4">Images</h3>
    {%- liquid 
      assign collection = collections['all']
      assign product = collection.products | first
      assign image = product.images | first
    -%}

    <div class="grid grid-cols-2 gap-4">
      <h4 class="m-0">Server Side</h4>
      <h4 class="m-0">Client Side</h4>
      <div class="p-4 bg-gray-100">
        {% render 'image' image: image lazy:false sizes: "(min-width: 768px) 25vw, 50vw" %}
      </div>
      <div x-data="{ imageSrc: ''}" class="flex items-center justify-center p-2 bg-gray-100">
        <template x-if="!!imageSrc">
          {% render 'image' attributes: ':src="imageSrc"' sizes: "(min-width: 768px) 25vw, 50vw" %}
        </template>
        <template x-if="!imageSrc">
          <button class="button button--primary" @click="imageSrc = '{{ image | image_url }}'">Add Source</button>
        </template>
      </div>
    </div>


		<hr class="my-5">

    <h3 class="underline mb4">Product Item</h3>

    <script>
      window.products = window.products || {}
      window.products[{{ product.handle | json }}] = {
        {% render 'product-item-data' product: product %}
      } 
    </script>

    <div class="flex gap-4">
      {% capture product_attributes %}
      x-data="{ product: window.products['{{ product.handle }}'] }" 
      x-init="
        if (!$data.product.compare_at_price) $data.product.compare_at_price = 50000
        $data.product.subtitle = 'Men\'s sunglasses'
        $data.product.variants = [1,1,1,1]
				setTimeout(reinitialiseRatingSnippet, 200)
      "
      {% endcapture %}
      {% render 'product-item' class: 'w-1/2' attributes: product_attributes %}
      {% render 'product-item' product: collection.products[2] class: 'w-1/2' %}
    </div>
</div>
