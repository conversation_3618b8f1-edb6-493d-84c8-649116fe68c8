{% liquid
	assign section_type = 'filtered-content'
%}

<section 
 class="section section--{{ section_type }} relative 
	 {% render 'class-settings', prefix:'wrapper_class', settings:section.settings %}"
 style="{% render 'style-settings', prefix:'wrapper_style', settings:section.settings %}" x-data="$store.filteredContent">

 	{% if section.settings.search %}

 		{% render 'search-form' resources:'products' settings:section.settings results:'inline' id:'FilteredContentSearch' click:'FilteredContent.selectProduct(product)' %}
 		
 	{% endif %}

 	<form steps>

	 	{% for step in section.blocks %}
		 	{% if step.type != 'step' %}{% continue %}{% endif %}

		 	<article step="{{ step.settings.key }}">
		 		<h2 class="type-section mt-0 mb-8 lg:text-left text-center">{{ step.settings.title }}</h2>
		 		<fieldset class="grid lg:grid-cols-4 grid-cols-2 lg:gap-3 gap-4">
		 			{% for option in section.blocks offset:forloop.index %}
		 				{% if option.type != 'option' %}{% break %}{% endif %}

		 				{% if option.settings.conditions %}
		 				<template x-if="window.Util.express(`{{ option.settings.conditions }}`, $store.filteredContent)">
		 				{% endif %}

		 					<div class="field field__image {% render 'class-settings' prefix:'option_item_class', settings:step.settings %}">
				 				<label for="{{ step.settings.key | handle }}_{{ option.settings.value | handle }}" class="flex cursor-pointer">
				 					{% if option.settings.image != blank %}
				 						<img src="{{ option.settings.image | image_url }}" alt="{{ option.settings.title }}">
				 					{% endif %}
				 					<input type="radio" id="{{ step.settings.key | handle }}_{{ option.settings.value | handle }}" class="" name="{{ step.settings.key }}" value="{{ option.settings.value }}" {% if option.settings.selected %}checked{% endif %} x-model="$store.filteredContent['{{ step.settings.key }}']" x-init="(() => {
										if (Util.url.param('data_tag_{{ step.settings.key }}') === '{{ option.settings.value }}') {
											$store.filteredContent['{{ step.settings.key }}'] = '{{ option.settings.value }}';
											FilteredContent.result()
										}})()">
				 					{{ option.settings.title }}
				 				</label>
				 			</div>

		 				{% if option.settings.conditions %}
		 				</template>
		 				{% endif %}


		 			{% endfor %}		
		 		</fieldset>
		 	</article>

	 	{% endfor %}

 	</form>


 	<article result></article>

 	<script>

 		const section = document.currentScript.parentNode

 		const FilteredContent = {
 			
 			init:() => {

 				const steps = Array.from(section.querySelectorAll('[step]'))
 				
 				FilteredContent.render()

 				steps.forEach( (step, index) => {

		 			step.addEventListener('click', () => {

		 				FilteredContent.render()

		 				steps.filter((s,i)=>i>index).forEach(s=>{
		 					// s.querySelectorAll('input').forEach(el=>el.checked=false)
		 					delete FilteredContent.data[s.getAttribute('step')]
		 				})

		 				if(!!steps[index+1]) {
		 					steps[index+1].scrollIntoView({behavior:'smooth'});
		 				} else {
		 					FilteredContent.result()
		 				}

		 			})

		 		})
 			
 			},

 			render:()=>{

 				section.querySelector('[result]').innerHTML = ''

 				FilteredContent.data = Util.form.values(section.querySelector('[steps]'))
				Alpine.store('filteredContent', FilteredContent.data)

 			},

 			result:()=>{

 				Util.remote.get(
			 		Util.literal(
			 			'{{ section.settings.path }}',
			 			FilteredContent.data
			 		),{select:'#MainContent'}).then(html=>{
			 		section.querySelector('[result]').innerHTML = html
			 		section.querySelector('[result]').scrollIntoView({behavior:'smooth'});
			 	})

 			},

 			selectProduct: product => {

 				const steps = Array.from(section.querySelectorAll('[step]'))
	 			FilteredContent.data = Object.fromEntries(
	 				steps.map(step=>{
	 					let key = step.getAttribute('step')
	 					return [
	 						key,
		 					product.tags.find(t=>t.includes(`${key}:`)).split(':')[1]
						];
					})
				)
	 			Alpine.store('filteredContent', FilteredContent.data)
	 			FilteredContent.result()

	 			Search.clear()
 			}

 		}

 		window.FilteredContent = FilteredContent;

 		window.addEventListener('DOMContentLoaded', FilteredContent.init)

 	</script>
		 

</section>


 

{% schema %}
{
	"name": "Filtered Content",
	"settings": [
		{
			"type":"liquid",
			"id":"path",
			"label":"Result Path"
		},
		{
			"type": "header",
			"content": "Search Settings"
		},
		{
			"type":"checkbox",
			"id":"search",
			"label":"Display Search Form",
			"default":true
		},
		{
			"type":"text",
			"id":"placeholder",
			"label":"Search Input Placeholder",
			"default": "Style Name"
		},
		{
			"type":"richtext",
			"id":"instruction",
			"label":"Search Instructions",
			"default": "<p>Canʻt find the style youʻre looking for? Select from the options below:</p>"
		},
    {
      "type": "header",
      "content": "Search Results Item"
    },
    {
      "type":"liquid",
      "id":"search_item_title_logic",
      "label":"Product Title",
      "info":"JavaScript logic that will output the item's title.",
      "default":"product.title"
    },
    {
      "type":"liquid",
      "id":"search_item_type_logic",
      "label":"Product Type",
      "info":"JavaScript logic that will output the item's type.",
      "default":"product.type"
    },
    {
      "type":"liquid",
      "id":"search_item_sku_logic",
      "label":"Product SKU",
      "info":"JavaScript logic that will output the item's SKU.",
      "default":"product.sku.split('|').at(0)"
    }
	],
	"blocks": [
		{
			"type":"step",
			"name":"Step",
			"settings":[
				{
					"type":"text",
					"id":"title",
					"label":"Step Title"
				},
				{
					"type":"text",
					"id":"key",
					"label":"Step Key"
				},
				{
					"type":"liquid",
					"id":"conditions",
					"label":"Conditional Display"
				},
				{
					"id":"option_item_class_format",
					"label":"Option Format",
					"type":"radio",
					"options":[
						{
							"value":"field__image--horizontal",
							"label":"Horizontal"
						},
						{
							"value":"ield__image--vertical",
							"label":"Vertical"
						}
					]
				}
			]
		},
		{
			"type":"option",
			"name":"Option",
			"settings":[
				{
					"type":"text",
					"id":"title",
					"label":"Option Label"
				},
				{
					"type":"text",
					"id":"value",
					"label":"Option Value"
				},
				{
					"type":"checkbox",
					"id":"selected",
					"label":"Pre-Selected"
				},
				{
					"id":"image",
					"label":"Option Image",
					"type":"image_picker"
				},
				{
					"type":"liquid",
					"id":"conditions",
					"label":"Conditional Display"
				}
			]
		}
	],
	"presets": [
		{
			"name": "Filtered Content",
			"category": "Content",
			"settings": {},
			"blocks": []
		}
	]
}
{% endschema %}
