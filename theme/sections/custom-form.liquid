{% liquid 
  assign section_type = 'custom-form'
%}

<section 
  class="section section--{{ section_type }} {% render 'class-settings' prefix:'wrapper_class' settings:section.settings %} relative" 
  style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}"> 

  {% if section.settings.wrapper_bg_image %}
    <img src="{{ section.settings.wrapper_bg_image | image_url }}" alt="{{ section.settings.wrapper_bg_image.alt }}" class="section__media absolute inset-0 w-full h-full object-cover" />
  {% endif %}

  <main class="section__container relative {% render 'class-settings' prefix:'container_class', settings:section.settings %}">

    {% render 'flex-nested-blocks' blocks:section.blocks offset:0 %}

  </main>

</section>

{% schema %}
{
	"name": "Custom Form",
	"class": "shopify-section--custom-form",
	"settings": [
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		},
		{
			"type": "header",
			"content": "@global: Wrapper Settings"
		},
		{
			"type": "paragraph",
			"content": "@include BackgroundStyles, @extends:SectionWrapper"
		},
		{
			"type": "color",
			"id": "wrapper_style_background_color",
			"label": "@global:  Background Color"
		},
		{
			"type": "color_background",
			"id": "wrapper_style_background",
			"label": "@global:  Background Gradient"
		},
		{
			"type": "image_picker",
			"label": "@global:  Background Image Mobile",
			"id": "wrapper_bg_image_mob"
		},
		{
			"type": "image_picker",
			"label": "@global:  Background Image",
			"id": "wrapper_bg_image"
		},
		{
			"type": "select",
			"id": "wrapper_class_vertical_padding",
			"label": "@global: Wrapper Vertical Padding",
			"options": [
				{
					"value": "@include Spacing prop:py",
					"label": "Inclusion"
				},
				{
					"value": "py-0",
					"label": "@global: None"
				},
				{
					"value": "py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "py-xs",
					"label": "@global: XS"
				},
				{
					"value": "py-sm",
					"label": "@global: SM"
				},
				{
					"value": "py-md",
					"label": "@global: MD"
				},
				{
					"value": "py-lg",
					"label": "@global: LG"
				},
				{
					"value": "py-xl",
					"label": "@global: XL"
				},
				{
					"value": "py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "py-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "wrapper_class_horizontal_padding",
			"label": "@global: Wrapper Horizontal Padding",
			"options": [
				{
					"value": "@include Spacing prop:px",
					"label": "Inclusion"
				},
				{
					"value": "px-0",
					"label": "@global: None"
				},
				{
					"value": "px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "px-xs",
					"label": "@global: XS"
				},
				{
					"value": "px-sm",
					"label": "@global: SM"
				},
				{
					"value": "px-md",
					"label": "@global: MD"
				},
				{
					"value": "px-lg",
					"label": "@global: LG"
				},
				{
					"value": "px-xl",
					"label": "@global: XL"
				},
				{
					"value": "px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "px-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "wrapper_class_vertical_padding_desktop",
			"label": "@global: Wrapper Vertical Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:py",
					"label": "Inclusion"
				},
				{
					"value": "lg:py-0",
					"label": "@global: None"
				},
				{
					"value": "lg:py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:py-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:py-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:py-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:py-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:py-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:py-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:py-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:py-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:py-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:py-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:py-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "select",
			"id": "wrapper_class_horizontal_padding_desktop",
			"label": "@global: Wrapper Horizontal Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:px",
					"label": "Inclusion"
				},
				{
					"value": "lg:px-0",
					"label": "@global: None"
				},
				{
					"value": "lg:px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:px-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:px-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:px-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:px-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:px-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:px-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:px-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:px-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:px-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:px-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:px-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "header",
			"content": "@global: Container Settings"
		},
		{
			"type": "select",
			"id": "container_class_container",
			"label": "@global:  Container",
			"options": [
				{
					"value": "w-full",
					"label": "Full Screen Width"
				},
				{
					"value": "container",
					"label": "Container Width"
				},
				{
					"value": "container container--wide",
					"label": "Wide Container"
				},
				{
					"value": "container container--narrow",
					"label": "Narrow Container"
				},
				{
					"value": "container container--tight",
					"label": "Very Narrow Container"
				},
				{
					"value": "container--product",
					"label": "Product Container Width"
				},
				{
					"value": "container--wide",
					"label": "Wide Container Width"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_vertical_padding",
			"label": "@global:  Vertical Padding",
			"options": [
				{
					"value": "@include Spacing prop:py",
					"label": "Inclusion"
				},
				{
					"value": "py-0",
					"label": "@global: None"
				},
				{
					"value": "py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "py-xs",
					"label": "@global: XS"
				},
				{
					"value": "py-sm",
					"label": "@global: SM"
				},
				{
					"value": "py-md",
					"label": "@global: MD"
				},
				{
					"value": "py-lg",
					"label": "@global: LG"
				},
				{
					"value": "py-xl",
					"label": "@global: XL"
				},
				{
					"value": "py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "py-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_horizontal_padding",
			"label": "@global:  Horizontal Padding",
			"options": [
				{
					"value": "@include Spacing prop:px",
					"label": "Inclusion"
				},
				{
					"value": "px-0",
					"label": "@global: None"
				},
				{
					"value": "px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "px-xs",
					"label": "@global: XS"
				},
				{
					"value": "px-sm",
					"label": "@global: SM"
				},
				{
					"value": "px-md",
					"label": "@global: MD"
				},
				{
					"value": "px-lg",
					"label": "@global: LG"
				},
				{
					"value": "px-xl",
					"label": "@global: XL"
				},
				{
					"value": "px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "px-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_vertical_padding_desktop",
			"label": "@global:  Vertical Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:py",
					"label": "Inclusion"
				},
				{
					"value": "lg:py-0",
					"label": "@global: None"
				},
				{
					"value": "lg:py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:py-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:py-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:py-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:py-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:py-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:py-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:py-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:py-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:py-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:py-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:py-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_horizontal_padding_desktop",
			"label": "@global:  Horizontal Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:px",
					"label": "Inclusion"
				},
				{
					"value": "lg:px-0",
					"label": "@global: None"
				},
				{
					"value": "lg:px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:px-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:px-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:px-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:px-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:px-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:px-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:px-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:px-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:px-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:px-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:px-8xl",
					"label": "@global: 8XL"
				}
			]
		}
	],
	"blocks": [
		{
			"type": "frame",
			"name": "⤷ Frame",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:article_class"
				},
				{
					"type": "header",
					"content": "@global: Display Settings"
				},
				{
					"type": "select",
					"id": "article_class_visibility",
					"label": "@global: Display Visibility",
					"options": [
						{
							"value": "",
							"label": "Mobile & Desktop"
						},
						{
							"value": "lg:hidden",
							"label": "Mobile"
						},
						{
							"value": "max-lg:hidden",
							"label": "Desktop"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "header",
					"content": "@global: Frame Width Settings"
				},
				{
					"type": "select",
					"id": "article_class_width",
					"label": "@global: Frame Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						},
						{
							"value": "col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "col-span-4",
							"label": "4 Grid Columns"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_width_desktop",
					"label": "@global: Frame Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						},
						{
							"value": "lg:col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Grid Columns"
						},
						{
							"value": "lg:col-span-5",
							"label": "5 Grid Columns"
						},
						{
							"value": "lg:col-span-6",
							"label": "6 Grid Columns"
						},
						{
							"value": "container",
							"label": "Standard Container"
						},
						{
							"value": "container container--narrow",
							"label": "Narrow Container"
						},
						{
							"value": "container container--wide",
							"label": "Wide Container"
						},
						{
							"value": "container--product",
							"label": "Product Container"
						}
					]
				},
				{
					"type": "color",
					"id": "article_style_background_color",
					"label": "Frame Background Color"
				},
				{
					"type": "color_background",
					"id": "article_style_background",
					"label": "Frame Background Gradient"
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "header",
					"content": "@global: Frame Layout"
				},
				{
					"type": "select",
					"id": "article_class_display",
					"label": "@global: Frame Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_display_desktop",
					"label": "@global: Frame Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "lg:hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_direction",
					"label": "@global: Frame Direction",
					"default": "flex-row",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_layout",
					"label": "@global: Frame Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-middle",
							"label": "Middle (Full Width)"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_layout_spacing",
					"label": "@global: Frame Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_vertical_padding",
					"label": "@global: Frame Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_horizontal_padding",
					"label": "@global: Frame Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_gap",
					"label": "@global: Frame Spacing Gap",
					"options": [
						{
							"value": "@include Spacing prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "gap-0",
							"label": "@global: None"
						},
						{
							"value": "gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "gap-md",
							"label": "@global: MD"
						},
						{
							"value": "gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "gap-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_vertical_padding_desktop",
					"label": "@global: Frame Vertical Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:py",
							"label": "Inclusion"
						},
						{
							"value": "lg:py-0",
							"label": "@global: None"
						},
						{
							"value": "lg:py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:py-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:py-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:py-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:py-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:py-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_horizontal_padding_desktop",
					"label": "@global: Frame Horizontal Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:px",
							"label": "Inclusion"
						},
						{
							"value": "lg:px-0",
							"label": "@global: None"
						},
						{
							"value": "lg:px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:px-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:px-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:px-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:px-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:px-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_gap_desktop",
					"label": "@global: Frame Spacing Gap Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "lg:gap-0",
							"label": "@global: None"
						},
						{
							"value": "lg:gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:gap-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:gap-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"id": "article_class_custom_class",
					"label": "Custom Classes",
					"type": "text"
				}
			]
		},
		{
			"type": "break",
			"name": "⤶ Frame Break",
			"settings": []
		},
		{
			"type": "dropdown-menu",
			"name": "Dropdown Menu",
			"settings": [
				{
					"type": "text",
					"id": "label",
					"label": "*Label"
				},
				{
					"type": "liquid",
					"id": "model",
					"label": "Data source/model"
				},
				{
					"type": "textarea",
					"id": "options",
					"label": "Options",
					"info": "value:Label",
					"default": "featured:Featured\nbest-selling:Best Selling\nprice-ascending:Lowest Price"
				},
				{
					"type": "liquid",
					"id": "onchange",
					"label": "on Change",
					"info": "JavaScript"
				}
			]
		},
		{
			"type": "toggle-switch",
			"name": "Toggle Switch",
			"settings": [
				{
					"type": "liquid",
					"id": "model",
					"label": "Data source/model"
				},
				{
					"type": "textarea",
					"id": "options",
					"label": "Toggle Switch Options",
					"info": "value:Selected|Unselected",
					"default": "Product:View on Product,Model:View on Model"
				},
				{
					"type": "liquid",
					"id": "onchange",
					"label": "on Change",
					"info": "JavaScript"
				}
			]
		},
		{
			"type": "title",
			"name": "Title",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include Text id_prefix:title, label_prefix:Title"
				},
				{
					"type": "header",
					"content": "@global: Title Settings"
				},
				{
					"type": "text",
					"id": "title_text",
					"label": "@global: Title Text"
				},
				{
					"type": "liquid",
					"id": "title_liquid",
					"label": "@global: Title Text (Liquid)"
				},
				{
					"type": "liquid",
					"id": "title_attr_x_text",
					"label": "@global: Title Dynamic Text (Alpine)"
				},
				{
					"type": "select",
					"id": "title_element",
					"label": "@global: Title Element",
					"default": "p",
					"options": [
						{
							"value": "@include TextElement",
							"label": "Inclusion"
						},
						{
							"value": "h1",
							"label": "@global:  Heading 1"
						},
						{
							"value": "h2",
							"label": "@global:  Heading 2"
						},
						{
							"value": "h3",
							"label": "@global:  Heading 3"
						},
						{
							"value": "h4",
							"label": "@global:  Heading 4"
						},
						{
							"value": "h5",
							"label": "@global:  Heading 5"
						},
						{
							"value": "p",
							"label": "@global:  Paragraph"
						},
						{
							"value": "div",
							"label": "@global:  Div"
						}
					]
				},
				{
					"type": "select",
					"id": "title_class_type_style",
					"label": "@global: Title Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "title_class_type_size",
					"label": "@global: Title Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "title_style_color",
					"label": "@global: Title Color"
				}
			]
		},
		{
			"type": "button",
			"name": "Button",
			"settings": [
				{
					"type": "select",
					"id": "style",
					"label": "Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "text",
					"id": "button_text",
					"label": "Button Text"
				},
				{
					"type": "text",
					"id": "leading_icon",
					"label": "Leading Icon"
				},
				{
					"type": "text",
					"id": "trailing_icon",
					"label": "Trailing Icon"
				},
				{
					"type": "url",
					"id": "link",
					"label": "Link"
				},
				{
					"type": "liquid",
					"id": "onclick",
					"label": "Click Event",
					"info": "JavaScript onclick event, overrides native functionality."
				},
				{
					"type": "checkbox",
					"id": "form_validity",
					"label": "Require Form Validity",
					"info": "Disables button until all fields in enclosing form are valid"
				}
			]
		},
		{
			"name": "⤷ Form",
			"type": "form",
			"settings": [
				{
					"type": "select",
					"label": "Form Type",
					"id": "shopify",
					"default": "",
					"options": [
						{
							"value": "",
							"label": "Custom"
						},
						{
							"value": "activate_customer_password",
							"label": "activate_customer_password"
						},
						{
							"value": "cart",
							"label": "cart"
						},
						{
							"value": "contact",
							"label": "contact"
						},
						{
							"value": "create_customer",
							"label": "create customer"
						},
						{
							"value": "currency",
							"label": "currency"
						},
						{
							"value": "customer",
							"label": "customer"
						},
						{
							"value": "customer_address",
							"label": "customer address"
						},
						{
							"value": "customer_login",
							"label": "customer login"
						},
						{
							"value": "guest_login",
							"label": "guest login"
						},
						{
							"value": "localization",
							"label": "localization"
						},
						{
							"value": "new_comment",
							"label": "new comment"
						},
						{
							"value": "product",
							"label": "product"
						},
						{
							"value": "recover_customer_password",
							"label": "recover customer password"
						},
						{
							"value": "reset_customer_password",
							"label": "reset customer password"
						},
						{
							"value": "storefront_password",
							"label": "storefront password"
						}
					]
				},
				{
					"type": "text",
					"label": "Form Action",
					"id": "form_action"
				},
				{
					"type": "select",
					"label": "Form Method",
					"id": "form_method",
					"default": "POST",
					"options": [
						{
							"value": "POST",
							"label": "POST"
						},
						{
							"value": "GET",
							"label": "GET"
						}
					]
				},
				{
					"type": "textarea",
					"label": "Hidden Fields",
					"id": "hidden_fields",
					"info": "Lines with name:value"
				},
				{
					"type": "liquid",
					"label": "Form onsubmit",
					"id": "form_onsubmit"
				},
				{
					"type": "checkbox",
					"label": "Block Default HTTP Submit",
					"id": "prevent_default"
				}
			]
		},
		{
			"name": "⤶ Form End",
			"type": "form-end",
			"settings": []
		},
		{
			"name": "Form Errors",
			"type": "form-errors",
			"settings": []
		},
		{
			"name": "Form Success",
			"type": "form-success",
			"settings": [
				{
					"type": "liquid",
					"label": "Success Message",
					"id": "success_message"
				},
				{
					"type": "select",
					"label": "Success State",
					"id": "success_class_state",
					"default": "form-success--hide-fields",
					"options": [
						{
							"label": "Show Success Message",
							"value": ""
						},
						{
							"label": "Hide Form & Show Success Message",
							"value": "form-success--hide-fields"
						}
					]
				}
			]
		},
		{
			"name": "Text field",
			"type": "text-field",
			"settings": [
				{
					"type": "text",
					"label": "Field Label",
					"id": "label"
				},
				{
					"type": "text",
					"label": "Field Name",
					"id": "name"
				},
				{
					"type": "select",
					"label": "Field Type",
					"id": "type",
					"default": "text",
					"options": [
						{
							"value": "text",
							"label": "Text"
						},
						{
							"value": "textarea",
							"label": "Textarea"
						},
						{
							"value": "password",
							"label": "Password"
						},
						{
							"value": "email",
							"label": "Email"
						},
						{
							"value": "date",
							"label": "Date"
						}
					]
				},
				{
					"type": "select",
					"label": "Label Style",
					"id": "field_class_label_style",
					"default": "field--floating-label",
					"options": [
						{
							"value": "field--floating-label",
							"label": "Floating"
						},
						{
							"value": "",
							"label": "Stacked"
						}
					]
				},
				{
					"type": "text",
					"label": "Placeholder Text",
					"id": "placeholder"
				},
				{
					"type": "text",
					"label": "Value Mask",
					"id": "mask",
					"info": "https://alpinejs.dev/plugins/mask"
				},
				{
					"type": "text",
					"label": "Validation Regex Pattern",
					"id": "pattern"
				},
				{
					"type": "checkbox",
					"label": "Required",
					"id": "required",
					"default": false
				},
				{
					"type": "richtext",
					"label": "Field Description",
					"id": "description"
				},
				{
					"type": "select",
					"label": "Justify Description Text",
					"id": "description_class_justify",
					"options": [
						{
							"value": "text-left",
							"label": "Left"
						},
						{
							"value": "text-right",
							"label": "Right"
						},
						{
							"value": "text-center",
							"label": "Center"
						}
					]
				}
			]
		},
		{
			"name": "Multi-Select",
			"type": "multiselect",
			"settings": [
				{
					"type": "text",
					"label": "Field Label",
					"id": "label"
				},
				{
					"type": "text",
					"label": "Field Name",
					"id": "name"
				},
				{
					"type": "select",
					"label": "Field Type",
					"id": "type",
					"default": "select",
					"options": [
						{
							"value": "select",
							"label": "Dropdown Select"
						},
						{
							"value": "radio",
							"label": "Radio Buttons"
						},
						{
							"value": "checkboxes",
							"label": "Checkboxes"
						}
					]
				},
				{
					"type": "select",
					"label": "Label Style",
					"id": "field_class_label_style",
					"default": "field--floating-label",
					"options": [
						{
							"value": "field--floating-label",
							"label": "Floating"
						},
						{
							"value": "",
							"label": "Stacked"
						}
					]
				},
				{
					"type": "textarea",
					"label": "Options",
					"id": "options"
				},
				{
					"type": "text",
					"label": "Placeholder Text",
					"id": "placeholder"
				},
				{
					"type": "checkbox",
					"label": "Required",
					"id": "required",
					"default": false
				}
			]
		},
		{
			"name": "Checkbox",
			"type": "checkbox",
			"settings": [
				{
					"type": "text",
					"label": "Field Label",
					"id": "label"
				},
				{
					"type": "text",
					"label": "Field Name",
					"id": "name"
				},
				{
					"type": "text",
					"label": "Checkbox Label",
					"id": "input_label"
				},
				{
					"type": "text",
					"label": "Description",
					"id": "description"
				},
				{
					"type": "checkbox",
					"label": "Checked by Default",
					"id": "checked"
				},
				{
					"type": "select",
					"label": "Alignment",
					"id": "label_class_align",
					"default": "items-start",
					"options": [
						{
							"value": "items-start",
							"label": "Top"
						},
						{
							"value": "items-center",
							"label": "Center"
						},
						{
							"value": "items-end",
							"label": "Bottom"
						}
					]
				}
			]
		},
		{
			"name": "Multi Option Field",
			"type": "multi-options",
			"settings": [
				{
					"type": "text",
					"label": "Field Label",
					"id": "field_label"
				},
				{
					"type": "text",
					"label": "Field Name",
					"id": "field_name"
				},
				{
					"type": "select",
					"label": "Field Type",
					"id": "field_type",
					"default": "checkbox",
					"options": [
						{
							"value": "checkbox",
							"label": "Checkbox"
						},
						{
							"value": "radio",
							"label": "Radio"
						},
						{
							"value": "select",
							"label": "Select"
						}
					]
				},
				{
					"type": "textarea",
					"label": "Options",
					"id": "field_options",
					"info": "Enter options for checkbox or radio fields, separated by line breaks."
				},
				{
					"type": "text",
					"label": "Default Value",
					"id": "default_value"
				},
				{
					"type": "checkbox",
					"label": "Required",
					"id": "field_required",
					"default": false
				}
			]
		},
		{
			"name": "Divider",
			"type": "divider",
			"settings": [
				{
					"type": "color",
					"id": "divider_style_border_color",
					"label": "Divider Color"
				}
			]
		},
		{
			"type": "image",
			"name": "Image",
			"settings": [
				{
					"type": "image_picker",
					"label": "Image",
					"id": "image"
				},
				{
					"type": "image_picker",
					"label": "Image (Desktop)",
					"id": "image_desktop"
				},
				{
					"type": "select",
					"label": "Image Position",
					"id": "media_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full w-full"
						}
					]
				}
			]
		},
		{
			"type": "video",
			"name": "Video",
			"settings": [
				{
					"type": "text",
					"label": "Video URL",
					"id": "video"
				},
				{
					"type": "select",
					"label": "Video Position",
					"id": "media_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full w-full"
						}
					]
				}
			]
		},
		{
			"type": "rich-text",
			"name": "Rich Text",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include RichText id_prefix:text, label_prefix:Rich Text"
				},
				{
					"type": "header",
					"content": "@global: Rich Text Settings"
				},
				{
					"type": "richtext",
					"id": "text_text",
					"label": "@global: Rich Text Text"
				},
				{
					"type": "liquid",
					"id": "text_liquid",
					"label": "@global: Rich Text Text (Liquid)"
				},
				{
					"type": "select",
					"id": "text_class_type_style",
					"label": "@global: Rich Text Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_class_type_size",
					"label": "@global: Rich Text Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_style_color",
					"label": "@global: Rich Text Color"
				}
			]
		},
		{
			"type": "custom_liquid",
			"name": "Liquid",
			"limit": 4,
			"settings": [
				{
					"type": "liquid",
					"id": "liquid",
					"label": "Liquid Block"
				}
			]
		}
	],
	"presets": [
		{
			"name": "Custom Form",
			"category": "Advanced",
			"settings": {},
			"blocks": []
		}
	]
}
{% endschema %}
