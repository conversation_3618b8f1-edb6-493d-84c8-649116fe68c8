{% if section.settings.video_source %}
<div class="article-video__video video-container relative aspect-[16/9] in-view bg-black" data-anim="fade-in-up" x-data="asyncVideo({
    loadingMethod: '{{ section.settings.loading_method }}',
    loadingDelay: {{ section.settings.loading_delay | times: 1000 }}
  })" x-init="initVideo()">


  <template x-if="shouldLoad">
    <iframe class="article-video__video video-container__video w-full h-full absolute inset-0" frameborder="0" allow="autoplay; fullscreen" muted playsinline="" title="{{ article.title }}" src="{{ section.settings.video_source }}"></iframe>
  </template>

</div>
{% endif %}
<section class="article-video__content bg-black text-white py-12 px-5">
  <div class="max-w-3xl">
    <header class="article-video__eyebrow flex items-center">
      <h2 class="article-video__category font-body my-0 text-sm mr-2 pr-2 border-r border-current">{{ blog.title }}</h2>
      <time class="article-video__date text-sm" datetime="Nov 30 2022">{{ article.published_at | time_tag: '%b %d, %Y' }}</time>
    </header>
    <div class="article-video__rte">
      <h1 class="article-video__title my-0">{{ article.title | escape }}</h1>
      {%- liquid 
        if article.excerpt != blank
          echo "<p>" | append: article.excerpt | append: "</p>"
        else
          echo article.content
        endif
      -%}</p>
    </div>
  </div>
</section>

{% schema %}
{
	"name": "Article Video",
	"settings": [
    {
      "id": "video_source",
      "type": "url",
      "label": "Source for Video"
    },
    {
      "type": "header",
      "content": "Video Defer Settings"
    },
    {
      "type": "select",
      "id": "loading_method",
      "label": "Video Loading Method",
      "options": [
        {
          "value": "immediate",
          "label": "Immediate"
        },
        {
          "value": "scroll",
          "label": "On Scroll"
        },
        {
          "value": "hover",
          "label": "On Hover"
        },
        {
          "value": "time",
          "label": "After Delay"
        }
      ],
      "default": "scroll"
    },
    {
      "type": "range",
      "id": "loading_delay",
      "label": "Loading Delay (seconds)",
      "min": 0,
      "max": 10,
      "step": 1,
      "default": 2,
      "info": "Only applies when loading method is After Delay"
    }
	]
}
{% endschema %}
