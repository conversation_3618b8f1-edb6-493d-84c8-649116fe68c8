{% liquid
    assign section_type = 'content-carousel'
    assign carousel = section
	assign remote = false
 %}

 {%- if section.settings.inclusion_liquid_carousel != '' -%}

 <section 
 	id="carousel-{{ section.id }}"
	class="section section--{{ section_type }}  
		{% render 'class-settings', prefix:'wrapper_class', settings:section.settings %}"
	style="{% render 'style-settings', prefix:'wrapper_style', settings:section.settings %}">


 	<main 
		class="section__container relative {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
		style="{% render 'style-settings' prefix:'container_styles' settings:section.settings %}"
	>

    {% if section.settings.split_into_tabs %}

		<div 
			x-data="{
				activeTab: 0,
				swipers: [],
				setActiveTab(index, carouselKey) {
					{% if settings.enable_logs -%}console.log('setActiveTab - setting active tab', index, carouselKey){%- endif %}
					this.activeTab = index
					this.$nextTick(() => {
						const swiperContainer = document.querySelector(`.tab-content[data-tab-index='${index}'] .swiper-container`);
						if (!swiperContainer) {
							{% if settings.enable_logs -%}console.log('setActiveTab - swiper not found for active tab', index){%- endif %}
							return
						}

						// retrieve data-carousel-key attribute value from swiper container.
						const configKey = swiperContainer.getAttribute('carousel-key')
						{% if settings.enable_logs -%}console.log('setActiveTab - Retrieved configKey:', configKey){%- endif %}

						// retrieve configuration for current swiper instance using configKey.
						const config = window.carouselConfigs[configKey];
						
						if (config) {
							{% if settings.enable_logs -%}console.log('setActiveTab - swiper config found for active tab', configKey, config){%- endif %}
							
							// create a new instance of swiper with the saved swiper settings
							this.swipers[configKey] = new Swiper(swiperContainer, config);
						} else {
							{% if settings.enable_logs -%}console.log('setActiveTab - swiper config not found for active tab', configKey){%- endif %}
						}
					});
				}
			}"
			class="tabs-wrapper"
		>
			<div class="flex flex-col-reverse items-center lg:flex-row lg:justify-between">
				<div article class="lg:flex lg:flex-grow lg:justify-center">
					<div class="lg:shrink-0 lg:absolute">
						{% liquid 
										
							capture text_item_1
								render 'class-settings' prefix:'text_item_1', settings:carousel.settings
							endcapture
							capture button_1
								render 'class-settings' prefix:'button_1', settings:carousel.settings
							endcapture
	
							if section.settings.title_product != blank 
								render 'carousel-product-title' settings:section.settings, price:false, blocks:section.blocks
							endif
	
							if carousel.settings.text_item_1_text != "" or carousel.settings.button_1_text != "" and section.settings.title_product == blank
	
								render 'content-item', item_classes:'content-item--carousel-header flex-col', content_classes: 'w-full justify-between', text_stack_classes: 'self-end gap-2 flex-col', button_stack_classes: 'self-end gap-2 flex-col', text_item_1_link:section.settings.title_link, text_item_1_text: carousel.settings.text_item_1_text, text_item_1_class: text_item_1, text_item_1_element: carousel.settings.text_item_1_element, text_item_2_text: product_total, text_item_2_class: 'text-sm', button_1_text: carousel.settings.button_1_text, button_1_classes: button_1, button_1_link: carousel.settings.button_1_link, settings: carousel.settings
							endif
	
						%} 
					</div>
				</div>
			</div>
			<div class="flex flex-col-reverse items-center lg:flex-row lg:justify-between">
				<!-- Tab Links -->
				<div class="mb-10 tab-link lg:shrink-0 tabs">
					{% assign index = 0 %}
					{% for block in section.blocks %}
						{% if block.type == 'shopify-recommendations' %}
						{% capture carousel_key %}swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}{% endcapture %}
							<button 
								class="tab"
								:class="{ 'active': activeTab === {{ index }} }"
								@mouseover="setActiveTab({{ index }}, '{{ carousel_key }}')"
								@focus="setActiveTab({{ index }}, '{{ carousel_key }}')"
							>
								{{ block.settings.alt_carousel_heading }}
							</button>
							{% assign index = index | plus: 1 %}
						{% endif %}
					{% endfor %}
				</div>
			</div>

        {% comment %} # separate carousels for specified block types {% endcomment %}
		{% assign index = 0 %}
        {% for block in section.blocks %}

            {% if block.type == 'shopify-recommendations' %}
				{% assign blockArray = block.settings %}
				<div id="tab-content-{{ index }}" data-tab-index="{{ index }}" class="tab-content carousel-container" x-show="activeTab === {{ index }}" style=""> 

				{% assign carousel_key = 'swiperCarousel' | append: section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' | append: '_' | append: block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' %}
				{% render 'carousel-config-update' settings:section.settings, section:section, block:block, carousel_key: carousel_key %}

					{% case block.type %}
					{% comment %} Save Carousel Settings {% endcomment %}

					{% comment %} END of Save Carousel Settings {% endcomment %}

						<!-- CAROUSEL shopify-collection -->
						{% when 'shopify-recommendations' %}
							{% liquid
								#assign source = '/recommendations/products.json?product_id=' | append: block.settings.product_id | append: '&limit=' | append: block.settings.limit
								assign source = 'https://services.mybcapps.com/bc-sf-filter/filter?shop=the-roark-revival.myshopify.com&page=1&limit=24&sort=&display=grid&collection_scope=************&product_available=false&variant_available=false&build_filter_tree=true'
								assign map = '{products:{from:"products",each:{title:"title",handle:"handle",type:"product_type",price:"price_min",compare_at_price:"compare_at_price",featured_image:"images[`1`]",hover_image:"images[`1`]",id:"id",variants:"variants"}}}'
								render 'carousel-product-data' source:source, map:map id:section.id, block_id:block.id, split_tabs: section.settings.split_into_tabs
								assign remote = true
							%}

							<div 
							    class="{{ section.id }} w-full" {% render 'carousel-config' settings:section.settings namespace:section.id %}
								data-config-key="{{ section.id }}_{{ block.id }}"
							>
								<div data-block-id="{{ block.id }}" 
									x-data="{
										blockId: '{{ block.id }}',
										products: [],
										productsAvailable: false,
										init() {
											{% if settings.enable_logs -%}console.log('init() - Block ID:', this.blockId);{%- endif %}
							
											// listener for product updates
											window.addEventListener('productsUpdated', (event) => {
												if (event.detail.blockId === this.blockId) {
													this.updateProducts();
												}
											});
							
											this.updateProducts();
										},
										updateProducts() {
											if (window.tabsProducts && window.tabsProducts[this.blockId]) {
												this.products = window.tabsProducts[this.blockId];
												this.productsAvailable = true;
											}
										}
									}" 
									x-init="init()"
								>
									<!-- Swiper Carousel for shopify-recommendations -->
									<div class="relative w-full h-full group/carousel" aria-live="polite">
										<!-- Swiper Container -->
										<div 
											x-init="init()"
											x-ref="swiperContainer"
											class="swiper swiper-container h-full {% if carousel.settings.outer_slides %}swiper--overflow-visible{% endif %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}"
											carousel-key="swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}"
										>
											<!-- Swiper Wrapper -->
											<div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper">
												<!-- Swiper Slides -->

													{% liquid

														for block in section.blocks
							
															assign prev = forloop.index0 | minus: 1
							
															if block.type contains 'item' and section.blocks[prev].type != 'overlay'
													
																echo '<div class="relative swiper-slide '
																echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
																if block.settings.product_position != blank
																	echo 'data-slide-position="' | append: block.settings.product_position | append:'"'
																endif
																echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
																echo '>'
							
																case block.type
							
																	when 'content-item'
																			render 'content-item' settings:block.settings blocks:section.blocks offset:forloop.index
							
																	when 'product-item'
																		for product in block.settings.product
																			render 'product-item' product:product settings:section.settings, _settings:settings
																			unless forloop.last
																				echo '</div><div class="relative swiper-slide '
																				echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view
																				echo '">'
																			endunless
																		endfor
							
																endcase
							
																echo '</div>'
							
															endif
														endfor
							
													%}

													{% if remote %}
														<template x-if="!products">
															<template x-for="i in {{ section.settings.slides_per_view}}">
																<article class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
																	<div class="flex flex-col gap-md">
																		<div class="block w-full h-full bg-gray-100 aspect-square"></div>
																		<div class="flex flex-col mb-2 gap-xs">
																			<div class="block w-[240px] h-[16px] bg-gray-100"></div>
																			<div class="block w-[120px] h-[12px] bg-gray-100"></div>
																			<div class="block w-[72px] h-[12px] bg-gray-100"></div>
																		</div>
																	</div>
																</article>
															</template>
														</template>
													{% endif %} 
											
													<template x-for="(product, index) in products" hidden>
																				
														<template x-if="product" hidden>
														
														<div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
															{%- render 'product-item' product:false, settings:section.settings, _settings:settings -%}
															
                              <template x-import.afterparent='`.{{section.id}} [data-slide-position="${index+1}"]`'></template>
											
														</div>
											
														</template>
											
													</template>
											
													{% for block in section.blocks %}
														{% if block.settings.shop_all_text != blank  and  block.settings.slider_shop_all %}
															<template x-if="!!products">
																<div class="swiper-slide swiper-slide--shop-all">
																	<div class="__show-all w-full h-full flex items-center justify-center aspect-[1/1]">
																		<a href="{{ block.settings.collection.url }}" class="button button--primary">
																			{{ block.settings.shop_all_text }}
																		</a>
																	</div>
																</div>
															</template>
															{% break %}
														{% endif %}
													{% endfor %}

												<!-- Insert Carousel Items Here -->
											</div>
											<!-- Add Swiper Navigation Arrows and Pagination Here -->
											{% if section.settings.show_pagination %}
												<style>
													#shopify-section-{{ section.id }} section {
														padding: 0 0 3.583rem;
													}
													@media screen and (min-width: 768px) {
														#shopify-section-{{ section.id }} section {
															padding: 3.583rem 0; /* match desktop */
														}
													}
												</style>
												<div class="swiper-pagination"></div>
												<script type="swiper/config">
													{
														pagination: {
															el: '.swiper-pagination',
															type: 'bullets',
															clickable: true,
															dynamicBullets: true,
															dynamicMainBullets: 4
														}
													}	
												</script>
											{% endif %}
									
									
											{% if carousel.settings.arrows or carousel.settings.arrows_mobile or carousel.settings.show_arrows_on_hover %}
												{% capture arrow_visibility_classes %}
													{% if carousel.settings.arrows_mobile and carousel.settings.arrows %}
														opacity-100
													{% elsif carousel.settings.arrows_mobile %}
														opacity-100 lg:opacity-0
													{% elsif carousel.settings.arrows %}
														opacity-0 lg:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
													{% if carousel.settings.show_arrows_on_hover %}
														opacity-0 lg:opacity-0 lg:group-hover/carousel:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
												{% endcapture %}
												
												<button class="top-1/2 transform -translate-y-1/2 absolute left-6 {{ arrow_visibility_classes }} swiper-button-prev btn-control" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:2 %}
												</button>
												<button class="top-1/2 transform -translate-y-1/2 absolute right-6 {{ arrow_visibility_classes }} swiper-button-next btn-control" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:2 %}
												</button>
											{% endif %}
										</div>
									</div>
								</div>
							</div>
							
						<!-- CAROUSEL bloomreach-recs -->

					{% endcase %}
				</div> <!-- END of Tabs -->
				{% assign index = index | plus: 1 %}
            {% endif %}
        {% endfor %}


		</div>
    {% else  %}
        {% liquid
			assign remote = false
            for block in section.blocks
			
                case block.type

                    when 'shopify-recommendations'
                        #assign source = '/recommendations/products.json?product_id=' | append: block.settings.product_id | append: '&limit=' | append: block.settings.limit
                        assign source = 'https://services.mybcapps.com/bc-sf-filter/filter?shop=the-roark-revival.myshopify.com&page=1&limit=24&sort=&display=grid&collection_scope=************&product_available=false&variant_available=false&build_filter_tree=true'
                        assign map = '{products:{from:"products",each:{title:"title",handle:"handle",type:"product_type",price:"price_min",compare_at_price:"compare_at_price",featured_image:"images_info[0].src",hover_image:"images_info[0].src",id:"id",variants:"variants"}}}'
                        render 'carousel-product-data' source:source, map:map id:section.id isPrefixSkuEnable:block.settings.is_prefix_sku_enable
                        assign remote = true
            
                endcase

            endfor
        %} 

        <div 
            class="{{ section.id }} w-full" {% render 'carousel-config' settings:section.settings namespace:section.id %}
			data-config-key="{{ section.id }}_{{ block.id }}"
		>

            {% liquid 

                capture text_item_1
                    render 'class-settings' prefix:'text_item_1', settings:carousel.settings
                endcapture
                capture button_1
                    render 'class-settings' prefix:'button_1', settings:carousel.settings
                endcapture

                if section.settings.title_product != blank 
                    render 'carousel-product-title' settings:section.settings, price:false
                endif

                if carousel.settings.text_item_1_text != "" or carousel.settings.button_1_text != "" and section.settings.title_product == blank 

                    render 'content-item', item_classes:'content-item--carousel-header flex-col', content_classes: 'w-full justify-between', text_stack_classes: 'self-end gap-2 flex-col', button_stack_classes: 'self-end gap-2 flex-col', text_item_1_link:section.settings.title_link, text_item_1_text: carousel.settings.text_item_1_text, text_item_1_class: text_item_1, text_item_1_element: carousel.settings.text_item_1_element, text_item_2_text: product_total, text_item_2_class: 'text-sm', button_1_text: carousel.settings.button_1_text, button_1_classes: button_1, button_1_link: carousel.settings.button_1_link, settings: carousel.settings , desk_hide_button1: true
                endif

            %} 

           <div class="relative w-full h-full group/carousel" aria-live="polite">
               <div class="swiper swiper-container h-full {% if carousel.settings.outer_slides %}swiper--overflow-visible{% endif %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}">
                   <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper">

                       {% liquid

                           for block in section.blocks

                               assign prev = forloop.index0 | minus: 1

                               if block.type contains 'item' and section.blocks[prev].type != 'overlay'
                       
                                   echo '<div class="relative swiper-slide '
                                   echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
                                   if block.settings.product_position != blank
                                       echo 'data-slide-position="' | append: block.settings.product_position | append:'"'
                                   endif
								   echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
                                   echo '>'

                                   case block.type

                                       when 'content-item'
                                               render 'content-item' settings:block.settings blocks:section.blocks offset:forloop.index

                                       when 'review-item' 
                                               render 'review-item' settings:block.settings same_height_review:section.settings.same_height_review product_item_show_button:section.settings.product_item_show_button product_type_mob_class:'items-start flex-col md:items-center md:flex-row custom-review-alignment'



                                       when 'article-item' 
                                               assign blog_handle = block.settings.article | split: '/' | first
                                               assign blog_object = blogs[blog_handle]
                                               render 'article-item' settings:block.settings, blog:blog_object, article:block.settings.article

                                       when 'product-item'
                                           for product in block.settings.product
                                               render 'product-item' product:product settings:section.settings, _settings:settings
                                               unless forloop.last
                                                   echo '</div><div class="relative swiper-slide '
                                                   echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view
                                                   echo '">'
                                               endunless
                                           endfor

                                   endcase

                                   echo '</div>'

                               endif

                               if block.type == 'blog-articles'
                               assign _blog = blogs[block.settings.blog] | default: blog
                               assign _count = 0

                                   for _article in _blog.articles

                                       if _article.id == article.id 
                                           continue
                                       endif

                                       assign _filters = block.settings.filters | split: ','
                                       assign _filtered_out = false
                                       for _filter in _filters
                                           if article.tags contains _filter or article == blank
                                               unless _article.tags contains _filter
                                                   assign _filtered_out = true
                                               endunless
                                           endif
                                       endfor

                                       if _filtered_out
                                           continue
                                       endif

                                       echo '<div class="relative swiper-slide '
                                       echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
									   echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
                                       echo '>'
                                       
                                       render 'article-item' settings:block.settings, blog:_blog, article:_article, layout:'vertical'

                                       echo '</div>'

                                       assign _count = _count | plus: 1
                                       if _count >= section.settings.limit
                                           break
                                       endif

                                   endfor
                               endif

                           endfor

                       %}


                       {% if remote %}
                           <template x-if="!section.products">
                               <template x-for="i in {{ section.settings.slides_per_view}}">
                                   <article class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                                       <div class="flex flex-col gap-md">
                                           <div class="block w-full h-full bg-gray-100 aspect-square"></div>
                                           <div class="flex flex-col mb-2 gap-xs">
                                               <div class="block w-[240px] h-[16px] bg-gray-100"></div>
                                               <div class="block w-[120px] h-[12px] bg-gray-100"></div>
                                               <div class="block w-[72px] h-[12px] bg-gray-100"></div>
                                           </div>
                                       </div>
                                   </article>
                               </template>
                           </template>
                       {% endif %} 

                       <template x-for="(product, index) in section.products" hidden>
                           
                           <template x-if="product" hidden>
                           
                           <div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                               {%- render 'product-item' product:false, settings:section.settings, _settings:settings -%}
                               
                               <template x-import.afterparent='`.{{section.id}} [data-slide-position="${index+1}"]`'></template>

                           </div>

                           </template>

                       </template>

                       {% for block in section.blocks %}
                           {% if block.settings.shop_all_text != blank  and  block.settings.slider_shop_all %}
                               <template x-if="!!section.products">
                               <div class="swiper-slide swiper-slide--shop-all">
                                   <div class="content-carousel__show-all  w-full h-full flex items-center justify-center aspect-[1/1]">
                                       <a href="{{ block.settings.collection.url }}" class="button  {{ block.settings.shop_all_button_1_class_style }} {{ block.settings.shop_all_button_1_class_size }}">
                                           {{ block.settings.shop_all_text }}
                                       </a>
                                   </div>
                               </div>
                               </template>
                           {% break %}
                           {% endif %}
                       {% endfor %}

                    </div>
               
					{% if carousel.settings.show_pagination == true %}
                        {% comment %} <div class="flex items-center p-4 lg:p-0">
                            
                                <div class="pagination"></div>
                            
                        </div> {% endcomment %}
                    {% endif %}
                    {% if section.settings.show_pagination %}
						<style>
							#shopify-section-{{ section.id }} section {
								padding: 0 0 3.583rem;
							}
							@media screen and (min-width: 768px) {
								#shopify-section-{{ section.id }} section {
									padding: 3.583rem 0; /* match desktop */
								}
							}
						</style>
						<div class="swiper-pagination"></div>
                        <script type="swiper/config">
							{
								pagination: {
									el: '.swiper-pagination',
									type: 'bullets',
									clickable: true,
									dynamicBullets: true,
									dynamicMainBullets: 4
								}
							}	
                        </script>
					{% endif %}


                   {% if carousel.settings.arrows or carousel.settings.arrows_mobile or carousel.settings.show_arrows_on_hover %}
                       {% capture arrow_visibility_classes %}
                           {% if carousel.settings.arrows_mobile and carousel.settings.arrows %}
                               opacity-100
                           {% elsif carousel.settings.arrows_mobile %}
                               opacity-100 lg:opacity-0
                           {% elsif carousel.settings.arrows %}
                               opacity-0 lg:opacity-100 pointer-events-none lg:pointer-events-auto
                           {% endif %}
                           {% if carousel.settings.show_arrows_on_hover %}
                               opacity-0 lg:opacity-0 lg:group-hover/carousel:opacity-100 pointer-events-none lg:pointer-events-auto
                           {% endif %}
                       {% endcapture %}
                       
                       <button class="top-1/2 transform -translate-y-1/2 absolute left-6 {{ arrow_visibility_classes }} swiper-button-prev btn-control" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
                           {% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:2 %}
                       </button>
                       <button class="top-1/2 transform -translate-y-1/2 absolute right-6 {{ arrow_visibility_classes }} swiper-button-next btn-control" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
                           {% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:2 %}
                       </button>
                   {% endif %}

               </div>

           </div>

		   {% if section.settings.button_1_text != blank %}
			<button {% if section.settings.button_1_onclick != blank %}onclick="{{ section.settings.button_1_onclick }};" {% else %}onclick="window.location='{{ button_1_link | default:section.settings.button_1_link }}'" {% endif %} class="button-mobile button collection-title-shop-all {{ section.settings.button_1_class_style }} {{ section.settings.button_1_class_size }} ">
			  <span>
				{{ button_1_text | default: section.settings.button_1_text }}
			  </span>
			</button>
		  {% endif %}
        </div>

    {% endif %}

	</main>

</section>

{%- endif -%}


{% style %}

	#shopify-section-{{ section.id }} .swiper-slide {
		width:calc(100% / {{ section.settings.slides_per_view_mobile }})
	}
	@media(min-width:1024px){
		#shopify-section-{{ section.id }} .swiper-slide {
			width:calc(100% / {{ section.settings.slides_per_view }})
		}
	}

  .section-slideshow-multiple .productitem--extra {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  .section-slideshow-multiple .productitem--wrap {
    height: 100%;
  }
  .section-slideshow-collection .swiper-button-disabled {
    opacity: .5;
  }


	{% if section.settings.split_into_tabs %}
		#shopify-section-{{ section.id }} button.tab {
			color: {{ section.settings.tabs_button_text }};
		}
		#shopify-section-{{ section.id }} button.tab.active {
			background-color: {{ section.settings.tabs_active_button_background }};
			color: {{ section.settings.tabs_active_button_text }};
		}
	{% endif %}
{% endstyle %}


{% comment %}
  BELOW SCRIPT IS FOR RECENTLY VIEWED PRODUCTS
{% endcomment %}

<script>
  (function(){
    
    let rv = {products:[]}
    try{
      rv = JSON.parse(localStorage.getItem('recentlyViewed'));
    } catch(err) {}
    
    if(!rv)
      rv = {products:[]}

    window.recentlyViewed = rv;

    {% if template contains 'product' %}

      var handle = {{product.handle|json}};
      rv.products = JSON.parse(JSON.stringify(
          rv.products.filter(function(p, i) {return p.handle != handle})
        )
      ).slice(0,{{ section.settings.limit }});
      window.recentlyViewed = JSON.parse(JSON.stringify(rv));

      rv.products.unshift({{product|json}})
      localStorage.setItem('recentlyViewed', JSON.stringify(rv))

    {% endif %}

  })()
</script>

<script>
	window.sections = window.sections || []
	window.addEventListener('DOMContentLoaded', ()=>{
  		window.sections.find(s=>s.id=='{{ section.id }}').data = {{ section.settings | json }}
  	})

</script>

{% schema %}
{
	"name": "Boost Recommendation",
	"settings": [
		{
			"type": "liquid",
			"label": "Liquid Logic Inclusion",
			"id": "inclusion_liquid_carousel",
			"info": "Insert any liquid logic that returns a value to display the section",
			"default": "true"
		},
		{
			"type": "paragraph",
			"content": "@include TabSettings, label_prefix:Tabs, id_prefix:tabs_class"
		},
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "paragraph",
			"content": "@include Text, id_prefix:text_item_1, label_prefix:Text 1"
		},
		{
			"type": "url",
			"id": "title_link",
			"label": "Title Link"
		},
		{
			"type": "product",
			"id": "title_product",
			"label": "Title Product"
		},
		{
			"type": "paragraph",
			"content": "@include Button, id_prefix:button_1, label_prefix:Button 1"
		},
		{
			"type": "paragraph",
			"content": "@include Carousel"
		},
		{
			"type": "paragraph",
			"content": "@include ProductItem"
		},
		{
			"type": "checkbox",
			"id": "same_height_review",
			"label": "Same Height Review",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "single_variant_direct_addtocart",
			"label": "Direct add-to-cart for single variant",
			"default": false
		},
		{
			"type": "select",
			"id": "product_item_show_price_right",
			"label": "Show Price on Right or Bottom",
			"default": "bottom",
			"options": [
				{
					"label": "Right Side of Title",
					"value": "right"
				},
				{
					"label": "Below of Title",
					"value": "bottom"
				}
			]
		}
	],
	"blocks": [
		{
			"name": "Boost Recommendations",
			"type": "shopify-recommendations",
			"limit": 1,
			"settings": [
				{
					"type": "header",
					"content": "Collection settings"
				},
				{
					"label": "Product ID",
					"id": "product_id",
					"type": "text",
					"info": "Enter product id e.g '6880309542983'"
				},
				{
					"label": "Limit",
					"id": "limit",
					"type": "number",
					"default": 8
				},
				{
					"label": "Alternate Carousel Heading",
					"id": "alt_carousel_heading",
					"type": "text",
					"info": "Needed when carousel is converted to tabs"
				},
				{
					"type": "checkbox",
					"id": "is_prefix_sku_enable",
					"label": "Hide same SKU prefix",
					"default": false
				}
			]
		}
	],
	"presets": [
		{
			"name": "Boost Recommendation",
			"category": "Content",
			"settings": {},
			"blocks": []
		}
	]
}
{% endschema %}