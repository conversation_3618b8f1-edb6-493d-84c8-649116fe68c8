{% liquid
    assign section_type = 'content-carousel'
    assign carousel = section
	assign remote = false
 %}

 {%- if section.settings.inclusion_liquid_carousel != '' -%}

 <section 
 	id="carousel-{{ section.id }}"
	class="section section--{{ section_type }}  
		{% render 'class-settings', prefix:'wrapper_class', settings:section.settings %}"
	style="{% render 'style-settings', prefix:'wrapper_style', settings:section.settings %}">


 	<main 
		class="section__container relative {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
		style="{% render 'style-settings' prefix:'container_styles' settings:section.settings %}"
	>

    {% if section.settings.split_into_tabs %}

		<div 
			x-data="{
				activeTab: 0,
				swipers: [],
				setActiveTab(index, carouselKey) {
					{% if settings.enable_logs -%}console.log('setActiveTab - setting active tab', index, carouselKey){%- endif %}
					this.activeTab = index
					this.$nextTick(() => {
						const swiperContainer = document.querySelector(`.tab-content[data-tab-index='${index}'] .swiper-container`);
						if (!swiperContainer) {
							{% if settings.enable_logs -%}console.log('setActiveTab - swiper not found for active tab', index){%- endif %}
							return
						}

						// retrieve data-carousel-key attribute value from swiper container.
						const configKey = swiperContainer.getAttribute('carousel-key')
						{% if settings.enable_logs -%}console.log('setActiveTab - Retrieved configKey:', configKey){%- endif %}

						// retrieve configuration for current swiper instance using configKey.
						const config = window.carouselConfigs[configKey];
						
						if (config) {
							{% if settings.enable_logs -%}console.log('setActiveTab - swiper config found for active tab', configKey, config){%- endif %}
							
							// create a new instance of swiper with the saved swiper settings
							this.swipers[configKey] = new Swiper(swiperContainer, config);
						} else {
							{% if settings.enable_logs -%}console.log('setActiveTab - swiper config not found for active tab', configKey){%- endif %}
						}
					});
				}
			}"
			class="tabs-wrapper"
		>
			<div class="flex flex-col-reverse items-center lg:flex-row lg:justify-between">
				<div article class="lg:flex lg:flex-grow lg:justify-center">
					<div class="lg:shrink-0 lg:absolute">
						{% liquid 
										
							capture text_item_1
								render 'class-settings' prefix:'text_item_1', settings:carousel.settings
							endcapture
							capture button_1
								render 'class-settings' prefix:'button_1', settings:carousel.settings
							endcapture
	
							if section.settings.title_product != blank 
								render 'carousel-product-title' settings:section.settings, price:false, blocks:section.blocks
							endif
	
							if carousel.settings.text_item_1_text != "" or carousel.settings.button_1_text != "" and section.settings.title_product == blank
	
								render 'content-item', item_classes:'content-item--carousel-header flex-col', content_classes: 'w-full justify-between', text_stack_classes: 'self-end gap-2 flex-col', button_stack_classes: 'self-end gap-2 flex-col', text_item_1_link:section.settings.title_link, text_item_1_text: carousel.settings.text_item_1_text, text_item_1_class: text_item_1, text_item_1_element: carousel.settings.text_item_1_element, text_item_2_text: product_total, text_item_2_class: 'text-sm', button_1_text: carousel.settings.button_1_text, button_1_classes: button_1, button_1_link: carousel.settings.button_1_link, settings: carousel.settings
							endif
	
						%} 
					</div>
				</div>
			</div>
			<div class="flex flex-col-reverse items-center lg:flex-row lg:justify-between">
				<!-- Tab Links -->
				<div class="mb-10 tab-link lg:shrink-0 tabs">
					{% assign index = 0 %}
					{% for block in section.blocks %}
						{% if block.type == 'shopify-recommendations' %}
						{% capture carousel_key %}swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}{% endcapture %}
							<button 
								class="tab"
								:class="{ 'active': activeTab === {{ index }} }"
								@mouseover="setActiveTab({{ index }}, '{{ carousel_key }}')"
								@focus="setActiveTab({{ index }}, '{{ carousel_key }}')"
							>
								{{ block.settings.alt_carousel_heading }}
							</button>
							{% assign index = index | plus: 1 %}
						{% endif %}
					{% endfor %}
				</div>
			</div>

        {% comment %} # separate carousels for specified block types {% endcomment %}
		{% assign index = 0 %}
        {% for block in section.blocks %}

            {% if block.type == 'shopify-recommendations' %}
				{% assign blockArray = block.settings %}
				<div id="tab-content-{{ index }}" data-tab-index="{{ index }}" class="tab-content carousel-container" x-show="activeTab === {{ index }}" style=""> 

				{% assign carousel_key = 'swiperCarousel' | append: section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' | append: '_' | append: block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' %}
				{% render 'carousel-config-update' settings:section.settings, section:section, block:block, carousel_key: carousel_key %}

					{% case block.type %}
					{% comment %} Save Carousel Settings {% endcomment %}

					{% comment %} END of Save Carousel Settings {% endcomment %}

						<!-- CAROUSEL shopify-collection -->
						{% when 'shopify-recommendations' %}
							{% liquid
								#assign source = '/recommendations/products.json?product_id=' | append: block.settings.product_id | append: '&limit=' | append: block.settings.limit
								assign source = 'https://services.mybcapps.com/bc-sf-filter/filter?shop=the-roark-revival.myshopify.com&page=1&limit=24&sort=&display=grid&collection_scope=************&product_available=false&variant_available=false&build_filter_tree=true'
								assign map = '{products:{from:"products",each:{title:"title",handle:"handle",type:"product_type",price:"price_min",compare_at_price:"compare_at_price",featured_image:"images[`1`]",hover_image:"images[`1`]",id:"id",variants:"variants"}}}'
								render 'carousel-product-data' source:source, map:map id:section.id, block_id:block.id, split_tabs: section.settings.split_into_tabs
								assign remote = true
							%}

							<div 
							    class="{{ section.id }} w-full" {% render 'carousel-config' settings:section.settings namespace:section.id %}
								data-config-key="{{ section.id }}_{{ block.id }}"
							>
								<div data-block-id="{{ block.id }}" 
									x-data="{
										blockId: '{{ block.id }}',
										products: [],
										productsAvailable: false,
										init() {
											{% if settings.enable_logs -%}console.log('init() - Block ID:', this.blockId);{%- endif %}
							
											// listener for product updates
											window.addEventListener('productsUpdated', (event) => {
												if (event.detail.blockId === this.blockId) {
													this.updateProducts();
												}
											});
							
											this.updateProducts();
										},
										updateProducts() {
											if (window.tabsProducts && window.tabsProducts[this.blockId]) {
												this.products = window.tabsProducts[this.blockId];
												this.productsAvailable = true;
											}
										}
									}" 
									x-init="init()"
								>
									<!-- Swiper Carousel for shopify-recommendations -->
									<div class="relative w-full h-full group/carousel" aria-live="polite">
										<!-- Swiper Container -->
										<div 
											x-init="init()"
											x-ref="swiperContainer"
											class="swiper swiper-container h-full {% if carousel.settings.outer_slides %}swiper--overflow-visible{% endif %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}"
											carousel-key="swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}"
										>
											<!-- Swiper Wrapper -->
											<div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper">
												<!-- Swiper Slides -->

													{% liquid

														for block in section.blocks
							
															assign prev = forloop.index0 | minus: 1
							
															if block.type contains 'item' and section.blocks[prev].type != 'overlay'
													
																echo '<div class="relative swiper-slide '
																echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
																if block.settings.product_position != blank
																	echo 'data-slide-position="' | append: block.settings.product_position | append:'"'
																endif
																echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
																echo '>'
							
																case block.type
							
																	when 'content-item'
																			render 'content-item' settings:block.settings blocks:section.blocks offset:forloop.index
							
																	when 'product-item'
																		for product in block.settings.product
																			render 'product-item' product:product settings:section.settings, _settings:settings
																			unless forloop.last
																				echo '</div><div class="relative swiper-slide '
																				echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view
																				echo '">'
																			endunless
																		endfor
							
																endcase
							
																echo '</div>'
							
															endif
														endfor
							
													%}

													{% if remote %}
														<template x-if="!products">
															<template x-for="i in {{ section.settings.slides_per_view}}">
																<article class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
																	<div class="flex flex-col gap-md">
																		<div class="block w-full h-full bg-gray-100 aspect-square"></div>
																		<div class="flex flex-col mb-2 gap-xs">
																			<div class="block w-[240px] h-[16px] bg-gray-100"></div>
																			<div class="block w-[120px] h-[12px] bg-gray-100"></div>
																			<div class="block w-[72px] h-[12px] bg-gray-100"></div>
																		</div>
																	</div>
																</article>
															</template>
														</template>
													{% endif %} 
											
													<template x-for="(product, index) in products" hidden>
																				
														<template x-if="product" hidden>
														
														<div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
															{%- render 'product-item' product:false, settings:section.settings, _settings:settings -%}
															
                              <template x-import.afterparent='`.{{section.id}} [data-slide-position="${index+1}"]`'></template>
											
														</div>
											
														</template>
											
													</template>
											
													{% for block in section.blocks %}
														{% if block.settings.shop_all_text != blank  and  block.settings.slider_shop_all %}
															<template x-if="!!products">
																<div class="swiper-slide swiper-slide--shop-all">
																	<div class="__show-all w-full h-full flex items-center justify-center aspect-[1/1]">
																		<a href="{{ block.settings.collection.url }}" class="button button--primary">
																			{{ block.settings.shop_all_text }}
																		</a>
																	</div>
																</div>
															</template>
															{% break %}
														{% endif %}
													{% endfor %}

												<!-- Insert Carousel Items Here -->
											</div>
											<!-- Add Swiper Navigation Arrows and Pagination Here -->
											{% if section.settings.show_pagination %}
												<style>
													#shopify-section-{{ section.id }} section {
														padding: 0 0 3.583rem;
													}
													@media screen and (min-width: 768px) {
														#shopify-section-{{ section.id }} section {
															padding: 3.583rem 0; /* match desktop */
														}
													}
												</style>
												<div class="swiper-pagination"></div>
												<script type="swiper/config">
													{
														pagination: {
															el: '.swiper-pagination',
															type: 'bullets',
															clickable: true,
															dynamicBullets: true,
															dynamicMainBullets: 4
														}
													}	
												</script>
											{% endif %}
									
									
											{% if carousel.settings.arrows or carousel.settings.arrows_mobile or carousel.settings.show_arrows_on_hover %}
												{% capture arrow_visibility_classes %}
													{% if carousel.settings.arrows_mobile and carousel.settings.arrows %}
														opacity-100
													{% elsif carousel.settings.arrows_mobile %}
														opacity-100 lg:opacity-0
													{% elsif carousel.settings.arrows %}
														opacity-0 lg:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
													{% if carousel.settings.show_arrows_on_hover %}
														opacity-0 lg:opacity-0 lg:group-hover/carousel:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
												{% endcapture %}
												
												<button class="top-1/2 transform -translate-y-1/2 absolute left-6 {{ arrow_visibility_classes }} swiper-button-prev btn-control" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:2 %}
												</button>
												<button class="top-1/2 transform -translate-y-1/2 absolute right-6 {{ arrow_visibility_classes }} swiper-button-next btn-control" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:2 %}
												</button>
											{% endif %}
										</div>
									</div>
								</div>
							</div>
							
						<!-- CAROUSEL bloomreach-recs -->

					{% endcase %}
				</div> <!-- END of Tabs -->
				{% assign index = index | plus: 1 %}
            {% endif %}
        {% endfor %}


		</div>
    {% else  %}
        {% liquid
			assign remote = false
            for block in section.blocks
			
                case block.type

                    when 'shopify-recommendations'
                        #assign source = '/recommendations/products.json?product_id=' | append: block.settings.product_id | append: '&limit=' | append: block.settings.limit
                        assign source = 'https://services.mybcapps.com/bc-sf-filter/filter?shop=the-roark-revival.myshopify.com&page=1&limit=24&sort=&display=grid&collection_scope=************&product_available=false&variant_available=false&build_filter_tree=true'
                        assign map = '{products:{from:"products",each:{title:"title",handle:"handle",type:"product_type",price:"price_min",compare_at_price:"compare_at_price",featured_image:"images_info[0].src",hover_image:"images_info[0].src",id:"id",variants:"variants"}}}'
                        render 'carousel-product-data' source:source, map:map id:section.id isPrefixSkuEnable:block.settings.is_prefix_sku_enable
                        assign remote = true
            
                endcase

            endfor
        %} 

        <div 
            class="{{ section.id }} w-full" {% render 'carousel-config' settings:section.settings namespace:section.id %}
			data-config-key="{{ section.id }}_{{ block.id }}"
		>

            {% liquid 

                capture text_item_1
                    render 'class-settings' prefix:'text_item_1', settings:carousel.settings
                endcapture
                capture button_1
                    render 'class-settings' prefix:'button_1', settings:carousel.settings
                endcapture

                if section.settings.title_product != blank 
                    render 'carousel-product-title' settings:section.settings, price:false
                endif

                if carousel.settings.text_item_1_text != "" or carousel.settings.button_1_text != "" and section.settings.title_product == blank 

                    render 'content-item', item_classes:'content-item--carousel-header flex-col', content_classes: 'w-full justify-between', text_stack_classes: 'self-end gap-2 flex-col', button_stack_classes: 'self-end gap-2 flex-col', text_item_1_link:section.settings.title_link, text_item_1_text: carousel.settings.text_item_1_text, text_item_1_class: text_item_1, text_item_1_element: carousel.settings.text_item_1_element, text_item_2_text: product_total, text_item_2_class: 'text-sm', button_1_text: carousel.settings.button_1_text, button_1_classes: button_1, button_1_link: carousel.settings.button_1_link, settings: carousel.settings , desk_hide_button1: true
                endif

            %} 

           <div class="relative w-full h-full group/carousel" aria-live="polite">
               <div class="swiper swiper-container h-full {% if carousel.settings.outer_slides %}swiper--overflow-visible{% endif %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}">
                   <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper">

                       {% liquid

                           for block in section.blocks

                               assign prev = forloop.index0 | minus: 1

                               if block.type contains 'item' and section.blocks[prev].type != 'overlay'
                       
                                   echo '<div class="relative swiper-slide '
                                   echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
                                   if block.settings.product_position != blank
                                       echo 'data-slide-position="' | append: block.settings.product_position | append:'"'
                                   endif
								   echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
                                   echo '>'

                                   case block.type

                                       when 'content-item'
                                               render 'content-item' settings:block.settings blocks:section.blocks offset:forloop.index

                                       when 'review-item' 
                                               render 'review-item' settings:block.settings same_height_review:section.settings.same_height_review product_item_show_button:section.settings.product_item_show_button product_type_mob_class:'items-start flex-col md:items-center md:flex-row custom-review-alignment'



                                       when 'article-item' 
                                               assign blog_handle = block.settings.article | split: '/' | first
                                               assign blog_object = blogs[blog_handle]
                                               render 'article-item' settings:block.settings, blog:blog_object, article:block.settings.article

                                       when 'product-item'
                                           for product in block.settings.product
                                               render 'product-item' product:product settings:section.settings, _settings:settings
                                               unless forloop.last
                                                   echo '</div><div class="relative swiper-slide '
                                                   echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view
                                                   echo '">'
                                               endunless
                                           endfor

                                   endcase

                                   echo '</div>'

                               endif

                               if block.type == 'blog-articles'
                               assign _blog = blogs[block.settings.blog] | default: blog
                               assign _count = 0

                                   for _article in _blog.articles

                                       if _article.id == article.id 
                                           continue
                                       endif

                                       assign _filters = block.settings.filters | split: ','
                                       assign _filtered_out = false
                                       for _filter in _filters
                                           if article.tags contains _filter or article == blank
                                               unless _article.tags contains _filter
                                                   assign _filtered_out = true
                                               endunless
                                           endif
                                       endfor

                                       if _filtered_out
                                           continue
                                       endif

                                       echo '<div class="relative swiper-slide '
                                       echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
									   echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
                                       echo '>'
                                       
                                       render 'article-item' settings:block.settings, blog:_blog, article:_article, layout:'vertical'

                                       echo '</div>'

                                       assign _count = _count | plus: 1
                                       if _count >= section.settings.limit
                                           break
                                       endif

                                   endfor
                               endif

                           endfor

                       %}


                       {% if remote %}
                           <template x-if="!section.products">
                               <template x-for="i in {{ section.settings.slides_per_view}}">
                                   <article class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                                       <div class="flex flex-col gap-md">
                                           <div class="block w-full h-full bg-gray-100 aspect-square"></div>
                                           <div class="flex flex-col mb-2 gap-xs">
                                               <div class="block w-[240px] h-[16px] bg-gray-100"></div>
                                               <div class="block w-[120px] h-[12px] bg-gray-100"></div>
                                               <div class="block w-[72px] h-[12px] bg-gray-100"></div>
                                           </div>
                                       </div>
                                   </article>
                               </template>
                           </template>
                       {% endif %} 

                       <template x-for="(product, index) in section.products" hidden>
                           
                           <template x-if="product" hidden>
                           
                           <div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                               {%- render 'product-item' product:false, settings:section.settings, _settings:settings -%}
                               
                               <template x-import.afterparent='`.{{section.id}} [data-slide-position="${index+1}"]`'></template>

                           </div>

                           </template>

                       </template>

                       {% for block in section.blocks %}
                           {% if block.settings.shop_all_text != blank  and  block.settings.slider_shop_all %}
                               <template x-if="!!section.products">
                               <div class="swiper-slide swiper-slide--shop-all">
                                   <div class="content-carousel__show-all  w-full h-full flex items-center justify-center aspect-[1/1]">
                                       <a href="{{ block.settings.collection.url }}" class="button  {{ block.settings.shop_all_button_1_class_style }} {{ block.settings.shop_all_button_1_class_size }}">
                                           {{ block.settings.shop_all_text }}
                                       </a>
                                   </div>
                               </div>
                               </template>
                           {% break %}
                           {% endif %}
                       {% endfor %}

                    </div>
               
					{% if carousel.settings.show_pagination == true %}
                        {% comment %} <div class="flex items-center p-4 lg:p-0">
                            
                                <div class="pagination"></div>
                            
                        </div> {% endcomment %}
                    {% endif %}
                    {% if section.settings.show_pagination %}
						<style>
							#shopify-section-{{ section.id }} section {
								padding: 0 0 3.583rem;
							}
							@media screen and (min-width: 768px) {
								#shopify-section-{{ section.id }} section {
									padding: 3.583rem 0; /* match desktop */
								}
							}
						</style>
						<div class="swiper-pagination"></div>
                        <script type="swiper/config">
							{
								pagination: {
									el: '.swiper-pagination',
									type: 'bullets',
									clickable: true,
									dynamicBullets: true,
									dynamicMainBullets: 4
								}
							}	
                        </script>
					{% endif %}


                   {% if carousel.settings.arrows or carousel.settings.arrows_mobile or carousel.settings.show_arrows_on_hover %}
                       {% capture arrow_visibility_classes %}
                           {% if carousel.settings.arrows_mobile and carousel.settings.arrows %}
                               opacity-100
                           {% elsif carousel.settings.arrows_mobile %}
                               opacity-100 lg:opacity-0
                           {% elsif carousel.settings.arrows %}
                               opacity-0 lg:opacity-100 pointer-events-none lg:pointer-events-auto
                           {% endif %}
                           {% if carousel.settings.show_arrows_on_hover %}
                               opacity-0 lg:opacity-0 lg:group-hover/carousel:opacity-100 pointer-events-none lg:pointer-events-auto
                           {% endif %}
                       {% endcapture %}
                       
                       <button class="top-1/2 transform -translate-y-1/2 absolute left-6 {{ arrow_visibility_classes }} swiper-button-prev btn-control" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
                           {% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:2 %}
                       </button>
                       <button class="top-1/2 transform -translate-y-1/2 absolute right-6 {{ arrow_visibility_classes }} swiper-button-next btn-control" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
                           {% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:2 %}
                       </button>
                   {% endif %}

               </div>

           </div>

		   {% if section.settings.button_1_text != blank %}
			<button {% if section.settings.button_1_onclick != blank %}onclick="{{ section.settings.button_1_onclick }};" {% else %}onclick="window.location='{{ button_1_link | default:section.settings.button_1_link }}'" {% endif %} class="button-mobile button collection-title-shop-all {{ section.settings.button_1_class_style }} {{ section.settings.button_1_class_size }} ">
			  <span>
				{{ button_1_text | default: section.settings.button_1_text }}
			  </span>
			</button>
		  {% endif %}
        </div>

    {% endif %}

	</main>

</section>

{%- endif -%}


{% style %}

	#shopify-section-{{ section.id }} .swiper-slide {
		width:calc(100% / {{ section.settings.slides_per_view_mobile }})
	}
	@media(min-width:1024px){
		#shopify-section-{{ section.id }} .swiper-slide {
			width:calc(100% / {{ section.settings.slides_per_view }})
		}
	}

  .section-slideshow-multiple .productitem--extra {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  .section-slideshow-multiple .productitem--wrap {
    height: 100%;
  }
  .section-slideshow-collection .swiper-button-disabled {
    opacity: .5;
  }


	{% if section.settings.split_into_tabs %}
		#shopify-section-{{ section.id }} button.tab {
			color: {{ section.settings.tabs_button_text }};
		}
		#shopify-section-{{ section.id }} button.tab.active {
			background-color: {{ section.settings.tabs_active_button_background }};
			color: {{ section.settings.tabs_active_button_text }};
		}
	{% endif %}
{% endstyle %}


{% comment %}
  BELOW SCRIPT IS FOR RECENTLY VIEWED PRODUCTS
{% endcomment %}

<script>
  (function(){
    
    let rv = {products:[]}
    try{
      rv = JSON.parse(localStorage.getItem('recentlyViewed'));
    } catch(err) {}
    
    if(!rv)
      rv = {products:[]}

    window.recentlyViewed = rv;

    {% if template contains 'product' %}

      var handle = {{product.handle|json}};
      rv.products = JSON.parse(JSON.stringify(
          rv.products.filter(function(p, i) {return p.handle != handle})
        )
      ).slice(0,{{ section.settings.limit }});
      window.recentlyViewed = JSON.parse(JSON.stringify(rv));

      rv.products.unshift({{product|json}})
      localStorage.setItem('recentlyViewed', JSON.stringify(rv))

    {% endif %}

  })()
</script>

<script>
	window.sections = window.sections || []
	window.addEventListener('DOMContentLoaded', ()=>{
  		window.sections.find(s=>s.id=='{{ section.id }}').data = {{ section.settings | json }}
  	})

</script>

{% schema %}
{
	"name": "Boost Recommendation",
	"settings": [
		{
			"type": "liquid",
			"label": "Liquid Logic Inclusion",
			"id": "inclusion_liquid_carousel",
			"info": "Insert any liquid logic that returns a value to display the section",
			"default": "true"
		},
		{
			"type": "paragraph",
			"content": "@include TabSettings, label_prefix:Tabs, id_prefix:tabs_class"
		},
		{
			"type": "header",
			"content": "@global: Convert Into Tabs"
		},
		{
			"type": "checkbox",
			"id": "split_into_tabs",
			"label": "@global: Split Carousel Into Tabs",
			"default": false,
			"info": "Can only add blocks from Shopify Collection, Bloomreach Recs and Bloomreach Recs"
		},
		{
			"type": "select",
			"id": "tab_interaction",
			"label": "@global: Tab Interaction",
			"default": "hover",
			"options": [
				{
					"value": "hover",
					"label": "Hover"
				},
				{
					"value": "click",
					"label": "Click"
				}
			]
		},
		{
			"type": "color",
			"id": "tabs_button_text",
			"label": "@global: Carousel Tab Button Text",
			"default": "#000000"
		},
		{
			"type": "color",
			"id": "tabs_active_button_text",
			"label": "@global: Carousel Tab Active Button Text",
			"default": "#000000"
		},
		{
			"type": "color_background",
			"id": "tabs_active_button_background",
			"label": "@global: Carousel Tab Active Button Background",
			"default": "linear-gradient(#ffffff, #000000)"
		},
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		},
		{
			"type": "header",
			"content": "@global: Wrapper Settings"
		},
		{
			"type": "paragraph",
			"content": "@include BackgroundStyles, @extends:SectionWrapper"
		},
		{
			"type": "color",
			"id": "wrapper_style_background_color",
			"label": "@global:  Background Color"
		},
		{
			"type": "color_background",
			"id": "wrapper_style_background",
			"label": "@global:  Background Gradient"
		},
		{
			"type": "image_picker",
			"label": "@global:  Background Image Mobile",
			"id": "wrapper_bg_image_mob"
		},
		{
			"type": "image_picker",
			"label": "@global:  Background Image",
			"id": "wrapper_bg_image"
		},
		{
			"type": "select",
			"id": "wrapper_class_vertical_padding",
			"label": "@global: Wrapper Vertical Padding",
			"options": [
				{
					"value": "@include Spacing prop:py",
					"label": "Inclusion"
				},
				{
					"value": "py-0",
					"label": "@global: None"
				},
				{
					"value": "py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "py-xs",
					"label": "@global: XS"
				},
				{
					"value": "py-sm",
					"label": "@global: SM"
				},
				{
					"value": "py-md",
					"label": "@global: MD"
				},
				{
					"value": "py-lg",
					"label": "@global: LG"
				},
				{
					"value": "py-xl",
					"label": "@global: XL"
				},
				{
					"value": "py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "py-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "wrapper_class_horizontal_padding",
			"label": "@global: Wrapper Horizontal Padding",
			"options": [
				{
					"value": "@include Spacing prop:px",
					"label": "Inclusion"
				},
				{
					"value": "px-0",
					"label": "@global: None"
				},
				{
					"value": "px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "px-xs",
					"label": "@global: XS"
				},
				{
					"value": "px-sm",
					"label": "@global: SM"
				},
				{
					"value": "px-md",
					"label": "@global: MD"
				},
				{
					"value": "px-lg",
					"label": "@global: LG"
				},
				{
					"value": "px-xl",
					"label": "@global: XL"
				},
				{
					"value": "px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "px-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "wrapper_class_vertical_padding_desktop",
			"label": "@global: Wrapper Vertical Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:py",
					"label": "Inclusion"
				},
				{
					"value": "lg:py-0",
					"label": "@global: None"
				},
				{
					"value": "lg:py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:py-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:py-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:py-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:py-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:py-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:py-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:py-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:py-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:py-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:py-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:py-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "select",
			"id": "wrapper_class_horizontal_padding_desktop",
			"label": "@global: Wrapper Horizontal Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:px",
					"label": "Inclusion"
				},
				{
					"value": "lg:px-0",
					"label": "@global: None"
				},
				{
					"value": "lg:px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:px-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:px-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:px-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:px-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:px-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:px-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:px-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:px-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:px-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:px-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:px-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "header",
			"content": "@global: Container Settings"
		},
		{
			"type": "select",
			"id": "container_class_container",
			"label": "@global:  Container",
			"options": [
				{
					"value": "w-full",
					"label": "Full Screen Width"
				},
				{
					"value": "container",
					"label": "Container Width"
				},
				{
					"value": "container container--wide",
					"label": "Wide Container"
				},
				{
					"value": "container container--narrow",
					"label": "Narrow Container"
				},
				{
					"value": "container container--tight",
					"label": "Very Narrow Container"
				},
				{
					"value": "container--product",
					"label": "Product Container Width"
				},
				{
					"value": "container--wide",
					"label": "Wide Container Width"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_vertical_padding",
			"label": "@global:  Vertical Padding",
			"options": [
				{
					"value": "@include Spacing prop:py",
					"label": "Inclusion"
				},
				{
					"value": "py-0",
					"label": "@global: None"
				},
				{
					"value": "py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "py-xs",
					"label": "@global: XS"
				},
				{
					"value": "py-sm",
					"label": "@global: SM"
				},
				{
					"value": "py-md",
					"label": "@global: MD"
				},
				{
					"value": "py-lg",
					"label": "@global: LG"
				},
				{
					"value": "py-xl",
					"label": "@global: XL"
				},
				{
					"value": "py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "py-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_horizontal_padding",
			"label": "@global:  Horizontal Padding",
			"options": [
				{
					"value": "@include Spacing prop:px",
					"label": "Inclusion"
				},
				{
					"value": "px-0",
					"label": "@global: None"
				},
				{
					"value": "px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "px-xs",
					"label": "@global: XS"
				},
				{
					"value": "px-sm",
					"label": "@global: SM"
				},
				{
					"value": "px-md",
					"label": "@global: MD"
				},
				{
					"value": "px-lg",
					"label": "@global: LG"
				},
				{
					"value": "px-xl",
					"label": "@global: XL"
				},
				{
					"value": "px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "px-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_vertical_padding_desktop",
			"label": "@global:  Vertical Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:py",
					"label": "Inclusion"
				},
				{
					"value": "lg:py-0",
					"label": "@global: None"
				},
				{
					"value": "lg:py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:py-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:py-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:py-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:py-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:py-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:py-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:py-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:py-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:py-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:py-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:py-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_horizontal_padding_desktop",
			"label": "@global:  Horizontal Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:px",
					"label": "Inclusion"
				},
				{
					"value": "lg:px-0",
					"label": "@global: None"
				},
				{
					"value": "lg:px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:px-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:px-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:px-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:px-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:px-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:px-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:px-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:px-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:px-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:px-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:px-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "paragraph",
			"content": "@include Text, id_prefix:text_item_1, label_prefix:Text 1"
		},
		{
			"type": "header",
			"content": "@global: Text 1 Settings"
		},
		{
			"type": "text",
			"id": "text_item_1_text",
			"label": "@global: Text 1 Text"
		},
		{
			"type": "liquid",
			"id": "text_item_1_liquid",
			"label": "@global: Text 1 Text (Liquid)"
		},
		{
			"type": "liquid",
			"id": "text_item_1_attr_x_text",
			"label": "@global: Text 1 Dynamic Text (Alpine)"
		},
		{
			"type": "select",
			"id": "text_item_1_element",
			"label": "@global: Text 1 Element",
			"default": "p",
			"options": [
				{
					"value": "@include TextElement",
					"label": "Inclusion"
				},
				{
					"value": "h1",
					"label": "@global:  Heading 1"
				},
				{
					"value": "h2",
					"label": "@global:  Heading 2"
				},
				{
					"value": "h3",
					"label": "@global:  Heading 3"
				},
				{
					"value": "h4",
					"label": "@global:  Heading 4"
				},
				{
					"value": "h5",
					"label": "@global:  Heading 5"
				},
				{
					"value": "p",
					"label": "@global:  Paragraph"
				},
				{
					"value": "div",
					"label": "@global:  Div"
				}
			]
		},
		{
			"type": "select",
			"id": "text_item_1_class_type_style",
			"label": "@global: Text 1 Type Style",
			"options": [
				{
					"value": "@include TypeStyle",
					"label": "Inclusion"
				},
				{
					"value": "",
					"label": "@global:  Auto"
				},
				{
					"value": "type-body",
					"label": "@global:  Body"
				},
				{
					"value": "type-hero",
					"label": "@global:  Hero"
				},
				{
					"value": "type-eyebrow",
					"label": "@global:  Eyebrow"
				},
				{
					"value": "type-headline",
					"label": "@global:  Headline"
				},
				{
					"value": "type-subline",
					"label": "@global:  Subline"
				},
				{
					"value": "type-micro",
					"label": "@global:  Micro"
				},
				{
					"value": "type-item",
					"label": "@global:  Item Title"
				},
				{
					"value": "type-section",
					"label": "@global:  Section Title"
				}
			]
		},
		{
			"type": "select",
			"id": "text_item_1_class_type_size",
			"label": "@global: Text 1 Type Size",
			"options": [
				{
					"value": "@include TypeSize",
					"label": "Inclusion"
				},
				{
					"value": "",
					"label": "@global:  Default"
				},
				{
					"value": "type--sm",
					"label": "@global:  Smaller"
				},
				{
					"value": "type--lg",
					"label": "@global:  Larger"
				}
			]
		},
		{
			"type": "color",
			"id": "text_item_1_style_color",
			"label": "@global: Text 1 Color"
		},
		{
			"type": "url",
			"id": "title_link",
			"label": "Title Link"
		},
		{
			"type": "product",
			"id": "title_product",
			"label": "Title Product"
		},
		{
			"type": "paragraph",
			"content": "@include Button, id_prefix:button_1, label_prefix:Button 1"
		},
		{
			"type": "header",
			"content": "@global: Button 1 Settings"
		},
		{
			"type": "text",
			"id": "button_1_text",
			"label": "@global: Button 1 Text"
		},
		{
			"type": "text",
			"id": "button_1_leading_icon",
			"label": "@global: Leading Icon"
		},
		{
			"type": "text",
			"id": "button_1_trailing_icon",
			"label": "@global: Trailing Icon"
		},
		{
			"type": "url",
			"id": "button_1_link",
			"label": "@global: Button 1 Link"
		},
		{
			"type": "liquid",
			"id": "button_1_liquid_link",
			"label": "@global: Button 1 Link (Liquid)",
			"info": "Replaces the basic Link setting."
		},
		{
			"type": "liquid",
			"id": "button_1_onclick",
			"label": "@global: Button 1 On Click"
		},
		{
			"type": "select",
			"id": "button_1_class_style",
			"label": "@global: Button 1 Style",
			"options": [
				{
					"value": "@include ButtonStyle",
					"label": "Inclusion"
				},
				{
					"value": "button--primary",
					"label": "@global:  Primary"
				},
				{
					"value": "button--secondary",
					"label": "@global:  Secondary"
				},
				{
					"value": "button--tertiary",
					"label": "@global:  Tertiary"
				},
				{
					"value": "button--light",
					"label": "@global:  Light"
				},
				{
					"value": "button--dark",
					"label": "@global:  Dark"
				},
				{
					"value": "button--pop",
					"label": "@global:  Pop"
				},
				{
					"value": "button--highlight",
					"label": "@global:  Highlight"
				},
				{
					"value": "button--action",
					"label": "@global:  Action"
				},
				{
					"value": "button--simple",
					"label": "@global:  Simple"
				},
				{
					"value": "button--emphasis",
					"label": "@global:  Emphasis"
				},
				{
					"value": "button--light-text-link",
					"label": "@global:  Light Text Link"
				},
				{
					"value": "button--link",
					"label": "@global:  Text Link"
				},
				{
					"value": "button--micro-link",
					"label": "@global:  Micro Text Link"
				},
				{
					"value": "button--icon",
					"label": "@global:  Icon"
				},
				{
					"value": "button--primary-hover",
					"label": "@global:  Primary Hover"
				},
				{
					"value": "button--secondary-hover",
					"label": "@global:  Secondary Hover"
				},
				{
					"value": "button--tertiary-hover",
					"label": "@global:  Tertiary Hover"
				}
			]
		},
		{
			"type": "select",
			"id": "button_1_class_size",
			"label": "@global: Button 1 Size",
			"options": [
				{
					"value": "",
					"label": "Standard"
				},
				{
					"value": "button--large",
					"label": "Large"
				}
			]
		},
		{
			"type": "paragraph",
			"content": "@include Carousel"
		},
		{
			"type": "header",
			"content": "@global: Product Settings"
		},
		{
			"type": "number",
			"id": "limit",
			"label": "@global: Max Number of Products",
			"default": 8
		},
		{
			"type": "checkbox",
			"id": "quick_add",
			"label": "@global: Include Quick Add",
			"default": true
		},
		{
			"type": "paragraph",
			"content": "@global: To select data source for products, add a block to this Section"
		},
		{
			"type": "header",
			"content": "@global: Slideshow Settings"
		},
		{
			"type": "checkbox",
			"id": "show_pagination",
			"label": "@global: Show Dot/Bullet Indicators",
			"default": false
		},
		{
			"type": "number",
			"id": "slides_per_view",
			"label": "@global: Slides per view (Desktop)",
			"default": 4
		},
		{
			"type": "number",
			"id": "slides_per_group",
			"label": "@global: Slides per group (Desktop)",
			"default": 4
		},
		{
			"type": "number",
			"id": "spacebetween",
			"label": "@global: Space between slides (Desktop)",
			"default": 16
		},
		{
			"type": "number",
			"id": "slides_per_view_mobile",
			"label": "@global: Slides per view (Mobile)",
			"default": 2
		},
		{
			"type": "number",
			"id": "slides_per_group_mobile",
			"label": "@global: Slides per group (Mobile)",
			"default": 2
		},
		{
			"type": "number",
			"id": "spacebetween_mobile",
			"label": "@global: Space between slides (Mobile)",
			"default": 16
		},
		{
			"type": "checkbox",
			"id": "autoplay",
			"label": "@global: Autoplay"
		},
		{
			"type": "checkbox",
			"id": "loop",
			"label": "@global: Loop"
		},
		{
			"type": "checkbox",
			"id": "center",
			"label": "@global: Center"
		},
		{
			"type": "range",
			"id": "autoplay_slide_duration",
			"label": "@global: Autoplay Slide Duration",
			"min": 3,
			"max": 8,
			"step": 1,
			"default": 8
		},
		{
			"type": "checkbox",
			"id": "arrows",
			"label": "@global: Show arrows (Desktop)",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "arrows_mobile",
			"label": "@global: Show arrows (Mobile)",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "show_arrows_on_hover",
			"label": "@global: Show arrows only on hover (Desktop)",
			"default": true
		},
		{
			"type": "checkbox",
			"label": "@global: Show outer slides on hover",
			"id": "outer_slides",
			"default": false
		},
		{
			"type": "checkbox",
			"label": "@global: Increase arrow tap target",
			"id": "increase_arrow_tap_target",
			"default": false
		},
		{
			"type": "paragraph",
			"content": "@include ProductItem"
		},
		{
			"type": "header",
			"content": "@global: Product Item Settings"
		},
		{
			"id": "product_item_settings",
			"label": "@global: Override Global Product Item Settings",
			"type": "checkbox"
		},
		{
			"type": "liquid",
			"id": "product_item_title_source",
			"label": "@global: Product Item Title Source",
			"default": "product.title.split('-')[0]"
		},
		{
			"type": "liquid",
			"id": "product_item_subtitle_source",
			"label": "@global: Product Item Subtitle Source",
			"default": "product.title.split('-')[1]"
		},
		{
			"type": "liquid",
			"id": "product_item_type_source",
			"label": "@global: Product Item Type Source",
			"default": "product.type"
		},
		{
			"type": "header",
			"content": "@global: Classes"
		},
		{
			"type": "text",
			"id": "classes_product_item",
			"label": "@global: Product Item"
		},
		{
			"type": "text",
			"id": "classes_product_item_image_wrapper",
			"label": "@global: Image"
		},
		{
			"type": "text",
			"id": "classes_product_item_title",
			"label": "@global: Product Item Title"
		},
		{
			"type": "text",
			"id": "classes_product_item_subtitle",
			"label": "@global: Product Item Subtitle"
		},
		{
			"type": "liquid",
			"id": "product_item_additional_liquid",
			"label": "@global: Additional Liquid"
		},
		{
			"type": "checkbox",
			"id": "product_item_show_title",
			"label": "@global: Show Title",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "product_item_show_subtitle",
			"label": "@global: Show Subtitle",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "product_item_show_type",
			"label": "@global: Show Product Type",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "product_item_show_price",
			"label": "@global: Show Price",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "product_item_show_reviews",
			"label": "@global: Show Review Stars",
			"default": true
		},
		{
			"type": "select",
			"id": "product_item_info_layout",
			"label": "@global: Info Layout",
			"options": [
				{
					"value": "",
					"label": "Column"
				},
				{
					"value": "flex flex-row justify-between",
					"label": "Spaced Row"
				}
			]
		},
		{
			"type": "select",
			"id": "product_item_quick_add_position_mobile",
			"label": "@global: Quick Add Button position (Mobile)",
			"default": "image",
			"options": [
				{
					"label": "Image Container",
					"value": "image"
				},
				{
					"label": "Info Container",
					"value": "info"
				}
			]
		},
		{
			"type": "select",
			"id": "product_item_quick_add_position_desktop",
			"label": "@global: Quick Add Button position (Desktop)",
			"default": "image",
			"options": [
				{
					"label": "Image Container",
					"value": "image"
				},
				{
					"label": "Info Container",
					"value": "info"
				}
			]
		},
		{
			"type": "select",
			"id": "product_item_show_button",
			"label": "@global: Show/Hide Button",
			"default": "hide",
			"options": [
				{
					"label": "Show",
					"value": "show"
				},
				{
					"label": "Hide",
					"value": "hide"
				}
			]
		},
		{
			"type": "checkbox",
			"id": "product_item_variant_selector",
			"label": "@global: Include Variant Form",
			"default": false
		},
		{
			"type": "checkbox",
			"id": "product_item_show_swatches",
			"label": "@global: Show Swatches"
		},
		{
			"type": "checkbox",
			"id": "product_item_show_oos_siblings",
			"label": "@global: Show Out of Stock Siblings",
			"default": false
		},
		{
			"type": "number",
			"id": "product_item_swatches_view",
			"label": "@global: Swatches Per View (Mobile)",
			"default": 3
		},
		{
			"type": "number",
			"id": "product_item_swatches_view_desktop",
			"label": "@global: Swatches Per View (Desktop)",
			"default": 5
		},
		{
			"type": "select",
			"id": "product_item_swatch_interaction",
			"label": "@global: User swatch interaction to update product item on desktop",
			"default": "click",
			"options": [
				{
					"label": "Hover",
					"value": "hover"
				},
				{
					"label": "Click",
					"value": "click"
				}
			]
		},
		{
			"type": "select",
			"id": "product_color_option_type",
			"label": "@global: Product Color Option Type",
			"default": "admin_setting",
			"options": [
				{
					"label": "Theme Setting",
					"value": "admin_setting"
				},
				{
					"label": "Image",
					"value": "image"
				},
				{
					"label": "Swatch Color",
					"value": "swatch"
				},
				{
					"label": "Swatch Image",
					"value": "swatch_image"
				}
			]
		},
		{
			"type": "checkbox",
			"id": "same_height_review",
			"label": "Same Height Review",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "single_variant_direct_addtocart",
			"label": "Direct add-to-cart for single variant",
			"default": false
		},
		{
			"type": "select",
			"id": "product_item_show_price_right",
			"label": "Show Price on Right or Bottom",
			"default": "bottom",
			"options": [
				{
					"label": "Right Side of Title",
					"value": "right"
				},
				{
					"label": "Below of Title",
					"value": "bottom"
				}
			]
		}
	],
	"blocks": [
		{
			"name": "Boost Recommendations",
			"type": "shopify-recommendations",
			"limit": 1,
			"settings": [
				{
					"type": "header",
					"content": "Collection settings"
				},
				{
					"label": "Product ID",
					"id": "product_id",
					"type": "text",
					"info": "Enter product id e.g '6880309542983'"
				},
				{
					"label": "Limit",
					"id": "limit",
					"type": "number",
					"default": 8
				},
				{
					"label": "Alternate Carousel Heading",
					"id": "alt_carousel_heading",
					"type": "text",
					"info": "Needed when carousel is converted to tabs"
				},
				{
					"type": "checkbox",
					"id": "is_prefix_sku_enable",
					"label": "Hide same SKU prefix",
					"default": false
				}
			]
		}
	],
	"presets": [
		{
			"name": "Boost Recommendation",
			"category": "Content",
			"settings": {},
			"blocks": []
		}
	]
}
{% endschema %}