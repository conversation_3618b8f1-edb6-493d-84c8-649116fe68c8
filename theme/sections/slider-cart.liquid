<dialog 
  data-modal="slider-cart" 
  class="modal modal--slider-cart slider-cart" 
  tabindex="0"
>
  <div class="section-blocks {{ section.settings.container_classes }}" style="max-height:100dvh;overflow:auto;" x-data>
		{% render 'flex-nested-blocks' blocks:section.blocks offset:0 %}
  </div>
</dialog>

<script>
	window.addEventListener('Cart:itemsAdded', e => {
	  Modal.open('slider-cart')
	})
  window.addEventListener('DOMContentLoaded', e => {
    Cart.get()
  })
  window.addEventListener("pageshow", e => {
    Cart.get()
  });
</script>

{% schema %}
{
	"name": "Slider Cart",
	"settings": [
		{
			"content": "@include VisibilitySettings id_prefix:mobile, label:Mobile",
			"type": "paragraph"
		},
		{
			"content": "@include VisibilitySettings id_prefix:desktop, label:Desktop",
			"type": "paragraph"
		},
		{
			"id": "classes",
			"label": "Header Classes",
			"type": "liquid",
			"default": "shadow-lg bg-white"
		},
		{
			"id": "container_classes",
			"label": "Container Classes",
			"type": "liquid",
			"default": "flex h-full flex-col overflow-y-scroll bg-white shadow-xl text-sm my-4 font-medium text-white"
		}
	],
	"blocks": [
		{
			"type": "frame",
			"name": "⤷ Frame",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤷ Frame"
				},
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:article_class"
				},
				{
					"type": "paragraph",
					"content": "@include LogicInclusion"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "header",
					"content": "Frame Height Settings"
				},
				{
					"type": "select",
					"id": "article_class_height",
					"label": "Frame Height",
					"options": [
						{
							"value": "h-auto",
							"label": "Auto"
						},
						{
							"value": "h-full",
							"label": "100%"
						},
						{
							"value": "h-3/4",
							"label": "75%"
						},
						{
							"value": "h-1/2",
							"label": "50%"
						},
						{
							"value": "h-1/4",
							"label": "25%"
						},
						{
							"value": "h-screen",
							"label": "Full Screen"
						},
						{
							"value": "h-dvh",
							"label": "Dynamic Viewport"
						},
						{
							"value": "h-lvh",
							"label": "Largest Viewport"
						},
						{
							"value": "h-svh",
							"label": "Smallest Viewport"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_height_desktop",
					"label": "Frame Height Desktop",
					"options": [
						{
							"value": "lg:h-auto",
							"label": "Auto"
						},
						{
							"value": "lg:h-full",
							"label": "100%"
						},
						{
							"value": "lg:h-3/4",
							"label": "75%"
						},
						{
							"value": "lg:h-1/2",
							"label": "50%"
						},
						{
							"value": "lg:h-1/4",
							"label": "25%"
						},
						{
							"value": "lg:h-screen",
							"label": "Full Screen"
						},
						{
							"value": "lg:h-main",
							"label": "Full Screen minus Header"
						}
					]
				},
				{
					"type": "color",
					"id": "article_style_background_color",
					"label": "Frame Background Color"
				},
				{
					"type": "color_background",
					"id": "article_style_background",
					"label": "Frame Background Gradient"
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "paragraph",
					"content": "_________________________"
				},
				{
					"type": "header",
					"content": "Advanced Settings"
				},
				{
					"type": "liquid",
					"id": "article_class_custom_classes",
					"label": "Custom Classes"
				}
			]
		},
		{
			"type": "break",
			"name": "⤶ Frame Break",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤶ Frame End"
				}
			]
		},
		{
			"name": "Spacer",
			"type": "spacer",
			"settings": [
				{
					"type": "select",
					"id": "spacer_class_vertical_spacing",
					"label": "Item Vertical Spacer",
					"options": [
						{
							"value": "@include Spacing prop:pb",
							"label": "Inclusion"
						},
						{
							"value": "pb-0",
							"label": "@global: None"
						},
						{
							"value": "pb-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "pb-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "pb-xs",
							"label": "@global: XS"
						},
						{
							"value": "pb-sm",
							"label": "@global: SM"
						},
						{
							"value": "pb-md",
							"label": "@global: MD"
						},
						{
							"value": "pb-lg",
							"label": "@global: LG"
						},
						{
							"value": "pb-xl",
							"label": "@global: XL"
						},
						{
							"value": "pb-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "pb-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "spacer_class_horizontal_spacing",
					"label": "Item Horizontal Spacer",
					"options": [
						{
							"value": "@include Spacing prop:pr",
							"label": "Inclusion"
						},
						{
							"value": "pr-0",
							"label": "@global: None"
						},
						{
							"value": "pr-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "pr-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "pr-xs",
							"label": "@global: XS"
						},
						{
							"value": "pr-sm",
							"label": "@global: SM"
						},
						{
							"value": "pr-md",
							"label": "@global: MD"
						},
						{
							"value": "pr-lg",
							"label": "@global: LG"
						},
						{
							"value": "pr-xl",
							"label": "@global: XL"
						},
						{
							"value": "pr-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "pr-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "spacer_class_vertical_spacing_desktop",
					"label": "Item Vertical Spacer Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:pb",
							"label": "Inclusion"
						},
						{
							"value": "lg:pb-0",
							"label": "@global: None"
						},
						{
							"value": "lg:pb-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:pb-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:pb-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:pb-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:pb-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:pb-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:pb-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:pb-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:pb-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:pb-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:pb-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:pb-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:pb-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:pb-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "spacer_class_horizontal_spacing_desktop",
					"label": "Item Horizontal Spacer Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:pr",
							"label": "Inclusion"
						},
						{
							"value": "lg:pr-0",
							"label": "@global: None"
						},
						{
							"value": "lg:pr-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:pr-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:pr-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:pr-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:pr-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:pr-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:pr-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:pr-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:pr-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:pr-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:pr-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:pr-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:pr-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:pr-8xl",
							"label": "@global: 8XL"
						}
					]
				}
			]
		},
		{
			"type": "content-item",
			"name": "Content Item",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:item_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include Aspect, id_prefix:item_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include ContentItem"
				}
			]
		},
		{
			"type": "title",
			"name": "Title",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include Text id_prefix:title, label_prefix:Title"
				}
			]
		},
		{
			"type": "text",
			"name": "Text",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include Text id_prefix:text, label_prefix:Text"
				}
			]
		},
		{
			"type": "cart-header",
			"name": "Header",
			"settings": [
				{
					"id": "header_classes",
					"label": "Header Classes",
					"type": "liquid"
				},
				{
					"id": "header_text_classes",
					"label": "Header Text Classes",
					"type": "liquid"
				},
				{
					"id": "content",
					"label": "Content",
					"type": "liquid"
				},
				{
					"type": "checkbox",
					"id": "close",
					"label": "Close Button",
					"default": true
				},
				{
					"id": "close_button_classes",
					"label": "Close Button Classes",
					"type": "liquid"
				},
				{
					"id": "icon_size",
					"label": "Icon Size",
					"type": "number",
					"default": 20
				},
				{
					"id": "display",
					"label": "Display",
					"type": "radio",
					"options": [
						{
							"value": "icon",
							"label": "Icon"
						},
						{
							"value": "text",
							"label": "Text"
						},
						{
							"value": "both",
							"label": "Both"
						}
					],
					"default": "icon"
				}
			]
		},
		{
			"type": "announcement",
			"name": "Announcement",
			"settings": [
				{
					"id": "inclusion",
					"label": "Liquid Inclusion Logic",
					"type": "liquid",
					"default": "1"
				},
				{
					"id": "inclusion_js",
					"label": "Javascript Inclusion Logic",
					"type": "liquid"
				},
				{
					"id": "text",
					"label": "Announcement Text",
					"type": "text"
				},
				{
					"id": "text_desktop",
					"label": "Desktop Announcment Text",
					"type": "text"
				},
				{
					"id": "button_text",
					"label": "Announcement Link Text",
					"type": "text"
				},
				{
					"id": "link",
					"label": "Announcement Link",
					"type": "url"
				},
				{
					"type": "checkbox",
					"id": "hide_button",
					"label": "Hide button (Mobile)",
					"default": true
				}
			]
		},
		{
			"type": "button",
			"name": "Button",
			"settings": [
				{
					"type": "select",
					"id": "style",
					"label": "Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "select",
					"id": "button_class_size",
					"label": "Button Size",
					"options": [
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button button--large",
							"label": "Large"
						}
					]
				},
				{
					"type": "text",
					"id": "button_text",
					"label": "Button Text"
				},
				{
					"type": "text",
					"id": "leading_icon",
					"label": "Leading Icon"
				},
				{
					"type": "text",
					"id": "trailing_icon",
					"label": "Trailing Icon"
				},
				{
					"type": "url",
					"id": "link",
					"label": "Link"
				},
				{
					"type": "text",
					"id": "onclick",
					"label": "Click Event",
					"info": "JavaScript onclick event, overrides native functionality. (Not Liquid)"
				},
				{
					"type": "text",
					"id": "onhover",
					"label": "Hover Event",
					"info": "JavaScript onhover event, overrides native functionality. (Not Liquid)"
				},
				{
					"type": "select",
					"id": "onclick_type",
					"label": "Type of Click Event",
					"options": [
						{
							"value": "x-data @",
							"label": "Alpine"
						},
						{
							"value": "on",
							"label": "Native"
						}
					],
					"default": "x-data @"
				},
				{
					"type": "checkbox",
					"id": "form_validity",
					"label": "Require Form Validity",
					"info": "Disables button until all fields in enclosing form are valid"
				},
				{
					"type": "paragraph",
					"content": "_________________________________"
				},
				{
					"type": "header",
					"content": "Advanced Settings"
				},
				{
					"type": "liquid",
					"id": "button_class_custom_classes",
					"label": "Custom Classes"
				},
				{
					"type": "liquid",
					"id": "button_attr_x_data",
					"label": "Dynamic Data Source"
				},
				{
					"type": "liquid",
					"id": "button_attr_x_show",
					"label": "JS Conditional Rendering"
				},
				{
					"type": "liquid",
					"id": "button_attr_x_text",
					"label": "Dynamic Text"
				},
				{
					"type": "liquid",
					"id": "button_attr_QQclass",
					"label": "Dynamic Classes"
				},
				{
					"type": "checkbox",
					"id": "disabled",
					"label": "Disable Button"
				}
			]
		},
		{
			"type": "cart-footer",
			"name": "Cart Buttons",
			"settings": [
				{
					"id": "cart_footer_classes",
					"label": "Checkout Footer Classes",
					"type": "liquid",
					"default": "p-4 mt-auto"
				},
				{
					"id": "checkout_button_style",
					"label": "Checkout Button Style",
					"type": "text",
					"default": "primary"
				},
				{
					"id": "checkout_button_classes",
					"label": "Checkout Button Classes",
					"type": "liquid",
					"default": "flex justify-center w-full "
				},
				{
					"id": "continue_button_style",
					"label": "Continue Button Style",
					"type": "text",
					"default": "custom"
				},
				{
					"id": "continue_button_classes",
					"label": "Continue Button Classes",
					"type": "liquid",
					"default": "flex justify-center w-full font-medium text-indigo-600 hover:text-indigo-500"
				}
			]
		},
		{
			"type": "gift_message",
			"name": "Gift Message",
			"settings": []
		},
		{
			"type": "custom_liquid",
			"name": "Custom Liquid",
			"settings": [
				{
					"type": "liquid",
					"label": "Custom Liquid",
					"id": "liquid"
				}
			]
		},
		{
			"type": "line_items",
			"name": "Line Items",
			"settings": [
				{
					"type": "liquid",
					"id": "empty_state",
					"label": "Empty State"
				},
				{
					"type": "liquid",
					"id": "item_badges",
					"label": "Item Badges",
					"info": "[JavaScript] Conditions and output with Template Literals from ${item}, following convention { condition }::{ value }::{ text color }::{ background color }, separated by line breaks"
				},
				{
					"type": "select",
					"id": "badge_position",
					"label": "Show Badge on Bottom of title or options",
					"default": "bottom_option",
					"options": [
						{
							"label": "Below of Option",
							"value": "bottom_option"
						},
						{
							"label": "Below of Title",
							"value": "bottom_title"
						}
					]
				},
				{
					"type": "liquid",
					"id": "line_item_title_source",
					"label": "Line Item Title Source",
					"default": "item.title.split(' - ')[0]"
				},
				{
					"type": "checkbox",
					"id": "hide_second_option",
					"label": "Hide Second option",
					"default": false
				},
				{
					"type": "checkbox",
					"id": "show_item_type",
					"label": "Show Item Type",
					"default": true
				},
				{
					"type": "checkbox",
					"id": "show_combined_options",
					"label": "Show Combined Product Options",
					"default": false
				},
				{
					"type": "liquid",
					"id": "line_item_type_source",
					"label": "Line Item Type Source",
					"default": "item.product_type"
				},
				{
					"type": "checkbox",
					"id": "show_gift_card_reviews",
					"label": "Show Gift Card Reviews"
				},
				{
					"type": "header",
					"content": "Offer Item Settings"
				},
				{
					"type": "checkbox",
					"id": "show_offer_options",
					"label": "Show Combined Product Options",
					"default": false
				},
				{
					"type": "checkbox",
					"id": "show_offer_price",
					"label": "Show Offer Item Price",
					"default": false
				}
			]
		},
		{
			"type": "cart_empty_state",
			"name": "Cart Empty State",
			"settings": [
				{
					"id": "title",
					"label": "Title",
					"type": "text"
				},
				{
					"id": "empty_state",
					"label": "Empty State",
					"type": "metaobject",
					"metaobject_type": "empty_cart"
				}
			]
		},
		{
			"type": "order_summary",
			"name": "Order Summary",
			"settings": []
		},
		{
			"type": "order_summary_simple",
			"name": "Simple Order Summary",
			"settings": []
		},
		{
			"type": "product_recs",
			"name": "Product Reccomendations",
			"settings": [
				{
					"id": "title",
					"label": "Title",
					"type": "text",
					"default": "Customers Also Bought"
				},
				{
					"type": "liquid",
					"label": "URL",
					"id": "url",
					"info": "Liquid with JavaScript template literals. Used as a as a source for the product data unless Cart Item Specific Recommendations is enabled."
				},
				{
					"id": "include_cart_recs",
					"label": "Cart Item Specific Recommendations",
					"info": "Includes product recommendations based on current cart items. Recommended products can be customized per product in the Search & Discovery App.",
					"type": "checkbox",
					"default": false
				},
				{
					"type": "liquid",
					"label": "Javascript Logic Inclusion",
					"id": "inclusion_js",
					"info": "Insert any javascript logic that evaluates to true to display the section"
				},
				{
					"type": "liquid",
					"label": "Data Map",
					"id": "map",
					"info": "Liquid with JavaScript template literals"
				},
				{
					"type": "header",
					"content": "Upsell Item Settings"
				},
				{
					"id": "enhanced_upsell_item",
					"label": "Enhanced Upsell Item",
					"type": "checkbox",
					"default": false
				},
				{
					"id": "include_swatches",
					"label": "Include Swatches",
					"type": "checkbox",
					"default": false
				},
				{
					"id": "include_review_summary",
					"label": "Include Review Summary",
					"type": "checkbox",
					"default": false
				},
				{
					"type": "header",
					"content": "Carousel Settings"
				},
				{
					"type": "number",
					"id": "slides_per_view",
					"label": "Slides per view",
					"default": 1
				},
				{
					"type": "number",
					"id": "slides_space_between",
					"label": "Space between slides",
					"default": 16
				},
				{
					"type": "number",
					"id": "slides_per_view_desktop",
					"label": "Slides per view (Desktop)",
					"default": 1
				},
				{
					"type": "number",
					"id": "slides_space_between_desktop",
					"label": "Space between slides (Desktop)",
					"default": 16
				},
				{
					"type": "checkbox",
					"id": "arrows",
					"label": "Show arrows",
					"default": true
				},
				{
					"id": "loop",
					"label": "Enable Loop",
					"type": "checkbox",
					"default": false
				}
			]
		},
		{
			"type": "offer_progress",
			"name": "Offer Progress",
			"settings": [
				{
					"id": "title",
					"label": "Title",
					"type": "text",
					"default": "Offer Progress",
					"info": "Internal Reference Only"
				},
				{
					"type": "text",
					"label": "Offer Key",
					"id": "key",
					"info": "Matching the key of an Offer in the Offers section"
				},
				{
					"type": "checkbox",
					"label": "Combinable",
					"id": "combinable",
					"info": "Whether to display combinable offers as one progress bar"
				},
				{
					"type": "checkbox",
					"id": "combinable_empty_cart",
					"label": "Offer Progress Bar Display on Empty Cart When Combinable",
					"default": false
				},
				{
					"type": "color",
					"id": "offer_style_background_color",
					"label": "Offer Background Color"
				},
				{
					"type": "color_background",
					"id": "offer_style_background",
					"label": "Offer Background Gradient"
				},
				{
					"type": "color",
					"id": "offer_progress_style_background_color",
					"label": "Offer Progress Background Color",
					"default": "#000000"
				},
				{
					"type": "color",
					"id": "offer_progress_bar_style_background_color",
					"label": "Offer Progress Bar Color",
					"default": "#CCCCCC"
				},
				{
					"type": "color",
					"id": "offer_threshold_passed_style_color",
					"label": "Offer Threshold Passed SVG Color",
					"default": "#FFFFFF"
				},
				{
					"type": "color",
					"id": "offer_threshold_label_style_color",
					"label": "Offer Threshold Label Text Color",
					"default": "#000000"
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:offer_class, label_prefix:Offer "
				},
				{
					"type": "header",
					"content": "Message Settings"
				},
				{
					"type": "select",
					"id": "offer_message_class_type_style",
					"label": "Message Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "offer_message_class_type_size",
					"label": "Message Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "offer_message_style_color",
					"label": "Message Text Color"
				}
			]
		},
		{
			"type": "offer_progress_summary",
			"name": "Offer Progress Summary",
			"settings": [
				{
					"type": "text",
					"id": "offer_combine_key",
					"label": "Offer Combine Key",
					"default": "ship",
					"info": "Enter a matching substring that is shared between offer keys you wise to combine."
				},
				{
					"type": "color",
					"id": "threshold_icon_style_background_color",
					"label": "Threshold Icon Background Color"
				},
				{
					"type": "color",
					"id": "threshold_icon_style_color",
					"label": "Threshold Icon Color"
				},
				{
					"type": "color",
					"id": "threshold_label_style_color",
					"label": "Threshold Label Color"
				}
			]
		},
		{
			"type": "payment_widget",
			"name": "Payment Widget",
			"settings": [
				{
					"type": "number",
					"id": "payment_count",
					"label": "Payment Count",
					"default": 4
				},
				{
					"type": "liquid",
					"id": "text",
					"label": "Widget Text",
					"info": "Or [ count ] interest-free payments of [ payment ] with ((klarna 40x16)) or ((afterpay 16x16))"
				},
				{
					"type": "header",
					"content": "Icon 1 settings"
				},
				{
					"type": "image_picker",
					"label": "Icon Image",
					"id": "image_1"
				},
				{
					"type": "url",
					"label": "Icon Image URL",
					"id": "link_1"
				},
				{
					"type": "header",
					"content": "Icon 2 settings"
				},
				{
					"type": "image_picker",
					"label": "Icon Image",
					"id": "image_2"
				},
				{
					"type": "url",
					"label": "Icon Image URL",
					"id": "link_2"
				},
				{
					"type": "header",
					"content": "Icon 3 settings"
				},
				{
					"type": "image_picker",
					"label": "Icon Image",
					"id": "image_3"
				},
				{
					"type": "url",
					"label": "Icon Image URL",
					"id": "link_3"
				},
				{
					"type": "liquid",
					"id": "inclusion_js",
					"label": "Conditional Inclusion Logic",
					"info": "[JavaScript] If populated, an expression which evaluates to a truthy value will render the payment widget."
				}
			]
		}
	],
	"default": {
		"settings": {},
		"blocks": [
			{
				"type": "cart-header"
			},
			{
				"type": "line_items"
			},
			{
				"type": "cart-footer"
			},
			{
				"type": "order_summary"
			}
		]
	}
}
{% endschema %}
