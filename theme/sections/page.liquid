<section class="{% render 'class-settings' prefix:'wrapper_class' settings:section.settings %}" 
	style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}">

  <article class="page-main h-full" itemscope itemtype="http://schema.org/WebPage">
    {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when '@app' -%}
          <div class="page-width page-width--narrow">
            {% render block %}
          </div>

        {%- when 'featured_image'-%}
          {%- if block.settings.image -%}
            <div class="m-0" {{ block.shopify_attributes }}>
              <div class="block relative {{ block.settings.height_mobile }} {{ block.settings.height }}" itemprop="image">
                {% render 'image' with image: block.settings.image, class: "page__featured-image block top-0 left-0 w-full h-full object-cover object-center" %}
              </div>
            </div>
          {%- endif -%}

        {%- when 'title'-%}
          <header class="page-main__header {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
            style="{% render 'style-settings' prefix:'container_style' settings:section.settings %}" {{ block.shopify_attributes }}>          
            <h1 class="page-main__title type-headline mt-0" itemprop="headline">{{ page.title | escape }}</h1>
          </header>

        {%- when 'content'-%}
          <div class="page-main__content mx-auto rte {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
            style="{% render 'style-settings' prefix:'container_style' settings:section.settings %}" {{ block.shopify_attributes }}>
            {{ page.content }}
          </div>   
      {%- endcase -%}
    {%- endfor -%}
  </article>
</section>

{% schema %}
{
	"name": "Page",
	"tag": "section",
	"settings": [
		{
			"type": "header",
			"content": "Background Settings"
		},
		{
			"type": "color",
			"id": "wrapper_style_background_color",
			"label": "Section Background Color"
		},
		{
			"type": "color_background",
			"id": "wrapper_style_background",
			"label": "Section Background Gradient"
		},
		{
			"type": "header",
			"content": "Text Settings"
		},
		{
			"type": "color",
			"id": "container_style_color",
			"label": "Section Text Color"
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "header",
			"content": "@global: Container Settings"
		},
		{
			"type": "select",
			"id": "container_class_container",
			"label": "@global:  Container",
			"options": [
				{
					"value": "w-full",
					"label": "Full Screen Width"
				},
				{
					"value": "container",
					"label": "Container Width"
				},
				{
					"value": "container container--wide",
					"label": "Wide Container"
				},
				{
					"value": "container container--narrow",
					"label": "Narrow Container"
				},
				{
					"value": "container container--tight",
					"label": "Very Narrow Container"
				},
				{
					"value": "container--product",
					"label": "Product Container Width"
				},
				{
					"value": "container--wide",
					"label": "Wide Container Width"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_vertical_padding",
			"label": "@global:  Vertical Padding",
			"options": [
				{
					"value": "@include Spacing prop:py",
					"label": "Inclusion"
				},
				{
					"value": "py-0",
					"label": "@global: None"
				},
				{
					"value": "py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "py-xs",
					"label": "@global: XS"
				},
				{
					"value": "py-sm",
					"label": "@global: SM"
				},
				{
					"value": "py-md",
					"label": "@global: MD"
				},
				{
					"value": "py-lg",
					"label": "@global: LG"
				},
				{
					"value": "py-xl",
					"label": "@global: XL"
				},
				{
					"value": "py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "py-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_horizontal_padding",
			"label": "@global:  Horizontal Padding",
			"options": [
				{
					"value": "@include Spacing prop:px",
					"label": "Inclusion"
				},
				{
					"value": "px-0",
					"label": "@global: None"
				},
				{
					"value": "px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "px-xs",
					"label": "@global: XS"
				},
				{
					"value": "px-sm",
					"label": "@global: SM"
				},
				{
					"value": "px-md",
					"label": "@global: MD"
				},
				{
					"value": "px-lg",
					"label": "@global: LG"
				},
				{
					"value": "px-xl",
					"label": "@global: XL"
				},
				{
					"value": "px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "px-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_vertical_padding_desktop",
			"label": "@global:  Vertical Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:py",
					"label": "Inclusion"
				},
				{
					"value": "lg:py-0",
					"label": "@global: None"
				},
				{
					"value": "lg:py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:py-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:py-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:py-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:py-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:py-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:py-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:py-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:py-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:py-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:py-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:py-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_horizontal_padding_desktop",
			"label": "@global:  Horizontal Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:px",
					"label": "Inclusion"
				},
				{
					"value": "lg:px-0",
					"label": "@global: None"
				},
				{
					"value": "lg:px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:px-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:px-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:px-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:px-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:px-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:px-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:px-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:px-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:px-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:px-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:px-8xl",
					"label": "@global: 8XL"
				}
			]
		}
	],
	"blocks": [
		{
			"type": "@app"
		},
		{
			"type": "featured_image",
			"name": "Featured Image",
			"limit": 1,
			"settings": [
				{
					"type": "select",
					"id": "height",
					"label": "Image Height (Desktop)",
					"options": [
						{
							"value": "lg:h-screen",
							"label": "Full height"
						},
						{
							"value": "lg:h-[75dvh]",
							"label": "Three quarter height"
						},
						{
							"value": "lg:h-[50dvh]",
							"label": "Half height"
						},
						{
							"value": "lg:h-[25dvh]",
							"label": "Quarter height"
						},
						{
							"value": "lg:h-auto",
							"label": "Text content height"
						},
						{
							"value": "h-auto-img-l",
							"label": "Image height"
						}
					],
					"default": "lg:h-screen"
				},
				{
					"type": "select",
					"id": "height_mobile",
					"label": "Image Height (Mobile)",
					"options": [
						{
							"value": "h-screen",
							"label": "Full height"
						},
						{
							"value": "h-[75dvh]",
							"label": "Three quarter height"
						},
						{
							"value": "h-[50dvh]",
							"label": "Half height"
						},
						{
							"value": "h-[25dvh]",
							"label": "Quarter height"
						},
						{
							"value": "h-auto",
							"label": "Text content height"
						},
						{
							"value": "h-auto-img",
							"label": "Image height"
						}
					],
					"default": "h-screen"
				},
				{
					"type": "image_picker",
					"id": "image",
					"label": "Image Source"
				}
			]
		},
		{
			"type": "title",
			"name": "Title",
			"limit": 1,
			"settings": []
		},
		{
			"type": "content",
			"name": "Content",
			"limit": 1
		}
	]
}
{% endschema %}
