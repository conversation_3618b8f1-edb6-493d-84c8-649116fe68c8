<section class="{% render 'class-settings' prefix:'wrapper_class' settings:section.settings %}" 
	style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}">

  <article class="page-main h-full" itemscope itemtype="http://schema.org/WebPage">
    {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when '@app' -%}
          <div class="page-width page-width--narrow">
            {% render block %}
          </div>

        {%- when 'featured_image'-%}
          {%- if block.settings.image -%}
            <div class="m-0" {{ block.shopify_attributes }}>
              <div class="block relative {{ block.settings.height_mobile }} {{ block.settings.height }}" itemprop="image">
                {% render 'image' with image: block.settings.image, class: "page__featured-image block top-0 left-0 w-full h-full object-cover object-center" %}
              </div>
            </div>
          {%- endif -%}

        {%- when 'title'-%}
          <header class="page-main__header {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
            style="{% render 'style-settings' prefix:'container_style' settings:section.settings %}" {{ block.shopify_attributes }}>          
            <h1 class="page-main__title type-headline mt-0" itemprop="headline">{{ page.title | escape }}</h1>
          </header>

        {%- when 'content'-%}
          <div class="page-main__content mx-auto rte {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
            style="{% render 'style-settings' prefix:'container_style' settings:section.settings %}" {{ block.shopify_attributes }}>
            {{ page.content }}
          </div>   
      {%- endcase -%}
    {%- endfor -%}
  </article>
</section>

{% schema %}
{
	"name": "Page",
	"tag": "section",
	"settings": [
		{
			"type": "header",
			"content": "Background Settings"
		},
		{
			"type": "color",
			"id": "wrapper_style_background_color",
			"label": "Section Background Color"
		},
		{
			"type": "color_background",
			"id": "wrapper_style_background",
			"label": "Section Background Gradient"
		},
		{
			"type": "header",
			"content": "Text Settings"
		},
		{
			"type": "color",
			"id": "container_style_color",
			"label": "Section Text Color"
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		}
	],
	"blocks": [
		{
			"type": "@app"
		},
		{
			"type": "featured_image",
			"name": "Featured Image",
			"limit": 1,
			"settings": [
				{
					"type": "select",
					"id": "height",
					"label": "Image Height (Desktop)",
					"options": [
						{
							"value": "lg:h-screen",
							"label": "Full height"
						},
						{
							"value": "lg:h-[75dvh]",
							"label": "Three quarter height"
						},
						{
							"value": "lg:h-[50dvh]",
							"label": "Half height"
						},
						{
							"value": "lg:h-[25dvh]",
							"label": "Quarter height"
						},
						{
							"value": "lg:h-auto",
							"label": "Text content height"
						},
						{
							"value": "h-auto-img-l",
							"label": "Image height"
						}
					],
					"default": "lg:h-screen"
				},
				{
					"type": "select",
					"id": "height_mobile",
					"label": "Image Height (Mobile)",
					"options": [
						{
							"value": "h-screen",
							"label": "Full height"
						},
						{
							"value": "h-[75dvh]",
							"label": "Three quarter height"
						},
						{
							"value": "h-[50dvh]",
							"label": "Half height"
						},
						{
							"value": "h-[25dvh]",
							"label": "Quarter height"
						},
						{
							"value": "h-auto",
							"label": "Text content height"
						},
						{
							"value": "h-auto-img",
							"label": "Image height"
						}
					],
					"default": "h-screen"
				},
				{
					"type": "image_picker",
					"id": "image",
					"label": "Image Source"
				}
			]
		},
		{
			"type": "title",
			"name": "Title",
			"limit": 1,
			"settings": []
		},
		{
			"type": "content",
			"name": "Content",
			"limit": 1
		}
	]
}
{% endschema %}
