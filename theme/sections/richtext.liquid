{% liquid 
	assign section_type = 'richtext'
%}

<section 
	class="section section--{{ section_type }} relative 
	{% render 'class-settings' prefix:'wrapper_class' settings:section.settings %}" 
	style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}">

	{% if section.settings.wrapper_image %}
		<img src="{{ section.settings.wrapper_image | image_url }}" alt="{{ section.settings.wrapper_image.alt }}" class="section__media absolute inset-0 w-full h-full object-cover" />
	{% endif %}

	<main 
		class="section__container relative {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
		style="{% render 'style-settings' prefix:'container_styles' settings:section.settings %}">
		<div class="section__blocks grid {% render 'class-settings' prefix:'grid_container_class', settings:section.settings %}">
			{%- liquid
				for block in section.blocks 
					case block.type 
						when 'text' 
							render 'richtext-item', settings:block.settings
					endcase 
				endfor 
			-%}
		</div>
	</main>
</section>

{% schema %}
{
	"name": "Rich Text",
	"settings": [
		{
			"type": "paragraph",
			"content": "@include SectionDisplay, label_prefix:Display, id_prefix:wrapper_class"
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		},
		{
			"type": "paragraph",
			"content": "@include GridContainer, label_prefix:Grid, id_prefix:grid_container"
		}
	],
	"blocks": [
		{
			"type": "text",
			"name": "Text",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include GridColumns, id_prefix:item_class"
				},
				{
					"type": "radio",
					"label": "Text Justification",
					"id": "item_class_text_alignment",
					"default": "text-center",
					"options": [
						{
							"label": "←",
							"value": "text-left"
						},
						{
							"label": "↔",
							"value": "text-center"
						},
						{
							"label": "→",
							"value": "text-right"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Text, label_prefix:Title, id_prefix:title"
				},
				{
					"type": "paragraph",
					"content": "@include RichText, label_prefix:Featured Content, id_prefix:featured_content"
				}
			]
		}
	],
	"presets": [
		{
			"name": "Rich Text",
			"category": "Text",
			"settings": {},
			"blocks": []
		}
	]
}
{% endschema %}
