<script>
  {%- capture CollectionBanners -%}
  {%- for block in section.blocks -%}
    {{ block.settings | json }},
  {%- endfor -%}
  {%- endcapture -%}
  {%- assign size = CollectionBanners | size | minus: 1 -%}
  window.collectionBanners = [{{ CollectionBanners | slice: 0, size }}]
</script>

{%- for block in section.blocks -%}
  <template banner="{{ block.settings.title }}" x-init="{% if block.settings.grid_position_mobile and block.settings.grid_position_desktop %}$watch('$store.collection',() => {
    const bannerIndex = (window.innerWidth < 1024) ? {{ block.settings.grid_position_mobile }} : {{ block.settings.grid_position_desktop }}
    const productItem = document.querySelectorAll('[product-grid] article')[bannerIndex - 1]
    if (productItem) {
      const div = document.createElement('div')
      div.innerHTML = $el.innerHTML
      productItem.parentNode.insertBefore(div.querySelector('.relative'), productItem)
    }
   }){% endif %}">
     <article class="relative {% render 'class-settings' settings:block.settings prefix: 'banner_class' %}">
       {% render 'content-item' settings:block.settings %}
     </article>
    </template>
{%- endfor -%}
  
{% schema %}
{
	"name": "Collection Banners",
	"settings": [],
	"blocks": [
		{
			"type": "banner",
			"name": "Banner",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Banner Title",
					"info": "For Internal Organization"
				},
				{
					"type": "paragraph",
					"content": "@include GridColumns, id_prefix:banner_class"
				},
				{
					"type": "paragraph",
					"content": "@include GridPosition"
				},
				{
					"type": "paragraph",
					"content": "@include ContentItem"
				}
			]
		}
	]
}
{% endschema %}
