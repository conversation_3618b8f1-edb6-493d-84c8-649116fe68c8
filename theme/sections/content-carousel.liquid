{% liquid
    assign section_type = 'content-carousel'
    assign carousel = section
	assign remote = false
 %}

 {%- if section.settings.inclusion_liquid_carousel != '' -%}

 <section 
 	id="carousel-{{ section.id }}"
	class="section section--{{ section_type }} relative {% render 'class-settings', prefix:'wrapper_class', settings:section.settings %}"
	style="{% render 'style-settings', prefix:'wrapper_style', settings:section.settings %}"
	x-data='{ inclusion_js: {% if section.settings.inclusion_js == "true" %}true{% elsif section.settings.inclusion_js == "false" %}false{% else %}`{{section.settings.inclusion_js}}`{% endif %} }' x-show="inclusion_js">

	{% if section.settings.wrapper_bg_image %}
		{% render 'image'
			image: section.settings.wrapper_bg_image
			alt: section.settings.wrapper_bg_image.alt
			class: 'section__media absolute inset-0 w-full h-full object-cover max-lg:hidden'
		%}
	{% endif %}
	{% if section.settings.wrapper_bg_image_mob %}
		{% render 'image' 
			image: section.settings.wrapper_bg_image_mob
			alt: section.settings.wrapper_bg_image_mob.alt
			class: 'section__media absolute inset-0 w-full h-full object-cover lg:hidden'
		%}
	{% endif %}

 	<main 
		class="section__container relative {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
		style="{% render 'style-settings' prefix:'container_styles' settings:section.settings %}"
	>

    {% if section.settings.split_into_tabs %}
		<div 
			x-data="{
				activeTab: 0,
        swipers: {},
				setActiveTab(index, carouselKey) {
					let self = this;
					this.activeTab = index
					this.$nextTick(() => {
						const swiperContainer = document.querySelector(`.tab-content[data-tab-index='${index}'] .swiper-container`);
						if (!swiperContainer) {
							return
						}

						// retrieve data-carousel-key attribute value from swiper container.
						const configKey = swiperContainer.getAttribute('carousel-key')

						// retrieve configuration for current swiper instance using configKey.
						const config = window.carouselConfigs[configKey];
						
						if (config) {
							// create a new instance of swiper with the saved swiper settings
							self.swipers[configKey] = new Swiper(swiperContainer, config);
						}
					});
				},
        updateActiveTab() {
          const redirectTitle = window.redirects && window.redirects[0] ? window.redirects[0].title.handle() : '';
          {% for block in section.blocks %}
            {% if block.type == 'shopify-collection' or block.type == 'shopify-recommendations' or block.type == 'bloomreach-recs' %}
              if ('{{ block.settings.alt_carousel_heading | handle }}' === redirectTitle) {
                this.activeTab = {{ forloop.index0 }};
                this.setActiveTab({{ forloop.index0 }}, 'swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}');
              }
            {% endif %}
          {% endfor %}
        }
    }"
    x-init="setTimeout(()=>{updateActiveTab()}, 500)"
    class="tabs-wrapper"
		>
			<div class="flex flex-col-reverse items-center lg:flex-row lg:justify-between">
				<div article class="lg:flex lg:flex-grow lg:justify-center">
					<div class="lg:shrink-0 lg:absolute">
						{% liquid 
										
							capture text_item_1
								render 'class-settings' prefix:'text_item_1', settings:carousel.settings
							endcapture
							capture button_1
								render 'class-settings' prefix:'button_1', settings:carousel.settings
							endcapture
	
							if section.settings.title_product != blank 
								render 'carousel-product-title' settings:section.settings, price:false, blocks:section.blocks
							endif
	
							if carousel.settings.text_item_1_text != "" or carousel.settings.button_1_text != "" and section.settings.title_product == blank
	
								render 'content-item', item_classes:'content-item--carousel-header flex-col', content_classes: 'w-full justify-between', text_stack_classes: 'self-end gap-2 flex-col', button_stack_classes: 'self-end gap-2 flex-col', text_item_1_link:section.settings.title_link, text_item_1_text: carousel.settings.text_item_1_text, text_item_1_class: text_item_1, text_item_1_element: carousel.settings.text_item_1_element, text_item_2_text: product_total, text_item_2_class: 'text-sm', button_1_text: carousel.settings.button_1_text, button_1_classes: button_1, button_1_link: carousel.settings.button_1_link, settings: carousel.settings
							endif
	
						%} 
					</div>
				</div>
			</div>
			<div class="flex flex-col-reverse items-center lg:flex-row lg:justify-between">
				<!-- Tab Links -->
				<div class="{{ section_type }}__tabs-mb-0 mb-10 tab-link lg:shrink-0 tabs">
					{% assign index = 0 %}
					{% for block in section.blocks %}
						{% if block.type == 'shopify-collection' or block.type == 'shopify-recommendations' or block.type == 'bloomreach-recs' %}
						{% capture carousel_key %}swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}{% endcapture %}
							<button 
								class="tab"
								:class="{ 'active': activeTab === {{ index }} }"
                {% if section.settings.tab_interaction contains "hover" %}
                  @mouseover="setActiveTab({{ index }}, '{{ carousel_key }}')"
                {% else %} 
                  @click="setActiveTab({{ index }}, '{{ carousel_key }}')"
                {% endif %}
								@focus="setActiveTab({{ index }}, '{{ carousel_key }}')"
							>
								{{ block.settings.alt_carousel_heading }}
							</button>
							{% assign index = index | plus: 1 %}
						{% endif %}
					{% endfor %}
				</div>
			</div>

        {% comment %} # separate carousels for specified block types {% endcomment %}
		{% assign index = 0 %}
        {% for block in section.blocks %}
            {% if block.type == 'shopify-collection' or block.type == 'shopify-recommendations' or block.type == 'bloomreach-recs' %}
				{% assign blockArray = block.settings %}
				<div id="tab-content-{{ index }}" data-tab-index="{{ index }}" class="tab-content carousel-container" :class="{ 'active': activeTab === {{ index }} }" style=""> 

				{% assign carousel_key = 'swiperCarousel' | append: section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' | append: '_' | append: block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' %}

					{% case block.type %}
					{% comment %} Save Carousel Settings {% endcomment %}

					{% comment %} END of Save Carousel Settings {% endcomment %}

						<!-- CAROUSEL shopify-collection -->
						{% when 'shopify-collection' %}
							{% if block.settings.collection %}
								{% liquid
									assign source = '/collections/' | append: block.settings.collection | append: '/products.json?limit=' | append: block.settings.limit
									assign map = '{products:{from:"products",each:{title:"title",handle:"handle",tags:"tags",type:"product_type",price:"variants.0.price|*100",compare_at_price:"variants.0.compare_at_price|*100",images:"images",id:"id",variants:{from:"variants",each:{id:"id",title:"title",price:"price|*100",available:"available",sku:"sku"}}}}}'
									render 'carousel-product-data' source:source, map:map id:section.id settings:false, block_id:block.id, split_tabs: section.settings.split_into_tabs
									assign remote = true
								%}
							{% endif %}
						
							<div 
								class="{{ section.id }} w-full" {% render 'carousel-config' settings:section.settings namespace:section.id block:block.id %}
								data-config-key="{{ section.id }}_{{ block.id }}"
							>

								<div data-block-id="{{ block.id }}" 
									x-data="{
										blockId: '{{ block.id }}',
										products: [],
										productsAvailable: false,
										init() {
											// listener for product updates
											window.addEventListener('productsUpdated', (event) => {
												if (event.detail.blockId === this.blockId) {
													this.updateProducts();
												}
											});
							
											this.updateProducts();
										},
										updateProducts() {
											if (window.tabsProducts && window.tabsProducts[this.blockId]) {
												this.products = window.tabsProducts[this.blockId];
												this.productsAvailable = true;
											}
										}
									}" 
									
								>
									

									<!-- Swiper Carousel for shopify-collection -->
									<div class="relative w-full h-full group/carousel" aria-live="polite">
										<!-- Swiper Container -->
										<div 
											x-init="init()"
											class="swiper swiper-container h-full {% if carousel.settings.outer_slides %}swiper--overflow-visible{% endif %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}"
											carousel-key="swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}"
										>
										
                      {% if remote and skeleton_items != true %}
                        <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper-skeleton" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
                          {%- for i in (1.. section.settings.slides_per_view) -%}
                            <div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                              {%- render 'product-item' product:false, skeleton:true, settings:section.settings, _settings:settings location:'carousel' -%}
                            </div>
                          {%- endfor -%}
                        </div>
                        {% assign skeleton_items = true %}
                      {% endif %} 
											
									
											<!-- Swiper Wrapper -->
                      <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
												<!-- Swiper Slides -->

													{% liquid

														for block in section.blocks
							
															assign prev = forloop.index0 | minus: 1
							
															if block.type contains 'item' and section.blocks[prev].type != 'overlay'
													
																echo '<div class="relative swiper-slide '
																echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
																if block.settings.product_position != blank
																	echo 'data-slide-position="' | append: block.settings.product_position | append:'"'
																endif
																echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
																echo '>'
							
																case block.type
							
																	when 'content-item'
																			render 'content-item' settings:block.settings blocks:section.blocks offset:forloop.index
							
																	when 'product-item'
																		for product in block.settings.product
																			render 'product-item' product:product settings:section.settings, _settings:settings
																			unless forloop.last
																				echo '</div><div class="relative swiper-slide '
																				echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view
																				echo '">'
																			endunless
																		endfor
							
																endcase
							
																echo '</div>'
							
															endif
														endfor
							
													%}

													<template x-for="(product, index) in products" hidden>
																				
														<template x-if="product" hidden>
														
														<div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
															{%- render 'product-item' product:false, settings:section.settings, _settings:settings -%}
															
															<template x-import.afterparent='`.{{section.id}} [data-slide-position="${index+1}"]`'></template>
											
														</div>
											
														</template>
											
													</template>

													{% if blockArray.shop_all_text != blank and  blockArray.slider_shop_all %}
														<template x-if="!!products">
															<div class="swiper-slide swiper-slide--shop-all">
																<div class="content-carousel__show-all w-full h-full flex items-center justify-center aspect-[1/1]">
																	<a href="{{ blockArray.collection.url }}" class="button button--primary">
																		{{ blockArray.shop_all_text }}
																	</a>
																</div>
															</div>
														</template>
													{% endif %}

												<!-- Insert Carousel Items Here -->
											</div>
											<!-- Add Swiper Navigation Arrows and Pagination Here -->
											{% if section.settings.show_pagination %}
												<style>
													#shopify-section-{{ section.id }} section {
														padding: 0 0 3.583rem;
													}
													@media screen and (min-width: 768px) {
														#shopify-section-{{ section.id }} section {
															padding: 3.583rem 0; /* match desktop */
														}
													}
												</style>
												<div class="swiper-pagination"></div>
												<script type="swiper/config">
													{
														pagination: {
															el: '.swiper-pagination',
															type: 'bullets',
															clickable: true,
															dynamicBullets: true,
															dynamicMainBullets: 4
														}
													}	
												</script>
											{% endif %}
									
									
											{% if carousel.settings.arrows or carousel.settings.arrows_mobile or carousel.settings.show_arrows_on_hover %}
												{% capture arrow_visibility_classes %}
													{% if carousel.settings.arrows_mobile and carousel.settings.arrows %}
														opacity-100
													{% elsif carousel.settings.arrows_mobile %}
														opacity-100 lg:opacity-0
													{% elsif carousel.settings.arrows %}
														opacity-0 lg:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
													{% if carousel.settings.show_arrows_on_hover %}
														opacity-0 lg:opacity-0 lg:group-hover/carousel:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
												{% endcapture %}
												
												<button class="top-1/2 transform -translate-y-1/2 absolute left-6 {{ arrow_visibility_classes }} swiper-button-prev btn-control {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %}" tabindex="0" aria-label="Next slide  " aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:2 %}
												</button>
												<button class="top-1/2 transform -translate-y-1/2 absolute right-6 {{ arrow_visibility_classes }} swiper-button-next btn-control {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %}" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:2 %}
												</button>
											{% endif %}

										</div>
									</div>

								</div><!-- END of data-block-id -->
							</div><!-- END of shopify-collection carousel-config render -->
							
		
						<!-- CAROUSEL shopify-collection -->
						{% when 'shopify-recommendations' %}
							{% liquid
								assign source = '/recommendations/products.json?product_id=' | append: block.settings.product_id | append: '&limit=' | append: block.settings.limit
								assign map = '{products:{from:"products",each:{title:"title",handle:"handle",tags:"tags",type:"type",price:"price",compare_at_price:"compare_at_price",featured_image:"featured_image",hover_image:"featured_image",id:"id",variants:{from:"variants",each:{id:"id",title:"title",price:"price|*100",available:"available",sku:"sku"}}}}}'
								render 'carousel-product-data' source:source, map:map id:section.id, block_id:block.id, split_tabs: section.settings.split_into_tabs
								assign remote = true
							%}

							<div 
							    class="{{ section.id }} w-full" {% render 'carousel-config' settings:section.settings namespace:section.id %}
								data-config-key="{{ section.id }}_{{ block.id }}"
							>
								<div data-block-id="{{ block.id }}" 
									x-data="{
										blockId: '{{ block.id }}',
										products: [],
										productsAvailable: false,
										init() {
											// listener for product updates
											window.addEventListener('productsUpdated', (event) => {
												if (event.detail.blockId === this.blockId) {
													this.updateProducts();
												}
											});
							
											this.updateProducts();
										},
										updateProducts() {
											if (window.tabsProducts && window.tabsProducts[this.blockId]) {
												this.products = window.tabsProducts[this.blockId];
												this.productsAvailable = true;
											}
										}
									}" 
									x-init="init()"
								>
									<!-- Swiper Carousel for shopify-recommendations -->
									<div class="relative w-full h-full group/carousel" aria-live="polite">
										<!-- Swiper Container -->
										<div 
											x-init="init()"
											x-ref="swiperContainer"
											class="swiper swiper-container h-full {% if carousel.settings.outer_slides %}swiper--overflow-visible{% endif %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}"
											carousel-key="swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}"
										>
                      {% if remote and skeleton_items != true %}
                        <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper-skeleton" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
                          {%- for i in (1.. section.settings.slides_per_view) -%}
                            <div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                              {%- render 'product-item' product:false, skeleton:true, settings:section.settings, _settings:settings -%}
                            </div>
                          {%- endfor -%}
                        </div>
                        {% assign skeleton_items = true %}
                      {% endif %} 
											
											<!-- Swiper Wrapper -->
											<div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
												<!-- Swiper Slides -->

													{% liquid

														for block in section.blocks
							
															assign prev = forloop.index0 | minus: 1
							
															if block.type contains 'item' and section.blocks[prev].type != 'overlay'
													
																echo '<div class="relative swiper-slide '
																echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
																if block.settings.product_position != blank
																	echo 'data-slide-position="' | append: block.settings.product_position | append:'"'
																endif
																echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
																echo '>'
							
																case block.type
							
																	when 'content-item'
																			render 'content-item' settings:block.settings blocks:section.blocks offset:forloop.index
							
																	when 'product-item'
																		for product in block.settings.product
																			render 'product-item' product:product settings:section.settings, _settings:settings
																			unless forloop.last
																				echo '</div><div class="relative swiper-slide '
																				echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view
																				echo '">'
																			endunless
																		endfor
							
																endcase
							
																echo '</div>'
							
															endif
														endfor
							
													%}

													<template x-for="(product, index) in products" hidden>
																				
														<template x-if="product" hidden>
														
														<div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
															{%- render 'product-item' product:false, settings:section.settings, _settings:settings -%}
															
                              <template x-import.afterparent='`.{{section.id}} [data-slide-position="${index+1}"]`'></template>
											
														</div>
											
														</template>
											
													</template>
											
													{% for block in section.blocks %}
														{% if block.settings.shop_all_text != blank  and  block.settings.slider_shop_all %}
															<template x-if="!!products">
																<div class="swiper-slide swiper-slide--shop-all">
																	<div class="__show-all w-full h-full flex items-center justify-center aspect-[1/1]">
																		<a href="{{ block.settings.collection.url }}" class="button button--primary">
																			{{ block.settings.shop_all_text }}
																		</a>
																	</div>
																</div>
															</template>
															{% break %}
														{% endif %}
													{% endfor %}

												<!-- Insert Carousel Items Here -->
											</div>
											<!-- Add Swiper Navigation Arrows and Pagination Here -->
											{% if section.settings.show_pagination %}
												<style>
													#shopify-section-{{ section.id }} section {
														padding: 0 0 3.583rem;
													}
													@media screen and (min-width: 768px) {
														#shopify-section-{{ section.id }} section {
															padding: 3.583rem 0; /* match desktop */
														}
													}
												</style>
												<div class="swiper-pagination"></div>
												<script type="swiper/config">
													{
														pagination: {
															el: '.swiper-pagination',
															type: 'bullets',
															clickable: true,
															dynamicBullets: true,
															dynamicMainBullets: 4
														}
													}	
												</script>
											{% endif %}
									
									
											{% if carousel.settings.arrows or carousel.settings.arrows_mobile or carousel.settings.show_arrows_on_hover %}
												{% capture arrow_visibility_classes %}
													{% if carousel.settings.arrows_mobile and carousel.settings.arrows %}
														opacity-100
													{% elsif carousel.settings.arrows_mobile %}
														opacity-100 lg:opacity-0
													{% elsif carousel.settings.arrows %}
														opacity-0 lg:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
													{% if carousel.settings.show_arrows_on_hover %}
														opacity-0 lg:opacity-0 lg:group-hover/carousel:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
												{% endcapture %}
												
												<button class="top-1/2 transform -translate-y-1/2 absolute left-6 {{ arrow_visibility_classes }} swiper-button-prev btn-control {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %}" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:2 %}
												</button> 
												<button class="top-1/2 transform -translate-y-1/2 absolute right-6 {{ arrow_visibility_classes }} swiper-button-next btn-control {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %}" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:2 %}
												</button>
											{% endif %}
										</div>
									</div>
								</div>
							</div>
							
						<!-- CAROUSEL bloomreach-recs -->
						{% when 'bloomreach-recs' %}
							{% liquid
								assign source = 'https://norwi2iw70.execute-api.us-west-2.amazonaws.com/exponea/recommendations'
								assign map = '{products:{from:"recommendations.results.0.value",each:{title:"title",handle:"handle",price:"price|*100",featured_image:"image"}}}'
								capture fetch_config
									if product != blank
										render 'bloomreach-recs-data' settings:block.settings section:section product:product
									else
										render 'bloomreach-recs-data' settings:block.settings section:section
									endif
								endcapture
								render 'carousel-product-data' source:source, map:map, settings:block.settings, id:section.id, fetch_config:fetch_config, block_id:block.id, split_tabs: section.settings.split_into_tabs
								assign remote = true
							%}

							<div 
							    class="{{ section.id }} w-full" {% render 'carousel-config' settings:section.settings namespace:section.id %}
								data-config-key="{{ section.id }}_{{ block.id }}"
							>
								<div data-block-id="{{ block.id }}" 
									x-data="{
										blockId: '{{ block.id }}',
										products: [],
										productsAvailable: false,
										init() {
											// listener for product updates
											window.addEventListener('productsUpdated', (event) => {
												if (event.detail.blockId === this.blockId) {
													this.updateProducts();
												}
											});
							
											this.updateProducts();
										},
										updateProducts() {
											if (window.tabsProducts && window.tabsProducts[this.blockId]) {
												this.products = window.tabsProducts[this.blockId];
												this.productsAvailable = true;
											}
										}
									}" 
									x-init="init()"
								>
									<!-- Swiper Carousel for bloomreach-recs -->
									<div class="relative w-full h-full group/carousel" aria-live="polite">
										<!-- Swiper Container -->
										<div 
											x-init="init()"
											x-ref="swiperContainer"
											class="swiper swiper-container h-full {% if carousel.settings.outer_slides %}swiper--overflow-visible{% endif %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}"
											carousel-key="swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}"
										>
                      {% if remote and skeleton_items != true %}
                        <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper-skeleton" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
                          {%- for i in (1.. section.settings.slides_per_view) -%}
                            <div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                              {%- render 'product-item' product:false, skeleton:true, settings:section.settings, _settings:settings -%}
                            </div>
                          {%- endfor -%}
                        </div>
                        {% assign skeleton_items = true %}
                      {% endif %} 
											
											<!-- Swiper Wrapper -->
											<div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
												<!-- Swiper Slides -->

													{% liquid

														for block in section.blocks
							
															assign prev = forloop.index0 | minus: 1
							
															if block.type contains 'item' and section.blocks[prev].type != 'overlay'
													
																echo '<div class="relative swiper-slide '
																echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
																if block.settings.product_position != blank
																	echo 'data-slide-position="' | append: block.settings.product_position | append:'"'
																endif
																echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
																echo '>'
							
																case block.type
							
																	when 'content-item'
																			render 'content-item' settings:block.settings blocks:section.blocks offset:forloop.index
							
																	when 'product-item'
																		for product in block.settings.product
																			render 'product-item' product:product settings:section.settings, _settings:settings
																			unless forloop.last
																				echo '</div><div class="relative swiper-slide '
																				echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view
																				echo '">'
																			endunless
																		endfor
							
																endcase
							
																echo '</div>'
							
															endif
														endfor
							
													%}

													<template x-for="(product, index) in products" hidden>
																				
														<template x-if="product" hidden>
														
														<div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
															{%- render 'product-item' product:false, settings:section.settings, _settings:settings -%}
															
															<template x-import.afterparent='`.{{section.id}} [data-slide-position="${index+1}"]`'></template>
											
														</div>
											
														</template>
											
													</template>
											
													{% for block in section.blocks %}
														{% if block.settings.shop_all_text != blank  and  block.settings.slider_shop_all %}
															<template x-if="!!products">
																<div class="swiper-slide swiper-slide--shop-all">
																	<div class="content-carousel__show-all w-full h-full flex items-center justify-center aspect-[1/1]">
																		<a href="{{ block.settings.collection.url }}" class="button button--primary">
																			{{ block.settings.shop_all_text }}
																		</a>
																	</div>
																</div>
															</template>
															{% break %}
														{% endif %}
													{% endfor %}

												<!-- Insert Carousel Items Here -->
											</div>
											<!-- Add Swiper Navigation Arrows and Pagination Here -->
											{% if section.settings.show_pagination %}
												<style>
													#shopify-section-{{ section.id }} section {
														padding: 0 0 3.583rem;
													}
													@media screen and (min-width: 768px) {
														#shopify-section-{{ section.id }} section {
															padding: 3.583rem 0; /* match desktop */
														}
													}
												</style>
												<div class="swiper-pagination"></div>
												<script type="swiper/config">
													{
														pagination: {
															el: '.swiper-pagination',
															type: 'bullets',
															clickable: true,
															dynamicBullets: true,
															dynamicMainBullets: 4
														}
													}	
												</script>
											{% endif %}
									
									
											{% if carousel.settings.arrows or carousel.settings.arrows_mobile or carousel.settings.show_arrows_on_hover %}
												{% capture arrow_visibility_classes %}
													{% if carousel.settings.arrows_mobile and carousel.settings.arrows %}
														opacity-100
													{% elsif carousel.settings.arrows_mobile %}
														opacity-100 lg:opacity-0
													{% elsif carousel.settings.arrows %}
														opacity-0 lg:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
													{% if carousel.settings.show_arrows_on_hover %}
														opacity-0 lg:opacity-0 lg:group-hover/carousel:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
												{% endcapture %}
												
												<button class="top-1/2 transform -translate-y-1/2 absolute left-6 {{ arrow_visibility_classes }} swiper-button-prev btn-control {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %}" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:2 %}
												</button> 
												<button class="top-1/2 transform -translate-y-1/2 absolute right-6 {{ arrow_visibility_classes }} swiper-button-next btn-control {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %}" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:2 %}
												</button>
											{% endif %}
										</div>
									</div>
								</div>					
							</div>

					{% endcase %}
				</div> <!-- END of Tabs -->
				{% assign index = index | plus: 1 %}
            {% endif %}
        {% endfor %}

		</div>
    {% else %}
        {% liquid
			assign remote = false
            for block in section.blocks
			
                case block.type

                    when 'data-remote'
                        render 'carousel-product-data' settings:block.settings id:section.id
                        assign remote = true

                    when 'shopify-recommendations'
                        assign source = '/recommendations/products.json?product_id=' | append: block.settings.product_id | append: '&limit=' | append: block.settings.limit
                        assign map = '{products:{from:"products",each:{title:"title",handle:"handle",tags:"tags",type:"type",price:"price",compare_at_price:"compare_at_price",featured_image:"featured_image",hover_image:"featured_image",id:"id",variants:{from:"variants",each:{id:"id",title:"title",price:"price|*100",available:"available",sku:"sku"}}}}}'
                        render 'carousel-product-data' source:source, map:map id:section.id isPrefixSkuEnable:block.settings.is_prefix_sku_enable
                        assign remote = true

                    when 'boost-recommendations'
                        #assign source = 'https://services.mybcapps.com/discovery/recommend?shop=the-roark-revival.myshopify.com'
                        assign map = '{products:{from:"products",each:{title:"title",handle:"handle",tags:"tags",type:"type",price:"price",compare_at_price:"compare_at_price",featured_image:"featured_image",hover_image:"featured_image",id:"id",variants:{from:"variants",each:{id:"id",title:"title",price:"price|*100",available:"available",sku:"sku"}}}}}'
                        render 'carousel-product-data' , settings:block.settings id:section.id isPrefixSkuEnable:block.settings.is_prefix_sku_enable
                        assign remote = true
                
                    when 'shopify-collection'
                        if block.settings.collection 
                            assign source = '/collections/' | append: block.settings.collection | append: '/products.json?limit=' | append: block.settings.limit
                            assign map = '{products:{from:"products",each:{title:"title",handle:"handle",tags:"tags",type:"product_type",price:"variants.0.price|*100",compare_at_price:"variants.0.compare_at_price|*100",images:"images",id:"id",variants:{from:"variants",each:{id:"id",title:"title",price:"price|*100",available:"available",sku:"sku"}}}}}'
                            render 'carousel-product-data' source:source, map:map id:section.id settings:false collection_handle:block.settings.collection
                            assign remote = true
                        endif


                    when 'bloomreach-recs'
                            assign source = 'https://norwi2iw70.execute-api.us-west-2.amazonaws.com/exponea/recommendations'
                            assign map = '{products:{from:"recommendations.results.0.value",each:{title:"title",handle:"handle",price:"price|*100",featured_image:"image"}}}'
                                capture fetch_config
                                    if product != blank
                                        render 'bloomreach-recs-data' settings:block.settings section:section product:product
                                    else
                                        render 'bloomreach-recs-data' settings:block.settings section:section
                                    endif
                                endcapture
                            render 'carousel-product-data' source:source, map:map, settings:block.settings, id:section.id, fetch_config:fetch_config
                            assign remote = true

                    when 'recently-viewed'
                        assign source = 'recentlyViewed'
            
                endcase

            endfor
        %} 

        <div 
            class="{{ section.id }} w-full" {% render 'carousel-config' settings:section.settings namespace:section.id %}
			data-config-key="{{ section.id }}_{{ block.id }}"
		>

            {% liquid 

                capture text_item_1
                    render 'class-settings' prefix:'text_item_1', settings:carousel.settings
                endcapture
                capture button_1
                    render 'class-settings' prefix:'button_1', settings:carousel.settings
                endcapture

                if section.settings.title_product != blank 
                    render 'carousel-product-title' settings:section.settings, price:false
                endif

                if carousel.settings.text_item_1_text != "" or carousel.settings.button_1_text != "" and section.settings.title_product == blank 

                    render 'content-item', item_classes:'content-item--carousel-header flex-col', content_classes: 'w-full justify-between', text_stack_classes: 'self-end gap-2 flex-col', button_stack_classes: 'self-end gap-2 flex-col', text_item_1_link:section.settings.title_link, text_item_1_text: carousel.settings.text_item_1_text, text_item_1_class: text_item_1, text_item_1_element: carousel.settings.text_item_1_element, text_item_2_text: product_total, text_item_2_class: 'text-sm', button_1_text: carousel.settings.button_1_text, button_1_classes: button_1, button_1_link: carousel.settings.button_1_link, settings: carousel.settings , desk_hide_button1: true
                endif

            %} 

           <div class="relative w-full h-full group/carousel" aria-live="polite">
               <div class="swiper swiper-container h-full {% if carousel.settings.outer_slides %}swiper--overflow-visible{% endif %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}">

                  {% if remote %}
                    <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper-skeleton" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
                      {%- for i in (1.. section.settings.slides_per_view) -%}
                        <div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                          {%- render 'product-item' product:false, skeleton:true, settings:section.settings, _settings:settings -%}
                        </div>
                      {%- endfor -%}
                    </div>
                  {% endif %} 

                   <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">

                       {% liquid

                           for block in section.blocks

                               assign prev = forloop.index0 | minus: 1

                               if block.type contains 'item' and section.blocks[prev].type != 'overlay'
                       
                                   echo '<div class="relative swiper-slide '
                                   echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
                                   if block.settings.product_position != blank
                                       echo 'data-slide-position="' | append: block.settings.product_position | append:'"'
                                   endif
								   echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
                                   echo '>'

                                   case block.type

                                       when 'content-item'
                                               render 'content-item' settings:block.settings blocks:section.blocks offset:forloop.index

                                       when 'review-item' 
                                               render 'review-item' settings:block.settings same_height_review:section.settings.same_height_review product_item_show_button:section.settings.product_item_show_button product_type_mob_class:'items-start flex-col md:items-center md:flex-row custom-review-alignment'



                                       when 'article-item' 
                                               assign blog_handle = block.settings.article | split: '/' | first
                                               assign blog_object = blogs[blog_handle]
                                               render 'article-item' settings:block.settings, blog:blog_object, article:block.settings.article

                                       when 'product-item'
                                           for product in block.settings.product
                                               render 'product-item' product:product settings:section.settings, _settings:settings
                                               unless forloop.last
                                                   echo '</div><div class="relative swiper-slide '
                                                   echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view
                                                   echo '">'
                                               endunless
                                           endfor

                                   endcase

                                   echo '</div>'

                               endif

                               if block.type == 'blog-articles'
                               assign _blog = blogs[block.settings.blog] | default: blog
                               assign _count = 0

                                   for _article in _blog.articles

                                       if _article.id == article.id 
                                           continue
                                       endif

                                       assign _filters = block.settings.filters | split: ','
                                       assign _filtered_out = false
                                       for _filter in _filters
                                           if article.tags contains _filter or article == blank
                                               unless _article.tags contains _filter
                                                   assign _filtered_out = true
                                               endunless
                                           endif
                                       endfor

                                       if _filtered_out
                                           continue
                                       endif

                                       echo '<div class="relative swiper-slide '
                                       echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
									   echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
                                       echo '>'
                                       
                                       render 'article-item' settings:block.settings, blog:_blog, article:_article, layout:'vertical'

                                       echo '</div>'

                                       assign _count = _count | plus: 1
                                       if _count >= section.settings.limit
                                           break
                                       endif

                                   endfor
                               endif

                           endfor

                       %}

                       {% if remote %}
                           <template x-if="!section.products">
                               <template x-for="i in {{ section.settings.slides_per_view}}">
                                   <article class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                                     {%- render 'product-item' product:false, skeleton:true, settings:section.settings, _settings:settings -%}
                                   </article>
                               </template>
                           </template>
                       {% endif %} 

                       <template x-for="(product, index) in section.products" hidden>
                           <template x-if="product" hidden> 
                           
                           <div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                               {%- render 'product-item' product:false, settings:section.settings, _settings:settings -%}
                               
                               <template x-import.afterparent='`.{{section.id}} [data-slide-position="${index+1}"]`'></template>

                           </div>

                           </template>
                       </template>

                       {% for block in section.blocks %}					   
                           {% if block.settings.shop_all_text != blank  and  block.settings.slider_shop_all %}
                               <template x-if="!!section.products">
                               <div class="swiper-slide swiper-slide--shop-all">
                                   <div class="content-carousel__show-all  w-full h-full flex items-center justify-center aspect-[1/1]">
                                       <a href="{{ block.settings.collection.url }}" class="button  {{ block.settings.shop_all_button_1_class_style }} {{ block.settings.shop_all_button_1_class_size }}">
                                           {{ block.settings.shop_all_text }}
                                       </a>
                                   </div>
                               </div>
                               </template>
                           {% break %}
                           {% endif %}
                       {% endfor %}

                    </div>
               
					{% if carousel.settings.show_pagination == true %}
                        {% comment %} <div class="flex items-center p-4 lg:p-0">
                            
                                <div class="pagination"></div>
                            
                        </div> {% endcomment %}
                    {% endif %}

                    {% if section.settings.show_pagination %}
						<style>
							#shopify-section-{{ section.id }} section {
                              					padding-bottom: 0px;
							}
							@media screen and (min-width: 768px) {
								#shopify-section-{{ section.id }} section {
                                  					padding-bottom: 3.583rem;
                                  					padding-top: 0px;
								}
							}
                            				@media screen and (min-width: 768px) and (max-width: 1023px) {
								#shopify-section-{{ section.id }} section {
                                  					padding-bottom: 0px;
								}
							}
						</style>
						<div class="swiper-pagination"></div>
                        <script type="swiper/config">
							{
								pagination: {
									el: '.swiper-pagination',
									type: 'bullets',
									clickable: true,
									dynamicBullets: true,
									dynamicMainBullets: 4
								}
							}	
                        </script>
					{% endif %}

                   {% if carousel.settings.arrows or carousel.settings.arrows_mobile or carousel.settings.show_arrows_on_hover %}
                       {% capture arrow_visibility_classes %}
                           {% if carousel.settings.arrows_mobile and carousel.settings.arrows %}
                               opacity-100
                           {% elsif carousel.settings.arrows_mobile %}
                               opacity-100 lg:opacity-0
                           {% elsif carousel.settings.arrows %}
                               opacity-0 lg:opacity-100 pointer-events-none lg:pointer-events-auto
                           {% endif %}
                           {% if carousel.settings.show_arrows_on_hover %}
                               opacity-0 lg:opacity-0 lg:group-hover/carousel:opacity-100 pointer-events-none lg:pointer-events-auto
                           {% endif %}
                       {% endcapture %}
                       
                       <div class="nav-btn-wrap-func-left">
                         <button class="top-1/2 transform -translate-y-1/2 absolute left-6 {{ arrow_visibility_classes }} {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %} swiper-button-prev btn-control" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
                             {% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:2 %}
                         </button>
                         {% if section.settings.increase_arrow_tap_target %}
	                         <a class="top-0 absolute left-0 h-full  {{ arrow_visibility_classes }} {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %} swiper-button-prev" style="width:90px;" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
	                         </a>
                         {% endif %}
                       </div>
													
                       <div class="nav-btn-wrap-func-right">
                         <button class="top-1/2 transform -translate-y-1/2 absolute right-6 {{ arrow_visibility_classes }} {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %} swiper-button-next btn-control" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
                             {% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:2 %}
                         </button>
                         {% if section.settings.increase_arrow_tap_target %}
	                         <a class="top-0 absolute right-0 {{ arrow_visibility_classes }} {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %} swiper-button-next" style="width:90px;" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
	                         </a>
                         {% endif %}
                       </div>
                   {% endif %}

               </div>

           </div>

		   {% if section.settings.button_1_text != blank %}
			<button {% if section.settings.button_1_onclick != blank %}onclick="{{ section.settings.button_1_onclick }};" {% else %}onclick="window.location='{{ button_1_link | default:section.settings.button_1_link }}'" {% endif %} class="button-mobile button collection-title-shop-all {{ section.settings.button_1_class_style }} {{ section.settings.button_1_class_size }} ">
			  <span>
				{{ button_1_text | default: section.settings.button_1_text }}
			  </span>
			</button>
		  {% endif %}
        </div>

    {% endif %}

	</main>

</section>

{%- endif -%}

{% style %}
	#shopify-section-{{ section.id }} .swiper-slide {
		width:calc(100% / {{ section.settings.slides_per_view_mobile }})
	}
	@media(min-width:1024px){
		#shopify-section-{{ section.id }} .swiper-slide {
			width:calc(100% / {{ section.settings.slides_per_view }})
		}
	}
  .nav-btn-wrap-func-right a {
    right: 0 !important;
    height: 100% !important;
    top: 0;
    margin: 0;
    cursor: pointer;
  }
  .nav-btn-wrap-func-left a {
    left: 0 !important;
    height: 100% !important;
    top: 0;
    margin: 0;
    cursor: pointer;
  }
  .section-slideshow-multiple .productitem--extra {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  .section-slideshow-multiple .productitem--wrap {
    height: 100%;
  }
  .section-slideshow-collection .swiper-button-disabled {
    opacity: .5;
  }
	{% if section.settings.split_into_tabs %}
		#shopify-section-{{ section.id }} button.tab {
			color: {{ section.settings.tabs_button_text }};
		}
		#shopify-section-{{ section.id }} button.tab.active {
			background-color: {{ section.settings.tabs_active_button_background }};
			color: {{ section.settings.tabs_active_button_text }};
		}
		.tabs-wrapper .tab-content {
			opacity: 0;
			visibility: hidden;
			height: 0;
		}
		.tabs-wrapper .tab-content.active {
			opacity: 1;
			visibility: visible;
			height: 100%;
		}
		#shopify-section-{{section.id}} section {
			overflow:hidden;
		}
	{% endif %}
	{% if carousel.settings.arrows_mobile %}
	@media only screen and (max-width:1023px) {
		button.show-arrow-mob {
			opacity: 1;
			pointer-events: auto;
			}
		}
		.swiper-wrapper:has(.swiper-slide-active:first-of-type) + button.swiper-button-prev {
			opacity: 0;
		} 
		.swiper-wrapper:has(.swiper-slide-active:first-of-type) + .nav-btn-wrap-func-left button.swiper-button-prev {
			opacity: 0;
		} 
	{% endif %}
{% endstyle %}


<script>
	window.carouselConfigs = window.carouselConfigs || {};

{% comment %}
  BELOW SCRIPT IS FOR RECENTLY VIEWED PRODUCTS
{% endcomment %}

  (function(){
    
    let rv = {products:[]}
    try{
      rv = JSON.parse(localStorage.getItem('recentlyViewed'));
    } catch(err) {}
    
    if(!rv)
      rv = {products:[]}

    window.recentlyViewed = rv;

    {% if template contains 'product' %}

      var handle = {{product.handle|json}};
      rv.products = JSON.parse(JSON.stringify(
          rv.products.filter(function(p, i) {return p.handle != handle})
        )
      ).slice(0,{{ section.settings.limit }});
      window.recentlyViewed = JSON.parse(JSON.stringify(rv));

      rv.products.unshift({{product|json}})
      localStorage.setItem('recentlyViewed', JSON.stringify(rv))

    {% endif %}

  })()

	window.sections = window.sections || []
	window.addEventListener('DOMContentLoaded', ()=>{
  		window.sections.find(s=>s.id=='{{ section.id }}').data = {{ section.settings | json }}
  	})
</script>

{% schema %}
{
	"name": "Carousel",
	"class": "section--content-carousel carousel-outer",
	"settings": [
		{
			"type": "liquid",
			"label": "Liquid Logic Inclusion",
			"id": "inclusion_liquid_carousel",
			"info": "Insert any liquid logic that returns a value to display the section",
			"default": "true"
		},
		{
			"type": "liquid",
			"label": "Javascript Logic Inclusion",
			"id": "inclusion_js",
			"info": "Insert any javascript logic that evaluates to true to display the section",
			"default": "true"
		},
		{
			"type": "paragraph",
			"content": "@include TabSettings, label_prefix:Tabs, id_prefix:tabs_class"
		},
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "paragraph",
			"content": "@include Text, id_prefix:text_item_1, label_prefix:Text 1"
		},
		{
			"type": "url",
			"id": "title_link",
			"label": "Title Link"
		},
		{
			"type": "product",
			"id": "title_product",
			"label": "Title Product"
		},
		{
			"type": "paragraph",
			"content": "@include Button, id_prefix:button_1, label_prefix:Button 1"
		},
		{
			"type": "paragraph",
			"content": "@include Carousel"
		},
		{
			"type": "paragraph",
			"content": "@include ProductItem"
		},
		{
			"type": "checkbox",
			"id": "same_height_review",
			"label": "Same Height Review",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "single_variant_direct_addtocart",
			"label": "Direct add-to-cart for single variant",
			"default": false
		},
		{
			"type": "select",
			"id": "product_item_show_price_right",
			"label": "Show Price on Right or Bottom",
			"default": "bottom",
			"options": [
				{
					"label": "Right Side of Title",
					"value": "right"
				},
				{
					"label": "Below of Title",
					"value": "bottom"
				}
			]
		}
	],
	"blocks": [
		{
			"name": "Shopify Collection",
			"type": "shopify-collection",
			"limit": 4,
			"settings": [
				{
					"type": "header",
					"content": "Collection settings"
				},
				{
					"label": "Collection",
					"id": "collection",
					"type": "collection"
				},
				{
					"label": "Limit",
					"id": "limit",
					"type": "number",
					"default": 8
				},
				{
					"label": "Shop All Button Text",
					"id": "shop_all_text",
					"type": "text",
					"default": "Shop All",
					"info": "If blank, button and enclosing slide will not display"
				},
				{
					"label": "Alternate Carousel Heading",
					"id": "alt_carousel_heading",
					"type": "text",
					"info": "Needed when carousel is converted to tabs"
				},
				{
					"label": "If Checked, Then Below Review Settings Will Work",
					"id": "enable_review_stars",
					"type": "checkbox",
					"default": false
				},
				{
					"label": "review stars below carousel title mobile",
					"id": "show_reviews_for_mobile",
					"type": "checkbox",
					"default": true
				},
				{
					"label": "review stars below carousel title desktop",
					"id": "show_reviews_for_desktop",
					"type": "checkbox",
					"default": true
				},
				{
					"label": "review stars below products mobile",
					"id": "show_reviews_for_product_mobile",
					"type": "checkbox",
					"default": true
				},
				{
					"label": "review stars below products desktop",
					"id": "show_reviews_for_product_desktop",
					"type": "checkbox",
					"default": true
				},
				{
					"type": "checkbox",
					"id": "slider_shop_all",
					"label": "Enable Shop All button",
					"default": false
				},
				{
					"type": "select",
					"id": "shop_all_button_1_class_style",
					"label": "Shop All Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "select",
					"id": "shop_all_button_1_class_size",
					"label": "Shop All button",
					"options": [
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				}
			]
		},
		{
			"name": "Shopify Recommendations",
			"type": "shopify-recommendations",
			"limit": 1,
			"settings": [
				{
					"type": "header",
					"content": "Collection settings"
				},
				{
					"label": "Product ID",
					"id": "product_id",
					"type": "liquid",
					"default": "{{ product.id }}"
				},
				{
					"label": "Limit",
					"id": "limit",
					"type": "number",
					"default": 8
				},
				{
					"label": "Alternate Carousel Heading",
					"id": "alt_carousel_heading",
					"type": "text",
					"info": "Needed when carousel is converted to tabs"
				},
				{
					"type": "checkbox",
					"id": "is_prefix_sku_enable",
					"label": "Hide same SKU prefix",
					"default": false
				}
			]
		},
		{
			"name": "Boost Recommendations",
			"type": "boost-recommendations",
			"limit": 1,
			"settings": [
				{
					"type": "header",
					"content": "HTTP Request"
				},
				{
					"id": "remote_url",
					"label": "Remote URL",
					"type": "liquid",
					"info": "Liquid with JavaScript Template Literals"
				},
				{
					"type": "select",
					"id": "config_method",
					"label": "Request Method",
					"default": "GET",
					"options": [
						{
							"value": "GET",
							"label": "GET"
						},
						{
							"value": "POST",
							"label": "POST"
						},
						{
							"value": "PUT",
							"label": "PUT"
						}
					]
				},
				{
					"type": "select",
					"id": "config_mode",
					"label": "CORS Request Mode",
					"default": "cors",
					"options": [
						{
							"value": "cors",
							"label": "cors"
						},
						{
							"value": "no-cors",
							"label": "no-cors"
						}
					]
				},
				{
					"id": "config_headers",
					"label": "Request Headers",
					"type": "liquid"
				},
				{
					"type": "header",
					"content": "Configuration Body Settings"
				},
				{
					"type": "select",
					"id": "config_bodyPart_recommendationType",
					"label": "Request Body Recommendation Type",
					"default": "bestsellers",
					"options": [
						{
							"value": "bestsellers",
							"label": "Bestsellers"
						},
						{
							"value": "newest-arrivals",
							"label": "Newest Arrivals"
						},
						{
							"value": "frequently-bought-together",
							"label": "Frequently Bought Together"
						},
						{
							"value": "trending-products",
							"label": "Trending Products"
						},
						{
							"value": "recently-viewed",
							"label": "Recently Viewed"
						},
						{
							"value": "most-viewed",
							"label": "Most Viewed"
						},
						{
							"value": "related-items",
							"label": "Related Items"
						}
					]
				},
				{
					"type": "select",
					"id": "config_bodyPart_modelType",
					"label": "Request Body model Type",
					"default": "none",
					"options": [
						{
							"value": "none",
							"label": "None"
						},
						{
							"value": "FBT",
							"label": "FBT"
						},
						{
							"value": "Complementary",
							"label": "Complementary"
						},
						{
							"value": "Alternative",
							"label": "Alternative"
						},
						{
							"value": "AIRelated",
							"label": "AIRelated"
						}
					]
				},
				{
					"type": "select",
					"id": "config_bodyPart_calculatedBasedOn",
					"label": "Result Calculated BasedOn",
					"default": "none",
					"options": [
						{
							"value": "none",
							"label": "None"
						},
						{
							"value": "view-events",
							"label": "View Events"
						},
						{
							"value": "purchase-events",
							"label": "Purchase Events"
						}
					]
				},
				{
					"type": "select",
					"id": "config_bodyPart_rangeOfTime",
					"label": "Result Calculated rangeOfTime",
					"default": "none",
					"options": [
						{
							"value": "none",
							"label": "None"
						},
						{
							"value": "1-day",
							"label": "1-day"
						},
						{
							"value": "2-day",
							"label": "2-day"
						},
						{
							"value": "3-day",
							"label": "3-day"
						},
						{
							"value": "4-day",
							"label": "4-day"
						},
						{
							"value": "5-day",
							"label": "5-day"
						},
						{
							"value": "6-day",
							"label": "6-day"
						},
						{
							"value": "7-day",
							"label": "7-day"
						}
					]
				},
				{
					"type": "number",
					"id": "config_bodyPart_limit",
					"label": "Request Body Result limit",
					"default": 16
				},
				{
					"type": "liquid",
					"id": "config_bodyPart_productIds",
					"label": "Related Products Id's",
					"default": "none"
				},
				{
					"id": "map",
					"label": "Response Data Map",
					"type": "liquid",
					"info": "Liquid with JavaScript Template Literals in Util.map()"
				},
				{
					"type": "header",
					"content": "RuleBased Settings"
				},
				{
					"type": "checkbox",
					"id": "config_bodyPart_ruleBased",
					"label": "Enable Rule Based Queries",
					"default": false
				},
				{
					"type": "select",
					"id": "config_bodyPart_type",
					"label": "Type of result",
					"default": "sameCollection",
					"options": [
						{
							"value": "sameCollection",
							"label": "Same Collection"
						},
						{
							"value": "sameProductType",
							"label": "Same Product Type"
						},
						{
							"value": "sameVendor",
							"label": "Same Vendor"
						},
						{
							"value": "sameTags",
							"label": "Same Tags"
						},
						{
							"value": "sameMetafield",
							"label": "Same Metafield"
						},
						{
							"value": "sameProductCategory",
							"label": "Same Product Category"
						}
					]
				},
				{
					"type": "select",
					"id": "config_bodyPart_status",
					"label": "Product Status",
					"default": "true",
					"options": [
						{
							"value": "true",
							"label": "True"
						},
						{
							"value": "false",
							"label": "False"
						}
					]
				}
			]
		},
		{
			"name": "Bloomreach Recs",
			"type": "bloomreach-recs",
			"limit": 1,
			"settings": [
				{
					"label": "Recomendation ID",
					"id": "recomendation_id",
					"type": "text"
				},
				{
					"label": "Limit",
					"id": "limit",
					"type": "number",
					"default": 8
				},
				{
					"label": "Alternate Carousel Heading",
					"id": "alt_carousel_heading",
					"type": "text",
					"info": "Needed when carousel is converted to tabs"
				}
			]
		},
		{
			"type": "data-remote",
			"name": "Open Remote Product Data",
			"limit": 1,
			"settings": [
				{
					"id": "remote_url",
					"label": "Remote URL",
					"type": "liquid",
					"info": "Liquid with JavaScript Template Literals"
				},
				{
					"type": "select",
					"id": "config_method",
					"label": "Request Method",
					"default": "GET",
					"options": [
						{
							"value": "GET",
							"label": "GET"
						},
						{
							"value": "POST",
							"label": "POST"
						},
						{
							"value": "PUT",
							"label": "PUT"
						}
					]
				},
				{
					"type": "select",
					"id": "config_mode",
					"label": "CORS Request Mode",
					"default": "cors",
					"options": [
						{
							"value": "cors",
							"label": "cors"
						},
						{
							"value": "no-cors",
							"label": "no-cors"
						}
					]
				},
				{
					"id": "config_headers",
					"label": "Request Headers",
					"type": "liquid"
				},
				{
					"id": "config_body",
					"label": "Request Body",
					"type": "liquid",
					"info": "Liquid with JavaScript Template Literals"
				},
				{
					"id": "map",
					"label": "Response Data Map",
					"type": "liquid",
					"info": "Liquid with JavaScript Template Literals in Util.map()"
				}
			]
		},
		{
			"name": "Content Item",
			"type": "content-item",
			"settings": [
				{
					"type": "text",
					"id": "product_position",
					"label": "Position among Products"
				},
				{
					"type": "paragraph",
					"content": "@include ContentItem"
				}
			]
		},
		{
			"name": "Overlay",
			"type": "overlay",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include OverlayItem"
				}
			]
		},
		{
			"name": "Hotspot",
			"type": "hotspot",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include Hotspot"
				}
			]
		},
		{
			"type": "review-item",
			"name": "Review Item",
			"settings": [
				{
					"type": "product",
					"id": "product",
					"label": "Product"
				},
				{
					"type": "image_picker",
					"id": "image",
					"label": "Image"
				},
				{
					"type": "textarea",
					"id": "review",
					"label": "Review"
				},
				{
					"type": "text",
					"id": "author",
					"label": "Author"
				}
			]
		},
		{
			"type": "article-item",
			"name": "Article Item",
			"settings": [
				{
					"type": "article",
					"id": "article",
					"label": "Article"
				},
				{
					"type": "header",
					"content": "Article Card Settings"
				},
				{
					"type": "select",
					"id": "item_class_card_layout",
					"label": "Card Layout",
					"default": "content-item__article--vertical",
					"options": [
						{
							"label": "Vertical",
							"value": "content-item__article--vertical"
						},
						{
							"label": "Horizontal",
							"value": ""
						}
					]
				}
			]
		},
		{
			"type": "blog-articles",
			"name": "Blog Articles",
			"settings": [
				{
					"type": "blog",
					"id": "blog",
					"label": "Blog"
				},
				{
					"type": "text",
					"id": "filters",
					"label": "Article Tag Filters",
					"info": "Filters articles by tags listed here (comma-separated). If carousel is loaded on article/blog post template, the tag filters each only apply to the carousel if the article itself also has the tag."
				}
			]
		},
		{
			"type": "product-item",
			"name": "Specific Products",
			"settings": [
				{
					"type": "product_list",
					"id": "product",
					"label": "Product"
				}
			]
		}
	],
	"presets": [
		{
			"name": "Content Carousel",
			"category": "Content",
			"settings": {},
			"blocks": []
		}
	]
}
{% endschema %}
