{% liquid
    assign section_type = 'content-carousel'
    assign carousel = section
	assign remote = false
 %}

 {%- if section.settings.inclusion_liquid_carousel != '' -%}

 <section 
 	id="carousel-{{ section.id }}"
	class="section section--{{ section_type }} relative {% render 'class-settings', prefix:'wrapper_class', settings:section.settings %}"
	style="{% render 'style-settings', prefix:'wrapper_style', settings:section.settings %}"
	x-data='{ inclusion_js: {% if section.settings.inclusion_js == "true" %}true{% elsif section.settings.inclusion_js == "false" %}false{% else %}`{{section.settings.inclusion_js}}`{% endif %} }' x-show="inclusion_js">

	{% if section.settings.wrapper_bg_image %}
		{% render 'image'
			image: section.settings.wrapper_bg_image
			alt: section.settings.wrapper_bg_image.alt
			class: 'section__media absolute inset-0 w-full h-full object-cover max-lg:hidden'
		%}
	{% endif %}
	{% if section.settings.wrapper_bg_image_mob %}
		{% render 'image' 
			image: section.settings.wrapper_bg_image_mob
			alt: section.settings.wrapper_bg_image_mob.alt
			class: 'section__media absolute inset-0 w-full h-full object-cover lg:hidden'
		%}
	{% endif %}

 	<main 
		class="section__container relative {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
		style="{% render 'style-settings' prefix:'container_styles' settings:section.settings %}"
	>

    {% if section.settings.split_into_tabs %}
		<div 
			x-data="{
				activeTab: 0,
        swipers: {},
				setActiveTab(index, carouselKey) {
					let self = this;
					this.activeTab = index
					this.$nextTick(() => {
						const swiperContainer = document.querySelector(`.tab-content[data-tab-index='${index}'] .swiper-container`);
						if (!swiperContainer) {
							return
						}

						// retrieve data-carousel-key attribute value from swiper container.
						const configKey = swiperContainer.getAttribute('carousel-key')

						// retrieve configuration for current swiper instance using configKey.
						const config = window.carouselConfigs[configKey];
						
						if (config) {
							// create a new instance of swiper with the saved swiper settings
							self.swipers[configKey] = new Swiper(swiperContainer, config);
						}
					});
				},
        updateActiveTab() {
          const redirectTitle = window.redirects && window.redirects[0] ? window.redirects[0].title.handle() : '';
          {% for block in section.blocks %}
            {% if block.type == 'shopify-collection' or block.type == 'shopify-recommendations' or block.type == 'bloomreach-recs' %}
              if ('{{ block.settings.alt_carousel_heading | handle }}' === redirectTitle) {
                this.activeTab = {{ forloop.index0 }};
                this.setActiveTab({{ forloop.index0 }}, 'swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}');
              }
            {% endif %}
          {% endfor %}
        }
    }"
    x-init="setTimeout(()=>{updateActiveTab()}, 500)"
    class="tabs-wrapper"
		>
			<div class="flex flex-col-reverse items-center lg:flex-row lg:justify-between">
				<div article class="lg:flex lg:flex-grow lg:justify-center">
					<div class="lg:shrink-0 lg:absolute">
						{% liquid 
										
							capture text_item_1
								render 'class-settings' prefix:'text_item_1', settings:carousel.settings
							endcapture
							capture button_1
								render 'class-settings' prefix:'button_1', settings:carousel.settings
							endcapture
	
							if section.settings.title_product != blank 
								render 'carousel-product-title' settings:section.settings, price:false, blocks:section.blocks
							endif
	
							if carousel.settings.text_item_1_text != "" or carousel.settings.button_1_text != "" and section.settings.title_product == blank
	
								render 'content-item', item_classes:'content-item--carousel-header flex-col', content_classes: 'w-full justify-between', text_stack_classes: 'self-end gap-2 flex-col', button_stack_classes: 'self-end gap-2 flex-col', text_item_1_link:section.settings.title_link, text_item_1_text: carousel.settings.text_item_1_text, text_item_1_class: text_item_1, text_item_1_element: carousel.settings.text_item_1_element, text_item_2_text: product_total, text_item_2_class: 'text-sm', button_1_text: carousel.settings.button_1_text, button_1_classes: button_1, button_1_link: carousel.settings.button_1_link, settings: carousel.settings
							endif
	
						%} 
					</div>
				</div>
			</div>
			<div class="flex flex-col-reverse items-center lg:flex-row lg:justify-between">
				<!-- Tab Links -->
				<div class="{{ section_type }}__tabs-mb-0 mb-10 tab-link lg:shrink-0 tabs">
					{% assign index = 0 %}
					{% for block in section.blocks %}
						{% if block.type == 'shopify-collection' or block.type == 'shopify-recommendations' or block.type == 'bloomreach-recs' %}
						{% capture carousel_key %}swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}{% endcapture %}
							<button 
								class="tab"
								:class="{ 'active': activeTab === {{ index }} }"
                {% if section.settings.tab_interaction contains "hover" %}
                  @mouseover="setActiveTab({{ index }}, '{{ carousel_key }}')"
                {% else %} 
                  @click="setActiveTab({{ index }}, '{{ carousel_key }}')"
                {% endif %}
								@focus="setActiveTab({{ index }}, '{{ carousel_key }}')"
							>
								{{ block.settings.alt_carousel_heading }}
							</button>
							{% assign index = index | plus: 1 %}
						{% endif %}
					{% endfor %}
				</div>
			</div>

        {% comment %} # separate carousels for specified block types {% endcomment %}
		{% assign index = 0 %}
        {% for block in section.blocks %}
            {% if block.type == 'shopify-collection' or block.type == 'shopify-recommendations' or block.type == 'bloomreach-recs' %}
				{% assign blockArray = block.settings %}
				<div id="tab-content-{{ index }}" data-tab-index="{{ index }}" class="tab-content carousel-container" :class="{ 'active': activeTab === {{ index }} }" style=""> 

				{% assign carousel_key = 'swiperCarousel' | append: section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' | append: '_' | append: block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' %}

					{% case block.type %}
					{% comment %} Save Carousel Settings {% endcomment %}

					{% comment %} END of Save Carousel Settings {% endcomment %}

						<!-- CAROUSEL shopify-collection -->
						{% when 'shopify-collection' %}
							{% if block.settings.collection %}
								{% liquid
									assign source = '/collections/' | append: block.settings.collection | append: '/products.json?limit=' | append: block.settings.limit
									assign map = '{products:{from:"products",each:{title:"title",handle:"handle",tags:"tags",type:"product_type",price:"variants.0.price|*100",compare_at_price:"variants.0.compare_at_price|*100",images:"images",id:"id",variants:{from:"variants",each:{id:"id",title:"title",price:"price|*100",available:"available",sku:"sku"}}}}}'
									render 'carousel-product-data' source:source, map:map id:section.id settings:false, block_id:block.id, split_tabs: section.settings.split_into_tabs
									assign remote = true
								%}
							{% endif %}
						
							<div 
								class="{{ section.id }} w-full" {% render 'carousel-config' settings:section.settings namespace:section.id block:block.id %}
								data-config-key="{{ section.id }}_{{ block.id }}"
							>

								<div data-block-id="{{ block.id }}" 
									x-data="{
										blockId: '{{ block.id }}',
										products: [],
										productsAvailable: false,
										init() {
											// listener for product updates
											window.addEventListener('productsUpdated', (event) => {
												if (event.detail.blockId === this.blockId) {
													this.updateProducts();
												}
											});
							
											this.updateProducts();
										},
										updateProducts() {
											if (window.tabsProducts && window.tabsProducts[this.blockId]) {
												this.products = window.tabsProducts[this.blockId];
												this.productsAvailable = true;
											}
										}
									}" 
									
								>
									

									<!-- Swiper Carousel for shopify-collection -->
									<div class="relative w-full h-full group/carousel" aria-live="polite">
										<!-- Swiper Container -->
										<div 
											x-init="init()"
											class="swiper swiper-container h-full {% if carousel.settings.outer_slides %}swiper--overflow-visible{% endif %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}"
											carousel-key="swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}"
										>
										
                      {% if remote and skeleton_items != true %}
                        <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper-skeleton" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
                          {%- for i in (1.. section.settings.slides_per_view) -%}
                            <div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                              {%- render 'product-item' product:false, skeleton:true, settings:section.settings, _settings:settings location:'carousel' -%}
                            </div>
                          {%- endfor -%}
                        </div>
                        {% assign skeleton_items = true %}
                      {% endif %} 
											
									
											<!-- Swiper Wrapper -->
                      <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
												<!-- Swiper Slides -->

													{% liquid

														for block in section.blocks
							
															assign prev = forloop.index0 | minus: 1
							
															if block.type contains 'item' and section.blocks[prev].type != 'overlay'
													
																echo '<div class="relative swiper-slide '
																echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
																if block.settings.product_position != blank
																	echo 'data-slide-position="' | append: block.settings.product_position | append:'"'
																endif
																echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
																echo '>'
							
																case block.type
							
																	when 'content-item'
																			render 'content-item' settings:block.settings blocks:section.blocks offset:forloop.index
							
																	when 'product-item'
																		for product in block.settings.product
																			render 'product-item' product:product settings:section.settings, _settings:settings
																			unless forloop.last
																				echo '</div><div class="relative swiper-slide '
																				echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view
																				echo '">'
																			endunless
																		endfor
							
																endcase
							
																echo '</div>'
							
															endif
														endfor
							
													%}

													<template x-for="(product, index) in products" hidden>
																				
														<template x-if="product" hidden>
														
														<div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
															{%- render 'product-item' product:false, settings:section.settings, _settings:settings -%}
															
															<template x-import.afterparent='`.{{section.id}} [data-slide-position="${index+1}"]`'></template>
											
														</div>
											
														</template>
											
													</template>

													{% if blockArray.shop_all_text != blank and  blockArray.slider_shop_all %}
														<template x-if="!!products">
															<div class="swiper-slide swiper-slide--shop-all">
																<div class="content-carousel__show-all w-full h-full flex items-center justify-center aspect-[1/1]">
																	<a href="{{ blockArray.collection.url }}" class="button button--primary">
																		{{ blockArray.shop_all_text }}
																	</a>
																</div>
															</div>
														</template>
													{% endif %}

												<!-- Insert Carousel Items Here -->
											</div>
											<!-- Add Swiper Navigation Arrows and Pagination Here -->
											{% if section.settings.show_pagination %}
												<style>
													#shopify-section-{{ section.id }} section {
														padding: 0 0 3.583rem;
													}
													@media screen and (min-width: 768px) {
														#shopify-section-{{ section.id }} section {
															padding: 3.583rem 0; /* match desktop */
														}
													}
												</style>
												<div class="swiper-pagination"></div>
												<script type="swiper/config">
													{
														pagination: {
															el: '.swiper-pagination',
															type: 'bullets',
															clickable: true,
															dynamicBullets: true,
															dynamicMainBullets: 4
														}
													}	
												</script>
											{% endif %}
									
									
											{% if carousel.settings.arrows or carousel.settings.arrows_mobile or carousel.settings.show_arrows_on_hover %}
												{% capture arrow_visibility_classes %}
													{% if carousel.settings.arrows_mobile and carousel.settings.arrows %}
														opacity-100
													{% elsif carousel.settings.arrows_mobile %}
														opacity-100 lg:opacity-0
													{% elsif carousel.settings.arrows %}
														opacity-0 lg:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
													{% if carousel.settings.show_arrows_on_hover %}
														opacity-0 lg:opacity-0 lg:group-hover/carousel:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
												{% endcapture %}
												
												<button class="top-1/2 transform -translate-y-1/2 absolute left-6 {{ arrow_visibility_classes }} swiper-button-prev btn-control {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %}" tabindex="0" aria-label="Next slide  " aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:2 %}
												</button>
												<button class="top-1/2 transform -translate-y-1/2 absolute right-6 {{ arrow_visibility_classes }} swiper-button-next btn-control {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %}" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:2 %}
												</button>
											{% endif %}

										</div>
									</div>

								</div><!-- END of data-block-id -->
							</div><!-- END of shopify-collection carousel-config render -->
							
		
						<!-- CAROUSEL shopify-collection -->
						{% when 'shopify-recommendations' %}
							{% liquid
								assign source = '/recommendations/products.json?product_id=' | append: block.settings.product_id | append: '&limit=' | append: block.settings.limit
								assign map = '{products:{from:"products",each:{title:"title",handle:"handle",tags:"tags",type:"type",price:"price",compare_at_price:"compare_at_price",featured_image:"featured_image",hover_image:"featured_image",id:"id",variants:{from:"variants",each:{id:"id",title:"title",price:"price|*100",available:"available",sku:"sku"}}}}}'
								render 'carousel-product-data' source:source, map:map id:section.id, block_id:block.id, split_tabs: section.settings.split_into_tabs
								assign remote = true
							%}

							<div 
							    class="{{ section.id }} w-full" {% render 'carousel-config' settings:section.settings namespace:section.id %}
								data-config-key="{{ section.id }}_{{ block.id }}"
							>
								<div data-block-id="{{ block.id }}" 
									x-data="{
										blockId: '{{ block.id }}',
										products: [],
										productsAvailable: false,
										init() {
											// listener for product updates
											window.addEventListener('productsUpdated', (event) => {
												if (event.detail.blockId === this.blockId) {
													this.updateProducts();
												}
											});
							
											this.updateProducts();
										},
										updateProducts() {
											if (window.tabsProducts && window.tabsProducts[this.blockId]) {
												this.products = window.tabsProducts[this.blockId];
												this.productsAvailable = true;
											}
										}
									}" 
									x-init="init()"
								>
									<!-- Swiper Carousel for shopify-recommendations -->
									<div class="relative w-full h-full group/carousel" aria-live="polite">
										<!-- Swiper Container -->
										<div 
											x-init="init()"
											x-ref="swiperContainer"
											class="swiper swiper-container h-full {% if carousel.settings.outer_slides %}swiper--overflow-visible{% endif %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}"
											carousel-key="swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}"
										>
                      {% if remote and skeleton_items != true %}
                        <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper-skeleton" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
                          {%- for i in (1.. section.settings.slides_per_view) -%}
                            <div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                              {%- render 'product-item' product:false, skeleton:true, settings:section.settings, _settings:settings -%}
                            </div>
                          {%- endfor -%}
                        </div>
                        {% assign skeleton_items = true %}
                      {% endif %} 
											
											<!-- Swiper Wrapper -->
											<div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
												<!-- Swiper Slides -->

													{% liquid

														for block in section.blocks
							
															assign prev = forloop.index0 | minus: 1
							
															if block.type contains 'item' and section.blocks[prev].type != 'overlay'
													
																echo '<div class="relative swiper-slide '
																echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
																if block.settings.product_position != blank
																	echo 'data-slide-position="' | append: block.settings.product_position | append:'"'
																endif
																echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
																echo '>'
							
																case block.type
							
																	when 'content-item'
																			render 'content-item' settings:block.settings blocks:section.blocks offset:forloop.index
							
																	when 'product-item'
																		for product in block.settings.product
																			render 'product-item' product:product settings:section.settings, _settings:settings
																			unless forloop.last
																				echo '</div><div class="relative swiper-slide '
																				echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view
																				echo '">'
																			endunless
																		endfor
							
																endcase
							
																echo '</div>'
							
															endif
														endfor
							
													%}

													<template x-for="(product, index) in products" hidden>
																				
														<template x-if="product" hidden>
														
														<div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
															{%- render 'product-item' product:false, settings:section.settings, _settings:settings -%}
															
                              <template x-import.afterparent='`.{{section.id}} [data-slide-position="${index+1}"]`'></template>
											
														</div>
											
														</template>
											
													</template>
											
													{% for block in section.blocks %}
														{% if block.settings.shop_all_text != blank  and  block.settings.slider_shop_all %}
															<template x-if="!!products">
																<div class="swiper-slide swiper-slide--shop-all">
																	<div class="__show-all w-full h-full flex items-center justify-center aspect-[1/1]">
																		<a href="{{ block.settings.collection.url }}" class="button button--primary">
																			{{ block.settings.shop_all_text }}
																		</a>
																	</div>
																</div>
															</template>
															{% break %}
														{% endif %}
													{% endfor %}

												<!-- Insert Carousel Items Here -->
											</div>
											<!-- Add Swiper Navigation Arrows and Pagination Here -->
											{% if section.settings.show_pagination %}
												<style>
													#shopify-section-{{ section.id }} section {
														padding: 0 0 3.583rem;
													}
													@media screen and (min-width: 768px) {
														#shopify-section-{{ section.id }} section {
															padding: 3.583rem 0; /* match desktop */
														}
													}
												</style>
												<div class="swiper-pagination"></div>
												<script type="swiper/config">
													{
														pagination: {
															el: '.swiper-pagination',
															type: 'bullets',
															clickable: true,
															dynamicBullets: true,
															dynamicMainBullets: 4
														}
													}	
												</script>
											{% endif %}
									
									
											{% if carousel.settings.arrows or carousel.settings.arrows_mobile or carousel.settings.show_arrows_on_hover %}
												{% capture arrow_visibility_classes %}
													{% if carousel.settings.arrows_mobile and carousel.settings.arrows %}
														opacity-100
													{% elsif carousel.settings.arrows_mobile %}
														opacity-100 lg:opacity-0
													{% elsif carousel.settings.arrows %}
														opacity-0 lg:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
													{% if carousel.settings.show_arrows_on_hover %}
														opacity-0 lg:opacity-0 lg:group-hover/carousel:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
												{% endcapture %}
												
												<button class="top-1/2 transform -translate-y-1/2 absolute left-6 {{ arrow_visibility_classes }} swiper-button-prev btn-control {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %}" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:2 %}
												</button> 
												<button class="top-1/2 transform -translate-y-1/2 absolute right-6 {{ arrow_visibility_classes }} swiper-button-next btn-control {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %}" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:2 %}
												</button>
											{% endif %}
										</div>
									</div>
								</div>
							</div>
							
						<!-- CAROUSEL bloomreach-recs -->
						{% when 'bloomreach-recs' %}
							{% liquid
								assign source = 'https://norwi2iw70.execute-api.us-west-2.amazonaws.com/exponea/recommendations'
								assign map = '{products:{from:"recommendations.results.0.value",each:{title:"title",handle:"handle",price:"price|*100",featured_image:"image"}}}'
								capture fetch_config
									if product != blank
										render 'bloomreach-recs-data' settings:block.settings section:section product:product
									else
										render 'bloomreach-recs-data' settings:block.settings section:section
									endif
								endcapture
								render 'carousel-product-data' source:source, map:map, settings:block.settings, id:section.id, fetch_config:fetch_config, block_id:block.id, split_tabs: section.settings.split_into_tabs
								assign remote = true
							%}

							<div 
							    class="{{ section.id }} w-full" {% render 'carousel-config' settings:section.settings namespace:section.id %}
								data-config-key="{{ section.id }}_{{ block.id }}"
							>
								<div data-block-id="{{ block.id }}" 
									x-data="{
										blockId: '{{ block.id }}',
										products: [],
										productsAvailable: false,
										init() {
											// listener for product updates
											window.addEventListener('productsUpdated', (event) => {
												if (event.detail.blockId === this.blockId) {
													this.updateProducts();
												}
											});
							
											this.updateProducts();
										},
										updateProducts() {
											if (window.tabsProducts && window.tabsProducts[this.blockId]) {
												this.products = window.tabsProducts[this.blockId];
												this.productsAvailable = true;
											}
										}
									}" 
									x-init="init()"
								>
									<!-- Swiper Carousel for bloomreach-recs -->
									<div class="relative w-full h-full group/carousel" aria-live="polite">
										<!-- Swiper Container -->
										<div 
											x-init="init()"
											x-ref="swiperContainer"
											class="swiper swiper-container h-full {% if carousel.settings.outer_slides %}swiper--overflow-visible{% endif %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}"
											carousel-key="swiperCarousel{{ section.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}_{{ block.id | replace: '--', '_' | replace: '__', '_' | replace: '-', '_' }}"
										>
                      {% if remote and skeleton_items != true %}
                        <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper-skeleton" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
                          {%- for i in (1.. section.settings.slides_per_view) -%}
                            <div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                              {%- render 'product-item' product:false, skeleton:true, settings:section.settings, _settings:settings -%}
                            </div>
                          {%- endfor -%}
                        </div>
                        {% assign skeleton_items = true %}
                      {% endif %} 
											
											<!-- Swiper Wrapper -->
											<div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
												<!-- Swiper Slides -->

													{% liquid

														for block in section.blocks
							
															assign prev = forloop.index0 | minus: 1
							
															if block.type contains 'item' and section.blocks[prev].type != 'overlay'
													
																echo '<div class="relative swiper-slide '
																echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
																if block.settings.product_position != blank
																	echo 'data-slide-position="' | append: block.settings.product_position | append:'"'
																endif
																echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
																echo '>'
							
																case block.type
							
																	when 'content-item'
																			render 'content-item' settings:block.settings blocks:section.blocks offset:forloop.index
							
																	when 'product-item'
																		for product in block.settings.product
																			render 'product-item' product:product settings:section.settings, _settings:settings
																			unless forloop.last
																				echo '</div><div class="relative swiper-slide '
																				echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view
																				echo '">'
																			endunless
																		endfor
							
																endcase
							
																echo '</div>'
							
															endif
														endfor
							
													%}

													<template x-for="(product, index) in products" hidden>
																				
														<template x-if="product" hidden>
														
														<div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
															{%- render 'product-item' product:false, settings:section.settings, _settings:settings -%}
															
															<template x-import.afterparent='`.{{section.id}} [data-slide-position="${index+1}"]`'></template>
											
														</div>
											
														</template>
											
													</template>
											
													{% for block in section.blocks %}
														{% if block.settings.shop_all_text != blank  and  block.settings.slider_shop_all %}
															<template x-if="!!products">
																<div class="swiper-slide swiper-slide--shop-all">
																	<div class="content-carousel__show-all w-full h-full flex items-center justify-center aspect-[1/1]">
																		<a href="{{ block.settings.collection.url }}" class="button button--primary">
																			{{ block.settings.shop_all_text }}
																		</a>
																	</div>
																</div>
															</template>
															{% break %}
														{% endif %}
													{% endfor %}

												<!-- Insert Carousel Items Here -->
											</div>
											<!-- Add Swiper Navigation Arrows and Pagination Here -->
											{% if section.settings.show_pagination %}
												<style>
													#shopify-section-{{ section.id }} section {
														padding: 0 0 3.583rem;
													}
													@media screen and (min-width: 768px) {
														#shopify-section-{{ section.id }} section {
															padding: 3.583rem 0; /* match desktop */
														}
													}
												</style>
												<div class="swiper-pagination"></div>
												<script type="swiper/config">
													{
														pagination: {
															el: '.swiper-pagination',
															type: 'bullets',
															clickable: true,
															dynamicBullets: true,
															dynamicMainBullets: 4
														}
													}	
												</script>
											{% endif %}
									
									
											{% if carousel.settings.arrows or carousel.settings.arrows_mobile or carousel.settings.show_arrows_on_hover %}
												{% capture arrow_visibility_classes %}
													{% if carousel.settings.arrows_mobile and carousel.settings.arrows %}
														opacity-100
													{% elsif carousel.settings.arrows_mobile %}
														opacity-100 lg:opacity-0
													{% elsif carousel.settings.arrows %}
														opacity-0 lg:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
													{% if carousel.settings.show_arrows_on_hover %}
														opacity-0 lg:opacity-0 lg:group-hover/carousel:opacity-100 pointer-events-none lg:pointer-events-auto
													{% endif %}
												{% endcapture %}
												
												<button class="top-1/2 transform -translate-y-1/2 absolute left-6 {{ arrow_visibility_classes }} swiper-button-prev btn-control {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %}" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:2 %}
												</button> 
												<button class="top-1/2 transform -translate-y-1/2 absolute right-6 {{ arrow_visibility_classes }} swiper-button-next btn-control {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %}" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
													{% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:2 %}
												</button>
											{% endif %}
										</div>
									</div>
								</div>					
							</div>

					{% endcase %}
				</div> <!-- END of Tabs -->
				{% assign index = index | plus: 1 %}
            {% endif %}
        {% endfor %}

		</div>
    {% else %}
        {% liquid
			assign remote = false
            for block in section.blocks
			
                case block.type

                    when 'data-remote'
                        render 'carousel-product-data' settings:block.settings id:section.id
                        assign remote = true

                    when 'shopify-recommendations'
                        assign source = '/recommendations/products.json?product_id=' | append: block.settings.product_id | append: '&limit=' | append: block.settings.limit
                        assign map = '{products:{from:"products",each:{title:"title",handle:"handle",tags:"tags",type:"type",price:"price",compare_at_price:"compare_at_price",featured_image:"featured_image",hover_image:"featured_image",id:"id",variants:{from:"variants",each:{id:"id",title:"title",price:"price|*100",available:"available",sku:"sku"}}}}}'
                        render 'carousel-product-data' source:source, map:map id:section.id isPrefixSkuEnable:block.settings.is_prefix_sku_enable
                        assign remote = true

                    when 'boost-recommendations'
                        #assign source = 'https://services.mybcapps.com/discovery/recommend?shop=the-roark-revival.myshopify.com'
                        assign map = '{products:{from:"products",each:{title:"title",handle:"handle",tags:"tags",type:"type",price:"price",compare_at_price:"compare_at_price",featured_image:"featured_image",hover_image:"featured_image",id:"id",variants:{from:"variants",each:{id:"id",title:"title",price:"price|*100",available:"available",sku:"sku"}}}}}'
                        render 'carousel-product-data' , settings:block.settings id:section.id isPrefixSkuEnable:block.settings.is_prefix_sku_enable
                        assign remote = true
                
                    when 'shopify-collection'
                        if block.settings.collection 
                            assign source = '/collections/' | append: block.settings.collection | append: '/products.json?limit=' | append: block.settings.limit
                            assign map = '{products:{from:"products",each:{title:"title",handle:"handle",tags:"tags",type:"product_type",price:"variants.0.price|*100",compare_at_price:"variants.0.compare_at_price|*100",images:"images",id:"id",variants:{from:"variants",each:{id:"id",title:"title",price:"price|*100",available:"available",sku:"sku"}}}}}'
                            render 'carousel-product-data' source:source, map:map id:section.id settings:false collection_handle:block.settings.collection
                            assign remote = true
                        endif


                    when 'bloomreach-recs'
                            assign source = 'https://norwi2iw70.execute-api.us-west-2.amazonaws.com/exponea/recommendations'
                            assign map = '{products:{from:"recommendations.results.0.value",each:{title:"title",handle:"handle",price:"price|*100",featured_image:"image"}}}'
                                capture fetch_config
                                    if product != blank
                                        render 'bloomreach-recs-data' settings:block.settings section:section product:product
                                    else
                                        render 'bloomreach-recs-data' settings:block.settings section:section
                                    endif
                                endcapture
                            render 'carousel-product-data' source:source, map:map, settings:block.settings, id:section.id, fetch_config:fetch_config
                            assign remote = true

                    when 'recently-viewed'
                        assign source = 'recentlyViewed'
            
                endcase

            endfor
        %} 

        <div 
            class="{{ section.id }} w-full" {% render 'carousel-config' settings:section.settings namespace:section.id %}
			data-config-key="{{ section.id }}_{{ block.id }}"
		>

            {% liquid 

                capture text_item_1
                    render 'class-settings' prefix:'text_item_1', settings:carousel.settings
                endcapture
                capture button_1
                    render 'class-settings' prefix:'button_1', settings:carousel.settings
                endcapture

                if section.settings.title_product != blank 
                    render 'carousel-product-title' settings:section.settings, price:false
                endif

                if carousel.settings.text_item_1_text != "" or carousel.settings.button_1_text != "" and section.settings.title_product == blank 

                    render 'content-item', item_classes:'content-item--carousel-header flex-col', content_classes: 'w-full justify-between', text_stack_classes: 'self-end gap-2 flex-col', button_stack_classes: 'self-end gap-2 flex-col', text_item_1_link:section.settings.title_link, text_item_1_text: carousel.settings.text_item_1_text, text_item_1_class: text_item_1, text_item_1_element: carousel.settings.text_item_1_element, text_item_2_text: product_total, text_item_2_class: 'text-sm', button_1_text: carousel.settings.button_1_text, button_1_classes: button_1, button_1_link: carousel.settings.button_1_link, settings: carousel.settings , desk_hide_button1: true
                endif

            %} 

           <div class="relative w-full h-full group/carousel" aria-live="polite">
               <div class="swiper swiper-container h-full {% if carousel.settings.outer_slides %}swiper--overflow-visible{% endif %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}">

                  {% if remote %}
                    <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper-skeleton" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">
                      {%- for i in (1.. section.settings.slides_per_view) -%}
                        <div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                          {%- render 'product-item' product:false, skeleton:true, settings:section.settings, _settings:settings -%}
                        </div>
                      {%- endfor -%}
                    </div>
                  {% endif %} 

                   <div class="{% unless section.settings.show_pagination %}items-stretch h-full {% endunless %}swiper-wrapper" style="--gap:{{ section.settings.spacebetween }}px; --gap-mobile: {{ section.settings.spacebetween_mobile }}px;">

                       {% liquid

                           for block in section.blocks

                               assign prev = forloop.index0 | minus: 1

                               if block.type contains 'item' and section.blocks[prev].type != 'overlay'
                       
                                   echo '<div class="relative swiper-slide '
                                   echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
                                   if block.settings.product_position != blank
                                       echo 'data-slide-position="' | append: block.settings.product_position | append:'"'
                                   endif
								   echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
                                   echo '>'

                                   case block.type

                                       when 'content-item'
                                               render 'content-item' settings:block.settings blocks:section.blocks offset:forloop.index

                                       when 'review-item' 
                                               render 'review-item' settings:block.settings same_height_review:section.settings.same_height_review product_item_show_button:section.settings.product_item_show_button product_type_mob_class:'items-start flex-col md:items-center md:flex-row custom-review-alignment'



                                       when 'article-item' 
                                               assign blog_handle = block.settings.article | split: '/' | first
                                               assign blog_object = blogs[blog_handle]
                                               render 'article-item' settings:block.settings, blog:blog_object, article:block.settings.article

                                       when 'product-item'
                                           for product in block.settings.product
                                               render 'product-item' product:product settings:section.settings, _settings:settings
                                               unless forloop.last
                                                   echo '</div><div class="relative swiper-slide '
                                                   echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view
                                                   echo '">'
                                               endunless
                                           endfor

                                   endcase

                                   echo '</div>'

                               endif

                               if block.type == 'blog-articles'
                               assign _blog = blogs[block.settings.blog] | default: blog
                               assign _count = 0

                                   for _article in _blog.articles

                                       if _article.id == article.id 
                                           continue
                                       endif

                                       assign _filters = block.settings.filters | split: ','
                                       assign _filtered_out = false
                                       for _filter in _filters
                                           if article.tags contains _filter or article == blank
                                               unless _article.tags contains _filter
                                                   assign _filtered_out = true
                                               endunless
                                           endif
                                       endfor

                                       if _filtered_out
                                           continue
                                       endif

                                       echo '<div class="relative swiper-slide '
                                       echo  ' w-1/' | append: section.settings.slides_per_view_mobile | append: ' lg:w-1/' | append:section.settings.slides_per_view | append: '"'
									   echo ' data-carousel-item-id="item-' | append: block.id | append: '"'
                                       echo '>'
                                       
                                       render 'article-item' settings:block.settings, blog:_blog, article:_article, layout:'vertical'

                                       echo '</div>'

                                       assign _count = _count | plus: 1
                                       if _count >= section.settings.limit
                                           break
                                       endif

                                   endfor
                               endif

                           endfor

                       %}

                       {% if remote %}
                           <template x-if="!section.products">
                               <template x-for="i in {{ section.settings.slides_per_view}}">
                                   <article class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                                     {%- render 'product-item' product:false, skeleton:true, settings:section.settings, _settings:settings -%}
                                   </article>
                               </template>
                           </template>
                       {% endif %} 

                       <template x-for="(product, index) in section.products" hidden>
                           <template x-if="product" hidden> 
                           
                           <div class="swiper-slide w-1/{{ section.settings.slides_per_view_mobile }} lg:w-1/{{ section.settings.slides_per_view}}">
                               {%- render 'product-item' product:false, settings:section.settings, _settings:settings -%}
                               
                               <template x-import.afterparent='`.{{section.id}} [data-slide-position="${index+1}"]`'></template>

                           </div>

                           </template>
                       </template>

                       {% for block in section.blocks %}					   
                           {% if block.settings.shop_all_text != blank  and  block.settings.slider_shop_all %}
                               <template x-if="!!section.products">
                               <div class="swiper-slide swiper-slide--shop-all">
                                   <div class="content-carousel__show-all  w-full h-full flex items-center justify-center aspect-[1/1]">
                                       <a href="{{ block.settings.collection.url }}" class="button  {{ block.settings.shop_all_button_1_class_style }} {{ block.settings.shop_all_button_1_class_size }}">
                                           {{ block.settings.shop_all_text }}
                                       </a>
                                   </div>
                               </div>
                               </template>
                           {% break %}
                           {% endif %}
                       {% endfor %}

                    </div>
               
					{% if carousel.settings.show_pagination == true %}
                        {% comment %} <div class="flex items-center p-4 lg:p-0">
                            
                                <div class="pagination"></div>
                            
                        </div> {% endcomment %}
                    {% endif %}

                    {% if section.settings.show_pagination %}
						<style>
							#shopify-section-{{ section.id }} section {
                              					padding-bottom: 0px;
							}
							@media screen and (min-width: 768px) {
								#shopify-section-{{ section.id }} section {
                                  					padding-bottom: 3.583rem;
                                  					padding-top: 0px;
								}
							}
                            				@media screen and (min-width: 768px) and (max-width: 1023px) {
								#shopify-section-{{ section.id }} section {
                                  					padding-bottom: 0px;
								}
							}
						</style>
						<div class="swiper-pagination"></div>
                        <script type="swiper/config">
							{
								pagination: {
									el: '.swiper-pagination',
									type: 'bullets',
									clickable: true,
									dynamicBullets: true,
									dynamicMainBullets: 4
								}
							}	
                        </script>
					{% endif %}

                   {% if carousel.settings.arrows or carousel.settings.arrows_mobile or carousel.settings.show_arrows_on_hover %}
                       {% capture arrow_visibility_classes %}
                           {% if carousel.settings.arrows_mobile and carousel.settings.arrows %}
                               opacity-100
                           {% elsif carousel.settings.arrows_mobile %}
                               opacity-100 lg:opacity-0
                           {% elsif carousel.settings.arrows %}
                               opacity-0 lg:opacity-100 pointer-events-none lg:pointer-events-auto
                           {% endif %}
                           {% if carousel.settings.show_arrows_on_hover %}
                               opacity-0 lg:opacity-0 lg:group-hover/carousel:opacity-100 pointer-events-none lg:pointer-events-auto
                           {% endif %}
                       {% endcapture %}
                       
                       <div class="nav-btn-wrap-func-left">
                         <button class="top-1/2 transform -translate-y-1/2 absolute left-6 {{ arrow_visibility_classes }} {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %} swiper-button-prev btn-control" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
                             {% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:2 %}
                         </button>
                         {% if section.settings.increase_arrow_tap_target %}
	                         <a class="top-0 absolute left-0 h-full  {{ arrow_visibility_classes }} {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %} swiper-button-prev" style="width:90px;" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
	                         </a>
                         {% endif %}
                       </div>
													
                       <div class="nav-btn-wrap-func-right">
                         <button class="top-1/2 transform -translate-y-1/2 absolute right-6 {{ arrow_visibility_classes }} {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %} swiper-button-next btn-control" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
                             {% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:2 %}
                         </button>
                         {% if section.settings.increase_arrow_tap_target %}
	                         <a class="top-0 absolute right-0 {{ arrow_visibility_classes }} {% if carousel.settings.arrows_mobile %}show-arrow-mob{% endif %} swiper-button-next" style="width:90px;" tabindex="0" aria-label="Next slide" aria-controls="swiper-wrapper-637bc1e5c4b19074">
	                         </a>
                         {% endif %}
                       </div>
                   {% endif %}

               </div>

           </div>

		   {% if section.settings.button_1_text != blank %}
			<button {% if section.settings.button_1_onclick != blank %}onclick="{{ section.settings.button_1_onclick }};" {% else %}onclick="window.location='{{ button_1_link | default:section.settings.button_1_link }}'" {% endif %} class="button-mobile button collection-title-shop-all {{ section.settings.button_1_class_style }} {{ section.settings.button_1_class_size }} ">
			  <span>
				{{ button_1_text | default: section.settings.button_1_text }}
			  </span>
			</button>
		  {% endif %}
        </div>

    {% endif %}

	</main>

</section>

{%- endif -%}

{% style %}
	#shopify-section-{{ section.id }} .swiper-slide {
		width:calc(100% / {{ section.settings.slides_per_view_mobile }})
	}
	@media(min-width:1024px){
		#shopify-section-{{ section.id }} .swiper-slide {
			width:calc(100% / {{ section.settings.slides_per_view }})
		}
	}
  .nav-btn-wrap-func-right a {
    right: 0 !important;
    height: 100% !important;
    top: 0;
    margin: 0;
    cursor: pointer;
  }
  .nav-btn-wrap-func-left a {
    left: 0 !important;
    height: 100% !important;
    top: 0;
    margin: 0;
    cursor: pointer;
  }
  .section-slideshow-multiple .productitem--extra {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  .section-slideshow-multiple .productitem--wrap {
    height: 100%;
  }
  .section-slideshow-collection .swiper-button-disabled {
    opacity: .5;
  }
	{% if section.settings.split_into_tabs %}
		#shopify-section-{{ section.id }} button.tab {
			color: {{ section.settings.tabs_button_text }};
		}
		#shopify-section-{{ section.id }} button.tab.active {
			background-color: {{ section.settings.tabs_active_button_background }};
			color: {{ section.settings.tabs_active_button_text }};
		}
		.tabs-wrapper .tab-content {
			opacity: 0;
			visibility: hidden;
			height: 0;
		}
		.tabs-wrapper .tab-content.active {
			opacity: 1;
			visibility: visible;
			height: 100%;
		}
		#shopify-section-{{section.id}} section {
			overflow:hidden;
		}
	{% endif %}
	{% if carousel.settings.arrows_mobile %}
	@media only screen and (max-width:1023px) {
		button.show-arrow-mob {
			opacity: 1;
			pointer-events: auto;
			}
		}
		.swiper-wrapper:has(.swiper-slide-active:first-of-type) + button.swiper-button-prev {
			opacity: 0;
		} 
		.swiper-wrapper:has(.swiper-slide-active:first-of-type) + .nav-btn-wrap-func-left button.swiper-button-prev {
			opacity: 0;
		} 
	{% endif %}
{% endstyle %}


<script>
	window.carouselConfigs = window.carouselConfigs || {};

{% comment %}
  BELOW SCRIPT IS FOR RECENTLY VIEWED PRODUCTS
{% endcomment %}

  (function(){
    
    let rv = {products:[]}
    try{
      rv = JSON.parse(localStorage.getItem('recentlyViewed'));
    } catch(err) {}
    
    if(!rv)
      rv = {products:[]}

    window.recentlyViewed = rv;

    {% if template contains 'product' %}

      var handle = {{product.handle|json}};
      rv.products = JSON.parse(JSON.stringify(
          rv.products.filter(function(p, i) {return p.handle != handle})
        )
      ).slice(0,{{ section.settings.limit }});
      window.recentlyViewed = JSON.parse(JSON.stringify(rv));

      rv.products.unshift({{product|json}})
      localStorage.setItem('recentlyViewed', JSON.stringify(rv))

    {% endif %}

  })()

	window.sections = window.sections || []
	window.addEventListener('DOMContentLoaded', ()=>{
  		window.sections.find(s=>s.id=='{{ section.id }}').data = {{ section.settings | json }}
  	})
</script>

{% schema %}
{
	"name": "Carousel",
	"class": "section--content-carousel carousel-outer",
	"settings": [
		{
			"type": "liquid",
			"label": "Liquid Logic Inclusion",
			"id": "inclusion_liquid_carousel",
			"info": "Insert any liquid logic that returns a value to display the section",
			"default": "true"
		},
		{
			"type": "liquid",
			"label": "Javascript Logic Inclusion",
			"id": "inclusion_js",
			"info": "Insert any javascript logic that evaluates to true to display the section",
			"default": "true"
		},
		{
			"type": "paragraph",
			"content": "@include TabSettings, label_prefix:Tabs, id_prefix:tabs_class"
		},
		{
			"type": "header",
			"content": "@global: Convert Into Tabs"
		},
		{
			"type": "checkbox",
			"id": "split_into_tabs",
			"label": "@global: Split Carousel Into Tabs",
			"default": false,
			"info": "Can only add blocks from Shopify Collection, Bloomreach Recs and Bloomreach Recs"
		},
		{
			"type": "select",
			"id": "tab_interaction",
			"label": "@global: Tab Interaction",
			"default": "hover",
			"options": [
				{
					"value": "hover",
					"label": "Hover"
				},
				{
					"value": "click",
					"label": "Click"
				}
			]
		},
		{
			"type": "color",
			"id": "tabs_button_text",
			"label": "@global: Carousel Tab Button Text",
			"default": "#000000"
		},
		{
			"type": "color",
			"id": "tabs_active_button_text",
			"label": "@global: Carousel Tab Active Button Text",
			"default": "#000000"
		},
		{
			"type": "color_background",
			"id": "tabs_active_button_background",
			"label": "@global: Carousel Tab Active Button Background",
			"default": "linear-gradient(#ffffff, #000000)"
		},
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		},
		{
			"type": "header",
			"content": "@global: Wrapper Settings"
		},
		{
			"type": "paragraph",
			"content": "@include BackgroundStyles, @extends:SectionWrapper"
		},
		{
			"type": "color",
			"id": "wrapper_style_background_color",
			"label": "@global:  Background Color"
		},
		{
			"type": "color_background",
			"id": "wrapper_style_background",
			"label": "@global:  Background Gradient"
		},
		{
			"type": "image_picker",
			"label": "@global:  Background Image Mobile",
			"id": "wrapper_bg_image_mob"
		},
		{
			"type": "image_picker",
			"label": "@global:  Background Image",
			"id": "wrapper_bg_image"
		},
		{
			"type": "select",
			"id": "wrapper_class_vertical_padding",
			"label": "@global: Wrapper Vertical Padding",
			"options": [
				{
					"value": "@include Spacing prop:py",
					"label": "Inclusion"
				},
				{
					"value": "py-0",
					"label": "@global: None"
				},
				{
					"value": "py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "py-xs",
					"label": "@global: XS"
				},
				{
					"value": "py-sm",
					"label": "@global: SM"
				},
				{
					"value": "py-md",
					"label": "@global: MD"
				},
				{
					"value": "py-lg",
					"label": "@global: LG"
				},
				{
					"value": "py-xl",
					"label": "@global: XL"
				},
				{
					"value": "py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "py-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "wrapper_class_horizontal_padding",
			"label": "@global: Wrapper Horizontal Padding",
			"options": [
				{
					"value": "@include Spacing prop:px",
					"label": "Inclusion"
				},
				{
					"value": "px-0",
					"label": "@global: None"
				},
				{
					"value": "px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "px-xs",
					"label": "@global: XS"
				},
				{
					"value": "px-sm",
					"label": "@global: SM"
				},
				{
					"value": "px-md",
					"label": "@global: MD"
				},
				{
					"value": "px-lg",
					"label": "@global: LG"
				},
				{
					"value": "px-xl",
					"label": "@global: XL"
				},
				{
					"value": "px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "px-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "wrapper_class_vertical_padding_desktop",
			"label": "@global: Wrapper Vertical Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:py",
					"label": "Inclusion"
				},
				{
					"value": "lg:py-0",
					"label": "@global: None"
				},
				{
					"value": "lg:py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:py-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:py-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:py-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:py-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:py-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:py-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:py-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:py-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:py-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:py-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:py-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "select",
			"id": "wrapper_class_horizontal_padding_desktop",
			"label": "@global: Wrapper Horizontal Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:px",
					"label": "Inclusion"
				},
				{
					"value": "lg:px-0",
					"label": "@global: None"
				},
				{
					"value": "lg:px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:px-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:px-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:px-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:px-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:px-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:px-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:px-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:px-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:px-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:px-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:px-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "header",
			"content": "@global: Container Settings"
		},
		{
			"type": "select",
			"id": "container_class_container",
			"label": "@global:  Container",
			"options": [
				{
					"value": "w-full",
					"label": "Full Screen Width"
				},
				{
					"value": "container",
					"label": "Container Width"
				},
				{
					"value": "container container--wide",
					"label": "Wide Container"
				},
				{
					"value": "container container--narrow",
					"label": "Narrow Container"
				},
				{
					"value": "container container--tight",
					"label": "Very Narrow Container"
				},
				{
					"value": "container--product",
					"label": "Product Container Width"
				},
				{
					"value": "container--wide",
					"label": "Wide Container Width"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_vertical_padding",
			"label": "@global:  Vertical Padding",
			"options": [
				{
					"value": "@include Spacing prop:py",
					"label": "Inclusion"
				},
				{
					"value": "py-0",
					"label": "@global: None"
				},
				{
					"value": "py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "py-xs",
					"label": "@global: XS"
				},
				{
					"value": "py-sm",
					"label": "@global: SM"
				},
				{
					"value": "py-md",
					"label": "@global: MD"
				},
				{
					"value": "py-lg",
					"label": "@global: LG"
				},
				{
					"value": "py-xl",
					"label": "@global: XL"
				},
				{
					"value": "py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "py-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_horizontal_padding",
			"label": "@global:  Horizontal Padding",
			"options": [
				{
					"value": "@include Spacing prop:px",
					"label": "Inclusion"
				},
				{
					"value": "px-0",
					"label": "@global: None"
				},
				{
					"value": "px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "px-xs",
					"label": "@global: XS"
				},
				{
					"value": "px-sm",
					"label": "@global: SM"
				},
				{
					"value": "px-md",
					"label": "@global: MD"
				},
				{
					"value": "px-lg",
					"label": "@global: LG"
				},
				{
					"value": "px-xl",
					"label": "@global: XL"
				},
				{
					"value": "px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "px-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_vertical_padding_desktop",
			"label": "@global:  Vertical Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:py",
					"label": "Inclusion"
				},
				{
					"value": "lg:py-0",
					"label": "@global: None"
				},
				{
					"value": "lg:py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:py-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:py-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:py-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:py-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:py-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:py-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:py-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:py-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:py-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:py-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:py-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "select",
			"id": "container_class_horizontal_padding_desktop",
			"label": "@global:  Horizontal Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:px",
					"label": "Inclusion"
				},
				{
					"value": "lg:px-0",
					"label": "@global: None"
				},
				{
					"value": "lg:px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:px-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:px-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:px-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:px-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:px-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:px-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:px-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:px-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:px-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:px-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:px-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "paragraph",
			"content": "@include Text, id_prefix:text_item_1, label_prefix:Text 1"
		},
		{
			"type": "header",
			"content": "@global: Text 1 Settings"
		},
		{
			"type": "text",
			"id": "text_item_1_text",
			"label": "@global: Text 1 Text"
		},
		{
			"type": "liquid",
			"id": "text_item_1_liquid",
			"label": "@global: Text 1 Text (Liquid)"
		},
		{
			"type": "liquid",
			"id": "text_item_1_attr_x_text",
			"label": "@global: Text 1 Dynamic Text (Alpine)"
		},
		{
			"type": "select",
			"id": "text_item_1_element",
			"label": "@global: Text 1 Element",
			"default": "p",
			"options": [
				{
					"value": "@include TextElement",
					"label": "Inclusion"
				},
				{
					"value": "h1",
					"label": "@global:  Heading 1"
				},
				{
					"value": "h2",
					"label": "@global:  Heading 2"
				},
				{
					"value": "h3",
					"label": "@global:  Heading 3"
				},
				{
					"value": "h4",
					"label": "@global:  Heading 4"
				},
				{
					"value": "h5",
					"label": "@global:  Heading 5"
				},
				{
					"value": "p",
					"label": "@global:  Paragraph"
				},
				{
					"value": "div",
					"label": "@global:  Div"
				}
			]
		},
		{
			"type": "select",
			"id": "text_item_1_class_type_style",
			"label": "@global: Text 1 Type Style",
			"options": [
				{
					"value": "@include TypeStyle",
					"label": "Inclusion"
				},
				{
					"value": "",
					"label": "@global:  Auto"
				},
				{
					"value": "type-body",
					"label": "@global:  Body"
				},
				{
					"value": "type-hero",
					"label": "@global:  Hero"
				},
				{
					"value": "type-eyebrow",
					"label": "@global:  Eyebrow"
				},
				{
					"value": "type-headline",
					"label": "@global:  Headline"
				},
				{
					"value": "type-subline",
					"label": "@global:  Subline"
				},
				{
					"value": "type-micro",
					"label": "@global:  Micro"
				},
				{
					"value": "type-item",
					"label": "@global:  Item Title"
				},
				{
					"value": "type-section",
					"label": "@global:  Section Title"
				}
			]
		},
		{
			"type": "select",
			"id": "text_item_1_class_type_size",
			"label": "@global: Text 1 Type Size",
			"options": [
				{
					"value": "@include TypeSize",
					"label": "Inclusion"
				},
				{
					"value": "",
					"label": "@global:  Default"
				},
				{
					"value": "type--sm",
					"label": "@global:  Smaller"
				},
				{
					"value": "type--lg",
					"label": "@global:  Larger"
				}
			]
		},
		{
			"type": "color",
			"id": "text_item_1_style_color",
			"label": "@global: Text 1 Color"
		},
		{
			"type": "url",
			"id": "title_link",
			"label": "Title Link"
		},
		{
			"type": "product",
			"id": "title_product",
			"label": "Title Product"
		},
		{
			"type": "paragraph",
			"content": "@include Button, id_prefix:button_1, label_prefix:Button 1"
		},
		{
			"type": "header",
			"content": "@global: Button 1 Settings"
		},
		{
			"type": "text",
			"id": "button_1_text",
			"label": "@global: Button 1 Text"
		},
		{
			"type": "text",
			"id": "button_1_leading_icon",
			"label": "@global: Leading Icon"
		},
		{
			"type": "text",
			"id": "button_1_trailing_icon",
			"label": "@global: Trailing Icon"
		},
		{
			"type": "url",
			"id": "button_1_link",
			"label": "@global: Button 1 Link"
		},
		{
			"type": "liquid",
			"id": "button_1_liquid_link",
			"label": "@global: Button 1 Link (Liquid)",
			"info": "Replaces the basic Link setting."
		},
		{
			"type": "liquid",
			"id": "button_1_onclick",
			"label": "@global: Button 1 On Click"
		},
		{
			"type": "select",
			"id": "button_1_class_style",
			"label": "@global: Button 1 Style",
			"options": [
				{
					"value": "@include ButtonStyle",
					"label": "Inclusion"
				},
				{
					"value": "button--primary",
					"label": "@global:  Primary"
				},
				{
					"value": "button--secondary",
					"label": "@global:  Secondary"
				},
				{
					"value": "button--tertiary",
					"label": "@global:  Tertiary"
				},
				{
					"value": "button--light",
					"label": "@global:  Light"
				},
				{
					"value": "button--dark",
					"label": "@global:  Dark"
				},
				{
					"value": "button--pop",
					"label": "@global:  Pop"
				},
				{
					"value": "button--highlight",
					"label": "@global:  Highlight"
				},
				{
					"value": "button--action",
					"label": "@global:  Action"
				},
				{
					"value": "button--simple",
					"label": "@global:  Simple"
				},
				{
					"value": "button--emphasis",
					"label": "@global:  Emphasis"
				},
				{
					"value": "button--light-text-link",
					"label": "@global:  Light Text Link"
				},
				{
					"value": "button--link",
					"label": "@global:  Text Link"
				},
				{
					"value": "button--micro-link",
					"label": "@global:  Micro Text Link"
				},
				{
					"value": "button--icon",
					"label": "@global:  Icon"
				},
				{
					"value": "button--primary-hover",
					"label": "@global:  Primary Hover"
				},
				{
					"value": "button--secondary-hover",
					"label": "@global:  Secondary Hover"
				},
				{
					"value": "button--tertiary-hover",
					"label": "@global:  Tertiary Hover"
				}
			]
		},
		{
			"type": "select",
			"id": "button_1_class_size",
			"label": "@global: Button 1 Size",
			"options": [
				{
					"value": "",
					"label": "Standard"
				},
				{
					"value": "button--large",
					"label": "Large"
				}
			]
		},
		{
			"type": "paragraph",
			"content": "@include Carousel"
		},
		{
			"type": "header",
			"content": "@global: Product Settings"
		},
		{
			"type": "number",
			"id": "limit",
			"label": "@global: Max Number of Products",
			"default": 8
		},
		{
			"type": "checkbox",
			"id": "quick_add",
			"label": "@global: Include Quick Add",
			"default": true
		},
		{
			"type": "paragraph",
			"content": "@global: To select data source for products, add a block to this Section"
		},
		{
			"type": "header",
			"content": "@global: Slideshow Settings"
		},
		{
			"type": "checkbox",
			"id": "show_pagination",
			"label": "@global: Show Dot/Bullet Indicators",
			"default": false
		},
		{
			"type": "number",
			"id": "slides_per_view",
			"label": "@global: Slides per view (Desktop)",
			"default": 4
		},
		{
			"type": "number",
			"id": "slides_per_group",
			"label": "@global: Slides per group (Desktop)",
			"default": 4
		},
		{
			"type": "number",
			"id": "spacebetween",
			"label": "@global: Space between slides (Desktop)",
			"default": 16
		},
		{
			"type": "number",
			"id": "slides_per_view_mobile",
			"label": "@global: Slides per view (Mobile)",
			"default": 2
		},
		{
			"type": "number",
			"id": "slides_per_group_mobile",
			"label": "@global: Slides per group (Mobile)",
			"default": 2
		},
		{
			"type": "number",
			"id": "spacebetween_mobile",
			"label": "@global: Space between slides (Mobile)",
			"default": 16
		},
		{
			"type": "checkbox",
			"id": "autoplay",
			"label": "@global: Autoplay"
		},
		{
			"type": "checkbox",
			"id": "loop",
			"label": "@global: Loop"
		},
		{
			"type": "checkbox",
			"id": "center",
			"label": "@global: Center"
		},
		{
			"type": "range",
			"id": "autoplay_slide_duration",
			"label": "@global: Autoplay Slide Duration",
			"min": 3,
			"max": 8,
			"step": 1,
			"default": 8
		},
		{
			"type": "checkbox",
			"id": "arrows",
			"label": "@global: Show arrows (Desktop)",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "arrows_mobile",
			"label": "@global: Show arrows (Mobile)",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "show_arrows_on_hover",
			"label": "@global: Show arrows only on hover (Desktop)",
			"default": true
		},
		{
			"type": "checkbox",
			"label": "@global: Show outer slides on hover",
			"id": "outer_slides",
			"default": false
		},
		{
			"type": "checkbox",
			"label": "@global: Increase arrow tap target",
			"id": "increase_arrow_tap_target",
			"default": false
		},
		{
			"type": "paragraph",
			"content": "@include ProductItem"
		},
		{
			"type": "header",
			"content": "@global: Product Item Settings"
		},
		{
			"id": "product_item_settings",
			"label": "@global: Override Global Product Item Settings",
			"type": "checkbox"
		},
		{
			"type": "liquid",
			"id": "product_item_title_source",
			"label": "@global: Product Item Title Source",
			"default": "product.title.split('-')[0]"
		},
		{
			"type": "liquid",
			"id": "product_item_subtitle_source",
			"label": "@global: Product Item Subtitle Source",
			"default": "product.title.split('-')[1]"
		},
		{
			"type": "liquid",
			"id": "product_item_type_source",
			"label": "@global: Product Item Type Source",
			"default": "product.type"
		},
		{
			"type": "header",
			"content": "@global: Classes"
		},
		{
			"type": "text",
			"id": "classes_product_item",
			"label": "@global: Product Item"
		},
		{
			"type": "text",
			"id": "classes_product_item_image_wrapper",
			"label": "@global: Image"
		},
		{
			"type": "text",
			"id": "classes_product_item_title",
			"label": "@global: Product Item Title"
		},
		{
			"type": "text",
			"id": "classes_product_item_subtitle",
			"label": "@global: Product Item Subtitle"
		},
		{
			"type": "liquid",
			"id": "product_item_additional_liquid",
			"label": "@global: Additional Liquid"
		},
		{
			"type": "checkbox",
			"id": "product_item_show_title",
			"label": "@global: Show Title",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "product_item_show_subtitle",
			"label": "@global: Show Subtitle",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "product_item_show_type",
			"label": "@global: Show Product Type",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "product_item_show_price",
			"label": "@global: Show Price",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "product_item_show_reviews",
			"label": "@global: Show Review Stars",
			"default": true
		},
		{
			"type": "select",
			"id": "product_item_info_layout",
			"label": "@global: Info Layout",
			"options": [
				{
					"value": "",
					"label": "Column"
				},
				{
					"value": "flex flex-row justify-between",
					"label": "Spaced Row"
				}
			]
		},
		{
			"type": "select",
			"id": "product_item_quick_add_position_mobile",
			"label": "@global: Quick Add Button position (Mobile)",
			"default": "image",
			"options": [
				{
					"label": "Image Container",
					"value": "image"
				},
				{
					"label": "Info Container",
					"value": "info"
				}
			]
		},
		{
			"type": "select",
			"id": "product_item_quick_add_position_desktop",
			"label": "@global: Quick Add Button position (Desktop)",
			"default": "image",
			"options": [
				{
					"label": "Image Container",
					"value": "image"
				},
				{
					"label": "Info Container",
					"value": "info"
				}
			]
		},
		{
			"type": "select",
			"id": "product_item_show_button",
			"label": "@global: Show/Hide Button",
			"default": "hide",
			"options": [
				{
					"label": "Show",
					"value": "show"
				},
				{
					"label": "Hide",
					"value": "hide"
				}
			]
		},
		{
			"type": "checkbox",
			"id": "product_item_variant_selector",
			"label": "@global: Include Variant Form",
			"default": false
		},
		{
			"type": "checkbox",
			"id": "product_item_show_swatches",
			"label": "@global: Show Swatches"
		},
		{
			"type": "checkbox",
			"id": "product_item_show_oos_siblings",
			"label": "@global: Show Out of Stock Siblings",
			"default": false
		},
		{
			"type": "number",
			"id": "product_item_swatches_view",
			"label": "@global: Swatches Per View (Mobile)",
			"default": 3
		},
		{
			"type": "number",
			"id": "product_item_swatches_view_desktop",
			"label": "@global: Swatches Per View (Desktop)",
			"default": 5
		},
		{
			"type": "select",
			"id": "product_item_swatch_interaction",
			"label": "@global: User swatch interaction to update product item on desktop",
			"default": "click",
			"options": [
				{
					"label": "Hover",
					"value": "hover"
				},
				{
					"label": "Click",
					"value": "click"
				}
			]
		},
		{
			"type": "select",
			"id": "product_color_option_type",
			"label": "@global: Product Color Option Type",
			"default": "admin_setting",
			"options": [
				{
					"label": "Theme Setting",
					"value": "admin_setting"
				},
				{
					"label": "Image",
					"value": "image"
				},
				{
					"label": "Swatch Color",
					"value": "swatch"
				},
				{
					"label": "Swatch Image",
					"value": "swatch_image"
				}
			]
		},
		{
			"type": "checkbox",
			"id": "same_height_review",
			"label": "Same Height Review",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "single_variant_direct_addtocart",
			"label": "Direct add-to-cart for single variant",
			"default": false
		},
		{
			"type": "select",
			"id": "product_item_show_price_right",
			"label": "Show Price on Right or Bottom",
			"default": "bottom",
			"options": [
				{
					"label": "Right Side of Title",
					"value": "right"
				},
				{
					"label": "Below of Title",
					"value": "bottom"
				}
			]
		}
	],
	"blocks": [
		{
			"name": "Shopify Collection",
			"type": "shopify-collection",
			"limit": 4,
			"settings": [
				{
					"type": "header",
					"content": "Collection settings"
				},
				{
					"label": "Collection",
					"id": "collection",
					"type": "collection"
				},
				{
					"label": "Limit",
					"id": "limit",
					"type": "number",
					"default": 8
				},
				{
					"label": "Shop All Button Text",
					"id": "shop_all_text",
					"type": "text",
					"default": "Shop All",
					"info": "If blank, button and enclosing slide will not display"
				},
				{
					"label": "Alternate Carousel Heading",
					"id": "alt_carousel_heading",
					"type": "text",
					"info": "Needed when carousel is converted to tabs"
				},
				{
					"label": "If Checked, Then Below Review Settings Will Work",
					"id": "enable_review_stars",
					"type": "checkbox",
					"default": false
				},
				{
					"label": "review stars below carousel title mobile",
					"id": "show_reviews_for_mobile",
					"type": "checkbox",
					"default": true
				},
				{
					"label": "review stars below carousel title desktop",
					"id": "show_reviews_for_desktop",
					"type": "checkbox",
					"default": true
				},
				{
					"label": "review stars below products mobile",
					"id": "show_reviews_for_product_mobile",
					"type": "checkbox",
					"default": true
				},
				{
					"label": "review stars below products desktop",
					"id": "show_reviews_for_product_desktop",
					"type": "checkbox",
					"default": true
				},
				{
					"type": "checkbox",
					"id": "slider_shop_all",
					"label": "Enable Shop All button",
					"default": false
				},
				{
					"type": "select",
					"id": "shop_all_button_1_class_style",
					"label": "Shop All Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "select",
					"id": "shop_all_button_1_class_size",
					"label": "Shop All button",
					"options": [
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				}
			]
		},
		{
			"name": "Shopify Recommendations",
			"type": "shopify-recommendations",
			"limit": 1,
			"settings": [
				{
					"type": "header",
					"content": "Collection settings"
				},
				{
					"label": "Product ID",
					"id": "product_id",
					"type": "liquid",
					"default": "{{ product.id }}"
				},
				{
					"label": "Limit",
					"id": "limit",
					"type": "number",
					"default": 8
				},
				{
					"label": "Alternate Carousel Heading",
					"id": "alt_carousel_heading",
					"type": "text",
					"info": "Needed when carousel is converted to tabs"
				},
				{
					"type": "checkbox",
					"id": "is_prefix_sku_enable",
					"label": "Hide same SKU prefix",
					"default": false
				}
			]
		},
		{
			"name": "Boost Recommendations",
			"type": "boost-recommendations",
			"limit": 1,
			"settings": [
				{
					"type": "header",
					"content": "HTTP Request"
				},
				{
					"id": "remote_url",
					"label": "Remote URL",
					"type": "liquid",
					"info": "Liquid with JavaScript Template Literals"
				},
				{
					"type": "select",
					"id": "config_method",
					"label": "Request Method",
					"default": "GET",
					"options": [
						{
							"value": "GET",
							"label": "GET"
						},
						{
							"value": "POST",
							"label": "POST"
						},
						{
							"value": "PUT",
							"label": "PUT"
						}
					]
				},
				{
					"type": "select",
					"id": "config_mode",
					"label": "CORS Request Mode",
					"default": "cors",
					"options": [
						{
							"value": "cors",
							"label": "cors"
						},
						{
							"value": "no-cors",
							"label": "no-cors"
						}
					]
				},
				{
					"id": "config_headers",
					"label": "Request Headers",
					"type": "liquid"
				},
				{
					"type": "header",
					"content": "Configuration Body Settings"
				},
				{
					"type": "select",
					"id": "config_bodyPart_recommendationType",
					"label": "Request Body Recommendation Type",
					"default": "bestsellers",
					"options": [
						{
							"value": "bestsellers",
							"label": "Bestsellers"
						},
						{
							"value": "newest-arrivals",
							"label": "Newest Arrivals"
						},
						{
							"value": "frequently-bought-together",
							"label": "Frequently Bought Together"
						},
						{
							"value": "trending-products",
							"label": "Trending Products"
						},
						{
							"value": "recently-viewed",
							"label": "Recently Viewed"
						},
						{
							"value": "most-viewed",
							"label": "Most Viewed"
						},
						{
							"value": "related-items",
							"label": "Related Items"
						}
					]
				},
				{
					"type": "select",
					"id": "config_bodyPart_modelType",
					"label": "Request Body model Type",
					"default": "none",
					"options": [
						{
							"value": "none",
							"label": "None"
						},
						{
							"value": "FBT",
							"label": "FBT"
						},
						{
							"value": "Complementary",
							"label": "Complementary"
						},
						{
							"value": "Alternative",
							"label": "Alternative"
						},
						{
							"value": "AIRelated",
							"label": "AIRelated"
						}
					]
				},
				{
					"type": "select",
					"id": "config_bodyPart_calculatedBasedOn",
					"label": "Result Calculated BasedOn",
					"default": "none",
					"options": [
						{
							"value": "none",
							"label": "None"
						},
						{
							"value": "view-events",
							"label": "View Events"
						},
						{
							"value": "purchase-events",
							"label": "Purchase Events"
						}
					]
				},
				{
					"type": "select",
					"id": "config_bodyPart_rangeOfTime",
					"label": "Result Calculated rangeOfTime",
					"default": "none",
					"options": [
						{
							"value": "none",
							"label": "None"
						},
						{
							"value": "1-day",
							"label": "1-day"
						},
						{
							"value": "2-day",
							"label": "2-day"
						},
						{
							"value": "3-day",
							"label": "3-day"
						},
						{
							"value": "4-day",
							"label": "4-day"
						},
						{
							"value": "5-day",
							"label": "5-day"
						},
						{
							"value": "6-day",
							"label": "6-day"
						},
						{
							"value": "7-day",
							"label": "7-day"
						}
					]
				},
				{
					"type": "number",
					"id": "config_bodyPart_limit",
					"label": "Request Body Result limit",
					"default": 16
				},
				{
					"type": "liquid",
					"id": "config_bodyPart_productIds",
					"label": "Related Products Id's",
					"default": "none"
				},
				{
					"id": "map",
					"label": "Response Data Map",
					"type": "liquid",
					"info": "Liquid with JavaScript Template Literals in Util.map()"
				},
				{
					"type": "header",
					"content": "RuleBased Settings"
				},
				{
					"type": "checkbox",
					"id": "config_bodyPart_ruleBased",
					"label": "Enable Rule Based Queries",
					"default": false
				},
				{
					"type": "select",
					"id": "config_bodyPart_type",
					"label": "Type of result",
					"default": "sameCollection",
					"options": [
						{
							"value": "sameCollection",
							"label": "Same Collection"
						},
						{
							"value": "sameProductType",
							"label": "Same Product Type"
						},
						{
							"value": "sameVendor",
							"label": "Same Vendor"
						},
						{
							"value": "sameTags",
							"label": "Same Tags"
						},
						{
							"value": "sameMetafield",
							"label": "Same Metafield"
						},
						{
							"value": "sameProductCategory",
							"label": "Same Product Category"
						}
					]
				},
				{
					"type": "select",
					"id": "config_bodyPart_status",
					"label": "Product Status",
					"default": "true",
					"options": [
						{
							"value": "true",
							"label": "True"
						},
						{
							"value": "false",
							"label": "False"
						}
					]
				}
			]
		},
		{
			"name": "Bloomreach Recs",
			"type": "bloomreach-recs",
			"limit": 1,
			"settings": [
				{
					"label": "Recomendation ID",
					"id": "recomendation_id",
					"type": "text"
				},
				{
					"label": "Limit",
					"id": "limit",
					"type": "number",
					"default": 8
				},
				{
					"label": "Alternate Carousel Heading",
					"id": "alt_carousel_heading",
					"type": "text",
					"info": "Needed when carousel is converted to tabs"
				}
			]
		},
		{
			"type": "data-remote",
			"name": "Open Remote Product Data",
			"limit": 1,
			"settings": [
				{
					"id": "remote_url",
					"label": "Remote URL",
					"type": "liquid",
					"info": "Liquid with JavaScript Template Literals"
				},
				{
					"type": "select",
					"id": "config_method",
					"label": "Request Method",
					"default": "GET",
					"options": [
						{
							"value": "GET",
							"label": "GET"
						},
						{
							"value": "POST",
							"label": "POST"
						},
						{
							"value": "PUT",
							"label": "PUT"
						}
					]
				},
				{
					"type": "select",
					"id": "config_mode",
					"label": "CORS Request Mode",
					"default": "cors",
					"options": [
						{
							"value": "cors",
							"label": "cors"
						},
						{
							"value": "no-cors",
							"label": "no-cors"
						}
					]
				},
				{
					"id": "config_headers",
					"label": "Request Headers",
					"type": "liquid"
				},
				{
					"id": "config_body",
					"label": "Request Body",
					"type": "liquid",
					"info": "Liquid with JavaScript Template Literals"
				},
				{
					"id": "map",
					"label": "Response Data Map",
					"type": "liquid",
					"info": "Liquid with JavaScript Template Literals in Util.map()"
				}
			]
		},
		{
			"name": "Content Item",
			"type": "content-item",
			"settings": [
				{
					"type": "text",
					"id": "product_position",
					"label": "Position among Products"
				},
				{
					"type": "paragraph",
					"content": "@include ContentItem"
				},
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:item_class, @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Display Settings"
				},
				{
					"type": "select",
					"id": "item_class_visibility",
					"label": "@global: Display Visibility",
					"options": [
						{
							"value": "",
							"label": "Mobile & Desktop"
						},
						{
							"value": "lg:hidden",
							"label": "Mobile"
						},
						{
							"value": "max-lg:hidden",
							"label": "Desktop"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:item_class, label_prefix:Item, default_direction:flex-col, @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Item Layout"
				},
				{
					"type": "select",
					"id": "item_class_display",
					"label": "@global: Item Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_display_desktop",
					"label": "@global: Item Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "lg:hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_direction",
					"label": "@global: Item Direction",
					"default": "flex-col",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_layout",
					"label": "@global: Item Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-middle",
							"label": "Middle (Full Width)"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_layout_spacing",
					"label": "@global: Item Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_vertical_padding",
					"label": "@global: Item Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_horizontal_padding",
					"label": "@global: Item Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_gap",
					"label": "@global: Item Spacing Gap",
					"options": [
						{
							"value": "@include Spacing prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "gap-0",
							"label": "@global: None"
						},
						{
							"value": "gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "gap-md",
							"label": "@global: MD"
						},
						{
							"value": "gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "gap-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_vertical_padding_desktop",
					"label": "@global: Item Vertical Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:py",
							"label": "Inclusion"
						},
						{
							"value": "lg:py-0",
							"label": "@global: None"
						},
						{
							"value": "lg:py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:py-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:py-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:py-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:py-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:py-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_horizontal_padding_desktop",
					"label": "@global: Item Horizontal Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:px",
							"label": "Inclusion"
						},
						{
							"value": "lg:px-0",
							"label": "@global: None"
						},
						{
							"value": "lg:px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:px-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:px-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:px-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:px-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:px-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_gap_desktop",
					"label": "@global: Item Spacing Gap Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "lg:gap-0",
							"label": "@global: None"
						},
						{
							"value": "lg:gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:gap-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:gap-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "color_background",
					"id": "item_style_background",
					"label": "@global: Item Background Color"
				},
				{
					"type": "color_background",
					"id": "content_style_background",
					"label": "@global: Content Background Color"
				},
				{
					"type": "header",
					"content": "@global: Content Interactivity"
				},
				{
					"type": "url",
					"id": "link",
					"label": "@global: Link"
				},
				{
					"type": "liquid",
					"id": "liquid_link",
					"label": "@global: Link (Liquid)",
					"info": "Replaces the basic Link setting."
				},
				{
					"type": "paragraph",
					"content": "@include Break, label:🅜🅔🅓🅘🅐, @extends:ContentItem"
				},
				{
					"type": "paragraph",
					"content": "@global: ▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀\n🅜🅔🅓🅘🅐\n▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀"
				},
				{
					"type": "header",
					"content": "@global: Media"
				},
				{
					"type": "image_picker",
					"label": "@global: Image",
					"id": "image"
				},
				{
					"type": "image_picker",
					"label": "@global: Image (Desktop)",
					"id": "image_desktop"
				},
				{
					"type": "select",
					"label": "@global: Image Position",
					"id": "image_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full w-full object-cover"
						}
					]
				},
				{
					"type": "select",
					"label": "@global: Loading",
					"id": "image_loading",
					"options": [
						{
							"label": "Lazy",
							"value": "lazy"
						},
						{
							"label": "Eager",
							"value": "eager"
						}
					],
					"default": "eager"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:media_class, label_prefix:Media , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Media Width Settings"
				},
				{
					"type": "select",
					"id": "media_class_width",
					"label": "@global: Media Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						},
						{
							"value": "col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "col-span-4",
							"label": "4 Grid Columns"
						}
					]
				},
				{
					"type": "select",
					"id": "media_class_width_desktop",
					"label": "@global: Media Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						},
						{
							"value": "lg:col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Grid Columns"
						},
						{
							"value": "lg:col-span-5",
							"label": "5 Grid Columns"
						},
						{
							"value": "lg:col-span-6",
							"label": "6 Grid Columns"
						},
						{
							"value": "container",
							"label": "Standard Container"
						},
						{
							"value": "container container--narrow",
							"label": "Narrow Container"
						},
						{
							"value": "container container--wide",
							"label": "Wide Container"
						},
						{
							"value": "container--product",
							"label": "Product Container"
						}
					]
				},
				{
					"type": "text",
					"label": "@global: Video",
					"id": "video"
				},
				{
					"type": "text",
					"label": "@global: Video For Mobile",
					"id": "videomobile"
				},
				{
					"type": "select",
					"label": "@global: Video Position",
					"id": "video_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full"
						}
					]
				},
				{
					"type": "select",
					"label": "@global: Video Position For Mobile",
					"id": "videomobile_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full"
						}
					]
				},
				{
					"type": "image_picker",
					"label": "@global: Video Poster For Desktop",
					"id": "posterDesktop"
				},
				{
					"type": "image_picker",
					"label": "@global: Video Poster for Mobile",
					"id": "posterMobile"
				},
				{
					"type": "checkbox",
					"label": "@global: Autoplay",
					"id": "video_attr_autoplay",
					"default": true
				},
				{
					"type": "checkbox",
					"label": "@global: Muted",
					"id": "video_attr_muted",
					"default": true
				},
				{
					"type": "checkbox",
					"label": "@global: Loop",
					"id": "video_attr_loop",
					"default": true
				},
				{
					"type": "checkbox",
					"label": "@global: Controls",
					"id": "video_attr_controls",
					"default": false
				},
				{
					"type": "checkbox",
					"label": "@global: Playsinline",
					"id": "video_attr_playsinline",
					"default": true
				},
				{
					"type": "header",
					"content": "@global: Video Defer Settings"
				},
				{
					"type": "select",
					"id": "loading_method",
					"label": "@global: Video Loading Method",
					"options": [
						{
							"value": "immediate",
							"label": "Immediate"
						},
						{
							"value": "scroll",
							"label": "On Scroll"
						},
						{
							"value": "hover",
							"label": "On Hover"
						},
						{
							"value": "time",
							"label": "After Delay"
						}
					],
					"default": "scroll"
				},
				{
					"type": "range",
					"id": "loading_delay",
					"label": "@global: Loading Delay (seconds)",
					"min": 0,
					"max": 10,
					"step": 1,
					"default": 2,
					"info": "Only applies when loading method is After Delay"
				},
				{
					"type": "paragraph",
					"content": "@include Break, label:🅒🅞🅝🅣🅔🅝🅣 🅛🅐🅨🅞🅤🅣, @extends:ContentItem"
				},
				{
					"type": "paragraph",
					"content": "@global: ▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀\n🅒🅞🅝🅣🅔🅝🅣 🅛🅐🅨🅞🅤🅣\n▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:content_class, label_prefix:Content , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Content Width Settings"
				},
				{
					"type": "select",
					"id": "content_class_width",
					"label": "@global: Content Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						},
						{
							"value": "col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "col-span-4",
							"label": "4 Grid Columns"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_width_desktop",
					"label": "@global: Content Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						},
						{
							"value": "lg:col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Grid Columns"
						},
						{
							"value": "lg:col-span-5",
							"label": "5 Grid Columns"
						},
						{
							"value": "lg:col-span-6",
							"label": "6 Grid Columns"
						},
						{
							"value": "container",
							"label": "Standard Container"
						},
						{
							"value": "container container--narrow",
							"label": "Narrow Container"
						},
						{
							"value": "container container--wide",
							"label": "Wide Container"
						},
						{
							"value": "container--product",
							"label": "Product Container"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:content_class, label_prefix:Content, @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Content Layout"
				},
				{
					"type": "select",
					"id": "content_class_display",
					"label": "@global: Content Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_display_desktop",
					"label": "@global: Content Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "lg:hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_direction",
					"label": "@global: Content Direction",
					"default": "flex-row",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_layout",
					"label": "@global: Content Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-middle",
							"label": "Middle (Full Width)"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_layout_spacing",
					"label": "@global: Content Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_vertical_padding",
					"label": "@global: Content Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_horizontal_padding",
					"label": "@global: Content Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_gap",
					"label": "@global: Content Spacing Gap",
					"options": [
						{
							"value": "@include Spacing prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "gap-0",
							"label": "@global: None"
						},
						{
							"value": "gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "gap-md",
							"label": "@global: MD"
						},
						{
							"value": "gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "gap-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_vertical_padding_desktop",
					"label": "@global: Content Vertical Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:py",
							"label": "Inclusion"
						},
						{
							"value": "lg:py-0",
							"label": "@global: None"
						},
						{
							"value": "lg:py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:py-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:py-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:py-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:py-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:py-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_horizontal_padding_desktop",
					"label": "@global: Content Horizontal Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:px",
							"label": "Inclusion"
						},
						{
							"value": "lg:px-0",
							"label": "@global: None"
						},
						{
							"value": "lg:px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:px-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:px-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:px-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:px-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:px-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_gap_desktop",
					"label": "@global: Content Spacing Gap Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "lg:gap-0",
							"label": "@global: None"
						},
						{
							"value": "lg:gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:gap-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:gap-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Aspect, id_prefix:content_class, label_prefix:Content , @extends:ContentItem"
				},
				{
					"type": "select",
					"id": "content_class_aspect",
					"label": "@global: Content Aspect Ratio",
					"options": [
						{
							"value": "aspect-auto",
							"label": "Auto"
						},
						{
							"value": "aspect-[2/1]",
							"label": "2:1"
						},
						{
							"value": "aspect-[16/9]",
							"label": "16:9"
						},
						{
							"value": "aspect-[4/3]",
							"label": "4:3"
						},
						{
							"value": "aspect-[1/1]",
							"label": "1:1"
						},
						{
							"value": "aspect-[3/1]",
							"label": "3:1"
						},
						{
							"value": "aspect-[3/4]",
							"label": "3:4"
						},
						{
							"value": "aspect-[8/30]",
							"label": "8:30"
						},
						{
							"value": "aspect-[9/16]",
							"label": "9:16"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_aspect_desktop",
					"label": "@global: Content Aspect Ratio Desktop",
					"options": [
						{
							"value": "lg:aspect-auto",
							"label": "Auto"
						},
						{
							"value": "lg:aspect-[2/1]",
							"label": "2:1"
						},
						{
							"value": "lg:aspect-[16/9]",
							"label": "16:9"
						},
						{
							"value": "lg:aspect-[4/3]",
							"label": "4:3"
						},
						{
							"value": "lg:aspect-[1/1]",
							"label": "1:1"
						},
						{
							"value": "lg:aspect-[3/1]",
							"label": "3:1"
						},
						{
							"value": "lg:aspect-[3/4]",
							"label": "3:4"
						},
						{
							"value": "lg:aspect-[8/30]",
							"label": "8:30"
						},
						{
							"value": "lg:aspect-[9/16]",
							"label": "9:16"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Break, label:🅣🅔🅧🅣 🅢🅣🅐🅒🅚, @extends:ContentItem"
				},
				{
					"type": "paragraph",
					"content": "@global: ▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀\n🅣🅔🅧🅣 🅢🅣🅐🅒🅚\n▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀"
				},
				{
					"type": "header",
					"content": "@global: Text Stack"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:text_stack_class, label_prefix:Text Stack , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Text Stack Width Settings"
				},
				{
					"type": "select",
					"id": "text_stack_class_width",
					"label": "@global: Text Stack Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						},
						{
							"value": "col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "col-span-4",
							"label": "4 Grid Columns"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_width_desktop",
					"label": "@global: Text Stack Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						},
						{
							"value": "lg:col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Grid Columns"
						},
						{
							"value": "lg:col-span-5",
							"label": "5 Grid Columns"
						},
						{
							"value": "lg:col-span-6",
							"label": "6 Grid Columns"
						},
						{
							"value": "container",
							"label": "Standard Container"
						},
						{
							"value": "container container--narrow",
							"label": "Narrow Container"
						},
						{
							"value": "container container--wide",
							"label": "Wide Container"
						},
						{
							"value": "container--product",
							"label": "Product Container"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:text_stack_class, label_prefix:Text, @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Text Layout"
				},
				{
					"type": "select",
					"id": "text_stack_class_display",
					"label": "@global: Text Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_display_desktop",
					"label": "@global: Text Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "lg:hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_direction",
					"label": "@global: Text Direction",
					"default": "flex-row",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_layout",
					"label": "@global: Text Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-middle",
							"label": "Middle (Full Width)"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_layout_spacing",
					"label": "@global: Text Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_vertical_padding",
					"label": "@global: Text Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_horizontal_padding",
					"label": "@global: Text Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_gap",
					"label": "@global: Text Spacing Gap",
					"options": [
						{
							"value": "@include Spacing prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "gap-0",
							"label": "@global: None"
						},
						{
							"value": "gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "gap-md",
							"label": "@global: MD"
						},
						{
							"value": "gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "gap-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_vertical_padding_desktop",
					"label": "@global: Text Vertical Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:py",
							"label": "Inclusion"
						},
						{
							"value": "lg:py-0",
							"label": "@global: None"
						},
						{
							"value": "lg:py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:py-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:py-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:py-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:py-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:py-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_horizontal_padding_desktop",
					"label": "@global: Text Horizontal Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:px",
							"label": "Inclusion"
						},
						{
							"value": "lg:px-0",
							"label": "@global: None"
						},
						{
							"value": "lg:px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:px-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:px-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:px-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:px-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:px-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_gap_desktop",
					"label": "@global: Text Spacing Gap Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "lg:gap-0",
							"label": "@global: None"
						},
						{
							"value": "lg:gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:gap-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:gap-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "image_picker",
					"label": "@global: Title Image",
					"id": "title_image"
				},
				{
					"type": "liquid",
					"id": "svg",
					"label": "@global: SVG"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:title_image_class, label_prefix:Title Image / SVG , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Title Image / SVG Width Settings"
				},
				{
					"type": "select",
					"id": "title_image_class_width",
					"label": "@global: Title Image / SVG Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						},
						{
							"value": "col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "col-span-4",
							"label": "4 Grid Columns"
						}
					]
				},
				{
					"type": "select",
					"id": "title_image_class_width_desktop",
					"label": "@global: Title Image / SVG Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						},
						{
							"value": "lg:col-span-1",
							"label": "1 Grid Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Grid Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Grid Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Grid Columns"
						},
						{
							"value": "lg:col-span-5",
							"label": "5 Grid Columns"
						},
						{
							"value": "lg:col-span-6",
							"label": "6 Grid Columns"
						},
						{
							"value": "container",
							"label": "Standard Container"
						},
						{
							"value": "container container--narrow",
							"label": "Narrow Container"
						},
						{
							"value": "container container--wide",
							"label": "Wide Container"
						},
						{
							"value": "container--product",
							"label": "Product Container"
						}
					]
				},
				{
					"type": "radio",
					"label": "@global: Text Justification",
					"id": "text_stack_class",
					"default": "text-center",
					"options": [
						{
							"label": "←",
							"value": "text-left"
						},
						{
							"label": "↔",
							"value": "text-center"
						},
						{
							"label": "→",
							"value": "text-right"
						}
					]
				},
				{
					"type": "color",
					"id": "content_style_color",
					"label": "@global: Text Color"
				},
				{
					"type": "paragraph",
					"content": "@include Text, id_prefix:text_item_1, label_prefix:Text 1 , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Text 1 Settings"
				},
				{
					"type": "text",
					"id": "text_item_1_text",
					"label": "@global: Text 1 Text"
				},
				{
					"type": "liquid",
					"id": "text_item_1_liquid",
					"label": "@global: Text 1 Text (Liquid)"
				},
				{
					"type": "liquid",
					"id": "text_item_1_attr_x_text",
					"label": "@global: Text 1 Dynamic Text (Alpine)"
				},
				{
					"type": "select",
					"id": "text_item_1_element",
					"label": "@global: Text 1 Element",
					"default": "p",
					"options": [
						{
							"value": "@include TextElement",
							"label": "Inclusion"
						},
						{
							"value": "h1",
							"label": "@global:  Heading 1"
						},
						{
							"value": "h2",
							"label": "@global:  Heading 2"
						},
						{
							"value": "h3",
							"label": "@global:  Heading 3"
						},
						{
							"value": "h4",
							"label": "@global:  Heading 4"
						},
						{
							"value": "h5",
							"label": "@global:  Heading 5"
						},
						{
							"value": "p",
							"label": "@global:  Paragraph"
						},
						{
							"value": "div",
							"label": "@global:  Div"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_1_class_type_style",
					"label": "@global: Text 1 Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_1_class_type_size",
					"label": "@global: Text 1 Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_item_1_style_color",
					"label": "@global: Text 1 Color"
				},
				{
					"type": "paragraph",
					"content": "@include Text, id_prefix:text_item_2, label_prefix:Text 2 , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Text 2 Settings"
				},
				{
					"type": "text",
					"id": "text_item_2_text",
					"label": "@global: Text 2 Text"
				},
				{
					"type": "liquid",
					"id": "text_item_2_liquid",
					"label": "@global: Text 2 Text (Liquid)"
				},
				{
					"type": "liquid",
					"id": "text_item_2_attr_x_text",
					"label": "@global: Text 2 Dynamic Text (Alpine)"
				},
				{
					"type": "select",
					"id": "text_item_2_element",
					"label": "@global: Text 2 Element",
					"default": "p",
					"options": [
						{
							"value": "@include TextElement",
							"label": "Inclusion"
						},
						{
							"value": "h1",
							"label": "@global:  Heading 1"
						},
						{
							"value": "h2",
							"label": "@global:  Heading 2"
						},
						{
							"value": "h3",
							"label": "@global:  Heading 3"
						},
						{
							"value": "h4",
							"label": "@global:  Heading 4"
						},
						{
							"value": "h5",
							"label": "@global:  Heading 5"
						},
						{
							"value": "p",
							"label": "@global:  Paragraph"
						},
						{
							"value": "div",
							"label": "@global:  Div"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_2_class_type_style",
					"label": "@global: Text 2 Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_2_class_type_size",
					"label": "@global: Text 2 Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_item_2_style_color",
					"label": "@global: Text 2 Color"
				},
				{
					"type": "paragraph",
					"content": "@include Text, id_prefix:text_item_3, label_prefix:Text 3 , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Text 3 Settings"
				},
				{
					"type": "text",
					"id": "text_item_3_text",
					"label": "@global: Text 3 Text"
				},
				{
					"type": "liquid",
					"id": "text_item_3_liquid",
					"label": "@global: Text 3 Text (Liquid)"
				},
				{
					"type": "liquid",
					"id": "text_item_3_attr_x_text",
					"label": "@global: Text 3 Dynamic Text (Alpine)"
				},
				{
					"type": "select",
					"id": "text_item_3_element",
					"label": "@global: Text 3 Element",
					"default": "p",
					"options": [
						{
							"value": "@include TextElement",
							"label": "Inclusion"
						},
						{
							"value": "h1",
							"label": "@global:  Heading 1"
						},
						{
							"value": "h2",
							"label": "@global:  Heading 2"
						},
						{
							"value": "h3",
							"label": "@global:  Heading 3"
						},
						{
							"value": "h4",
							"label": "@global:  Heading 4"
						},
						{
							"value": "h5",
							"label": "@global:  Heading 5"
						},
						{
							"value": "p",
							"label": "@global:  Paragraph"
						},
						{
							"value": "div",
							"label": "@global:  Div"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_3_class_type_style",
					"label": "@global: Text 3 Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_3_class_type_size",
					"label": "@global: Text 3 Type Size",
					"options": [
						{
							"value": "@include TypeSize",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Default"
						},
						{
							"value": "type--sm",
							"label": "@global:  Smaller"
						},
						{
							"value": "type--lg",
							"label": "@global:  Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_item_3_style_color",
					"label": "@global: Text 3 Color"
				},
				{
					"type": "header",
					"content": "@global: Custom Liquid"
				},
				{
					"type": "liquid",
					"id": "liquid",
					"label": "@global: Custom Liquid/HTML"
				},
				{
					"type": "paragraph",
					"content": "@include Break, label:🅑🅤🅣🅣🅞🅝 🅢🅔🅣, @extends:ContentItem"
				},
				{
					"type": "paragraph",
					"content": "@global: ▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀\n🅑🅤🅣🅣🅞🅝 🅢🅔🅣\n▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀"
				},
				{
					"type": "header",
					"content": "@global: Button Settings"
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout id_prefix:buttons_, label_prefix:Buttons, @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Buttons Layout"
				},
				{
					"type": "select",
					"id": "buttons__display",
					"label": "@global: Buttons Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__display_desktop",
					"label": "@global: Buttons Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						},
						{
							"value": "lg:hidden",
							"label": "Hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__direction",
					"label": "@global: Buttons Direction",
					"default": "flex-row",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__layout",
					"label": "@global: Buttons Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-middle",
							"label": "Middle (Full Width)"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__layout_spacing",
					"label": "@global: Buttons Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__vertical_padding",
					"label": "@global: Buttons Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__horizontal_padding",
					"label": "@global: Buttons Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__gap",
					"label": "@global: Buttons Spacing Gap",
					"options": [
						{
							"value": "@include Spacing prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "gap-0",
							"label": "@global: None"
						},
						{
							"value": "gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "gap-md",
							"label": "@global: MD"
						},
						{
							"value": "gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "gap-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__vertical_padding_desktop",
					"label": "@global: Buttons Vertical Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:py",
							"label": "Inclusion"
						},
						{
							"value": "lg:py-0",
							"label": "@global: None"
						},
						{
							"value": "lg:py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:py-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:py-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:py-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:py-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:py-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__horizontal_padding_desktop",
					"label": "@global: Buttons Horizontal Padding Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:px",
							"label": "Inclusion"
						},
						{
							"value": "lg:px-0",
							"label": "@global: None"
						},
						{
							"value": "lg:px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:px-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:px-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:px-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:px-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:px-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__gap_desktop",
					"label": "@global: Buttons Spacing Gap Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:gap",
							"label": "Inclusion"
						},
						{
							"value": "lg:gap-0",
							"label": "@global: None"
						},
						{
							"value": "lg:gap-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:gap-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:gap-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:gap-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Button id_prefix:button_1, label_prefix:Button 1 , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Button 1 Settings"
				},
				{
					"type": "text",
					"id": "button_1_text",
					"label": "@global: Button 1 Text"
				},
				{
					"type": "text",
					"id": "button_1_leading_icon",
					"label": "@global: Leading Icon"
				},
				{
					"type": "text",
					"id": "button_1_trailing_icon",
					"label": "@global: Trailing Icon"
				},
				{
					"type": "url",
					"id": "button_1_link",
					"label": "@global: Button 1 Link"
				},
				{
					"type": "liquid",
					"id": "button_1_liquid_link",
					"label": "@global: Button 1 Link (Liquid)",
					"info": "Replaces the basic Link setting."
				},
				{
					"type": "liquid",
					"id": "button_1_onclick",
					"label": "@global: Button 1 On Click"
				},
				{
					"type": "select",
					"id": "button_1_class_style",
					"label": "@global: Button 1 Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "select",
					"id": "button_1_class_size",
					"label": "@global: Button 1 Size",
					"options": [
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Button id_prefix:button_2, label_prefix:Button 2 , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Button 2 Settings"
				},
				{
					"type": "text",
					"id": "button_2_text",
					"label": "@global: Button 2 Text"
				},
				{
					"type": "text",
					"id": "button_2_leading_icon",
					"label": "@global: Leading Icon"
				},
				{
					"type": "text",
					"id": "button_2_trailing_icon",
					"label": "@global: Trailing Icon"
				},
				{
					"type": "url",
					"id": "button_2_link",
					"label": "@global: Button 2 Link"
				},
				{
					"type": "liquid",
					"id": "button_2_liquid_link",
					"label": "@global: Button 2 Link (Liquid)",
					"info": "Replaces the basic Link setting."
				},
				{
					"type": "liquid",
					"id": "button_2_onclick",
					"label": "@global: Button 2 On Click"
				},
				{
					"type": "select",
					"id": "button_2_class_style",
					"label": "@global: Button 2 Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "select",
					"id": "button_2_class_size",
					"label": "@global: Button 2 Size",
					"options": [
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include Button id_prefix:button_3, label_prefix:Button 3 , @extends:ContentItem"
				},
				{
					"type": "header",
					"content": "@global: Button 3 Settings"
				},
				{
					"type": "text",
					"id": "button_3_text",
					"label": "@global: Button 3 Text"
				},
				{
					"type": "text",
					"id": "button_3_leading_icon",
					"label": "@global: Leading Icon"
				},
				{
					"type": "text",
					"id": "button_3_trailing_icon",
					"label": "@global: Trailing Icon"
				},
				{
					"type": "url",
					"id": "button_3_link",
					"label": "@global: Button 3 Link"
				},
				{
					"type": "liquid",
					"id": "button_3_liquid_link",
					"label": "@global: Button 3 Link (Liquid)",
					"info": "Replaces the basic Link setting."
				},
				{
					"type": "liquid",
					"id": "button_3_onclick",
					"label": "@global: Button 3 On Click"
				},
				{
					"type": "select",
					"id": "button_3_class_style",
					"label": "@global: Button 3 Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "select",
					"id": "button_3_class_size",
					"label": "@global: Button 3 Size",
					"options": [
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				}
			]
		},
		{
			"name": "Overlay",
			"type": "overlay",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include OverlayItem"
				},
				{
					"type": "paragraph",
					"content": "@include Position id_prefix:overlay_class_, @extends:OverlayItem"
				},
				{
					"type": "header",
					"content": "@global: Position Settings"
				},
				{
					"type": "range",
					"id": "pos_x",
					"label": "@global: Position X",
					"min": 0,
					"max": 100,
					"unit": "%",
					"default": 0
				},
				{
					"type": "range",
					"id": "pos_y",
					"label": "@global: Position Y",
					"min": 0,
					"max": 100,
					"unit": "%",
					"default": 0
				},
				{
					"type": "select",
					"id": "content_origin",
					"label": "@global: Content Origin",
					"options": [
						{
							"label": "Top Left",
							"value": "translate(0,0)"
						},
						{
							"label": "Top Center",
							"value": "translate(-50%,0)"
						},
						{
							"label": "Top Right",
							"value": "translate(-100%,0)"
						},
						{
							"label": "Middle Left",
							"value": "translate(0,-50%)"
						},
						{
							"label": "Middle Center",
							"value": "translate(-50%,-50%)"
						},
						{
							"label": "Middle Right",
							"value": "translate(-100%,-50%)"
						},
						{
							"label": "Bottom Left",
							"value": "translate(0,-100%)"
						},
						{
							"label": "Bottom Center",
							"value": "translate(-50%,-100%)"
						},
						{
							"label": "Bottom Right",
							"value": "translate(-100%,-100%)"
						}
					]
				},
				{
					"type": "range",
					"id": "pos_x_mobile",
					"label": "@global: Position X (mobile)",
					"min": 0,
					"max": 100,
					"unit": "%",
					"default": 0
				},
				{
					"type": "range",
					"id": "pos_y_mobile",
					"label": "@global: Position Y (mobile)",
					"min": 0,
					"max": 100,
					"unit": "%",
					"default": 0
				},
				{
					"type": "select",
					"id": "content_origin_mobile",
					"label": "@global: Content Origin (mobile)",
					"options": [
						{
							"label": "Top Left",
							"value": "translate(0,0)"
						},
						{
							"label": "Top Center",
							"value": "translate(-50%,0)"
						},
						{
							"label": "Top Right",
							"value": "translate(-100%,0)"
						},
						{
							"label": "Middle Left",
							"value": "translate(0,-50%)"
						},
						{
							"label": "Middle Center",
							"value": "translate(-50%,-50%)"
						},
						{
							"label": "Middle Right",
							"value": "translate(-100%,-50%)"
						},
						{
							"label": "Bottom Left",
							"value": "translate(0,-100%)"
						},
						{
							"label": "Bottom Center",
							"value": "translate(-50%,-100%)"
						},
						{
							"label": "Bottom Right",
							"value": "translate(-100%,-100%)"
						}
					]
				},
				{
					"type": "select",
					"id": "overlay_class_width",
					"label": "@global: Width",
					"options": [
						{
							"value": "@include SizePercent prop:w",
							"label": "Inclusion"
						},
						{
							"value": "w-auto",
							"label": "@global: Auto"
						},
						{
							"value": "w-0",
							"label": "@global: 0%"
						},
						{
							"value": "w-[10%]",
							"label": "@global: 10%"
						},
						{
							"value": "w-1/5",
							"label": "@global: 20%"
						},
						{
							"value": "w-3/10",
							"label": "@global: 30%"
						},
						{
							"value": "w-2/5",
							"label": "@global: 40%"
						},
						{
							"value": "w-1/2",
							"label": "@global: 50%"
						},
						{
							"value": "w-3/5",
							"label": "@global: 60%"
						},
						{
							"value": "w-7/10",
							"label": "@global: 70%"
						},
						{
							"value": "w-4/5",
							"label": "@global: 80%"
						},
						{
							"value": "w-9/10",
							"label": "@global: 90%"
						},
						{
							"value": "w-full",
							"label": "@global: 100%"
						}
					]
				},
				{
					"type": "select",
					"id": "overlay_class_height",
					"label": "@global: Height",
					"options": [
						{
							"value": "@include SizePercent prop:h",
							"label": "Inclusion"
						},
						{
							"value": "h-auto",
							"label": "@global: Auto"
						},
						{
							"value": "h-0",
							"label": "@global: 0%"
						},
						{
							"value": "h-[10%]",
							"label": "@global: 10%"
						},
						{
							"value": "h-1/5",
							"label": "@global: 20%"
						},
						{
							"value": "h-3/10",
							"label": "@global: 30%"
						},
						{
							"value": "h-2/5",
							"label": "@global: 40%"
						},
						{
							"value": "h-1/2",
							"label": "@global: 50%"
						},
						{
							"value": "h-3/5",
							"label": "@global: 60%"
						},
						{
							"value": "h-7/10",
							"label": "@global: 70%"
						},
						{
							"value": "h-4/5",
							"label": "@global: 80%"
						},
						{
							"value": "h-9/10",
							"label": "@global: 90%"
						},
						{
							"value": "h-full",
							"label": "@global: 100%"
						}
					]
				},
				{
					"type": "select",
					"id": "overlay_class_width_desktop",
					"label": "@global: Width (Desktop)",
					"options": [
						{
							"value": "@include SizePercent prop:w, variant:lg",
							"label": "Inclusion"
						},
						{
							"value": "lg:w-auto",
							"label": "@global: Auto"
						},
						{
							"value": "lg:w-0",
							"label": "@global: 0%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "@global: 10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "@global: 20%"
						},
						{
							"value": "lg:w-3/10",
							"label": "@global: 30%"
						},
						{
							"value": "lg:w-2/5",
							"label": "@global: 40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "@global: 50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "@global: 60%"
						},
						{
							"value": "lg:w-7/10",
							"label": "@global: 70%"
						},
						{
							"value": "lg:w-4/5",
							"label": "@global: 80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "@global: 90%"
						},
						{
							"value": "lg:w-full",
							"label": "@global: 100%"
						}
					]
				},
				{
					"type": "select",
					"id": "overlay_class_height_desktop",
					"label": "@global: Height (Desktop)",
					"options": [
						{
							"value": "@include SizePercent prop:h, variant:lg",
							"label": "Inclusion"
						},
						{
							"value": "lg:h-auto",
							"label": "@global: Auto"
						},
						{
							"value": "lg:h-0",
							"label": "@global: 0%"
						},
						{
							"value": "lg:h-[10%]",
							"label": "@global: 10%"
						},
						{
							"value": "lg:h-1/5",
							"label": "@global: 20%"
						},
						{
							"value": "lg:h-3/10",
							"label": "@global: 30%"
						},
						{
							"value": "lg:h-2/5",
							"label": "@global: 40%"
						},
						{
							"value": "lg:h-1/2",
							"label": "@global: 50%"
						},
						{
							"value": "lg:h-3/5",
							"label": "@global: 60%"
						},
						{
							"value": "lg:h-7/10",
							"label": "@global: 70%"
						},
						{
							"value": "lg:h-4/5",
							"label": "@global: 80%"
						},
						{
							"value": "lg:h-9/10",
							"label": "@global: 90%"
						},
						{
							"value": "lg:h-full",
							"label": "@global: 100%"
						}
					]
				}
			]
		},
		{
			"name": "Hotspot",
			"type": "hotspot",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include Hotspot"
				},
				{
					"type": "paragraph",
					"content": "@include Position id_prefix:overlay_class_, @extends:Hotspot"
				},
				{
					"type": "header",
					"content": "@global: Position Settings"
				},
				{
					"type": "range",
					"id": "pos_x",
					"label": "@global: Position X",
					"min": 0,
					"max": 100,
					"unit": "%",
					"default": 0
				},
				{
					"type": "range",
					"id": "pos_y",
					"label": "@global: Position Y",
					"min": 0,
					"max": 100,
					"unit": "%",
					"default": 0
				},
				{
					"type": "select",
					"id": "content_origin",
					"label": "@global: Content Origin",
					"options": [
						{
							"label": "Top Left",
							"value": "translate(0,0)"
						},
						{
							"label": "Top Center",
							"value": "translate(-50%,0)"
						},
						{
							"label": "Top Right",
							"value": "translate(-100%,0)"
						},
						{
							"label": "Middle Left",
							"value": "translate(0,-50%)"
						},
						{
							"label": "Middle Center",
							"value": "translate(-50%,-50%)"
						},
						{
							"label": "Middle Right",
							"value": "translate(-100%,-50%)"
						},
						{
							"label": "Bottom Left",
							"value": "translate(0,-100%)"
						},
						{
							"label": "Bottom Center",
							"value": "translate(-50%,-100%)"
						},
						{
							"label": "Bottom Right",
							"value": "translate(-100%,-100%)"
						}
					]
				},
				{
					"type": "range",
					"id": "pos_x_mobile",
					"label": "@global: Position X (mobile)",
					"min": 0,
					"max": 100,
					"unit": "%",
					"default": 0
				},
				{
					"type": "range",
					"id": "pos_y_mobile",
					"label": "@global: Position Y (mobile)",
					"min": 0,
					"max": 100,
					"unit": "%",
					"default": 0
				},
				{
					"type": "select",
					"id": "content_origin_mobile",
					"label": "@global: Content Origin (mobile)",
					"options": [
						{
							"label": "Top Left",
							"value": "translate(0,0)"
						},
						{
							"label": "Top Center",
							"value": "translate(-50%,0)"
						},
						{
							"label": "Top Right",
							"value": "translate(-100%,0)"
						},
						{
							"label": "Middle Left",
							"value": "translate(0,-50%)"
						},
						{
							"label": "Middle Center",
							"value": "translate(-50%,-50%)"
						},
						{
							"label": "Middle Right",
							"value": "translate(-100%,-50%)"
						},
						{
							"label": "Bottom Left",
							"value": "translate(0,-100%)"
						},
						{
							"label": "Bottom Center",
							"value": "translate(-50%,-100%)"
						},
						{
							"label": "Bottom Right",
							"value": "translate(-100%,-100%)"
						}
					]
				},
				{
					"type": "select",
					"id": "hotspot_tooltip_direction",
					"label": "@global: Tooltip Direction",
					"options": [
						{
							"value": "top",
							"label": "Top"
						},
						{
							"value": "bottom",
							"label": "Bottom"
						},
						{
							"value": "left",
							"label": "Left"
						},
						{
							"value": "right",
							"label": "Right"
						}
					]
				},
				{
					"type": "product",
					"id": "hotspot_product",
					"label": "@global: Product"
				}
			]
		},
		{
			"type": "review-item",
			"name": "Review Item",
			"settings": [
				{
					"type": "product",
					"id": "product",
					"label": "Product"
				},
				{
					"type": "image_picker",
					"id": "image",
					"label": "Image"
				},
				{
					"type": "textarea",
					"id": "review",
					"label": "Review"
				},
				{
					"type": "text",
					"id": "author",
					"label": "Author"
				}
			]
		},
		{
			"type": "article-item",
			"name": "Article Item",
			"settings": [
				{
					"type": "article",
					"id": "article",
					"label": "Article"
				},
				{
					"type": "header",
					"content": "Article Card Settings"
				},
				{
					"type": "select",
					"id": "item_class_card_layout",
					"label": "Card Layout",
					"default": "content-item__article--vertical",
					"options": [
						{
							"label": "Vertical",
							"value": "content-item__article--vertical"
						},
						{
							"label": "Horizontal",
							"value": ""
						}
					]
				}
			]
		},
		{
			"type": "blog-articles",
			"name": "Blog Articles",
			"settings": [
				{
					"type": "blog",
					"id": "blog",
					"label": "Blog"
				},
				{
					"type": "text",
					"id": "filters",
					"label": "Article Tag Filters",
					"info": "Filters articles by tags listed here (comma-separated). If carousel is loaded on article/blog post template, the tag filters each only apply to the carousel if the article itself also has the tag."
				}
			]
		},
		{
			"type": "product-item",
			"name": "Specific Products",
			"settings": [
				{
					"type": "product_list",
					"id": "product",
					"label": "Product"
				}
			]
		}
	],
	"presets": [
		{
			"name": "Content Carousel",
			"category": "Content",
			"settings": {},
			"blocks": []
		}
	]
}
{% endschema %}
