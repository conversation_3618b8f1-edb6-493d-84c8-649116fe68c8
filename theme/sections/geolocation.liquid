{% assign sites = section.blocks | map: 'settings' %}
{% render 'geolocation' sites:sites settings:section.settings %}

{% schema %}
{
  "name": "Geolocation",
  "settings": [
    {
      "type": "liquid",
      "id": "heading",
      "label": "Welcome Mat Title",
      "info":"Liquid with JavaScript Literals from geolocation Object"
    },
    {
      "type": "checkbox",
      "id": "currency_selector",
      "label": "Include Currency Selector in Welcome Mat"
    },
    {
      "type": "checkbox",
      "id": "language_selector",
      "label": "Include Language Selector in Welcome Mat"
    },
    {
      "type": "checkbox",
      "id": "site_switcher",
      "label": "Include Dynamic Site Switcher in Welcome Mat"
    },
    {
      "type": "checkbox",
      "id": "site_list",
      "label": "Include Site List in Welcome Mat"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "Change Settings"
    },
    {
        "type": "radio",
        "id": "show_flag",
        "label": "Show Flag",
        "default": "yes",
        "options": [
          {
            "value": "yes",
            "label": "Yes"
          },
          {
            "value": "no",
            "label": "No"
          }
        ]
    },
    {
      "type": "text",
      "id": "flag",
      "label": "Flag URL Path",
      "default": "https:\/\/flagcdn.com\/216x162"
    },
    {
      "type": "text",
      "id": "geoip",
      "label": "GeoIP Service URL",
      "default": "https:\/\/get.geojs.io\/v1\/ip\/country.json"
    }
  ],
  "blocks": [
    {
      "type": "site",
      "name": "Site",
      "settings": [
        {
          "type": "text",
          "id": "name",
          "label": "Site Name"
        },
        {
          "type": "checkbox",
          "id": "current",
          "label": "This Website"
        },
        {
          "type": "text",
          "id": "code",
          "label": "Region Code"
        },
        {
          "type": "text",
          "id": "countries",
          "label": "Supported Countries",
          "info":"Comma-separated list of suported country codes, * for wildcard"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Site URL"
        },
        {
          "type": "checkbox",
          "id": "redirect",
          "label": "Automatically Redirect"
        },
        {
          "type": "checkbox",
          "id": "welcome",
          "label": "Open Welcome Mat"
        }
      ]
    }
  ]
}
{% endschema %}