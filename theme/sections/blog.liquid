{{ 'section-main-blog.css' | asset_url | stylesheet_tag }}

{% paginate blog.articles by 12 %}

{% liquid

  assign the_articles = blog.articles

  if blog.handle == section.settings.all_blogs_handle

    assign inclusions = section.settings.all_blogs_inclusions | split: ','
    for category in inclusions
      if blogs[category].articles
         assign the_articles = the_articles | concat:blogs[category].articles
      endif
    endfor

  endif

%}

<div class="container py-10 flex flex-wrap">

  <main class="lg:w-3/4 w-full lg:px-10 lg:px-8">

    {% for block in section.blocks %}

      {%- case block.type -%}

        {%- when 'search' -%}

          <div class="mb-6 px-6 lg:hidden">
            
            <form action="/search" class="flex w-full relative">
              <input type="hidden" name="type" value="article">
              <input type="hidden" name="view" value="blog">
              <input type="search" name="q" class="w-full p-4 pl-12 bg-white rounded-l-md" placeholder="Search articles...">
              {% render 'icon' icon:'search' width:20 height:20 class:'-translate-y-1/2 absolute icon icon-search left-2 left-4 top-1/2 transform'%}
              <button class="bg-gray-400 hover:bg-gray-800 rounded-r-md text-center text-white w-20">Go</button>
            </form>

          </div>

      {% endcase %}

    {% endfor %}    

    <h1 class="title--primary title--page lg:px-0 px-6">
    {% if blog.handle contains 'index' %}
      {{ blog.title | split: '|' | first | escape }}
    {% else %}
      {% if current_tags %}
        {{ current_tags[0] | capitalize }} in 
      {% endif %}
      {{ blog.title | escape }}

    {% endif %}
    </h1>

    {% if blog.handle == section.settings.all_blogs_handle %}

      {% for category in inclusions %}

        <div class="flex items-start justify-between lg:px-0 px-6">
          <h2 class="title--secondary title--section">
          {{ blogs[category].title }}</h2>
          <a href="/blogs/{{category}}" class="p-2 flex items-center">
            <span>See All</span>
            {% render 'icon' icon:'arrow-right' class:'rounded-full bg-white p-1 ml-2'%}
          </a>
        </div>

        {%- for article in blogs[category].articles limit: 4 -%}
          {%- render 'article-card', article: article, show_image: true, show_date: true, show_author: true, card_classes: 'lg:mb-6 pb-6 border-b' -%}
        {% endfor %}
      {% endfor %}

    {% else %}

    {%- for article in the_articles limit: 12-%}
      
      {%- render 'article-card', article: article, show_image: true, show_date: true, show_author: true, card_classes: 'lg:mb-6 pb-6 border-b' -%}
    
    {%- endfor -%}  

    {%- if paginate.pages > 1 and blog.handle != section.settings.all_blogs_handle -%}
      {%- render 'pagination', paginate: paginate -%}
    {%- endif -%}



    {% endif %}

    

  </main>

  <aside class="lg:w-1/4 w-full lg:pr-10 lg:pl-0 px-6 pt-0 lg:pt-0">

    {% for block in section.blocks %}

      {%- case block.type -%}

        {%- when 'search' -%}

          <div class="mb-6">
            
            <form action="/search" class="flex w-full relative">
              <input type="hidden" name="type" value="article">
              <input type="hidden" name="view" value="blog">
              <input type="search" name="q" class="w-full p-4 pl-12 bg-white rounded-l-md" placeholder="Search articles...">
              {% render 'icon' icon:'search' width:20 height:20 class:'-translate-y-1/2 absolute icon icon-search left-2 left-4 top-1/2 transform'%}
              <button class="bg-gray-400 hover:bg-gray-800 rounded-r-md text-center text-white w-20">Go</button>
            </form>

          </div>

        {%- when 'tag-cloud' -%}
          {% if blog.tags.size > 0 %}
            <div class="mb-6">

              <h3 class="title--secondary title--article" style="margin-top: .5rem;">{{ block.settings.title }}</h3>
              {% for tag in blog.tags %}
              {% if block.settings.skip contains tag %}{% continue %}{% endif %}
                <a class="inline-block py-1 px-1 bg-gray-100 rounded-2 mr-1 mb-2 dim text-sm" href="{{ blog.url }}/tagged/{{ tag | handle }}">#{{ tag | handle}}</a>
              {% endfor %}

            </div>
          {% endif %}

        {%- when 'liquid' -%}
          
          <div class="mb-6">
            {% if block.settings.title != blank %}
            <h3 class="title--secondary title--article" style="margin-top: .5rem;">{{ block.settings.title }}</h3>
            {% endif %}
            <div>
              {{ block.settings.content }}
            </div>
          </div>

        {%- when 'html' -%}
          
          <div class="mb-6">
            {% if block.settings.title != blank %}
            <h3 class="title--secondary title--article" style="margin-top: .5rem;">{{ block.settings.title }}</h3>
            {% endif %}
            <div>
              {{ block.settings.content }}
            </div>
          </div>

        {%- when 'product' -%}
          
          <div class="mb-6">
            {% if block.settings.title != blank %}
            <h3 class="title--secondary title--article" style="margin-top: .5rem;">{{ block.settings.title }}</h3>
            {% endif %}
            <div>
              {% render 'product-item-liquid' classes:'no-bg' extras_classes:'px-4 no-review-count' product:all_products[block.settings.product] %}
            </div>
          </div>

        {%- when 'article' -%}
          
          <div class="mb-6">
            {% if block.settings.title != blank %}
            <h3 class="title--secondary title--article" style="margin-top: .5rem;">{{ block.settings.title }}</h3>
            {% endif %}
            <div>

              {% render 'article-card' article:articles[block.settings.article] show_image:true layout:'lg:flex-col' img_width:'lg:w-full' container_classes:'p-4' header_classes:'px-0' author:false excerpt:false tags:false comments:false %}

            </div>
          </div>

      {% endcase %}

    {% endfor %}    

  </aside>

</div>


{% endpaginate %}



{% schema %}
{
  "name": "t:sections.main-blog.name",
  "tag": "section",
  "class": "spaced-section",
  "settings": [
    {
      "type": "header",
      "content": "Blog Landing Page"
    },
    {
      "type": "text",
      "id": "all_blogs_handle",
      "label": "All Blogs Handle",
      "default": "index"
    },
    {
      "type": "textarea",
      "id": "all_blogs_inclusions",
      "label": "All Blogs Inclusions",
      "default": "health-wellness,fitness-programs,products,nutrition,workouts"
    },
    {
      "type": "header",
      "content": "t:sections.main-blog.settings.header.content"
    },
    {
      "type": "checkbox",
      "id": "show_image",
      "default": true,
      "label": "t:sections.main-blog.settings.show_image.label",
      "info": "t:sections.main-blog.settings.show_image.info"
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "default": true,
      "label": "t:sections.main-blog.settings.show_date.label"
    },
    {
      "type": "checkbox",
      "id": "show_author",
      "default": false,
      "label": "t:sections.main-blog.settings.show_author.label"
    },
    {
      "type": "paragraph",
      "content": "t:sections.main-blog.settings.paragraph.content"
    }
  ],
  "blocks": [
    {
      "type": "search",
      "name": "Search Bar",
      "limit": 2
    },
    {
      "type": "tag-cloud",
      "name": "Tag Cloud",
      "limit": 2,
      "settings": [
        {
          "id":"title",
          "label":"Title",
          "type":"text",
          "default": "Related Topics"
        },
        {
          "id":"skip",
          "label":"Tag Exclusions",
          "type":"html",
          "default":"Author: Sydney Eaton"
        }
      ]
    },
    {
      "type": "liquid",
      "name": "Custom Liquid",
      "limit": 5,
      "settings": [
        {
          "id":"title",
          "label":"Title",
          "type":"text"
        },
        {
          "id":"content",
          "label":"Content",
          "type":"liquid"
        }
      ]
    },
    {
      "type": "html",
      "name": "Custom HTML",
      "limit": 5,
      "settings": [
        {
          "id":"title",
          "label":"Title",
          "type":"text"
        },
        {
          "id":"content",
          "label":"Content",
          "type":"html"
        }
      ]
    },
    {
      "type": "product",
      "name": "Featured Product",
      "limit": 5,
      "settings": [
        {
          "id":"title",
          "label":"Title",
          "type":"text"
        },
        {
          "id":"product",
          "label":"Product",
          "type":"product"
        }
      ]
    },
    {
      "type": "article",
      "name": "Featured Article",
      "limit": 5,
      "settings": [
        {
          "id":"title",
          "label":"Title",
          "type":"text"
        },
        {
          "id":"article",
          "label":"Article",
          "type":"article"
        }
      ]
    }
  ]
}
{% endschema %}
