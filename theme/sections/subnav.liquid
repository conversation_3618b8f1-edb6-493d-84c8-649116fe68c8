{% 
  liquid
  assign menu = linklists[section.settings.menu]
  if section.settings.dynamic
    assign menu = false
    for link in linklists[section.settings.menu].links
      if link.current and link.links.size > 0
        assign menu = link  
      endif
      for sublink in link.links
        if sublink.current and sublink.links.size > 0
          assign menu = sublink  
        endif
      endfor 
    endfor 
  endif 
%}

{% if menu.links != blank %}
  <nav class="subnav swiper-container {% render 'class-settings' prefix:'section_class' settings:section.settings %}" swiper>
    <script type="swiper/config">
      {
        init: true,
        slidesPerView: 'auto',
        loop: false,
        freeMode: true,
      }
    </script>
  
    <ul class="subnav__wrapper swiper-wrapper">
      {% for link in menu.links %}
        <li class="subnav__item swiper-slide">
          <a class="subnav__link" href="{{ link.url }}">{{ link.title }}</a>
        </li>
      {%- endfor -%}
    </ul>
  </nav>
{% endif %}

{% schema %}
{
  "name": "Inline Navigation",
  "tag": "section",
  "class": "shopify-section--subnav",
  "settings": [
    {
      "type": "liquid",
      "id": "section_classes",
      "label": "Section Classes"
    },
    {
      "type": "link_list",
      "id": "menu",
      "label": "Menu"
    },
    {
      "type": "checkbox",
      "id": "dynamic",
      "label": "Dynamic Drilldown"
    }
  ],
  "presets": [
    {
      "name": "Inline Navigation"
    }
  ]
}
{% endschema %}