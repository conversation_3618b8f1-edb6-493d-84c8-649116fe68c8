{% liquid 
	assign section_type = 'flex'
%}
<section 
	class="section section--{{ section_type }} relative {% render 'class-settings' prefix:'wrapper_class' settings:section.settings %}" 
	style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}">

	{% if section.settings.wrapper_bg_image %}
		<img src="{{ section.settings.wrapper_bg_image | image_url }}" alt="{{ section.settings.wrapper_bg_image.alt }}" class="section__media absolute inset-0 w-full h-full object-cover" />
	{% endif %}
	
	<main 
		class="section__container relative {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
		style="{% render 'style-settings' prefix:'container_styles' settings:section.settings %}">

			<div
				class="section__blocks flex relative {% render 'class-settings' prefix:'section_class' settings:section.settings %}" 
				style="{% render 'style-settings' prefix:'section_style' settings:section.settings %}">
				{% render 'flex-nested-blocks' blocks:section.blocks offset:0  product:product %}
			</div>

	</main>

</section>



{% schema %}
{
	"name": "Flex",
	"settings": [
		{
			"type": "paragraph",
			"content": "@include BackgroundStyles, label_prefix:Section"
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "select",
			"id": "section_class_height",
			"label": "Section Height",
			"options": [
				{
					"value": "h-auto",
					"label": "Auto"
				},
				{
					"value": "h-screen",
					"label": "Full Screen"
				},
				{
					"value": "h-dvh",
					"label": "Dynamic Viewport"
				},
				{
					"value": "h-lvh",
					"label": "Largest Viewport"
				},
				{
					"value": "h-svh",
					"label": "Smallest Viewport"
				}
			]
		},
		{
			"type": "select",
			"id": "section_class_height_desktop",
			"label": "Section Height Desktop",
			"options": [
				{
					"value": "lg:h-auto",
					"label": "Auto"
				},
				{
					"value": "lg:h-screen",
					"label": "Full Screen"
				},
				{
					"value": "lg:h-main",
					"label": "Full Screen minus Header"
				}
			]
		},
		{
			"type": "paragraph",
			"content": "@include FlexLayout, label_prefix:Section, id_prefix:section_class"
		}
	],
	"blocks": [
		{
			"type": "frame",
			"name": "⤷ Frame",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤷ Frame",
					"info": "Renames the Frame block, and also gets added as an ID to the DOM element in a handleized form."
				},
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:article_class"
				},
				{
					"type": "paragraph",
					"content": "@include LogicInclusion"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "header",
					"content": "Frame Height Settings"
				},
				{
					"type": "select",
					"id": "article_class_height",
					"label": "Frame Height",
					"options": [
						{
							"value": "h-auto",
							"label": "Auto"
						},
						{
							"value": "h-full",
							"label": "100%"
						},
						{
							"value": "h-3/4",
							"label": "75%"
						},
						{
							"value": "h-1/2",
							"label": "50%"
						},
						{
							"value": "h-1/4",
							"label": "25%"
						},
						{
							"value": "h-screen",
							"label": "Full Screen"
						},
						{
							"value": "h-dvh",
							"label": "Dynamic Viewport"
						},
						{
							"value": "h-lvh",
							"label": "Largest Viewport"
						},
						{
							"value": "h-svh",
							"label": "Smallest Viewport"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_height_desktop",
					"label": "Frame Height Desktop",
					"options": [
						{
							"value": "lg:h-auto",
							"label": "Auto"
						},
						{
							"value": "lg:h-full",
							"label": "100%"
						},
						{
							"value": "lg:h-3/4",
							"label": "75%"
						},
						{
							"value": "lg:h-1/2",
							"label": "50%"
						},
						{
							"value": "lg:h-1/4",
							"label": "25%"
						},
						{
							"value": "lg:h-screen",
							"label": "Full Screen"
						},
						{
							"value": "lg:h-main",
							"label": "Full Screen minus Header"
						}
					]
				},
				{
					"type": "color",
					"id": "article_style_background_color",
					"label": "Frame Background Color"
				},
				{
					"type": "color_background",
					"id": "article_style_background",
					"label": "Frame Background Gradient"
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "paragraph",
					"content": "_________________________________"
				},
				{
					"type": "header",
					"content": "Advanced Settings"
				},
				{
					"type": "select",
					"id": "article_class_position",
					"label": "Position",
					"default": "relative",
					"options": [
						{
							"value": "",
							"label": "Unset"
						},
						{
							"value": "relative",
							"label": "Relative"
						},
						{
							"value": "absolute",
							"label": "Absolute"
						},
						{
							"value": "sticky top-0",
							"label": "Sticky Top"
						},
						{
							"value": "sticky bottom-0",
							"label": "Sticky Bottom"
						},
						{
							"value": "fixed top-0",
							"label": "Fixed Top"
						},
						{
							"value": "fixed bottom-0",
							"label": "Fixed Bottom"
						}
					]
				},
				{
					"type": "liquid",
					"id": "article_class_custom_classes",
					"label": "Custom Classes"
				},
				{
					"type": "liquid",
					"id": "article_attr_x_data",
					"label": "Dynamic Data Source"
				},
				{
					"type": "liquid",
					"id": "article_attr_x_if",
					"label": "JS Conditional Rendering"
				},
				{
					"type": "paragraph",
					"content": "@include Overflow, id_prefix:article"
				},
				{
					"type": "textarea",
					"id": "article_class_custom",
					"label": "Custom Frame Classes"
				},
				{
					"type": "text",
					"id": "teleport",
					"label": "Teleport",
					"info": "Display frame in a different location in the DOM. Enter the ID of the target element."
				}
			]
		},
		{
			"type": "break",
			"name": "⤶ Frame Break",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤶ Frame End"
				}
			]
		},
		{
			"type": "image",
			"name": "Image",
			"settings": [
				{
					"type": "image_picker",
					"label": "Image",
					"id": "image"
				},
				{
					"type": "image_picker",
					"label": "Image (Desktop)",
					"id": "image_desktop"
				},
				{
					"type": "select",
					"label": "Image Position",
					"id": "media_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full w-full object-cover"
						}
					]
				},
				{
					"type": "select",
					"label": "Loading",
					"id": "image_loading",
					"options": [
						{
							"label": "Lazy",
							"value": "lazy"
						},
						{
							"label": "Eager",
							"value": "eager"
						}
					],
					"default": "eager"
				},
				{
					"type": "liquid",
					"id": "media_class_custom",
					"label": "Custom Image Classes"
				}
			]
		},
		{
			"type": "video",
			"name": "Video",
			"settings": [
				{
					"type": "text",
					"label": "Video URL",
					"id": "video_attr_src"
				},
				{
					"type": "select",
					"label": "Video Position",
					"id": "video_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full w-full object-cover"
						}
					]
				},
				{
					"type": "checkbox",
					"label": "Autoplay",
					"id": "video_attr_autoplay",
					"default": true
				},
				{
					"type": "checkbox",
					"label": "Autoplay",
					"id": "video_attr_playsinline",
					"default": true
				},
				{
					"type": "checkbox",
					"label": "Muted",
					"id": "video_attr_muted",
					"default": true
				},
				{
					"type": "checkbox",
					"label": "Loop",
					"id": "video_attr_loop",
					"default": true
				}
			]
		},
		{
			"type": "rich-text",
			"name": "Rich Text",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include RichText id_prefix:text, label_prefix:Rich Text"
				},
				{
					"type": "select",
					"id": "text_class_justification",
					"label": "Text Justification",
					"options": [
						{
							"value": "text-left",
							"label": "⇐"
						},
						{
							"value": "text-center",
							"label": "→←"
						},
						{
							"value": "text-right",
							"label": "⇒"
						},
						{
							"value": "text-justify",
							"label": "≡"
						}
					]
				},
				{
					"type": "select",
					"id": "text_class_measure",
					"label": "Text Measure",
					"options": [
						{
							"value": "",
							"label": "None"
						},
						{
							"value": "measure",
							"label": "Contain"
						}
					]
				},
				{
					"type": "select",
					"id": "text_class_wrap",
					"label": "Text Wrap",
					"options": [
						{
							"value": "text-wrap",
							"label": "Wrap"
						},
						{
							"value": "text-nowrap",
							"label": "No Wrap"
						},
						{
							"value": "text-balance",
							"label": "Balance"
						},
						{
							"value": "text-pretty",
							"label": "Pretty"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:text_class, label_prefix:Text "
				}
			]
		},
		{
			"type": "title",
			"name": "Title",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include Text id_prefix:title, label_prefix:Title"
				},
				{
					"type": "select",
					"id": "title_class_justification",
					"label": "Text Justification",
					"options": [
						{
							"value": "text-left",
							"label": "⇐"
						},
						{
							"value": "text-center",
							"label": "→←"
						},
						{
							"value": "text-right",
							"label": "⇒"
						},
						{
							"value": "text-justify",
							"label": "≡"
						}
					]
				},
				{
					"type": "select",
					"id": "text_class_wrap",
					"label": "Text Wrap",
					"options": [
						{
							"value": "text-wrap",
							"label": "Wrap"
						},
						{
							"value": "text-nowrap",
							"label": "No Wrap"
						},
						{
							"value": "text-balance",
							"label": "Balance"
						},
						{
							"value": "text-pretty",
							"label": "Pretty"
						}
					]
				}
			]
		},
		{
			"type": "button",
			"name": "Button",
			"settings": [
				{
					"type": "select",
					"id": "style",
					"label": "Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "text",
					"id": "button_text",
					"label": "Button Text"
				},
				{
					"type": "text",
					"id": "leading_icon",
					"label": "Leading Icon"
				},
				{
					"type": "text",
					"id": "trailing_icon",
					"label": "Trailing Icon"
				},
				{
					"type": "url",
					"id": "link",
					"label": "Link"
				},
				{
					"type": "text",
					"id": "onclick",
					"label": "Click Event",
					"info": "JavaScript onclick event, overrides native functionality. (Not Liquid)"
				},
				{
					"type": "text",
					"id": "onhover",
					"label": "Hover Event",
					"info": "JavaScript onhover event, overrides native functionality. (Not Liquid)"
				},
				{
					"type": "select",
					"id": "onclick_type",
					"label": "Type of Click Event",
					"options": [
						{
							"value": "x-data @",
							"label": "Alpine"
						},
						{
							"value": "on",
							"label": "Native"
						}
					],
					"default": "x-data @"
				},
				{
					"type": "checkbox",
					"id": "form_validity",
					"label": "Require Form Validity",
					"info": "Disables button until all fields in enclosing form are valid"
				},
				{
					"type": "paragraph",
					"content": "_________________________________"
				},
				{
					"type": "header",
					"content": "Advanced Settings"
				},
				{
					"type": "liquid",
					"id": "button_class_custom_classes",
					"label": "Custom Classes"
				},
				{
					"type": "liquid",
					"id": "button_attr_x_data",
					"label": "Dynamic Data Source"
				},
				{
					"type": "liquid",
					"id": "button_attr_x_show",
					"label": "JS Conditional Rendering"
				},
				{
					"type": "liquid",
					"id": "button_attr_x_text",
					"label": "Dynamic Text"
				},
				{
					"type": "liquid",
					"id": "button_attr_QQclass",
					"label": "Dynamic Classes"
				},
				{
					"type": "checkbox",
					"id": "disabled",
					"label": "Disable Button"
				}
			]
		},
		{
			"type": "dropdown-menu",
			"name": "Dropdown Menu",
			"settings": [
				{
					"type": "text",
					"id": "label",
					"label": "*Label"
				},
				{
					"type": "liquid",
					"id": "model",
					"label": "Data source/model"
				},
				{
					"type": "textarea",
					"id": "options",
					"label": "Options",
					"info": "value:Label",
					"default": "featured:Featured\nbest-selling:Best Selling\nprice-ascending:Lowest Price"
				},
				{
					"type": "liquid",
					"id": "onchange",
					"label": "on Change",
					"info": "JavaScript"
				}
			]
		},
		{
			"type": "toggle-switch",
			"name": "Toggle Switch",
			"settings": [
				{
					"type": "liquid",
					"id": "model",
					"label": "Data source/model"
				},
				{
					"type": "textarea",
					"id": "options",
					"label": "Toggle Switch Options",
					"info": "value:Selected|Unselected",
					"default": "Product:View on Product,Model:View on Model"
				},
				{
					"type": "liquid",
					"id": "onchange",
					"label": "on Change",
					"info": "JavaScript"
				},
				{
					"type": "select",
					"id": "toggle_interaction",
					"label": "Toggle Interaction",
					"default": "click",
					"options": [
						{
							"value": "hover",
							"label": "Hover"
						},
						{
							"value": "click",
							"label": "Click"
						}
					]
				}
			]
		},
		{
			"name": "Link List",
			"type": "linklist",
			"settings": [
				{
					"id": "link_list",
					"type": "link_list",
					"label": "Navigation Menu"
				},
				{
					"id": "linklist_class_format",
					"type": "select",
					"label": "Linklist Format",
					"options": [
						{
							"value": "link-list--basic",
							"label": "Basic Tree"
						},
						{
							"value": "link-list--collapsable",
							"label": "Collapsable Submenus"
						},
						{
							"value": "link-list--collapsable link-list--panels",
							"label": "Panels"
						}
					]
				},
				{
					"id": "link_style_color",
					"type": "color",
					"label": "Link Color"
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:linklist_class, label_prefix:Link List "
				}
			]
		},
		{
			"type": "content-item",
			"name": "Content Item",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:item_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include Aspect, id_prefix:item_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include ContentItem"
				}
			]
		},
		{
			"type": "nav-item",
			"name": "Visual Nav Item",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:item_class, label_prefix:Item "
				},
				{
					"type": "select",
					"label": "Shrink to fit in flex container",
					"id": "item_class_shrink",
					"options": [
						{
							"value": "flex-shrink-0",
							"label": "Keep Size"
						},
						{
							"value": "",
							"label": "Shrink"
						}
					],
					"default": ""
				},
				{
					"type": "liquid",
					"label": "Liquid Logic Inclusion",
					"id": "inclusion_liquid",
					"info": "Insert any liquid logic that returns a value to display the section",
					"default": "true"
				},
				{
					"id": "inclusion_js",
					"label": "Javascript Inclusion Logic",
					"type": "liquid"
				},
				{
					"type": "url",
					"label": "Link",
					"id": "link"
				},
				{
					"type": "image_picker",
					"label": "Image",
					"id": "image"
				},
				{
					"type": "text",
					"label": "link Text",
					"id": "title"
				},
				{
					"type": "richtext",
					"label": "Description Text",
					"id": "description"
				},
				{
					"type": "paragraph",
					"content": "@include BackgroundStyles, label_prefix:Nav Item, id_prefix:item_"
				},
				{
					"type": "select",
					"id": "item_class_vertical_padding",
					"label": "Item Vertical Padding",
					"options": [
						{
							"value": "@include Spacing prop:py",
							"label": "Inclusion"
						},
						{
							"value": "py-0",
							"label": "@global: None"
						},
						{
							"value": "py-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "py-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "py-xs",
							"label": "@global: XS"
						},
						{
							"value": "py-sm",
							"label": "@global: SM"
						},
						{
							"value": "py-md",
							"label": "@global: MD"
						},
						{
							"value": "py-lg",
							"label": "@global: LG"
						},
						{
							"value": "py-xl",
							"label": "@global: XL"
						},
						{
							"value": "py-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "py-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_horizontal_padding",
					"label": "Item Horizontal Padding",
					"options": [
						{
							"value": "@include Spacing prop:px",
							"label": "Inclusion"
						},
						{
							"value": "px-0",
							"label": "@global: None"
						},
						{
							"value": "px-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "px-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "px-xs",
							"label": "@global: XS"
						},
						{
							"value": "px-sm",
							"label": "@global: SM"
						},
						{
							"value": "px-md",
							"label": "@global: MD"
						},
						{
							"value": "px-lg",
							"label": "@global: LG"
						},
						{
							"value": "px-xl",
							"label": "@global: XL"
						},
						{
							"value": "px-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "px-3xl",
							"label": "@global: 3XL"
						}
					]
				}
			]
		},
		{
			"type": "mini-form",
			"name": "Mini Form",
			"settings": [
				{
					"type": "liquid",
					"id": "on_button_click",
					"label": "JavaScript OnButtonClick"
				},
				{
					"type": "select",
					"id": "form_method",
					"label": "Form Method",
					"default": "POST",
					"options": [
						{
							"value": "GET",
							"label": "GET"
						},
						{
							"value": "POST",
							"label": "POST"
						}
					]
				},
				{
					"type": "text",
					"id": "form_action",
					"label": "Form Action"
				},
				{
					"type": "checkbox",
					"id": "prevent_default",
					"label": "Block Default HTTP Submit",
					"default": true
				},
				{
					"type": "liquid",
					"id": "on_submit",
					"label": "JavaScript OnSubmit"
				},
				{
					"type": "select",
					"id": "field_type",
					"label": "Input Field Type",
					"default": "email",
					"options": [
						{
							"value": "email",
							"label": "Email"
						},
						{
							"value": "tel",
							"label": "Tel"
						},
						{
							"value": "text",
							"label": "Text"
						}
					]
				},
				{
					"type": "text",
					"id": "trigger_text",
					"label": "Trigger Text"
				},
				{
					"type": "text",
					"id": "field_name",
					"label": "Field Name",
					"default": "email"
				},
				{
					"type": "text",
					"id": "placeholder_text",
					"label": "Placeholder Text"
				},
				{
					"type": "text",
					"id": "input_pattern",
					"label": "Input RexEx Pattern"
				},
				{
					"type": "text",
					"id": "input_mask",
					"label": "Input Mask"
				},
				{
					"type": "liquid",
					"id": "on_input",
					"label": "JavaScript OnInput"
				},
				{
					"type": "checkbox",
					"label": "Activate email field validation",
					"id": "activate_email_validation",
					"default": false
				},
				{
					"type": "text",
					"id": "error_message",
					"label": "Field Validation Error Text"
				},
				{
					"type": "text",
					"id": "submit_text",
					"label": "Submit Text",
					"default": "Submit"
				},
				{
					"type": "select",
					"id": "button_style",
					"label": "Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "richtext",
					"id": "info_text",
					"label": "Revealed Information"
				},
				{
					"type": "richtext",
					"id": "success_text",
					"label": "Success Message"
				}
			]
		},
		{
			"name": "Hotspot",
			"type": "hotspot",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include Hotspot"
				}
			]
		},
		{
			"name": "Accordion",
			"type": "accordion",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:accordion_detail_class"
				},
				{
					"type": "paragraph",
					"content": "@include Accordion"
				}
			]
		},
		{
			"name": "⤷ Rich Accordion",
			"type": "rich_accordion",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:accordion_detail_class"
				},
				{
					"type": "paragraph",
					"content": "@include Accordion"
				}
			]
		},
		{
			"name": "⤶ Rich Accordion Close",
			"type": "rich_acordion_close",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤶ Frame Break"
				}
			]
		},
		{
			"type": "nav-tools",
			"name": "Nav Tools",
			"settings": [
				{
					"id": "column_classes",
					"label": "Column Classes",
					"type": "text"
				},
				{
					"id": "button_classes",
					"label": "Button Classes",
					"type": "text",
					"default": "items-center ml-4 text-xs"
				},
				{
					"id": "search",
					"label": "Search",
					"type": "checkbox",
					"default": true
				},
				{
					"id": "wishlist",
					"label": "Wishlist",
					"type": "checkbox",
					"default": true
				},
				{
					"id": "account",
					"label": "Account",
					"type": "checkbox",
					"default": true
				},
				{
					"id": "geo",
					"label": "Geolocation",
					"type": "checkbox",
					"default": true
				},
				{
					"id": "cart",
					"label": "Cart",
					"type": "checkbox",
					"default": true
				},
				{
					"id": "icon_size",
					"label": "Icon Size",
					"type": "number",
					"default": 20
				},
				{
					"id": "display",
					"label": "Display",
					"type": "radio",
					"options": [
						{
							"value": "icons",
							"label": "Icons"
						},
						{
							"value": "text",
							"label": "Text"
						},
						{
							"value": "both",
							"label": "Both"
						}
					],
					"default": "icons"
				},
				{
					"id": "greeting",
					"label": "Greeting",
					"type": "liquid"
				}
			]
		},
		{
			"type": "announcements",
			"name": "Announcements",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤷ Announcements"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:column_class"
				},
				{
					"id": "column_classes",
					"label": "Column Classes",
					"type": "text"
				},
				{
					"id": "interval",
					"label": "Autoplay Timer",
					"type": "number",
					"default": 5
				}
			]
		},
		{
			"type": "announcements-end",
			"name": "⤶ Announcements End",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤶ Announcements End"
				}
			]
		},
		{
			"type": "announcement",
			"name": "Announcement",
			"settings": [
				{
					"id": "inclusion",
					"label": "Liquid Inclusion Logic",
					"type": "liquid",
					"default": "1"
				},
				{
					"id": "inclusion_js",
					"label": "Javascript Inclusion Logic",
					"type": "liquid"
				},
				{
					"id": "text",
					"label": "Announcement Text",
					"type": "text"
				},
				{
					"id": "text_desktop",
					"label": "Desktop Announcment Text",
					"type": "text"
				},
				{
					"id": "image",
					"label": "Announcement Logo",
					"type": "image_picker"
				},
				{
					"id": "svg",
					"label": "Announcement Logo SVG",
					"type": "html",
					"info": "for adding an svg"
				},
				{
					"id": "image_size",
					"label": "Announcement Logo Size",
					"type": "text",
					"info": "set dimensions, example: 100x100"
				},
				{
					"id": "link",
					"label": "Announcement Link",
					"type": "url"
				}
			]
		},
		{
			"name": "Divider",
			"type": "divider",
			"settings": [
				{
					"type": "color",
					"id": "divider_style_border_color",
					"label": "Color"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:divider_class, label_prefix:Divider "
				}
			]
		},
		{
			"name": "Spacer",
			"type": "spacer",
			"settings": [
				{
					"type": "select",
					"id": "spacer_class_vertical_spacing",
					"label": "Item Vertical Spacer",
					"options": [
						{
							"value": "@include Spacing prop:pb",
							"label": "Inclusion"
						},
						{
							"value": "pb-0",
							"label": "@global: None"
						},
						{
							"value": "pb-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "pb-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "pb-xs",
							"label": "@global: XS"
						},
						{
							"value": "pb-sm",
							"label": "@global: SM"
						},
						{
							"value": "pb-md",
							"label": "@global: MD"
						},
						{
							"value": "pb-lg",
							"label": "@global: LG"
						},
						{
							"value": "pb-xl",
							"label": "@global: XL"
						},
						{
							"value": "pb-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "pb-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "spacer_class_horizontal_spacing",
					"label": "Item Horizontal Spacer",
					"options": [
						{
							"value": "@include Spacing prop:pr",
							"label": "Inclusion"
						},
						{
							"value": "pr-0",
							"label": "@global: None"
						},
						{
							"value": "pr-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "pr-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "pr-xs",
							"label": "@global: XS"
						},
						{
							"value": "pr-sm",
							"label": "@global: SM"
						},
						{
							"value": "pr-md",
							"label": "@global: MD"
						},
						{
							"value": "pr-lg",
							"label": "@global: LG"
						},
						{
							"value": "pr-xl",
							"label": "@global: XL"
						},
						{
							"value": "pr-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "pr-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "spacer_class_vertical_spacing_desktop",
					"label": "Item Vertical Spacer Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:pb",
							"label": "Inclusion"
						},
						{
							"value": "lg:pb-0",
							"label": "@global: None"
						},
						{
							"value": "lg:pb-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:pb-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:pb-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:pb-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:pb-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:pb-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:pb-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:pb-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:pb-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:pb-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:pb-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:pb-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:pb-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:pb-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "spacer_class_horizontal_spacing_desktop",
					"label": "Item Horizontal Spacer Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:pr",
							"label": "Inclusion"
						},
						{
							"value": "lg:pr-0",
							"label": "@global: None"
						},
						{
							"value": "lg:pr-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:pr-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:pr-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:pr-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:pr-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:pr-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:pr-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:pr-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:pr-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:pr-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:pr-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:pr-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:pr-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:pr-8xl",
							"label": "@global: 8XL"
						}
					]
				}
			]
		},
		{
			"type": "product_actions",
			"name": "⤷ Product Actions",
			"settings": [
				{
					"type": "radio",
					"id": "sticky_block_class_layout",
					"label": "Block Horizontal Layout",
					"options": [
						{
							"value": "layout-left",
							"label": "←"
						},
						{
							"value": "layout-right",
							"label": "→"
						},
						{
							"value": "layout-center",
							"label": "↔"
						}
					]
				}
			]
		},
		{
			"type": "product_actions_close",
			"name": "⤶ Product Actions Close",
			"settings": []
		},
		{
			"type": "features",
			"name": "Features",
			"settings": [
				{
					"type": "product",
					"label": "Origin Product",
					"id": "product"
				},
				{
					"type": "liquid",
					"label": "Source Path",
					"id": "source"
				},
				{
					"type": "textarea",
					"label": "Icon Map",
					"id": "icon_map"
				}
			]
		},
		{
			"name": "Custom Liquid",
			"type": "liquid",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"info": "Internal Reference Only"
				},
				{
					"type": "liquid",
					"id": "liquid",
					"label": "Custom Liquid"
				}
			]
		},
		{
			"name": "Product Item(s)",
			"type": "product-item",
			"settings": [
				{
					"type": "product",
					"id": "product",
					"label": "Product"
				},
				{
					"type": "product_list",
					"id": "products",
					"label": "Specific Products"
				},
				{
					"type": "collection",
					"id": "collection",
					"label": "Collection Products"
				},
				{
					"type": "number",
					"id": "limit",
					"label": "Product Limit",
					"default": 5
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:product_item_class, label_prefix:Product Item "
				},
				{
					"type": "paragraph",
					"content": "@include ProductItem"
				}
			]
		},
		{
			"name": "Upsell",
			"type": "upsell",
			"settings": [
				{
					"type": "checkbox",
					"id": "selected",
					"label": "Products selected by default",
					"default": false
				},
				{
					"type": "liquid",
					"id": "separator_content",
					"label": "Separator Content",
					"default": "+"
				},
				{
					"type": "select",
					"id": "separator_class_visibility",
					"label": "Separator Display",
					"default": "",
					"options": [
						{
							"label": "Mobile",
							"value": "lg:hidden"
						},
						{
							"label": "Desktop",
							"value": "hidden lg:block"
						},
						{
							"label": "Desktop & Mobile",
							"value": ""
						},
						{
							"label": "None",
							"value": "hidden"
						}
					]
				},
				{
					"type": "select",
					"id": "separator_class_type_style",
					"label": "Separator Type Style",
					"options": [
						{
							"value": "@include TypeStyle",
							"label": "Inclusion"
						},
						{
							"value": "",
							"label": "@global:  Auto"
						},
						{
							"value": "type-body",
							"label": "@global:  Body"
						},
						{
							"value": "type-hero",
							"label": "@global:  Hero"
						},
						{
							"value": "type-eyebrow",
							"label": "@global:  Eyebrow"
						},
						{
							"value": "type-headline",
							"label": "@global:  Headline"
						},
						{
							"value": "type-subline",
							"label": "@global:  Subline"
						},
						{
							"value": "type-micro",
							"label": "@global:  Micro"
						},
						{
							"value": "type-item",
							"label": "@global:  Item Title"
						},
						{
							"value": "type-section",
							"label": "@global:  Section Title"
						}
					]
				},
				{
					"type": "color",
					"id": "separator_style_color",
					"label": "Separator Text Color"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:product_item_wrapper_class, label_prefix:Product Item Wrapper "
				}
			]
		},
		{
			"type": "promo",
			"name": "Promo",
			"settings": [
				{
					"id": "headline",
					"label": "Headline",
					"type": "text"
				},
				{
					"id": "description",
					"label": "Description",
					"type": "richtext"
				},
				{
					"id": "link_link",
					"label": "Link URL",
					"type": "url"
				},
				{
					"id": "icon",
					"info": "Copy image file url. [View files](/admin/settings/files)",
					"label": "Image (80x80)",
					"type": "text"
				},
				{
					"id": "tag",
					"label": "Tag",
					"info": "Show only if display specific tag. Blank will show on all products using this template.",
					"type": "text"
				}
			]
		},
		{
			"type": "enunciation",
			"name": "Enunciation",
			"settings": [
				{
					"type": "header",
					"content": "Content Management"
				},
				{
					"type": "paragraph",
					"content": "This content is managed in the enunciation metafield on the product."
				}
			]
		},
		{
			"type": "fit_guide",
			"name": "Fit Guide",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "Fit Guide"
				},
				{
					"content": "@include FitGuide",
					"type": "paragraph"
				},
				{
					"type": "select",
					"label": "Shop Category Name on Top",
					"id": "brand_fit_guide",
					"options": [
						{
							"value": "left",
							"label": "Left"
						},
						{
							"value": "top",
							"label": "Top"
						}
					],
					"default": "left"
				},
				{
					"type": "product",
					"label": "Override Product",
					"id": "product"
				}
			]
		},
		{
			"name": "⤷ Carousel",
			"type": "carousel",
			"settings": [
				{
					"type": "number",
					"id": "slides_per_view",
					"label": "Slides per View",
					"default": 1
				},
				{
					"type": "number",
					"id": "slides_per_view_desktop",
					"label": "Slides per View Desktop",
					"default": 1
				},
				{
					"type": "select",
					"id": "carousel_pagination",
					"label": "Carousel Pagination Type",
					"default": "progressbar",
					"options": [
						{
							"value": "",
							"label": "None"
						},
						{
							"value": "bullets",
							"label": "Bullets"
						},
						{
							"value": "progressbar",
							"label": "Progress bar"
						}
					]
				}
			]
		},
		{
			"type": "carousel-end",
			"name": "⤶ Carousel End",
			"settings": []
		}
	],
	"presets": [
		{
			"name": "Flex",
			"category": "Advanced",
			"settings": {},
			"blocks": []
		}
	]
}
{% endschema %}
