{% liquid 
	assign section_type = 'article-list'
%}

<section 
	class="section section--{{ section_type }} relative {% render 'class-settings' prefix:'wrapper_class' settings:section.settings %}" 
	style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}">


	{% if section.settings.wrapper_image %}
		<img src="{{ section.settings.wrapper_image | image_url }}" alt="{{ section.settings.wrapper_image.alt }}" class="section__media absolute inset-0 w-full h-full object-cover" />
	{% endif %}

	{% if section.settings.section_title_link != blank %}
	<a href="{{ section.settings.section_title_link }}">
	{% endif %}

		{% if section.settings.section_title_text != blank %}
			{% assign element = section.settings.section_title_element | default: 'p' %}
			<{{ element }} class="relative mt-0 {% render 'class-settings' prefix:'section_title_class' settings:section.settings %}"
				style="{% render 'style-settings' prefix:'section_title_style' settings:section.settings %}">{{section_title_text | default: section.settings.section_title_text}}</{{ element}}>
		{% endif %}

	{% if section.settings.section_title_link != blank %}
	</a>
	{% endif %}

	{%- assign _blogs = section.blocks | where: 'type', 'blog' -%}

	<main 
		class="section__container relative {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
		style="{%- render 'style-settings' prefix:'container_styles' settings:section.settings -%}">

		{% if section.settings.nav != blank %}
		<nav class="p-xl">
			<ul class="tabs flex pb-8 scroll-to-active no-scrollbar overflow-x-scroll">
				{% if section.settings.nav == 'tabs' %}
		      {% for _blog in _blogs %}
		        <li>
		          {% if blog.settings.blog_page %}
		          	 <a 
		              class="whitespace-nowrap px-8 py-2 font-bold border-current border-b-2 {% if forloop.first %}active{% endif %}" 
		             	href="/blogs/{{ blog.settings.blog_page }}"
		            _>{{ _blog.settings.tab_title }}</a>
							{% else %}
							 <button 
		              class="whitespace-nowrap px-8 py-2 font-bold border-current border-b-2 {% if forloop.first %}active{% endif %}"
		            >{{ _blog.settings.tab_title }}</button>
		          {% endif %}
		        </li>
	        {% endfor %}
  			{% elsif section.settings.nav == 'links' %}
		      {% for link in linklists[section.settings.menu].links %}
		        <li>
	          	<a 
	              class="whitespace-nowrap px-8 py-2 font-bold border-current border-b-2 {% if link.current %}active{% endif %}" 
	             	href="{{ link.url }}"
	            _>{{ link.title }}</a>
		        </li>
	        {% endfor %}
  			{% endif %}
	    </ul>
	  </nav>
	  {% endif %}

	  {% assign _blog = blogs[_blogs[0].settings.blog] | default: blog %}

	  {% paginate _blog.articles by section.settings.limit %}
		
		<div class="article-grid grid {% render 'class-settings' prefix:'article_grid_class' settings:section.settings %}" >
      
	  	{% liquid
	  		
	  		for article in _blog.articles
	        render 'article-item' article:article blog:_blog settings:section.settings
	      endfor
		      
	    %}

    	{% if paginate.next %}<div class="paginate-next hidden">{{ paginate.next.url }}</div>{% endif %}

    </div>

    {% if section.settings.fetch_posts %}

    <footer class="p-xl text-center">
    	{% if paginate.next %}
			<button data-url="{{ paginate.next.url }}" onclick="this.children[0].textContent='Loading'; Util.remote.get(`${this.dataset.url}&sections={{ section.id }}`, {path:'{{ section.id }}',select:'.article-grid'}).then(grid=>{
					document.querySelector('.article-grid').insertAdjacentHTML('beforeend',grid);
					if(grid.includes('paginate-next')){
						this.dataset.url = Array.from(document.querySelectorAll('.paginate-next')).at(-1).innerHTML
						this.children[0].textContent='{{ section.settings.load_more_button_text }}';
					} else {
						this.remove()
					}
				})" class="button {% render 'class-settings' prefix:'load_more_button_class' settings:section.settings %}">
				<span class="button__text">{{ section.settings.load_more_button_text }}</span></button>
			{% endif %}
    </footer>

    {% endif %}

    {% endpaginate %}

	</main>
 
</section>



{% schema %}
{
	"name": "Article List",
	"settings": [
		{
			"type": "paragraph",
			"content": "@include Heading, label_prefix:Section Title, id_prefix:section_title"
		},
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "header",
			"content": "Article Card Settings"
		},
		{
			"type": "select",
			"id": "item_class_card_layout",
			"label": "Card Layout",
			"default": "content-item__article--vertical",
			"options": [
				{
					"label": "Vertical",
					"value": "content-item__article--vertical"
				},
				{
					"label": "Horizontal",
					"value": ""
				}
			]
		},
		{
			"type": "paragraph",
			"content": "@include BlockGrid, label_prefix:Article, id_prefix:article_grid_class"
		},
		{
			"type": "select",
			"id": "article_grid_class_gap",
			"label": "Article Spacing Gap",
			"options": [
				{
					"value": "@include Spacing prop:gap",
					"label": "Inclusion"
				},
				{
					"value": "gap-0",
					"label": "@global: None"
				},
				{
					"value": "gap-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "gap-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "gap-xs",
					"label": "@global: XS"
				},
				{
					"value": "gap-sm",
					"label": "@global: SM"
				},
				{
					"value": "gap-md",
					"label": "@global: MD"
				},
				{
					"value": "gap-lg",
					"label": "@global: LG"
				},
				{
					"value": "gap-xl",
					"label": "@global: XL"
				},
				{
					"value": "gap-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "gap-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "article_grid_class_gap_desktop",
			"label": "Article Spacing Gap Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:gap",
					"label": "Inclusion"
				},
				{
					"value": "lg:gap-0",
					"label": "@global: None"
				},
				{
					"value": "lg:gap-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:gap-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:gap-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:gap-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:gap-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:gap-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:gap-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:gap-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:gap-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:gap-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:gap-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:gap-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:gap-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:gap-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "number",
			"id": "limit",
			"default": 12,
			"label": "Max number of Posts"
		},
		{
			"type": "checkbox",
			"id": "fetch_posts",
			"label": "Show Load More Button"
		},
		{
			"type": "text",
			"id": "load_more_button_text",
			"label": "Load More Button Text",
			"default": "More please!"
		},
		{
			"type": "select",
			"id": "load_more_button_class_style",
			"label": "Load More Button Style",
			"options": [
				{
					"value": "@include ButtonStyle",
					"label": "Inclusion"
				},
				{
					"value": "button--primary",
					"label": "@global:  Primary"
				},
				{
					"value": "button--secondary",
					"label": "@global:  Secondary"
				},
				{
					"value": "button--tertiary",
					"label": "@global:  Tertiary"
				},
				{
					"value": "button--light",
					"label": "@global:  Light"
				},
				{
					"value": "button--dark",
					"label": "@global:  Dark"
				},
				{
					"value": "button--pop",
					"label": "@global:  Pop"
				},
				{
					"value": "button--highlight",
					"label": "@global:  Highlight"
				},
				{
					"value": "button--action",
					"label": "@global:  Action"
				},
				{
					"value": "button--simple",
					"label": "@global:  Simple"
				},
				{
					"value": "button--emphasis",
					"label": "@global:  Emphasis"
				},
				{
					"value": "button--light-text-link",
					"label": "@global:  Light Text Link"
				},
				{
					"value": "button--link",
					"label": "@global:  Text Link"
				},
				{
					"value": "button--micro-link",
					"label": "@global:  Micro Text Link"
				},
				{
					"value": "button--icon",
					"label": "@global:  Icon"
				},
				{
					"value": "button--primary-hover",
					"label": "@global:  Primary Hover"
				},
				{
					"value": "button--secondary-hover",
					"label": "@global:  Secondary Hover"
				},
				{
					"value": "button--tertiary-hover",
					"label": "@global:  Tertiary Hover"
				}
			]
		},
		{
			"type": "select",
			"id": "nav",
			"label": "Navigation",
			"options": [
				{
					"value": "",
					"label": "None"
				},
				{
					"value": "tabs",
					"label": "Tabs"
				},
				{
					"value": "links",
					"label": "Links"
				}
			]
		},
		{
			"type": "link_list",
			"id": "menu",
			"label": "Nav Menu"
		}
	],
	"blocks": [
		{
			"type": "blog",
			"name": "Blog Source",
			"settings": [
				{
					"type": "text",
					"id": "tab_title",
					"label": "Tab Title"
				},
				{
					"type": "blog",
					"id": "blog",
					"label": "Blog"
				}
			]
		}
	],
	"presets": [
		{
			"name": "Article List",
			"category": "Blog",
			"settings": {},
			"blocks": []
		}
	]
}
{% endschema %}
