<div class="container p-10">

  <h1 class="title--primary title--page">
    Article {{ 'general.search.heading.other' | t }} "{{ search.terms }}"
  </h1>

  <div class="flex flex-wrap lg:flex-nowrap">

    <main class="lg:w-3/4">
      
      {% paginate search.results by 20 %}

      {% for result in search.results %}

        {% if result.object_type == 'article' %}

          {%- render 'article-card', article: result, show_image: true, show_date: true, show_author: true, card_classes: 'mb-6 pb-6 border-b' -%}

        {%- endif -%}

      {% endfor %}

      {%- if paginate.pages > 1 -%}

        {%- render 'pagination', paginate: paginate -%}

      {%- endif -%}

      {% endpaginate %}

    </main>

    <aside>
      


    </aside>

  </div>

</div>

{% schema %}
  {
    "name": "Search Articles",
    "settings": [
    ],
    "blocks": [
    ]
  }
{% endschema %}