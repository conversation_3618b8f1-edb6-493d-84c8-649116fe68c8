{% liquid 
  assign section_type = 'sticky'
%}

<section 
  class="section section--{{ section_type }} fixed w-full z-30
  {% render 'class-settings' prefix:'wrapper_class' settings:section.settings %}" 
  style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}"> 

  {% if section.settings.wrapper_image %}
    <img src="{{ section.settings.wrapper_image | image_url }}" alt="{{ section.settings.wrapper_image.alt }}" class="section__media absolute inset-0 w-full h-full object-cover" />
  {% endif %}

  <main class="section__container relative">
    <div name="Blocks" class="section__blocks flex flex-row layout items-center  whitespace-nowrap {% render 'class-settings' prefix:'container_class', settings:section.settings %}">

    	{% render 'flex-nested-blocks' blocks:section.blocks offset:0 %}

    </div>
  </main>
    {% if section.settings.trigger %}
    	{% assign section_id_string = section.index %}
		<script type="text/javascript">    
			const section_{{section_id_string}} = document.currentScript.parentNode
			window.addEventListener('DOMContentLoaded', ()=>{
			window.addEventListener('scroll',Util.debounce(e=>{
				if({{ section.settings.trigger}}){
				section_{{section_id_string}}.classList.add('active')
				} else {
				section_{{section_id_string}}.classList.remove('active')
				}
			},10))

			})
		</script>
  	{% endif %}
</section>


{% schema %}
{
	"name": "Sticky Elements",
	"tag": "section",
	"class": "shopify-section--sticky",
	"settings": [
		{
			"type": "liquid",
			"id": "trigger",
			"label": "Scroll Visibility Conditions"
		},
		{
			"id": "wrapper_class_position",
			"label": "Bar Position",
			"type": "select",
			"options": [
				{
					"value": "hidden",
					"label": "None"
				},
				{
					"value": "bottom-0",
					"label": "Bottom"
				},
				{
					"value": "top-main",
					"label": "Top"
				}
			],
			"default": "top-main"
		},
		{
			"id": "wrapper_class_position_desktop",
			"label": "Bar Position Desktop",
			"type": "select",
			"options": [
				{
					"value": "hidden",
					"label": "None"
				},
				{
					"value": "lg:top-auto lg:bottom-0",
					"label": "Bottom"
				},
				{
					"value": "lg:bottom-auto lg:top-main",
					"label": "Top"
				}
			],
			"default": "lg:bottom-auto lg:top-main"
		},
		{
			"type": "paragraph",
			"content": "@include SectionDisplay, label_prefix:Display, id_prefix:wrapper_class"
		},
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		}
	],
	"blocks": [
		{
			"type": "frame",
			"name": "⤷ Frame",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:article_class"
				},
				{
					"type": "paragraph",
					"content": "@include LogicInclusion"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:article_class, label_prefix:Frame "
				}
			]
		},
		{
			"type": "break",
			"name": "⤶ Frame Break",
			"settings": []
		},
		{
			"type": "divider",
			"name": "Divider",
			"settings": []
		},
		{
			"type": "product-header",
			"name": "Product Header",
			"settings": [
				{
					"type": "product",
					"id": "product",
					"label": "Product"
				},
				{
					"type": "liquid",
					"id": "title_source",
					"label": "Product Title Source",
					"info": "Liquid to extract the product title",
					"default": "{{ product.title|split:'-'|first }}"
				},
				{
					"type": "checkbox",
					"id": "display_dynamic_product_title",
					"label": "Display Dynamic Product Title",
					"default": false
				}
			]
		},
		{
			"type": "product_actions",
			"name": "⤷ Product Actions",
			"settings": [
				{
					"type": "radio",
					"id": "sticky_block_class_layout",
					"label": "Block Horizontal Layout",
					"options": [
						{
							"value": "layout-left",
							"label": "←"
						},
						{
							"value": "layout-right",
							"label": "→"
						},
						{
							"value": "layout-center",
							"label": "↔"
						}
					]
				}
			]
		},
		{
			"type": "product_actions_close",
			"name": "⤶ Product Actions Close",
			"settings": []
		},
		{
			"type": "gate",
			"name": "Purchase Gate",
			"settings": [
				{
					"type": "radio",
					"id": "sticky_block_class_layout",
					"label": "Block Horizontal Layout",
					"options": [
						{
							"value": "layout-left",
							"label": "←"
						},
						{
							"value": "layout-right",
							"label": "→"
						},
						{
							"value": "layout-center",
							"label": "↔"
						}
					]
				},
				{
					"id": "title",
					"label": "Title",
					"type": "text",
					"info": "Internal reference only."
				},
				{
					"id": "inclusion",
					"label": "Liquid Inclusion Logic",
					"type": "liquid"
				},
				{
					"id": "inclusion_js",
					"label": "Javascript Inclusion Logic",
					"type": "liquid"
				},
				{
					"id": "hide",
					"label": "Hides Element",
					"type": "radio",
					"default": ".product-form__actions",
					"options": [
						{
							"label": "None",
							"value": ""
						},
						{
							"label": "Product Form",
							"value": ".product-form"
						},
						{
							"label": "Add To Cart",
							"value": ".product-form__actions"
						}
					]
				},
				{
					"id": "unset_button_text",
					"label": "Unset Variant Button Text",
					"type": "text",
					"default": "Select Options and Get Access"
				},
				{
					"id": "disabled_button_text",
					"label": "Unavailable Button Text",
					"type": "text",
					"default": "Not Available"
				},
				{
					"id": "enabled_button_text",
					"label": "Available Button Text",
					"type": "text",
					"default": "Get Access"
				},
				{
					"id": "click",
					"label": "On Click",
					"type": "liquid"
				}
			]
		},
		{
			"type": "dropdown-menu",
			"name": "Dropdown Menu",
			"settings": [
				{
					"type": "text",
					"id": "label",
					"label": "*Label"
				},
				{
					"type": "liquid",
					"id": "model",
					"label": "Data source/model"
				},
				{
					"type": "textarea",
					"id": "options",
					"label": "Options",
					"info": "value:Label",
					"default": "featured:Featured\nbest-selling:Best Selling\nprice-ascending:Lowest Price"
				},
				{
					"type": "liquid",
					"id": "onchange",
					"label": "on Change",
					"info": "JavaScript"
				}
			]
		},
		{
			"type": "toggle-switch",
			"name": "Toggle Switch",
			"settings": [
				{
					"type": "liquid",
					"id": "model",
					"label": "Data source/model"
				},
				{
					"type": "textarea",
					"id": "options",
					"label": "Toggle Switch Options",
					"info": "value:Selected|Unselected",
					"default": "Product:View on Product,Model:View on Model"
				},
				{
					"type": "liquid",
					"id": "onchange",
					"label": "on Change",
					"info": "JavaScript"
				},
				{
					"type": "select",
					"id": "toggle_interaction",
					"label": "Toggle Interaction",
					"default": "click",
					"options": [
						{
							"value": "hover",
							"label": "Hover"
						},
						{
							"value": "click",
							"label": "Click"
						}
					]
				}
			]
		},
		{
			"type": "title",
			"name": "Title",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include Text id_prefix:title, label_prefix:Title"
				}
			]
		},
		{
			"type": "button",
			"name": "Button",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"info": "Internal Reference only"
				},
				{
					"type": "liquid",
					"id": "inclusion_js",
					"label": "Inclusion Logic (JavaScript)",
					"info": "JavaScript code that evaluates to a non-false value will output this button"
				},
				{
					"type": "select",
					"id": "style",
					"label": "Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "select",
					"id": "button_class_size",
					"label": "Button Size",
					"default": "",
					"options": [
						{
							"value": "button--small",
							"label": "Small"
						},
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				},
				{
					"type": "checkbox",
					"id": "disabled",
					"label": "Disable Button"
				},
				{
					"type": "text",
					"id": "button_text",
					"label": "Button Text"
				},
				{
					"type": "text",
					"id": "leading_icon",
					"label": "Leading Icon"
				},
				{
					"type": "text",
					"id": "trailing_icon",
					"label": "Trailing Icon"
				},
				{
					"type": "liquid",
					"id": "onclick",
					"label": "Click Event",
					"info": "JavaScript onclick event"
				},
				{
					"type": "select",
					"id": "onclick_type",
					"label": "Type of Click Event",
					"options": [
						{
							"value": "x-data @",
							"label": "Alpine"
						},
						{
							"value": "on",
							"label": "Native"
						}
					],
					"default": "x-data @"
				}
			]
		},
		{
			"type": "custom_liquid",
			"name": "Liquid",
			"limit": 4,
			"settings": [
				{
					"type": "liquid",
					"id": "liquid",
					"label": "Liquid Block"
				}
			]
		}
	],
	"presets": [
		{
			"name": "Sticky Elements"
		}
	]
}
{% endschema %}
