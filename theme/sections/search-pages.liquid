<div class="container p-10">

  <h1 class="title--primary title--page">
    {{ 'general.search.heading.one' | t }} "{{ search.terms }}"
  </h1>

  <div class="flex flex-wrap lg:flex-nowrap">

    <main class="lg:w-3/4">
      
      {% paginate search.results by 2000 %}

      {% for result in search.results %}

        {% if result.object_type == 'page' %}

          {%- render 'article-card', article: result, show_image: true, show_date: true, show_author: true, show_meta: false, card_classes: 'mb-6 pb-6 border-b' -%}

        {%- endif -%}

      {% endfor %}

      {%- if paginate.pages > 1 -%}

        {%- render 'pagination', paginate: paginate -%}

      {%- endif -%}

      {% endpaginate %}

    </main>

    <aside>
      


    </aside>

  </div>

</div>

{% schema %}
  {
    "name": "Search Pages",
    "settings": [
    ],
    "blocks": [
    ]
  }
{% endschema %}