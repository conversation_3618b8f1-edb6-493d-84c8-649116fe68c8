{% schema %}
{
	"name": "Liquid",
	"settings": [
		{
			"type": "paragraph",
			"content": "@include ConditionalDisplay"
		},
		{
			"type": "header",
			"content": "Custom Liquid"
		},
		{
			"type": "liquid",
			"id": "liquid",
			"label": "Custom Liquid Code"
		}
	],
	"blocks": [],
	"presets": [
		{
			"name": "Liquid",
			"category": "Advanced",
			"settings": {},
			"blocks": []
		}
	]
}
{% endschema %}

{% liquid 
	capture conditional_display
		render 'conditional-display' settings: section.settings
	endcapture
	render 'conditional-display-js' settings: section.settings id: section.id
%}

{%- unless conditional_display contains 'hide' -%}
<div class="section-{{ id }} {% render 'conditional-display-classes' settings: section.settings %}">
  {{ section.settings.liquid }}
</div>
{%- else -%}
{% if conditional_display contains 'async' %}
	{{ conditional_display }}
	<script type="async/section"></script>
{% endif %}
{%- endunless -%}
