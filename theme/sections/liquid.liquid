{% schema %}
{
	"name": "Liquid",
	"settings": [
		{
			"type": "paragraph",
			"content": "@include ConditionalDisplay"
		},
		{
			"type": "header",
			"content": "@global: Display Settings"
		},
		{
			"type": "checkbox",
			"id": "show_desktop",
			"label": "@global: Show desktop",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "show_mobile",
			"label": "@global: Show mobile",
			"default": true
		},
		{
			"type": "text",
			"id": "start_date",
			"label": "@global: Display Start Date"
		},
		{
			"type": "text",
			"id": "end_date",
			"label": "@global: Display End Date"
		},
		{
			"type": "liquid",
			"label": "@global: Liquid Logic",
			"id": "show_liquid",
			"info": "Insert any liquid logic that returns a value to display the section"
		},
		{
			"type": "textarea",
			"label": "@global: Javascript Logic",
			"id": "show_js",
			"info": "Insert any javascript logic that evaluates to true to display the section"
		},
		{
			"type": "checkbox",
			"label": "@global: Load Section Asynchonously on Scroll",
			"id": "async"
		},
		{
			"type": "header",
			"content": "Custom Liquid"
		},
		{
			"type": "liquid",
			"id": "liquid",
			"label": "Custom Liquid Code"
		}
	],
	"blocks": [],
	"presets": [
		{
			"name": "Liquid",
			"category": "Advanced",
			"settings": {},
			"blocks": []
		}
	]
}
{% endschema %}

{% liquid 
	capture conditional_display
		render 'conditional-display' settings: section.settings
	endcapture
	render 'conditional-display-js' settings: section.settings id: section.id
%}

{%- unless conditional_display contains 'hide' -%}
<div class="section-{{ id }} {% render 'conditional-display-classes' settings: section.settings %}">
  {{ section.settings.liquid }}
</div>
{%- else -%}
{% if conditional_display contains 'async' %}
	{{ conditional_display }}
	<script type="async/section"></script>
{% endif %}
{%- endunless -%}
