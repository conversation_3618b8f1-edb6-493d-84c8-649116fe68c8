{% liquid 
	assign section_type = 'collection'
%}
<section 
  class="section section--{{ section_type }} collection flex flex-wrap lg:flex-nowrap flex-row-reverse {{ section.settings.container_class }} items-start {% render 'class-settings' prefix:'wrapper_class' settings:section.settings %} {% if section.settings.quick_add == false %}no-quickadd{% endif %}"
  style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}" 
  x-data="{ collection: Alpine.reactive(window.collection)}" 
  x-init="window.collection = Alpine.reactive(window.collection||{}); window.Util.events.dispatch('Collection:loaded')"
  :view="collection.view"
  :class="$store.sidebar.visible ? '{{ section.settings.wrapper_class_gap_desktop }}' : 'lg:!gap-0'"
>

	{% assign sidebar_inclusion_restriction = false %}
	{% for block in section.blocks %} 
		{% if block.type == 'start-sidebar' %}
			{% assign sidebar_inclusion_restriction = block.settings.inclusion_javascript %}
		{% endif %}
	{% endfor %}

  <main x-data='{ inclusion_main: {% if sidebar_inclusion_restriction == "true" %}true{% elsif sidebar_inclusion_restriction == "false" %}false{% else %}{{sidebar_inclusion_restriction}}{% endif %} }' :class="{'lg:w-[100%] full-width-search-main': !{{ sidebar_inclusion_restriction }} }" class="w-full {% render 'class-settings' prefix:'main_class' settings:section.settings %} ">

    <header class="collection__header flex justify-between">

      {% render 'flex-nested-blocks' blocks:section.blocks from:'start-header', until:'end-header' %}

    </header>

		<section class="collection__header flex justify-between flex-col text-center ">

			{% render 'flex-nested-blocks' blocks:section.blocks from:'search_words_suggest', until:'render-till' %}
		
		</section>

    {% capture rows %}{%- render 'flex-nested-blocks' blocks:section.blocks from:'start-main', until:'end-main' -%}{% endcapture %}
    {%- if rows != blank -%}
      <section product-rows class="empty:hidden collection__carousel-rows {% render 'class-settings' prefix:'product_grid_class' settings:section.settings %}">
				{{ rows }}
			</section>
    {%- endif -%}
	
		<section product-grid class="collection__product-grid grid {% render 'class-settings' prefix:'product_grid_class' settings:section.settings %}" x-init="setTimeout(reinitialiseRatingSnippet, 200);">

			<template x-for="(product,index) in collection.products">
		
				<article :style="'order:'+(index + 1)" @click="window.history.replaceState({page:product.page,index:product.index},window.document.title,document.location.pathname+document.location.search)" :data-index="index">
					{% render 'product-item' settings:section.settings, _settings:settings %}
				</article>
		
			</template> 

			{% comment %} Content Item Grid Column {% endcomment %}
			{%- if section.settings.content_position != blank or section.settings.content_position_desktop != blank -%}
				{% liquid

					if section.settings.content_position != blank and section.settings.content_position != '0'
						assign order_class_mobile = section.settings.content_position | plus: 0
						assign order_class_mobile = order_class_mobile | minus: 1 | prepend: 'order-' | replace: 'order-0', 'order-first'
					else
						assign order_class_mobile = 'hidden'
					endif

					if section.settings.content_position_desktop != blank and section.settings.content_position_desktop != '0' and section.settings.image != blank
						assign order_class_desktop = section.settings.content_position_desktop | plus: 0
						assign order_class_desktop = order_class_desktop | minus: 1 | prepend: ' lg:order-' | replace: 'lg:order-0', 'lg:order-first'
					else
						assign order_class_desktop = order_class_desktop | append: ' lg:hidden'
					endif

					assign order_classes = order_class_mobile | append: order_class_desktop
					assign content_width = section.settings.content_class_gridcols | append: ' ' | append: section.settings.content_class_gridcols_desktop 
					assign content_item_classes = order_classes | append: ' ' | append: content_width

					render 'content-item' settings:section.settings item_classes:content_item_classes collection_grid:true
				%}
			{%- endif -%}
	
		</section>
  
	  <footer class="collection__footer flex justify-between">
  
			{% render 'flex-nested-blocks' blocks:section.blocks from:'start-footer', until:'end-footer' %}
  
	  </footer>
  
	</main>

	<!-- collection.total_products is to hide the sidebar on no search results -->
  <aside 
    :class="$store.sidebar.visible ? '{{section.settings.sidebar_class_width_desktop}}' : 'lg:!w-0 lg:!min-w-0'" 
		{% if section.settings.sidebar_visibility == false %}x-cloak{% endif %}
    x-data='{ interval:false, elapsed: 0, inclusion_js_aside: {% if sidebar_inclusion_restriction == "true" %}true{% elsif sidebar_inclusion_restriction == "false" %}false{% else %}{{sidebar_inclusion_restriction}}{% endif %} }' x-init="
        this.interval = setInterval(() => {
            window.dispatchEvent(new Event('resize'))
            elapsed += 500;
            if (elapsed >= 2000) {
                clearInterval(interval);
            }
        }, 100);
    " x-show='{% if sidebar_inclusion_restriction == "true" %}true{% elsif sidebar_inclusion_restriction == "false" %}false{% else %}{{sidebar_inclusion_restriction}}{% endif %}' data-modal="filters" class="modal max-md:modal--left w-full collection__sidebar overflow-x-hidden overflow-y-hidden lg:overflow-y-hidden  h-screen {% render 'class-settings' prefix:'sidebar_class' settings:section.settings %}">
  
		<template x-if="Object.keys(collection.filters.applied).length == 0 ">
			<header class="flex items-center collection__sidebar-header collection__sidebar-header--clear">
				<div class="label flex items-center w-full">
					{% render 'icon' icon:'filter' %}
					<span class="my-0">{{ 'sections.collection_template.filter_and_sort' | t }}</span>
				</div>
				<button onclick="Modal.close()" class="ml-auto">{% render 'icon' icon:'x' %}</button>
			</header>
		</template>
  
		<template x-if="Object.keys(collection.filters.applied).length > 0 ">
			<header class="flex items-stretch collection__sidebar-header collection__sidebar-header--filtered">
				<button class="flex items-center w-1/2" onclick="Collection.clear()">
					{% render 'icon' icon:'filter' %}
					<span class="my-0">{{ 'sections.collection_template.clear_all' | t }} (<span x-text="Object.keys(collection.filters.applied).length"></span>)</span>
				</button>
				<button class="apply w-1/2" onclick="Modal.close()">
					{{ 'sections.collection_template.filter_apply' | t }}
				</button>
			</header>
		</template>
  
		<main class="flex flex-col items-start no-scrollbar collection__sidebar-body max-h-full lg:overflow-y-hidden ">
			
			{% render 'flex-nested-blocks' blocks:section.blocks from:'start-sidebar', until:'end-sidebar' components:'collection-filters' %}
  
		</main>
  
	</aside>
  
</section>

<script>
	window.collection = {
		title: {{ collection.title | json }},
		id: {{ collection.id | json }},
		handle: {{ collection.handle | json }},
		settings: {{ section.settings | json }},
		view:'product',
		default_sort: {{ section.settings.sort | json }},
		innerWidth: window.innerWidth
	};  

	window.swatch_settings = {
		{% if shop.metafields.pfs-swatch-settings.size > 0 -%}
			{% for key in shop.metafields.pfs-swatch-settings %}
			{{ key[0] | json }}: {{ key[1] | replace: '=>', ':' }},
			{% endfor %}
		{%- endif %}
	}

	collection.settings.remote = []
	{%- for block in section.blocks -%}
		{% case block.type %}
			{% when 'data-remote' %}
				collection.settings.remote = {{ block.settings | json }}
				collection.settings.remote_url = '{{ block.settings.remote_url }}';
				collection.settings.map = {{ block.settings.map | default: 'false'}};
			{% when 'data-boost' %}
				{%- if block.settings.oos_products_display -%}
					{%- assign show_oos = 'false' -%}
				{%- else -%}
					{%- assign show_oos = 'true' -%}
				{%- endif -%}
				{% render 'boost-collection-settings', collection:collection, search:search, show_oos:show_oos %}
		{% endcase %}
	{%- endfor -%}

	document.addEventListener('alpine:init', function() {
		Alpine.store('sidebar', {
			visible: {{ section.settings.sidebar_visibility }},
			toggle() {
				this.visible = ! this.visible
			}
		})
	})

	if (typeof window.productMetafields === 'undefined') {
		window.productMetafields = []
	}
	{%- if collection.handle -%}
		{%- paginate collections[collection.handle].products by 1000 -%}
			{%- for product in collections[collection.handle].products -%}
				if (typeof window.productMetafields["style"] === 'undefined') {
					window.productMetafields["style"] = []
					window.productMetafields["style"]["{{ product.handle }}"] = []
					window.productMetafields["style"]["{{ product.handle }}"]["subtitle"] = []
				} else if (typeof window.productMetafields["style"]["{{ product.handle }}"] === 'undefined') {
					window.productMetafields["style"]["{{ product.handle }}"] = []
					window.productMetafields["style"]["{{ product.handle }}"]["subtitle"] = []
				} 
				window.productMetafields["style"]["{{ product.handle }}"]["subtitle"].push("{{- product.metafields.product.style.value.subtitle -}}");
			{%- endfor -%}
		{%- endpaginate -%}
	{%- endif -%}
</script>

<script src="{{ 'collection.js' | asset_url }}" defer="defer"></script>

{%- if section.settings.paging == 'scroll' -%}
	<script src="{{ 'collection-endless.js' | asset_url }}" defer="defer"></script>
{% endif %}

{%- if section.settings.carousel_swap -%}
	{%- render 'collection-carousel-swap' -%}
{%- endif -%}

{% schema %}
{
	"name": "Collection",
	"tag": "section",
	"class": "shopify-section--collection",
	"settings": [
		{
			"type": "number",
			"id": "limit",
			"label": "Products per Page",
			"default": 9
		},
		{
			"type": "checkbox",
			"id": "quick_add",
			"label": "Include Quick Add",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "single_variant_direct_addtocart",
			"label": "Direct add-to-cart for single variant",
			"default": false
		},
		{
			"type": "text",
			"id": "sort",
			"label": "Default Sort",
			"default": "featured"
		},
		{
			"type": "select",
			"id": "default_image_type",
			"label": "Default Image Type",
			"options": [
				{
					"value": "product_image",
					"label": "Product"
				},
				{
					"value": "model_image",
					"label": "Model"
				}
			],
			"default": "product_image"
		},
		{
			"type": "header",
			"content": "Container Settings"
		},
		{
			"type": "select",
			"id": "wrapper_class_container",
			"label": "Wrapper Container",
			"options": [
				{
					"value": "w-full",
					"label": "Full Screen Width"
				},
				{
					"value": "container",
					"label": "Container Width"
				},
				{
					"value": "container container--wide",
					"label": "Wide Container"
				},
				{
					"value": "container container--narrow",
					"label": "Narrow Container"
				},
				{
					"value": "container container--tight",
					"label": "Very Narrow Container"
				},
				{
					"value": "container--product",
					"label": "Product Container"
				}
			]
		},
		{
			"type": "paragraph",
			"content": "@include FlexLayout, label_prefix:Section, id_prefix:wrapper_class"
		},
		{
			"type": "select",
			"id": "sidebar_class_width_desktop",
			"label": "Sidebar Width (Desktop)",
			"default": "lg:w-1/4",
			"options": [
				{
					"value": "lg:w-1/10",
					"label": "10%"
				},
				{
					"value": "lg:min-w-7/40",
					"label": "17.5%"
				},
				{
					"value": "lg:w-1/5",
					"label": "20%"
				},
				{
					"value": "lg:w-1/4",
					"label": "25%"
				},
				{
					"value": "lg:w-1/3",
					"label": "33%"
				},
				{
					"value": "lg:w-2/5",
					"label": "40%"
				},
				{
					"value": "lg:w-1/2",
					"label": "50%"
				}
			]
		},
		{
			"type": "select",
			"id": "main_class_width_desktop",
			"label": "Main Width (Desktop)",
			"default": "lg:w-3/4",
			"options": [
				{
					"value": "lg:w-1/2",
					"label": "50%"
				},
				{
					"value": "lg:w-3/5",
					"label": "60%"
				},
				{
					"value": "lg:w-2/3",
					"label": "66%"
				},
				{
					"value": "lg:w-3/4",
					"label": "75%"
				},
				{
					"value": "lg:w-4/5",
					"label": "80%"
				},
				{
					"value": "lg:w-[82.5%]",
					"label": "82.5%"
				},
				{
					"value": "lg:w-9/10",
					"label": "90%"
				},
				{
					"value": "lg:w-full",
					"label": "100%"
				}
			]
		},
		{
			"type": "checkbox",
			"id": "sidebar_visibility",
			"label": "Display Sidebar On Load",
			"default": true
		},
		{
			"type": "checkbox",
			"id": "sidebar_calculate_height",
			"label": "Calculate Sidebar Height On Load",
			"default": true
		},
		{
			"type": "paragraph",
			"content": "@include BlockGrid, label_prefix:Product Grid, id_prefix:product_grid_class"
		},
		{
			"type": "select",
			"id": "product_grid_class_vertical_padding",
			"label": "Product Grid Vertical Padding",
			"options": [
				{
					"value": "@include Spacing prop:py",
					"label": "Inclusion"
				},
				{
					"value": "py-0",
					"label": "@global: None"
				},
				{
					"value": "py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "py-xs",
					"label": "@global: XS"
				},
				{
					"value": "py-sm",
					"label": "@global: SM"
				},
				{
					"value": "py-md",
					"label": "@global: MD"
				},
				{
					"value": "py-lg",
					"label": "@global: LG"
				},
				{
					"value": "py-xl",
					"label": "@global: XL"
				},
				{
					"value": "py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "py-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "product_grid_class_horizontal_padding",
			"label": "Product Grid Horizontal Padding",
			"options": [
				{
					"value": "@include Spacing prop:px",
					"label": "Inclusion"
				},
				{
					"value": "px-0",
					"label": "@global: None"
				},
				{
					"value": "px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "px-xs",
					"label": "@global: XS"
				},
				{
					"value": "px-sm",
					"label": "@global: SM"
				},
				{
					"value": "px-md",
					"label": "@global: MD"
				},
				{
					"value": "px-lg",
					"label": "@global: LG"
				},
				{
					"value": "px-xl",
					"label": "@global: XL"
				},
				{
					"value": "px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "px-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "product_grid_class_gap",
			"label": "Product Grid Spacing Gap",
			"options": [
				{
					"value": "@include Spacing prop:gap",
					"label": "Inclusion"
				},
				{
					"value": "gap-0",
					"label": "@global: None"
				},
				{
					"value": "gap-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "gap-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "gap-xs",
					"label": "@global: XS"
				},
				{
					"value": "gap-sm",
					"label": "@global: SM"
				},
				{
					"value": "gap-md",
					"label": "@global: MD"
				},
				{
					"value": "gap-lg",
					"label": "@global: LG"
				},
				{
					"value": "gap-xl",
					"label": "@global: XL"
				},
				{
					"value": "gap-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "gap-3xl",
					"label": "@global: 3XL"
				}
			]
		},
		{
			"type": "select",
			"id": "product_grid_class_vertical_padding_desktop",
			"label": "Product Grid Vertical Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:py",
					"label": "Inclusion"
				},
				{
					"value": "lg:py-0",
					"label": "@global: None"
				},
				{
					"value": "lg:py-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:py-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:py-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:py-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:py-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:py-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:py-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:py-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:py-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:py-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:py-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:py-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:py-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:py-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "select",
			"id": "product_grid_class_horizontal_padding_desktop",
			"label": "Product Grid Horizontal Padding Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:px",
					"label": "Inclusion"
				},
				{
					"value": "lg:px-0",
					"label": "@global: None"
				},
				{
					"value": "lg:px-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:px-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:px-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:px-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:px-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:px-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:px-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:px-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:px-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:px-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:px-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:px-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:px-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:px-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "select",
			"id": "product_grid_class_gap_desktop",
			"label": "Product Grid Spacing Gap Desktop",
			"options": [
				{
					"value": "@include SpacingDesktop prop:gap",
					"label": "Inclusion"
				},
				{
					"value": "lg:gap-0",
					"label": "@global: None"
				},
				{
					"value": "lg:gap-3xs",
					"label": "@global: 3XS"
				},
				{
					"value": "lg:gap-2xs",
					"label": "@global: 2XS"
				},
				{
					"value": "lg:gap-xs",
					"label": "@global: XS"
				},
				{
					"value": "lg:gap-sm",
					"label": "@global: SM"
				},
				{
					"value": "lg:gap-md",
					"label": "@global: MD"
				},
				{
					"value": "lg:gap-lg",
					"label": "@global: LG"
				},
				{
					"value": "lg:gap-xl",
					"label": "@global: XL"
				},
				{
					"value": "lg:gap-2xl",
					"label": "@global: 2XL"
				},
				{
					"value": "lg:gap-3xl",
					"label": "@global: 3XL"
				},
				{
					"value": "lg:gap-4xl",
					"label": "@global: 4XL"
				},
				{
					"value": "lg:gap-5xl",
					"label": "@global: 5XL"
				},
				{
					"value": "lg:gap-6xl",
					"label": "@global: 6XL"
				},
				{
					"value": "lg:gap-7xl",
					"label": "@global: 7XL"
				},
				{
					"value": "lg:gap-8xl",
					"label": "@global: 8XL"
				}
			]
		},
		{
			"type": "text",
			"id": "product_carousel_classes",
			"label": "Product Carousels Classes",
			"default": "lg:w-3/4 p-5 mt-6 grid grid-cols-1 lg:gap-2xl"
		},
		{
			"type": "text",
			"id": "product_filters_classes",
			"label": "Product Filters Classes",
			"default": "w-1/4 mt-6 sticky top-24"
		},
		{
			"type": "checkbox",
			"id": "enable_clear_all_filter",
			"label": "Enable Clear All filter",
			"default": true
		},
		{
			"type": "text",
			"id": "filter_accordion_icon",
			"label": "Filter Set Acordion Icon",
			"default": "chevron-up"
		},
		{
			"type": "radio",
			"id": "paging",
			"label": "Collection Paging",
			"default": "scroll",
			"options": [
				{
					"value": "scroll",
					"label": "Scroll-Triggered Auto Loading"
				}
			]
		},
		{
			"type": "checkbox",
			"id": "carousel_swap",
			"label": "Carousel/Grid Toggle",
			"info": "Toggle between carousels and product Grid depending on applied filters"
		},
		{
			"type": "header",
			"content": "Content Item Grid Column Settings"
		},
		{
			"type": "select",
			"id": "content_class_gridcols",
			"label": "Content Item Grid Columns",
			"options": [
				{
					"value": "col-span-1",
					"label": "1 Column"
				},
				{
					"value": "col-span-2",
					"label": "2 Columns"
				},
				{
					"value": "col-span-3",
					"label": "3 Columns"
				}
			],
			"default": "col-span-1"
		},
		{
			"type": "select",
			"id": "content_class_gridcols_desktop",
			"label": "Content Item Grid Columns Desktop",
			"options": [
				{
					"value": "lg:col-span-1",
					"label": "1 Column"
				},
				{
					"value": "lg:col-span-2",
					"label": "2 Columns"
				},
				{
					"value": "lg:col-span-3",
					"label": "3 Columns"
				},
				{
					"value": "lg:col-span-4",
					"label": "4 Columns"
				}
			],
			"default": "lg:col-span-1"
		},
		{
			"type": "text",
			"id": "content_position",
			"label": "Content Item Position"
		},
		{
			"type": "text",
			"id": "content_position_desktop",
			"label": "Content Item Position Desktop"
		},
		{
			"type": "header",
			"content": "Content Settings"
		},
		{
			"type": "checkbox",
			"id": "include_header",
			"label": "Include Collection Header",
			"default": true
		},
		{
			"type": "paragraph",
			"content": "@include ContentItem"
		},
		{
			"type": "paragraph",
			"content": "@include ProductItem"
		},
		{
			"type": "select",
			"id": "product_item_show_price_right",
			"label": "Show Price on Right or Bottom",
			"default": "bottom",
			"options": [
				{
					"label": "Right Side of Title",
					"value": "right"
				},
				{
					"label": "Below of Title",
					"value": "bottom"
				}
			]
		}
	],
	"blocks": [
		{
			"type": "start-header",
			"name": "⤷ Header"
		},
		{
			"type": "end-header",
			"name": "⤶ Header End"
		},
		{
			"type": "start-sidebar",
			"name": "⤷ Sidebar",
			"settings": [
				{
					"type": "textarea",
					"label": "Javascript Logic Inclusion",
					"id": "inclusion_javascript",
					"info": "Insert any javascript logic that evaluates to true to display the section",
					"default": "true"
				}
			]
		},
		{
			"type": "end-sidebar",
			"name": "⤶ Sidebar End"
		},
		{
			"type": "start-main",
			"name": "⤷ Main (unfiltered)"
		},
		{
			"type": "end-main",
			"name": "⤶ Main End"
		},
		{
			"type": "start-footer",
			"name": "⤷ Footer"
		},
		{
			"type": "end-footer",
			"name": "⤶ Footer End"
		},
		{
			"type": "frame",
			"name": "⤷ Frame",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤷ Frame"
				},
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:article_class"
				},
				{
					"type": "paragraph",
					"content": "@include LogicInclusion"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "header",
					"content": "Frame Height Settings"
				},
				{
					"type": "select",
					"id": "article_class_height",
					"label": "Frame Height",
					"options": [
						{
							"value": "h-auto",
							"label": "Auto"
						},
						{
							"value": "h-full",
							"label": "100%"
						},
						{
							"value": "h-3/4",
							"label": "75%"
						},
						{
							"value": "h-1/2",
							"label": "50%"
						},
						{
							"value": "h-1/4",
							"label": "25%"
						},
						{
							"value": "h-screen",
							"label": "Full Screen"
						},
						{
							"value": "h-dvh",
							"label": "Dynamic Viewport"
						},
						{
							"value": "h-lvh",
							"label": "Largest Viewport"
						},
						{
							"value": "h-svh",
							"label": "Smallest Viewport"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_height_desktop",
					"label": "Frame Height Desktop",
					"options": [
						{
							"value": "lg:h-auto",
							"label": "Auto"
						},
						{
							"value": "lg:h-full",
							"label": "100%"
						},
						{
							"value": "lg:h-3/4",
							"label": "75%"
						},
						{
							"value": "lg:h-1/2",
							"label": "50%"
						},
						{
							"value": "lg:h-1/4",
							"label": "25%"
						},
						{
							"value": "lg:h-screen",
							"label": "Full Screen"
						},
						{
							"value": "lg:h-main",
							"label": "Full Screen minus Header"
						}
					]
				},
				{
					"type": "color",
					"id": "article_style_background_color",
					"label": "Frame Background Color"
				},
				{
					"type": "color_background",
					"id": "article_style_background",
					"label": "Frame Background Gradient"
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "paragraph",
					"content": "_________________________________"
				},
				{
					"type": "header",
					"content": "Advanced Settings"
				},
				{
					"type": "select",
					"id": "article_class_position",
					"label": "Position",
					"default": "relative",
					"options": [
						{
							"value": "",
							"label": "Unset"
						},
						{
							"value": "relative",
							"label": "Relative"
						},
						{
							"value": "absolute",
							"label": "Absolute"
						},
						{
							"value": "sticky top-0",
							"label": "Sticky Top"
						},
						{
							"value": "sticky bottom-0",
							"label": "Sticky Bottom"
						},
						{
							"value": "fixed top-0",
							"label": "Fixed Top"
						},
						{
							"value": "fixed bottom-0",
							"label": "Fixed Bottom"
						}
					]
				},
				{
					"type": "select",
					"id": "article_class_overflow",
					"label": "Horizontal Overflow",
					"default": "",
					"options": [
						{
							"value": "",
							"label": "Contain"
						},
						{
							"value": "overflow-x-scroll snap-none overscroll-x-contain",
							"label": "Smooth Scroll"
						},
						{
							"value": "overflow-x-scroll snap-x snap-start no-scrollbar",
							"label": "Snap Start"
						},
						{
							"value": "overflow-x-scroll snap-x snap-center no-scrollbar",
							"label": "Snap Center"
						}
					]
				},
				{
					"type": "liquid",
					"id": "article_class_custom_classes",
					"label": "Custom Classes"
				},
				{
					"type": "liquid",
					"id": "article_attr_x_data",
					"label": "Dynamic Data Source"
				},
				{
					"type": "liquid",
					"id": "article_attr_x_if",
					"label": "JS Conditional Rendering"
				}
			]
		},
		{
			"type": "break",
			"name": "⤶ Frame Break",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤶ Frame Break"
				}
			]
		},
		{
			"type": "applied-filters",
			"name": "Applied Filters",
			"settings": []
		},
		{
			"type": "content-item",
			"name": "Content Item",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:item_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include Aspect, id_prefix:item_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include ContentItem"
				}
			]
		},
		{
			"type": "collection-filters",
			"name": "Collection Filters"
		},
		{
			"type": "rich-text",
			"name": "Rich Text",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include RichText id_prefix:text, label_prefix:Rich Text"
				},
				{
					"type": "select",
					"id": "text_class_justification",
					"label": "Text Justification",
					"options": [
						{
							"value": "text-left",
							"label": "⇐"
						},
						{
							"value": "text-center",
							"label": "→←"
						},
						{
							"value": "text-right",
							"label": "⇒"
						},
						{
							"value": "text-justify",
							"label": "≡"
						}
					]
				},
				{
					"type": "select",
					"id": "text_class_measure",
					"label": "Text Measure",
					"options": [
						{
							"value": "",
							"label": "None"
						},
						{
							"value": "measure",
							"label": "Contain"
						}
					]
				}
			]
		},
		{
			"type": "title",
			"name": "Title",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include Text, id_prefix:title, label_prefix:Title"
				}
			]
		},
		{
			"type": "dropdown-menu",
			"name": "Dropdown Menu",
			"settings": [
				{
					"type": "text",
					"id": "label",
					"label": "*Label"
				},
				{
					"type": "liquid",
					"id": "model",
					"label": "Data source/model"
				},
				{
					"type": "textarea",
					"id": "options",
					"label": "Options",
					"info": "value:Label",
					"default": "featured:Featured\nbest-selling:Best Selling\nprice-ascending:Lowest Price"
				},
				{
					"type": "liquid",
					"id": "onchange",
					"label": "on Change",
					"info": "JavaScript"
				}
			]
		},
		{
			"type": "toggle-switch",
			"name": "Toggle Switch",
			"settings": [
				{
					"type": "liquid",
					"id": "model",
					"label": "Data source/model"
				},
				{
					"type": "textarea",
					"id": "options",
					"label": "Toggle Switch Options",
					"info": "value:Selected|Unselected",
					"default": "Product:View on Product,Model:View on Model"
				},
				{
					"type": "liquid",
					"id": "onchange",
					"label": "on Change",
					"info": "JavaScript"
				}
			]
		},
		{
			"type": "button",
			"name": "Button",
			"settings": [
				{
					"type": "select",
					"id": "style",
					"label": "Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "text",
					"id": "button_text",
					"label": "Button Text"
				},
				{
					"type": "text",
					"id": "leading_icon",
					"label": "Leading Icon"
				},
				{
					"type": "text",
					"id": "trailing_icon",
					"label": "Trailing Icon"
				},
				{
					"type": "url",
					"id": "link",
					"label": "Link"
				},
				{
					"type": "liquid",
					"id": "onclick",
					"label": "Click Event",
					"info": "JavaScript onclick event, overrides native functionality."
				},
				{
					"type": "select",
					"id": "onclick_type",
					"label": "Type of Click Event",
					"options": [
						{
							"value": "x-data @",
							"label": "Alpine"
						},
						{
							"value": "on",
							"label": "Native"
						}
					],
					"default": "x-data @"
				},
				{
					"type": "checkbox",
					"id": "form_validity",
					"label": "Require Form Validity",
					"info": "Disables button until all fields in enclosing form are valid"
				},
				{
					"type": "paragraph",
					"content": "_________________________________"
				},
				{
					"type": "header",
					"content": "Advanced Settings"
				},
				{
					"type": "liquid",
					"id": "button_class_custom_classes",
					"label": "Custom Classes"
				},
				{
					"type": "liquid",
					"id": "button_attr_x_data",
					"label": "Dynamic Data Source"
				},
				{
					"type": "liquid",
					"id": "button_attr_x_show",
					"label": "JS Conditional Rendering"
				},
				{
					"type": "liquid",
					"id": "button_attr_x_text",
					"label": "Dynamic Text"
				},
				{
					"type": "liquid",
					"id": "button_attr_QQclass",
					"label": "Dynamic Classes"
				},
				{
					"type": "checkbox",
					"id": "disabled",
					"label": "Disable Button"
				}
			]
		},
		{
			"type": "data-boost",
			"name": "BoostCommerce Data",
			"limit": 1,
			"settings": [
				{
					"id": "oos_products_display",
					"label": "Show Out of Stock Products on PLP",
					"type": "checkbox",
					"default": false
				}
			]
		},
		{
			"type": "data-remote",
			"name": "Open Remote Data",
			"limit": 1,
			"settings": [
				{
					"id": "remote_url",
					"label": "Remote URL",
					"type": "liquid"
				},
				{
					"type": "select",
					"id": "config_method",
					"label": "Request Method",
					"default": "GET",
					"options": [
						{
							"value": "GET",
							"label": "GET"
						},
						{
							"value": "POST",
							"label": "POST"
						},
						{
							"value": "PUT",
							"label": "PUT"
						}
					]
				},
				{
					"type": "select",
					"id": "config_mode",
					"label": "CORS Request Mode",
					"default": "cors",
					"options": [
						{
							"value": "cors",
							"label": "cors"
						},
						{
							"value": "no-cors",
							"label": "no-cors"
						}
					]
				},
				{
					"id": "config_headers",
					"label": "Request Headers",
					"type": "liquid"
				},
				{
					"id": "config_body",
					"label": "Request Body",
					"type": "liquid"
				},
				{
					"id": "map",
					"label": "Response Data Map",
					"type": "liquid"
				}
			]
		},
		{
			"type": "collection_carousel",
			"name": "Collection Carousel",
			"settings": [
				{
					"type": "product",
					"id": "title_product",
					"label": "Title Product"
				},
				{
					"type": "collection",
					"id": "collection",
					"label": "Collection"
				},
				{
					"type": "text",
					"id": "shop_all_text",
					"label": "Shop All Button Text",
					"default": "Shop all"
				},
				{
					"type": "paragraph",
					"content": "@include Carousel"
				},
				{
					"type": "paragraph",
					"content": "@include ProductItem"
				},
				{
					"type": "number",
					"id": "content_position",
					"label": "Content Item Position"
				},
				{
					"type": "paragraph",
					"content": "@include ContentItem"
				}
			]
		},
		{
			"name": "Divider",
			"type": "divider",
			"settings": [
				{
					"type": "color",
					"id": "divider_style_border_color",
					"label": "Color"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:divider_class, label_prefix:Divider "
				}
			]
		},
		{
			"name": "Spacer",
			"type": "spacer",
			"settings": [
				{
					"type": "select",
					"id": "spacer_class_vertical_spacing",
					"label": "Item Vertical Spacer",
					"options": [
						{
							"value": "@include Spacing prop:pb",
							"label": "Inclusion"
						},
						{
							"value": "pb-0",
							"label": "@global: None"
						},
						{
							"value": "pb-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "pb-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "pb-xs",
							"label": "@global: XS"
						},
						{
							"value": "pb-sm",
							"label": "@global: SM"
						},
						{
							"value": "pb-md",
							"label": "@global: MD"
						},
						{
							"value": "pb-lg",
							"label": "@global: LG"
						},
						{
							"value": "pb-xl",
							"label": "@global: XL"
						},
						{
							"value": "pb-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "pb-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "spacer_class_horizontal_spacing",
					"label": "Item Horizontal Spacer",
					"options": [
						{
							"value": "@include Spacing prop:pr",
							"label": "Inclusion"
						},
						{
							"value": "pr-0",
							"label": "@global: None"
						},
						{
							"value": "pr-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "pr-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "pr-xs",
							"label": "@global: XS"
						},
						{
							"value": "pr-sm",
							"label": "@global: SM"
						},
						{
							"value": "pr-md",
							"label": "@global: MD"
						},
						{
							"value": "pr-lg",
							"label": "@global: LG"
						},
						{
							"value": "pr-xl",
							"label": "@global: XL"
						},
						{
							"value": "pr-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "pr-3xl",
							"label": "@global: 3XL"
						}
					]
				},
				{
					"type": "select",
					"id": "spacer_class_vertical_spacing_desktop",
					"label": "Item Vertical Spacer Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:pb",
							"label": "Inclusion"
						},
						{
							"value": "lg:pb-0",
							"label": "@global: None"
						},
						{
							"value": "lg:pb-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:pb-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:pb-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:pb-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:pb-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:pb-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:pb-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:pb-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:pb-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:pb-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:pb-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:pb-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:pb-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:pb-8xl",
							"label": "@global: 8XL"
						}
					]
				},
				{
					"type": "select",
					"id": "spacer_class_horizontal_spacing_desktop",
					"label": "Item Horizontal Spacer Desktop",
					"options": [
						{
							"value": "@include SpacingDesktop prop:pr",
							"label": "Inclusion"
						},
						{
							"value": "lg:pr-0",
							"label": "@global: None"
						},
						{
							"value": "lg:pr-3xs",
							"label": "@global: 3XS"
						},
						{
							"value": "lg:pr-2xs",
							"label": "@global: 2XS"
						},
						{
							"value": "lg:pr-xs",
							"label": "@global: XS"
						},
						{
							"value": "lg:pr-sm",
							"label": "@global: SM"
						},
						{
							"value": "lg:pr-md",
							"label": "@global: MD"
						},
						{
							"value": "lg:pr-lg",
							"label": "@global: LG"
						},
						{
							"value": "lg:pr-xl",
							"label": "@global: XL"
						},
						{
							"value": "lg:pr-2xl",
							"label": "@global: 2XL"
						},
						{
							"value": "lg:pr-3xl",
							"label": "@global: 3XL"
						},
						{
							"value": "lg:pr-4xl",
							"label": "@global: 4XL"
						},
						{
							"value": "lg:pr-5xl",
							"label": "@global: 5XL"
						},
						{
							"value": "lg:pr-6xl",
							"label": "@global: 6XL"
						},
						{
							"value": "lg:pr-7xl",
							"label": "@global: 7XL"
						},
						{
							"value": "lg:pr-8xl",
							"label": "@global: 8XL"
						}
					]
				}
			]
		},
		{
			"type": "collection_grid",
			"name": "Collection Grid",
			"settings": [
				{
					"type": "text",
					"id": "id",
					"label": "Section ID",
					"info": "Useful for providing an anchor point for jumping between sections."
				},
				{
					"type": "collection",
					"id": "collection",
					"label": "Collection"
				},
				{
					"type": "header",
					"content": "Header Interactivity"
				},
				{
					"type": "url",
					"id": "header_link",
					"label": "Link"
				},
				{
					"type": "checkbox",
					"id": "filter_button",
					"label": "Display Filter Button (Mobile)",
					"default": false
				},
				{
					"type": "header",
					"content": "Header Text Layout"
				},
				{
					"type": "select",
					"id": "header_class_display",
					"label": "Header Text Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						}
					]
				},
				{
					"type": "select",
					"id": "header_class_display_desktop",
					"label": "Header Text Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						}
					]
				},
				{
					"type": "select",
					"id": "header_class_direction",
					"label": "Header Text Direction",
					"default": "flex-row",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "header_class_layout",
					"label": "Header Text Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "header_class_layout_spacing",
					"label": "Header Text Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "header_class_vertical_padding",
					"label": "Header Text Vertical Padding",
					"options": [
						{
							"value": "py-0",
							"label": "None"
						},
						{
							"value": "py-2xs",
							"label": "2XS"
						},
						{
							"value": "py-xs",
							"label": "XS"
						},
						{
							"value": "py-sm",
							"label": "SM"
						},
						{
							"value": "py-md",
							"label": "MD"
						},
						{
							"value": "py-lg",
							"label": "LG"
						},
						{
							"value": "py-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "header_class_horizontal_padding",
					"label": "Header Text Horizontal Padding",
					"options": [
						{
							"value": "px-0",
							"label": "None"
						},
						{
							"value": "px-2xs",
							"label": "2XS"
						},
						{
							"value": "px-xs",
							"label": "XS"
						},
						{
							"value": "px-sm",
							"label": "SM"
						},
						{
							"value": "px-md",
							"label": "MD"
						},
						{
							"value": "px-lg",
							"label": "LG"
						},
						{
							"value": "px-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "header_class_gap",
					"label": "Header Text Spacing Gap",
					"options": [
						{
							"value": "gap-0",
							"label": "None"
						},
						{
							"value": "gap-2xs",
							"label": "2XS"
						},
						{
							"value": "gap-xs",
							"label": "XS"
						},
						{
							"value": "gap-sm",
							"label": "SM"
						},
						{
							"value": "gap-md",
							"label": "MD"
						},
						{
							"value": "gap-lg",
							"label": "LG"
						},
						{
							"value": "gap-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "header_class_vertical_padding_desktop",
					"label": "Header Text Vertical Padding Desktop",
					"options": [
						{
							"value": "lg:py-0",
							"label": "None"
						},
						{
							"value": "lg:py-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "XS"
						},
						{
							"value": "lg:py-sm",
							"label": "SM"
						},
						{
							"value": "lg:py-md",
							"label": "MD"
						},
						{
							"value": "lg:py-lg",
							"label": "LG"
						},
						{
							"value": "lg:py-xl",
							"label": "XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "select",
					"id": "header_class_horizontal_padding_desktop",
					"label": "Header Text Horizontal Padding Desktop",
					"options": [
						{
							"value": "lg:px-0",
							"label": "None"
						},
						{
							"value": "lg:px-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "XS"
						},
						{
							"value": "lg:px-sm",
							"label": "SM"
						},
						{
							"value": "lg:px-md",
							"label": "MD"
						},
						{
							"value": "lg:px-lg",
							"label": "LG"
						},
						{
							"value": "lg:px-xl",
							"label": "XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "select",
					"id": "header_class_gap_desktop",
					"label": "Header Text Spacing Gap Desktop",
					"options": [
						{
							"value": "lg:gap-0",
							"label": "None"
						},
						{
							"value": "lg:gap-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "SM"
						},
						{
							"value": "lg:gap-md",
							"label": "MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "header",
					"content": "Title Settings"
				},
				{
					"type": "text",
					"id": "title_text",
					"label": "Title Text"
				},
				{
					"type": "select",
					"id": "title_element",
					"label": "Title Element",
					"default": "p",
					"options": [
						{
							"value": "h1",
							"label": " Heading 1"
						},
						{
							"value": "h2",
							"label": " Heading 2"
						},
						{
							"value": "h3",
							"label": " Heading 3"
						},
						{
							"value": "h4",
							"label": " Heading 4"
						},
						{
							"value": "h5",
							"label": " Heading 5"
						},
						{
							"value": "p",
							"label": " Paragraph"
						},
						{
							"value": "div",
							"label": " Div"
						}
					]
				},
				{
					"type": "select",
					"id": "title_class_type_style",
					"label": "Title Type Style",
					"options": [
						{
							"value": "",
							"label": " Auto"
						},
						{
							"value": "type-body",
							"label": " Body"
						},
						{
							"value": "type-hero",
							"label": " Hero"
						},
						{
							"value": "type-eyebrow",
							"label": " Eyebrow"
						},
						{
							"value": "type-headline",
							"label": " Headline"
						},
						{
							"value": "type-subline",
							"label": " Subline"
						},
						{
							"value": "type-micro",
							"label": " Micro"
						},
						{
							"value": "type-item",
							"label": " Item Title"
						},
						{
							"value": "type-section",
							"label": " Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "title_class_type_size",
					"label": "Title Type Size",
					"options": [
						{
							"value": "",
							"label": " Default"
						},
						{
							"value": "type--sm",
							"label": " Smaller"
						},
						{
							"value": "type--lg",
							"label": " Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "title_style_color",
					"label": "Title Color"
				},
				{
					"type": "header",
					"content": "Subtitle Settings"
				},
				{
					"type": "text",
					"id": "subtitle_text",
					"label": "Subtitle Text"
				},
				{
					"type": "select",
					"id": "subtitle_element",
					"label": "Subtitle Element",
					"default": "p",
					"options": [
						{
							"value": "h1",
							"label": " Heading 1"
						},
						{
							"value": "h2",
							"label": " Heading 2"
						},
						{
							"value": "h3",
							"label": " Heading 3"
						},
						{
							"value": "h4",
							"label": " Heading 4"
						},
						{
							"value": "h5",
							"label": " Heading 5"
						},
						{
							"value": "p",
							"label": " Paragraph"
						},
						{
							"value": "div",
							"label": " Div"
						}
					]
				},
				{
					"type": "select",
					"id": "subtitle_class_type_style",
					"label": "Subtitle Type Style",
					"options": [
						{
							"value": "",
							"label": " Auto"
						},
						{
							"value": "type-body",
							"label": " Body"
						},
						{
							"value": "type-hero",
							"label": " Hero"
						},
						{
							"value": "type-eyebrow",
							"label": " Eyebrow"
						},
						{
							"value": "type-headline",
							"label": " Headline"
						},
						{
							"value": "type-subline",
							"label": " Subline"
						},
						{
							"value": "type-micro",
							"label": " Micro"
						},
						{
							"value": "type-item",
							"label": " Item Title"
						},
						{
							"value": "type-section",
							"label": " Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "subtitle_class_type_size",
					"label": "Subtitle Type Size",
					"options": [
						{
							"value": "",
							"label": " Default"
						},
						{
							"value": "type--sm",
							"label": " Smaller"
						},
						{
							"value": "type--lg",
							"label": " Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "subtitle_style_color",
					"label": "Subtitle Color"
				},
				{
					"type": "paragraph",
					"content": "@include ProductItem"
				},
				{
					"type": "header",
					"content": "Collection Grid Settings"
				},
				{
					"type": "select",
					"id": "collection_grid_class_column",
					"label": "Grid Mobile Columns",
					"options": [
						{
							"value": "grid-cols-1",
							"label": "1 Column"
						},
						{
							"value": "grid-cols-2",
							"label": "2 Columns"
						},
						{
							"value": "grid-cols-3",
							"label": "3 Columns"
						},
						{
							"value": "grid-cols-4",
							"label": "4 Columns"
						}
					],
					"default": "grid-cols-1"
				},
				{
					"type": "select",
					"id": "collection_grid_class_column_desktop",
					"label": "Grid Desktop Columns",
					"options": [
						{
							"value": "lg:grid-cols-1",
							"label": "1 Column"
						},
						{
							"value": "lg:grid-cols-2",
							"label": "2 Columns"
						},
						{
							"value": "lg:grid-cols-3",
							"label": "3 Columns"
						},
						{
							"value": "lg:grid-cols-4",
							"label": "4 Columns"
						}
					],
					"default": "lg:grid-cols-1"
				},
				{
					"type": "select",
					"id": "collection_grid_class_gap",
					"label": "Grid Mobile Gap",
					"options": [
						{
							"value": "gap-0",
							"label": "None"
						},
						{
							"value": "gap-2xs",
							"label": "2XS"
						},
						{
							"value": "gap-xs",
							"label": "XS"
						},
						{
							"value": "gap-sm",
							"label": "SM"
						},
						{
							"value": "gap-md",
							"label": "MD"
						},
						{
							"value": "gap-lg",
							"label": "LG"
						},
						{
							"value": "gap-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "collection_grid_class_gap_desktop",
					"label": "Grid Desktop Gap",
					"options": [
						{
							"value": "lg:gap-0",
							"label": "None"
						},
						{
							"value": "lg:gap-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "SM"
						},
						{
							"value": "lg:gap-md",
							"label": "MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "header",
					"content": "Content Item Grid Column Settings"
				},
				{
					"type": "select",
					"id": "content_class_gridcols",
					"label": "Content Item Grid Columns",
					"options": [
						{
							"value": "col-span-1",
							"label": "1 Column"
						},
						{
							"value": "col-span-2",
							"label": "2 Columns"
						},
						{
							"value": "col-span-3",
							"label": "3 Columns"
						}
					],
					"default": "col-span-1"
				},
				{
					"type": "select",
					"id": "content_class_gridcols_desktop",
					"label": "Content Item Grid Columns Desktop",
					"options": [
						{
							"value": "lg:col-span-1",
							"label": "1 Column"
						},
						{
							"value": "lg:col-span-2",
							"label": "2 Columns"
						},
						{
							"value": "lg:col-span-3",
							"label": "3 Columns"
						},
						{
							"value": "lg:col-span-4",
							"label": "4 Columns"
						}
					],
					"default": "lg:col-span-1"
				},
				{
					"type": "text",
					"id": "content_position",
					"label": "Content Item Position"
				},
				{
					"type": "text",
					"id": "content_position_desktop",
					"label": "Content Item Position Desktop"
				},
				{
					"type": "header",
					"content": "Display Settings"
				},
				{
					"type": "select",
					"id": "item_class_visibility",
					"label": "Display Visibility",
					"options": [
						{
							"value": "",
							"label": "Mobile & Desktop"
						},
						{
							"value": "lg:hidden",
							"label": "Mobile"
						},
						{
							"value": "max-lg:hidden",
							"label": "Desktop"
						}
					]
				},
				{
					"type": "header",
					"content": "Item Layout"
				},
				{
					"type": "select",
					"id": "item_class_display",
					"label": "Item Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_display_desktop",
					"label": "Item Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_direction",
					"label": "Item Direction",
					"default": "flex-col",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_layout",
					"label": "Item Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_layout_spacing",
					"label": "Item Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_vertical_padding",
					"label": "Item Vertical Padding",
					"options": [
						{
							"value": "py-0",
							"label": "None"
						},
						{
							"value": "py-2xs",
							"label": "2XS"
						},
						{
							"value": "py-xs",
							"label": "XS"
						},
						{
							"value": "py-sm",
							"label": "SM"
						},
						{
							"value": "py-md",
							"label": "MD"
						},
						{
							"value": "py-lg",
							"label": "LG"
						},
						{
							"value": "py-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_horizontal_padding",
					"label": "Item Horizontal Padding",
					"options": [
						{
							"value": "px-0",
							"label": "None"
						},
						{
							"value": "px-2xs",
							"label": "2XS"
						},
						{
							"value": "px-xs",
							"label": "XS"
						},
						{
							"value": "px-sm",
							"label": "SM"
						},
						{
							"value": "px-md",
							"label": "MD"
						},
						{
							"value": "px-lg",
							"label": "LG"
						},
						{
							"value": "px-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_gap",
					"label": "Item Spacing Gap",
					"options": [
						{
							"value": "gap-0",
							"label": "None"
						},
						{
							"value": "gap-2xs",
							"label": "2XS"
						},
						{
							"value": "gap-xs",
							"label": "XS"
						},
						{
							"value": "gap-sm",
							"label": "SM"
						},
						{
							"value": "gap-md",
							"label": "MD"
						},
						{
							"value": "gap-lg",
							"label": "LG"
						},
						{
							"value": "gap-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_vertical_padding_desktop",
					"label": "Item Vertical Padding Desktop",
					"options": [
						{
							"value": "lg:py-0",
							"label": "None"
						},
						{
							"value": "lg:py-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "XS"
						},
						{
							"value": "lg:py-sm",
							"label": "SM"
						},
						{
							"value": "lg:py-md",
							"label": "MD"
						},
						{
							"value": "lg:py-lg",
							"label": "LG"
						},
						{
							"value": "lg:py-xl",
							"label": "XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_horizontal_padding_desktop",
					"label": "Item Horizontal Padding Desktop",
					"options": [
						{
							"value": "lg:px-0",
							"label": "None"
						},
						{
							"value": "lg:px-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "XS"
						},
						{
							"value": "lg:px-sm",
							"label": "SM"
						},
						{
							"value": "lg:px-md",
							"label": "MD"
						},
						{
							"value": "lg:px-lg",
							"label": "LG"
						},
						{
							"value": "lg:px-xl",
							"label": "XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "select",
					"id": "item_class_gap_desktop",
					"label": "Item Spacing Gap Desktop",
					"options": [
						{
							"value": "lg:gap-0",
							"label": "None"
						},
						{
							"value": "lg:gap-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "SM"
						},
						{
							"value": "lg:gap-md",
							"label": "MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "color_background",
					"id": "item_style_background",
					"label": "Item Background Color"
				},
				{
					"type": "color_background",
					"id": "content_style_background",
					"label": "Content Background Color"
				},
				{
					"type": "header",
					"content": "Content Interactivity"
				},
				{
					"type": "url",
					"id": "link",
					"label": "Link"
				},
				{
					"type": "paragraph",
					"content": "▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀\n🅜🅔🅓🅘🅐\n▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀"
				},
				{
					"type": "header",
					"content": "Media"
				},
				{
					"type": "image_picker",
					"label": "Image",
					"id": "image"
				},
				{
					"type": "image_picker",
					"label": "Image (Desktop)",
					"id": "image_desktop"
				},
				{
					"type": "select",
					"label": "Image Position",
					"id": "image_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full w-full object-cover"
						}
					]
				},
				{
					"type": "header",
					"content": "Media Width Settings"
				},
				{
					"type": "select",
					"id": "media_class_width",
					"label": "Media Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						}
					]
				},
				{
					"type": "select",
					"id": "media_class_width_desktop",
					"label": "Media Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						}
					]
				},
				{
					"type": "text",
					"label": "Video",
					"id": "video"
				},
				{
					"type": "text",
					"label": "Video For Mobile",
					"id": "videomobile"
				},
				{
					"type": "select",
					"label": "Video Position",
					"id": "video_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full"
						}
					]
				},
				{
					"type": "select",
					"label": "Video Position For Mobile",
					"id": "videomobile_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full"
						}
					]
				},
				{
					"type": "image_picker",
					"label": "Video Poster",
					"id": "posterDesktop"
				},
				{
					"type": "paragraph",
					"content": "▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀\n🅒🅞🅝🅣🅔🅝🅣 🅛🅐🅨🅞🅤🅣\n▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀"
				},
				{
					"type": "header",
					"content": "Content Width Settings"
				},
				{
					"type": "select",
					"id": "content_class_width",
					"label": "Content Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_width_desktop",
					"label": "Content Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						}
					]
				},
				{
					"type": "header",
					"content": "Content Layout"
				},
				{
					"type": "select",
					"id": "content_class_display",
					"label": "Content Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_display_desktop",
					"label": "Content Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_direction",
					"label": "Content Direction",
					"default": "flex-row",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_layout",
					"label": "Content Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_layout_spacing",
					"label": "Content Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_vertical_padding",
					"label": "Content Vertical Padding",
					"options": [
						{
							"value": "py-0",
							"label": "None"
						},
						{
							"value": "py-2xs",
							"label": "2XS"
						},
						{
							"value": "py-xs",
							"label": "XS"
						},
						{
							"value": "py-sm",
							"label": "SM"
						},
						{
							"value": "py-md",
							"label": "MD"
						},
						{
							"value": "py-lg",
							"label": "LG"
						},
						{
							"value": "py-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_horizontal_padding",
					"label": "Content Horizontal Padding",
					"options": [
						{
							"value": "px-0",
							"label": "None"
						},
						{
							"value": "px-2xs",
							"label": "2XS"
						},
						{
							"value": "px-xs",
							"label": "XS"
						},
						{
							"value": "px-sm",
							"label": "SM"
						},
						{
							"value": "px-md",
							"label": "MD"
						},
						{
							"value": "px-lg",
							"label": "LG"
						},
						{
							"value": "px-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_gap",
					"label": "Content Spacing Gap",
					"options": [
						{
							"value": "gap-0",
							"label": "None"
						},
						{
							"value": "gap-2xs",
							"label": "2XS"
						},
						{
							"value": "gap-xs",
							"label": "XS"
						},
						{
							"value": "gap-sm",
							"label": "SM"
						},
						{
							"value": "gap-md",
							"label": "MD"
						},
						{
							"value": "gap-lg",
							"label": "LG"
						},
						{
							"value": "gap-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_vertical_padding_desktop",
					"label": "Content Vertical Padding Desktop",
					"options": [
						{
							"value": "lg:py-0",
							"label": "None"
						},
						{
							"value": "lg:py-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "XS"
						},
						{
							"value": "lg:py-sm",
							"label": "SM"
						},
						{
							"value": "lg:py-md",
							"label": "MD"
						},
						{
							"value": "lg:py-lg",
							"label": "LG"
						},
						{
							"value": "lg:py-xl",
							"label": "XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_horizontal_padding_desktop",
					"label": "Content Horizontal Padding Desktop",
					"options": [
						{
							"value": "lg:px-0",
							"label": "None"
						},
						{
							"value": "lg:px-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "XS"
						},
						{
							"value": "lg:px-sm",
							"label": "SM"
						},
						{
							"value": "lg:px-md",
							"label": "MD"
						},
						{
							"value": "lg:px-lg",
							"label": "LG"
						},
						{
							"value": "lg:px-xl",
							"label": "XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_gap_desktop",
					"label": "Content Spacing Gap Desktop",
					"options": [
						{
							"value": "lg:gap-0",
							"label": "None"
						},
						{
							"value": "lg:gap-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "SM"
						},
						{
							"value": "lg:gap-md",
							"label": "MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_aspect",
					"label": "Content Aspect Ratio",
					"options": [
						{
							"value": "aspect-auto",
							"label": "Auto"
						},
						{
							"value": "aspect-[2/1]",
							"label": "2:1"
						},
						{
							"value": "aspect-[16/9]",
							"label": "16:9"
						},
						{
							"value": "aspect-[4/3]",
							"label": "4:3"
						},
						{
							"value": "aspect-[1/1]",
							"label": "1:1"
						},
						{
							"value": "aspect-[3/1]",
							"label": "3:1"
						},
						{
							"value": "aspect-[3/4]",
							"label": "3:4"
						},
						{
							"value": "aspect-[8/30]",
							"label": "8:30"
						},
						{
							"value": "aspect-[9/16]",
							"label": "9:16"
						}
					]
				},
				{
					"type": "select",
					"id": "content_class_aspect_desktop",
					"label": "Content Aspect Ratio Desktop",
					"options": [
						{
							"value": "lg:aspect-auto",
							"label": "Auto"
						},
						{
							"value": "lg:aspect-[2/1]",
							"label": "2:1"
						},
						{
							"value": "lg:aspect-[16/9]",
							"label": "16:9"
						},
						{
							"value": "lg:aspect-[4/3]",
							"label": "4:3"
						},
						{
							"value": "lg:aspect-[1/1]",
							"label": "1:1"
						},
						{
							"value": "aspect-[3/1]",
							"label": "3:1"
						},
						{
							"value": "lg:aspect-[3/4]",
							"label": "3:4"
						},
						{
							"value": "aspect-[8/30]",
							"label": "8:30"
						},
						{
							"value": "lg:aspect-[9/16]",
							"label": "9:16"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀\n🅣🅔🅧🅣 🅢🅣🅐🅒🅚\n▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀"
				},
				{
					"type": "header",
					"content": "Text Stack"
				},
				{
					"type": "header",
					"content": "Text Stack Width Settings"
				},
				{
					"type": "select",
					"id": "text_stack_class_width",
					"label": "Text Stack Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_width_desktop",
					"label": "Text Stack Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						}
					]
				},
				{
					"type": "header",
					"content": "Text Layout"
				},
				{
					"type": "select",
					"id": "text_stack_class_display",
					"label": "Text Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_display_desktop",
					"label": "Text Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_direction",
					"label": "Text Direction",
					"default": "flex-row",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_layout",
					"label": "Text Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_layout_spacing",
					"label": "Text Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_vertical_padding",
					"label": "Text Vertical Padding",
					"options": [
						{
							"value": "py-0",
							"label": "None"
						},
						{
							"value": "py-2xs",
							"label": "2XS"
						},
						{
							"value": "py-xs",
							"label": "XS"
						},
						{
							"value": "py-sm",
							"label": "SM"
						},
						{
							"value": "py-md",
							"label": "MD"
						},
						{
							"value": "py-lg",
							"label": "LG"
						},
						{
							"value": "py-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_horizontal_padding",
					"label": "Text Horizontal Padding",
					"options": [
						{
							"value": "px-0",
							"label": "None"
						},
						{
							"value": "px-2xs",
							"label": "2XS"
						},
						{
							"value": "px-xs",
							"label": "XS"
						},
						{
							"value": "px-sm",
							"label": "SM"
						},
						{
							"value": "px-md",
							"label": "MD"
						},
						{
							"value": "px-lg",
							"label": "LG"
						},
						{
							"value": "px-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_gap",
					"label": "Text Spacing Gap",
					"options": [
						{
							"value": "gap-0",
							"label": "None"
						},
						{
							"value": "gap-2xs",
							"label": "2XS"
						},
						{
							"value": "gap-xs",
							"label": "XS"
						},
						{
							"value": "gap-sm",
							"label": "SM"
						},
						{
							"value": "gap-md",
							"label": "MD"
						},
						{
							"value": "gap-lg",
							"label": "LG"
						},
						{
							"value": "gap-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_vertical_padding_desktop",
					"label": "Text Vertical Padding Desktop",
					"options": [
						{
							"value": "lg:py-0",
							"label": "None"
						},
						{
							"value": "lg:py-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "XS"
						},
						{
							"value": "lg:py-sm",
							"label": "SM"
						},
						{
							"value": "lg:py-md",
							"label": "MD"
						},
						{
							"value": "lg:py-lg",
							"label": "LG"
						},
						{
							"value": "lg:py-xl",
							"label": "XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_horizontal_padding_desktop",
					"label": "Text Horizontal Padding Desktop",
					"options": [
						{
							"value": "lg:px-0",
							"label": "None"
						},
						{
							"value": "lg:px-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "XS"
						},
						{
							"value": "lg:px-sm",
							"label": "SM"
						},
						{
							"value": "lg:px-md",
							"label": "MD"
						},
						{
							"value": "lg:px-lg",
							"label": "LG"
						},
						{
							"value": "lg:px-xl",
							"label": "XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "select",
					"id": "text_stack_class_gap_desktop",
					"label": "Text Spacing Gap Desktop",
					"options": [
						{
							"value": "lg:gap-0",
							"label": "None"
						},
						{
							"value": "lg:gap-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "SM"
						},
						{
							"value": "lg:gap-md",
							"label": "MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "image_picker",
					"label": "Title Image",
					"id": "title_image"
				},
				{
					"type": "liquid",
					"id": "svg",
					"label": "SVG"
				},
				{
					"type": "header",
					"content": "Title Image / SVG Width Settings"
				},
				{
					"type": "select",
					"id": "title_image_class_width",
					"label": "Title Image / SVG Width",
					"options": [
						{
							"value": "container",
							"label": "Container"
						},
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						}
					]
				},
				{
					"type": "select",
					"id": "title_image_class_width_desktop",
					"label": "Title Image / SVG Desktop Width",
					"options": [
						{
							"value": "lg:container",
							"label": "Container"
						},
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-[10%]",
							"label": "10%"
						},
						{
							"value": "lg:w-1/5",
							"label": "20%"
						},
						{
							"value": "lg:w-1/4",
							"label": "25%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-3/5",
							"label": "60%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-3/4",
							"label": "75%"
						},
						{
							"value": "lg:w-4/5",
							"label": "80%"
						},
						{
							"value": "lg:w-9/10",
							"label": "90%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						}
					]
				},
				{
					"type": "radio",
					"label": "Text Justification",
					"id": "text_stack_class",
					"default": "text-center",
					"options": [
						{
							"label": "←",
							"value": "text-left"
						},
						{
							"label": "↔",
							"value": "text-center"
						},
						{
							"label": "→",
							"value": "text-right"
						}
					]
				},
				{
					"type": "color",
					"id": "content_style_color",
					"label": "Text Color"
				},
				{
					"type": "header",
					"content": "Text 1 Settings"
				},
				{
					"type": "text",
					"id": "text_item_1_text",
					"label": "Text 1 Text"
				},
				{
					"type": "select",
					"id": "text_item_1_element",
					"label": "Text 1 Element",
					"default": "p",
					"options": [
						{
							"value": "h1",
							"label": " Heading 1"
						},
						{
							"value": "h2",
							"label": " Heading 2"
						},
						{
							"value": "h3",
							"label": " Heading 3"
						},
						{
							"value": "h4",
							"label": " Heading 4"
						},
						{
							"value": "h5",
							"label": " Heading 5"
						},
						{
							"value": "p",
							"label": " Paragraph"
						},
						{
							"value": "div",
							"label": " Div"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_1_class_type_style",
					"label": "Text 1 Type Style",
					"options": [
						{
							"value": "",
							"label": " Auto"
						},
						{
							"value": "type-body",
							"label": " Body"
						},
						{
							"value": "type-hero",
							"label": " Hero"
						},
						{
							"value": "type-eyebrow",
							"label": " Eyebrow"
						},
						{
							"value": "type-headline",
							"label": " Headline"
						},
						{
							"value": "type-subline",
							"label": " Subline"
						},
						{
							"value": "type-micro",
							"label": " Micro"
						},
						{
							"value": "type-item",
							"label": " Item Title"
						},
						{
							"value": "type-section",
							"label": " Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_1_class_type_size",
					"label": "Text 1 Type Size",
					"options": [
						{
							"value": "",
							"label": " Default"
						},
						{
							"value": "type--sm",
							"label": " Smaller"
						},
						{
							"value": "type--lg",
							"label": " Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_item_1_style_color",
					"label": "Text 1 Color"
				},
				{
					"type": "header",
					"content": "Text 2 Settings"
				},
				{
					"type": "text",
					"id": "text_item_2_text",
					"label": "Text 2 Text"
				},
				{
					"type": "select",
					"id": "text_item_2_element",
					"label": "Text 2 Element",
					"default": "p",
					"options": [
						{
							"value": "h1",
							"label": " Heading 1"
						},
						{
							"value": "h2",
							"label": " Heading 2"
						},
						{
							"value": "h3",
							"label": " Heading 3"
						},
						{
							"value": "h4",
							"label": " Heading 4"
						},
						{
							"value": "h5",
							"label": " Heading 5"
						},
						{
							"value": "p",
							"label": " Paragraph"
						},
						{
							"value": "div",
							"label": " Div"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_2_class_type_style",
					"label": "Text 2 Type Style",
					"options": [
						{
							"value": "",
							"label": " Auto"
						},
						{
							"value": "type-body",
							"label": " Body"
						},
						{
							"value": "type-hero",
							"label": " Hero"
						},
						{
							"value": "type-eyebrow",
							"label": " Eyebrow"
						},
						{
							"value": "type-headline",
							"label": " Headline"
						},
						{
							"value": "type-subline",
							"label": " Subline"
						},
						{
							"value": "type-micro",
							"label": " Micro"
						},
						{
							"value": "type-item",
							"label": " Item Title"
						},
						{
							"value": "type-section",
							"label": " Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_2_class_type_size",
					"label": "Text 2 Type Size",
					"options": [
						{
							"value": "",
							"label": " Default"
						},
						{
							"value": "type--sm",
							"label": " Smaller"
						},
						{
							"value": "type--lg",
							"label": " Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_item_2_style_color",
					"label": "Text 2 Color"
				},
				{
					"type": "header",
					"content": "Text 3 Settings"
				},
				{
					"type": "text",
					"id": "text_item_3_text",
					"label": "Text 3 Text"
				},
				{
					"type": "select",
					"id": "text_item_3_element",
					"label": "Text 3 Element",
					"default": "p",
					"options": [
						{
							"value": "h1",
							"label": " Heading 1"
						},
						{
							"value": "h2",
							"label": " Heading 2"
						},
						{
							"value": "h3",
							"label": " Heading 3"
						},
						{
							"value": "h4",
							"label": " Heading 4"
						},
						{
							"value": "h5",
							"label": " Heading 5"
						},
						{
							"value": "p",
							"label": " Paragraph"
						},
						{
							"value": "div",
							"label": " Div"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_3_class_type_style",
					"label": "Text 3 Type Style",
					"options": [
						{
							"value": "",
							"label": " Auto"
						},
						{
							"value": "type-body",
							"label": " Body"
						},
						{
							"value": "type-hero",
							"label": " Hero"
						},
						{
							"value": "type-eyebrow",
							"label": " Eyebrow"
						},
						{
							"value": "type-headline",
							"label": " Headline"
						},
						{
							"value": "type-subline",
							"label": " Subline"
						},
						{
							"value": "type-micro",
							"label": " Micro"
						},
						{
							"value": "type-item",
							"label": " Item Title"
						},
						{
							"value": "type-section",
							"label": " Section Title"
						}
					]
				},
				{
					"type": "select",
					"id": "text_item_3_class_type_size",
					"label": "Text 3 Type Size",
					"options": [
						{
							"value": "",
							"label": " Default"
						},
						{
							"value": "type--sm",
							"label": " Smaller"
						},
						{
							"value": "type--lg",
							"label": " Larger"
						}
					]
				},
				{
					"type": "color",
					"id": "text_item_3_style_color",
					"label": "Text 3 Color"
				},
				{
					"type": "header",
					"content": "Custom Liquid"
				},
				{
					"type": "liquid",
					"id": "liquid",
					"label": "Custom Liquid/HTML"
				},
				{
					"type": "paragraph",
					"content": "▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀\n🅑🅤🅣🅣🅞🅝 🅢🅔🅣\n▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀"
				},
				{
					"type": "header",
					"content": "Button Settings"
				},
				{
					"type": "header",
					"content": "Buttons Layout"
				},
				{
					"type": "select",
					"id": "buttons__display",
					"label": "Buttons Display",
					"default": "flex",
					"options": [
						{
							"value": "flex",
							"label": "Flex"
						},
						{
							"value": "grid grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "grid grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "grid grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "grid grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "grid grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "grid grid-cols-6",
							"label": "Grid 6 Column"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__display_desktop",
					"label": "Buttons Desktop Display",
					"default": "lg:flex",
					"options": [
						{
							"value": "lg:flex",
							"label": "Flex"
						},
						{
							"value": "lg:grid lg:grid-cols-1",
							"label": "Grid 1 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-2",
							"label": "Grid 2 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-3",
							"label": "Grid 3 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-4",
							"label": "Grid 4 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-5",
							"label": "Grid 5 Column"
						},
						{
							"value": "lg:grid lg:grid-cols-6",
							"label": "Grid 6 Column"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__direction",
					"label": "Buttons Direction",
					"default": "flex-row",
					"options": [
						{
							"value": "flex-row",
							"label": "→"
						},
						{
							"value": "flex-row-reverse",
							"label": "←"
						},
						{
							"value": "flex-col",
							"label": "↓"
						},
						{
							"value": "flex-col-reverse",
							"label": "↑"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__layout",
					"label": "Buttons Layout",
					"options": [
						{
							"value": "layout-top",
							"label": "Top (Full Width)"
						},
						{
							"value": "layout-left layout-top",
							"label": "Top Left"
						},
						{
							"value": "layout-center layout-top",
							"label": "Top Center"
						},
						{
							"value": "layout-spaced w-full layout-top",
							"label": "Top Spaced"
						},
						{
							"value": "layout-right layout-top",
							"label": "Top Right"
						},
						{
							"value": "layout-left layout-middle",
							"label": "Middle Left"
						},
						{
							"value": "layout-center layout-middle",
							"label": "Middle Center"
						},
						{
							"value": "layout-spaced w-full layout-middle",
							"label": "Middle Spaced"
						},
						{
							"value": "layout-right layout-middle",
							"label": "Middle Right"
						},
						{
							"value": "layout-left layout-bottom",
							"label": "Bottom Left"
						},
						{
							"value": "layout-center layout-bottom",
							"label": "Bottom Center"
						},
						{
							"value": "layout-spaced w-full layout-bottom",
							"label": "Bottom Spaced"
						},
						{
							"value": "layout-right layout-bottom",
							"label": "Bottom Right"
						},
						{
							"value": "layout-bottom",
							"label": "Bottom (Full Width)"
						},
						{
							"value": "layout-left",
							"label": "Left (Full Height)"
						},
						{
							"value": "layout-right",
							"label": "Right (Full Height)"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__layout_spacing",
					"label": "Buttons Layout Spacing",
					"options": [
						{
							"value": "layout-space-packed",
							"label": "Packed"
						},
						{
							"value": "layout-space-between",
							"label": "Space Bewteen"
						},
						{
							"value": "layout-space-around",
							"label": "Space Around"
						},
						{
							"value": "layout-space-evenly",
							"label": "Spaced Evenly"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__vertical_padding",
					"label": "Buttons Vertical Padding",
					"options": [
						{
							"value": "py-0",
							"label": "None"
						},
						{
							"value": "py-2xs",
							"label": "2XS"
						},
						{
							"value": "py-xs",
							"label": "XS"
						},
						{
							"value": "py-sm",
							"label": "SM"
						},
						{
							"value": "py-md",
							"label": "MD"
						},
						{
							"value": "py-lg",
							"label": "LG"
						},
						{
							"value": "py-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__horizontal_padding",
					"label": "Buttons Horizontal Padding",
					"options": [
						{
							"value": "px-0",
							"label": "None"
						},
						{
							"value": "px-2xs",
							"label": "2XS"
						},
						{
							"value": "px-xs",
							"label": "XS"
						},
						{
							"value": "px-sm",
							"label": "SM"
						},
						{
							"value": "px-md",
							"label": "MD"
						},
						{
							"value": "px-lg",
							"label": "LG"
						},
						{
							"value": "px-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__gap",
					"label": "Buttons Spacing Gap",
					"options": [
						{
							"value": "gap-0",
							"label": "None"
						},
						{
							"value": "gap-2xs",
							"label": "2XS"
						},
						{
							"value": "gap-xs",
							"label": "XS"
						},
						{
							"value": "gap-sm",
							"label": "SM"
						},
						{
							"value": "gap-md",
							"label": "MD"
						},
						{
							"value": "gap-lg",
							"label": "LG"
						},
						{
							"value": "gap-xl",
							"label": "XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__vertical_padding_desktop",
					"label": "Buttons Vertical Padding Desktop",
					"options": [
						{
							"value": "lg:py-0",
							"label": "None"
						},
						{
							"value": "lg:py-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:py-xs",
							"label": "XS"
						},
						{
							"value": "lg:py-sm",
							"label": "SM"
						},
						{
							"value": "lg:py-md",
							"label": "MD"
						},
						{
							"value": "lg:py-lg",
							"label": "LG"
						},
						{
							"value": "lg:py-xl",
							"label": "XL"
						},
						{
							"value": "lg:py-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:py-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:py-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:py-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:py-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:py-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__horizontal_padding_desktop",
					"label": "Buttons Horizontal Padding Desktop",
					"options": [
						{
							"value": "lg:px-0",
							"label": "None"
						},
						{
							"value": "lg:px-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:px-xs",
							"label": "XS"
						},
						{
							"value": "lg:px-sm",
							"label": "SM"
						},
						{
							"value": "lg:px-md",
							"label": "MD"
						},
						{
							"value": "lg:px-lg",
							"label": "LG"
						},
						{
							"value": "lg:px-xl",
							"label": "XL"
						},
						{
							"value": "lg:px-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:px-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:px-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:px-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:px-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:px-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "select",
					"id": "buttons__gap_desktop",
					"label": "Buttons Spacing Gap Desktop",
					"options": [
						{
							"value": "lg:gap-0",
							"label": "None"
						},
						{
							"value": "lg:gap-2xs",
							"label": "2XS"
						},
						{
							"value": "lg:gap-xs",
							"label": "XS"
						},
						{
							"value": "lg:gap-sm",
							"label": "SM"
						},
						{
							"value": "lg:gap-md",
							"label": "MD"
						},
						{
							"value": "lg:gap-lg",
							"label": "LG"
						},
						{
							"value": "lg:gap-xl",
							"label": "XL"
						},
						{
							"value": "lg:gap-2xl",
							"label": "2XL"
						},
						{
							"value": "lg:gap-3xl",
							"label": "3XL"
						},
						{
							"value": "lg:gap-4xl",
							"label": "4XL"
						},
						{
							"value": "lg:gap-5xl",
							"label": "5XL"
						},
						{
							"value": "lg:gap-6xl",
							"label": "6XL"
						},
						{
							"value": "lg:gap-7xl",
							"label": "7XL"
						}
					]
				},
				{
					"type": "header",
					"content": "Button 1 Settings"
				},
				{
					"type": "text",
					"id": "button_1_text",
					"label": "Button 1 Text"
				},
				{
					"type": "url",
					"id": "button_1_link",
					"label": "Button 1 Link"
				},
				{
					"type": "liquid",
					"id": "button_1_onclick",
					"label": "Button 1 On Click"
				},
				{
					"type": "select",
					"id": "button_1_class_style",
					"label": "Button 1 Style",
					"options": [
						{
							"value": "button--primary",
							"label": " Primary"
						},
						{
							"value": "button--secondary",
							"label": " Secondary"
						},
						{
							"value": "button--tertiary",
							"label": " Tertiary"
						},
						{
							"value": "button--light",
							"label": " Light"
						},
						{
							"value": "button--dark",
							"label": " Dark"
						},
						{
							"value": "button--pop",
							"label": " Pop"
						},
						{
							"value": "button--highlight",
							"label": " Highlight"
						},
						{
							"value": "button--action",
							"label": " Action"
						},
						{
							"value": "button--simple",
							"label": " Simple"
						},
						{
							"value": "button--emphasis",
							"label": " Emphasis"
						},
						{
							"value": "button--link",
							"label": " Text Link"
						},
						{
							"value": "button--micro-link",
							"label": " Micro Text Link"
						}
					]
				},
				{
					"type": "select",
					"id": "button_1_class_size",
					"label": "Button 1 Size",
					"options": [
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				},
				{
					"type": "header",
					"content": "Button 2 Settings"
				},
				{
					"type": "text",
					"id": "button_2_text",
					"label": "Button 2 Text"
				},
				{
					"type": "url",
					"id": "button_2_link",
					"label": "Button 2 Link"
				},
				{
					"type": "liquid",
					"id": "button_2_onclick",
					"label": "Button 2 On Click"
				},
				{
					"type": "select",
					"id": "button_2_class_style",
					"label": "Button 2 Style",
					"options": [
						{
							"value": "button--primary",
							"label": " Primary"
						},
						{
							"value": "button--secondary",
							"label": " Secondary"
						},
						{
							"value": "button--tertiary",
							"label": " Tertiary"
						},
						{
							"value": "button--light",
							"label": " Light"
						},
						{
							"value": "button--dark",
							"label": " Dark"
						},
						{
							"value": "button--pop",
							"label": " Pop"
						},
						{
							"value": "button--highlight",
							"label": " Highlight"
						},
						{
							"value": "button--action",
							"label": " Action"
						},
						{
							"value": "button--simple",
							"label": " Simple"
						},
						{
							"value": "button--emphasis",
							"label": " Emphasis"
						},
						{
							"value": "button--link",
							"label": " Text Link"
						},
						{
							"value": "button--micro-link",
							"label": " Micro Text Link"
						}
					]
				},
				{
					"type": "select",
					"id": "button_2_class_size",
					"label": "Button 2 Size",
					"options": [
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				},
				{
					"type": "header",
					"content": "Button 3 Settings"
				},
				{
					"type": "text",
					"id": "button_3_text",
					"label": "Button 3 Text"
				},
				{
					"type": "url",
					"id": "button_3_link",
					"label": "Button 3 Link"
				},
				{
					"type": "liquid",
					"id": "button_3_onclick",
					"label": "Button 3 On Click"
				},
				{
					"type": "select",
					"id": "button_3_class_style",
					"label": "Button 3 Style",
					"options": [
						{
							"value": "button--primary",
							"label": " Primary"
						},
						{
							"value": "button--secondary",
							"label": " Secondary"
						},
						{
							"value": "button--tertiary",
							"label": " Tertiary"
						},
						{
							"value": "button--light",
							"label": " Light"
						},
						{
							"value": "button--dark",
							"label": " Dark"
						},
						{
							"value": "button--pop",
							"label": " Pop"
						},
						{
							"value": "button--highlight",
							"label": " Highlight"
						},
						{
							"value": "button--action",
							"label": " Action"
						},
						{
							"value": "button--simple",
							"label": " Simple"
						},
						{
							"value": "button--emphasis",
							"label": " Emphasis"
						},
						{
							"value": "button--link",
							"label": " Text Link"
						},
						{
							"value": "button--micro-link",
							"label": " Micro Text Link"
						}
					]
				},
				{
					"type": "select",
					"id": "button_3_class_size",
					"label": "Button 3 Size",
					"options": [
						{
							"value": "",
							"label": "Standard"
						},
						{
							"value": "button--large",
							"label": "Large"
						}
					]
				}
			]
		},
		{
			"type": "custom_liquid",
			"name": "Liquid",
			"limit": 4,
			"settings": [
				{
					"type": "liquid",
					"id": "liquid",
					"label": "Liquid Block"
				}
			]
		},
		{
			"type": "search_words_suggest",
			"name": "Search Words Suggest",
			"limit": 1,
			"settings": [
				{
					"type": "textarea",
					"id": "search_suggestions_inclusion_js",
					"label": "Javascript Logic Inclusion",
					"default": "true",
					"info": "Insert any javascript logic that evaluates to true to display the section"
				},
				{
					"type": "text",
					"id": "no_search_paragraph",
					"label": "No Search text",
					"default": "We couldn't find exactly what you were looking for, but try these popular search terms"
				},
				{
					"type": "textarea",
					"id": "search_suggestions",
					"label": "Search Suggestions",
					"default": "trunks",
					"info": "comma separated terms"
				}
			]
		},
		{
			"type": "render-till",
			"name": "⤶ Rendering End"
		}
	]
}
{% endschema %}
