<script>
  window.loyalty = {
    tiers:{{ section.blocks | map: 'settings' | json }}
  }
</script>

{% schema %}
{
  "name": "Loyalty",
  "settings":[
    {
      "type": "text",
      "id": "title",
      "label": "Loyalty Program Title"
    }
  ],
  "blocks": [
    {
      "type": "tier",
      "name": "Loyalty Tier",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title *"
        },
        {
          "type": "text",
          "id": "key",
          "label": "Key *"
        },
        {
          "type": "number",
          "id": "points",
          "label": "Points *"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Description"
        }
      ]
    }
  ]
}
{% endschema %}
