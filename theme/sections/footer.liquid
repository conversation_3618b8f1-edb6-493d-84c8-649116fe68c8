{% liquid 
  assign section_type = 'footer'
%}

<section
  class="footer relative {% render 'class-settings' prefix:'wrapper_class' settings:section.settings %}"
  style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}">

  {% if section.settings.wrapper_bg_image %}
    <img 
    src="{{ section.settings.wrapper_bg_image | image_url }}" 
    alt="{{ section.settings.wrapper_bg_image.alt }}" 
    class="section__media absolute inset-0 w-full h-full object-cover" />
  {% endif %}

  <main
    tabindex="0" 
    class="section__container relative {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
    style="{% render 'style-settings' prefix:'container_styles' settings:section.settings %}">
  
    {% render 'flex-nested-blocks' blocks:section.blocks offset:offset stop:'menu' %}

  </main>

  <footer class="footer__credits flex flex-wrap">
  	<span class="footer__credit footer__credit--copyright">{{ section.settings.credits }}</span>
  	<span class="flex footer__credit--links">
  	{% for link in linklists[section.settings.credits_link_list].links %}
  	<a class="footer__credit footer__credit--link" href="{{ link.url }}">{{ link.title }}</a>
  	{% endfor %}
  </span>
  </footer>

</section>
{% if settings.klaviyo_new_registration_check %}
	<script>{{ settings.klaviyo_new_registration_check }}</script>
{% endif %}


{% schema %}
{
	"name": "Footer",
	"tag": "footer",
	"settings": [
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class, label_prefix:Menu "
		},
		{
			"type": "liquid",
			"id": "credits",
			"label": "Copyright Information"
		},
		{
			"type": "link_list",
			"id": "credits_link_list",
			"label": "Policies Menu"
		}
	],
	"blocks": [
		{
			"type": "frame",
			"name": "Frame",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:article_class"
				},
				{
					"type": "paragraph",
					"content": "@include LogicInclusion"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "color",
					"id": "article_style_background_color",
					"label": "Frame Background Color"
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:article_class, label_prefix:Frame "
				}
			]
		},
		{
			"type": "break",
			"name": "Frame Break",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:article_class, label_prefix:Frame "
				}
			]
		},
		{
			"type": "nav-item",
			"name": "Visual Nav Item",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:item_class, label_prefix:Item "
				},
				{
					"type": "liquid",
					"label": "Liquid Logic Inclusion",
					"id": "inclusion_liquid",
					"info": "Insert any liquid logic that returns a value to display the section",
					"default": "true"
				},
				{
					"id": "inclusion_js",
					"label": "Javascript Inclusion Logic",
					"type": "liquid"
				},
				{
					"type": "url",
					"label": "Link",
					"id": "link"
				},
				{
					"type": "image_picker",
					"label": "Image",
					"id": "image"
				},
				{
					"type": "text",
					"label": "link Text",
					"id": "title"
				}
			]
		},
		{
			"type": "menu-item",
			"name": "Menu Item",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:item_class, label_prefix:Menu "
				},
				{
					"type": "paragraph",
					"content": "@include MenuItem, id_prefix:item_class, label_prefix:Menu "
				}
			]
		},
		{
			"type": "content-item",
			"name": "Content Item",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:item_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include ContentItem"
				}
			]
		}
	]
}
{% endschema %}
