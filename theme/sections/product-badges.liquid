{% schema %}
{
  "name": "Product Badges",
	"settings": [],
  "blocks": [
    {
      "name": "Badge",
      "type": "badge",
      "settings": [
        {
          "label": "Title",
          "id": "title",
          "type": "text",
          "info": "Internal reference only"
        },
        {
          "label": "Tag",
          "id": "tag",
          "type": "text"
        },
        {
          "type": "header",
          "content": "Badge Label Settings"
        },
        {
          "label": "Grid Item Badge",
          "id": "grid",
          "type": "text"
        },
        {
          "label": "Product Detail Page Badge",
          "id": "detail",
          "type": "text"
        },
        {
          "label": "Carousel Item Badge",
          "id": "carousel",
          "type": "text"
        },
        {
          "label": "Search Item Badge",
          "id": "search",
          "type": "text"
        },
        {
          "label": "Badge Color",
          "id": "color",
          "type": "color",
          "default": "#000"
        },
        {
          "label": "Badge Background Color",
          "id": "bg_color",
          "type": "color"
        }
      ]
		}
  ]
}

{% endschema %}

<script>
  window.productBadges = [];

  {% liquid 

    assign now = "now" | date: "%Y-%m-%d %H:%M"

    assign badges = section.blocks
    
    for block in badges
      assign include_badge = true

      if include_badge
        echo block.settings | json | prepend: 'productBadges.push(' | append: ');'
      endif
    endfor
  %}
</script>
