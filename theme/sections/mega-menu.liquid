{% liquid 
  assign section_type = 'mega-menu'
%}

{% for menu in section.blocks %}

  {% liquid 
    if menu.type != 'menu'
      continue
    endif
    assign offset = forloop.index 
  %}

  {%- unless request.design_mode -%}
  <template x-data x-teleport="{{ menu.settings.teleport }}">
  {%- endunless -%}

    <article
      class="relative {% render 'class-settings' prefix:'wrapper_class' settings:menu.settings %}"
      style="{% render 'style-settings' prefix:'wrapper_style' settings:menu.settings %}">

      {% if menu.settings.wrapper_bg_image %}
        <img 
        src="{{ menu.settings.wrapper_bg_image | image_url }}" 
        alt="{{ menu.settings.wrapper_bg_image.alt }}" 
        class="absolute inset-0 object-cover w-full h-full section__media" />
      {% endif %}
    
      <main
        tabindex="0" 
        class="section__container relative {% render 'class-settings' prefix:'container_class' settings:menu.settings %}" 
        style="{% render 'style-settings' prefix:'container_styles' settings:menu.settings %}">
      
        {% render 'flex-nested-blocks' blocks:section.blocks offset:offset stop:'menu' %}

      </main>

    </article>
    
  {%- unless request.design_mode -%}
  </template>
  {%- endunless -%}

{% endfor %}

<script type="text/javascript">
	window.addEventListener('DOMContentLoaded', () => {
	  window.menu = window.menu || []
	  {% for menu in section.blocks %}
		{% if menu.type == 'menu' %}
		  window.menu.push({
			title: '{{ menu.settings.title }}',
			teleport: '{{ menu.settings.teleport }}',
			redirect: '{{ menu.settings.redirect }}'
		  })
		{% endif %}
	  {% endfor %}
	})
</script>
  

<script>
    window.addEventListener('Details:toggle', e => {
        // if(window.window.innerWidth > 1023) return true
        setTimeout(()=>{
            if(!!e.detail.querySelector('.mega-menu') || !!e.detail.closest('.mega-menu')){
                if(window.innerWidth > 1023) { // not desktop
					document.body.style.overflow = e.detail.open ? 'hidden' : 'auto'
					e.detail.querySelector('.mega-menu').style.overflowY = e.detail.open ? 'auto' : 'hidden'
						
					let vh80 = window.innerHeight * 0.8
					if (e.detail.querySelector('.mega-menu').scrollHeight > vh80) {
						e.detail.querySelector('.mega-menu').style.height = '80vh'
					}
				} else {
          function setMenuHeight() {
            e.detail.closest('.header-bar__block--menu').style.marginBottom = `${ e.detail.open ? e.detail.querySelector('.mega-menu').scrollHeight : 0}px`
          } 
          setMenuHeight()
          Util.wait(300).then(setMenuHeight)
				}
            }
        },1)
    })

	window.addEventListener('Modal.close', e => {
		document.body.style.overflow = 'auto'
	})

</script>

{%- if request.design_mode -%}
<script>
	window.addEventListener('shopify:section:select', e=>{	
		if(e.detail.sectionId == '{{ section.id }}'){
      document.querySelector('#shopify-section-mega-menu').setAttribute('style', 'display: block !important;')
		}
	})
	window.addEventListener('shopify:section:deselect', e=>{
		if(e.detail.sectionId == '{{ section.id }}'){
      document.querySelector('#shopify-section-mega-menu').setAttribute('style', 'display: none !important;')
		}
	});
</script>
{%- endif -%}


{% schema %}
{
	"name": "Mega-Menu",
	"settings": [
		{
			"type": "textarea",
			"id": "menu_text",
			"label": "Enter Menu Title by Comma Seperator",
			"info": "Example:- Sale, Men"
		},
		{
			"type": "color",
			"id": "color_selector",
			"label": "Color selector",
			"default": "#000000"
		}
	],
	"blocks": [
		{
			"type": "menu",
			"name": "⤷ Mega Menu",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"info": "Internal Reference"
				},
				{
					"type": "text",
					"id": "teleport",
					"label": "Menu Sublink Connector",
					"info": "Should match the value of the #mega- link in the navigation item"
				},
				{
					"type": "text",
					"id": "redirect",
					"label": "Menu Redirect Connector",
					"info": "Connect A Redirect Block. Should match the value of the Redirect Title"
				},
				{
					"type": "color",
					"id": "wrapper_style_background_color",
					"label": "Menu Background Color"
				},
				{
					"type": "paragraph",
					"content": "@include Container, id_prefix:container_class, label_prefix:Menu "
				}
			]
		},
		{
			"type": "frame",
			"name": "⤷ Frame",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤷ Frame"
				},
				{
					"type": "paragraph",
					"content": "@include LogicInclusion"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "color",
					"id": "article_style_background_color",
					"label": "Frame Background Color"
				},
				{
					"type": "paragraph",
					"content": "@include FlexLayout, id_prefix:article_class, label_prefix:Frame "
				},
				{
					"type": "textarea",
					"id": "article_class_custom",
					"label": "Custom Frame Classes"
				}
			]
		},
		{
			"type": "break",
			"name": "⤶ Frame Break",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "⤶ Frame End"
				}
			]
		},
		{
			"type": "nav-item",
			"name": "Visual Nav Item",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:item_class, label_prefix:Item "
				},
				{
					"type": "liquid",
					"label": "Liquid Logic Inclusion",
					"id": "inclusion_liquid",
					"info": "Insert any liquid logic that returns a value to display the section",
					"default": "true"
				},
				{
					"id": "inclusion_js",
					"label": "Javascript Inclusion Logic",
					"type": "liquid"
				},
				{
					"type": "url",
					"label": "Link",
					"id": "link"
				},
				{
					"type": "image_picker",
					"label": "Image",
					"id": "image"
				},
				{
					"type": "text",
					"label": "link Text",
					"id": "title"
				},
				{
					"type": "richtext",
					"label": "Description Text",
					"id": "description"
				}
			]
		},
		{
			"type": "menu-item",
			"name": "Menu Item",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:item_class, label_prefix:Menu "
				},
				{
					"type": "paragraph",
					"content": "@include MenuItem, id_prefix:item_class, label_prefix:Menu "
				},
				{
					"type": "textarea",
					"id": "default_open_mobile",
					"label": "Default to open on Mobile",
					"info": "Add the parent link titles to newlines to enable"
				}
			]
		},
		{
			"type": "content-item",
			"name": "Content Item",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:item_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include ContentItem"
				}
			]
		},
		{
			"type": "image",
			"name": "Image",
			"settings": [
				{
					"type": "image_picker",
					"label": "Image",
					"id": "image"
				},
				{
					"type": "image_picker",
					"label": "Image (Desktop)",
					"id": "image_desktop"
				},
				{
					"type": "select",
					"label": "Image Position",
					"id": "media_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full w-full object-cover"
						}
					]
				},
				{
					"type": "liquid",
					"id": "media_class_custom",
					"label": "Custom Image Classes"
				}
			]
		},
		{
			"type": "video",
			"name": "Video",
			"settings": [
				{
					"type": "text",
					"label": "Video URL",
					"id": "video_attr_src"
				},
				{
					"type": "select",
					"label": "Video Position",
					"id": "video_class_position",
					"options": [
						{
							"label": "Inline",
							"value": ""
						},
						{
							"label": "Background Fill",
							"value": "absolute inset-0 h-full w-full object-cover"
						}
					]
				},
				{
					"type": "checkbox",
					"label": "Autoplay",
					"id": "video_attr_autoplay",
					"default": true
				},
				{
					"type": "checkbox",
					"label": "Autoplay",
					"id": "video_attr_playsinline",
					"default": true
				},
				{
					"type": "checkbox",
					"label": "Muted",
					"id": "video_attr_muted",
					"default": true
				},
				{
					"type": "checkbox",
					"label": "Loop",
					"id": "video_attr_loop",
					"default": true
				}
			]
		},
		{
			"type": "rich-text",
			"name": "Rich Text",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include RichText id_prefix:text, label_prefix:Rich Text"
				},
				{
					"type": "select",
					"id": "text_class_justification",
					"label": "Text Justification",
					"options": [
						{
							"value": "text-left",
							"label": "⇐"
						},
						{
							"value": "text-center",
							"label": "→←"
						},
						{
							"value": "text-right",
							"label": "⇒"
						},
						{
							"value": "text-justify",
							"label": "≡"
						}
					]
				},
				{
					"type": "select",
					"id": "text_class_measure",
					"label": "Text Measure",
					"options": [
						{
							"value": "",
							"label": "None"
						},
						{
							"value": "measure",
							"label": "Contain"
						}
					]
				},
				{
					"type": "select",
					"id": "text_class_wrap",
					"label": "Text Wrap",
					"options": [
						{
							"value": "text-wrap",
							"label": "Wrap"
						},
						{
							"value": "text-nowrap",
							"label": "No Wrap"
						},
						{
							"value": "text-balance",
							"label": "Balance"
						},
						{
							"value": "text-pretty",
							"label": "Pretty"
						}
					]
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:text_class, label_prefix:Text "
				}
			]
		},
		{
			"type": "title",
			"name": "Title",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include Text id_prefix:title, label_prefix:Title"
				},
				{
					"type": "select",
					"id": "title_class_justification",
					"label": "Text Justification",
					"options": [
						{
							"value": "text-left",
							"label": "⇐"
						},
						{
							"value": "text-center",
							"label": "→←"
						},
						{
							"value": "text-right",
							"label": "⇒"
						},
						{
							"value": "text-justify",
							"label": "≡"
						}
					]
				},
				{
					"type": "select",
					"id": "text_class_wrap",
					"label": "Text Wrap",
					"options": [
						{
							"value": "text-wrap",
							"label": "Wrap"
						},
						{
							"value": "text-nowrap",
							"label": "No Wrap"
						},
						{
							"value": "text-balance",
							"label": "Balance"
						},
						{
							"value": "text-pretty",
							"label": "Pretty"
						}
					]
				}
			]
		},
		{
			"type": "button",
			"name": "Button",
			"settings": [
				{
					"type": "select",
					"id": "style",
					"label": "Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "text",
					"id": "button_text",
					"label": "Button Text"
				},
				{
					"type": "text",
					"id": "leading_icon",
					"label": "Leading Icon"
				},
				{
					"type": "text",
					"id": "trailing_icon",
					"label": "Trailing Icon"
				},
				{
					"type": "url",
					"id": "link",
					"label": "Link"
				},
				{
					"type": "text",
					"id": "onclick",
					"label": "Click Event",
					"info": "JavaScript onclick event, overrides native functionality. (Not Liquid)"
				},
				{
					"type": "checkbox",
					"id": "form_validity",
					"label": "Require Form Validity",
					"info": "Disables button until all fields in enclosing form are valid"
				},
				{
					"type": "paragraph",
					"content": "_________________________________"
				},
				{
					"type": "header",
					"content": "Advanced Settings"
				},
				{
					"type": "liquid",
					"id": "button_class_custom_classes",
					"label": "Custom Classes"
				},
				{
					"type": "liquid",
					"id": "button_attr_x_data",
					"label": "Dynamic Data Source"
				},
				{
					"type": "liquid",
					"id": "button_attr_x_show",
					"label": "JS Conditional Rendering"
				},
				{
					"type": "liquid",
					"id": "button_attr_x_text",
					"label": "Dynamic Text"
				},
				{
					"type": "liquid",
					"id": "button_attr_QQclass",
					"label": "Dynamic Classes"
				},
				{
					"type": "checkbox",
					"id": "disabled",
					"label": "Disable Button"
				}
			]
		},
		{
			"name": "Divider",
			"type": "divider",
			"settings": [
				{
					"type": "color",
					"id": "divider_style_border_color",
					"label": "Color"
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:divider_class, label_prefix:Divider "
				}
			]
		},
		{
			"name": "Custom Liquid",
			"type": "liquid",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"info": "Internal Reference Only"
				},
				{
					"type": "liquid",
					"id": "liquid",
					"label": "Custom Liquid"
				}
			]
		}
	]
}
{% endschema %}
