{% liquid 
	assign section_type = 'account'
%}

<section 
	class="section section--{{ section_type }} relative {% render 'class-settings' prefix:'wrapper_class' settings:section.settings %}" 
	style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}">

	<div 
		class="section__container relative flex flex-wrap {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
		style="{%- render 'style-settings' prefix:'container_styles' settings:section.settings -%}">

		<header class="section__header relative grow basis-full {% render 'class-settings' prefix:'sidebar_class' settings:section.settings %}" 
			style="{%- render 'style-settings' prefix:'sidebar_styles' settings:section.settings -%}">
			
			<h1 class="header__title type-headline m-0">{{ customer.name }}</h1>
		</header>

		<aside class="section__sidebar relative flex-initial {% render 'class-settings' prefix:'sidebar_class' settings:section.settings %}" 
			style="{%- render 'style-settings' prefix:'sidebar_styles' settings:section.settings -%}">
			
			<nav class="sidebar">
				<ul class="sidebar__list no-scrollbar scroll-to-active">
					

					{% for link in linklists[section.settings.account_nav].links %}
						{%- liquid
	 						assign activeLink = false 
						 	if template.suffix  
						 		assign param = '?view=' | append: template.suffix
								if link.url contains param or link.url contains template.suffix
									assign activeLink = true 
								endif 
						 	else
								if link.active 
									assign activeLink = true 
								endif 
						 	endif 
						-%}
							
						<li class="sidebar__item">
							<a href="{{ link.url }}" class="sidebar__link  {% if activeLink %}active {% endif %}" >{{ link.title }}</a>
						</li>
					{% endfor %}
				</ul>
			</nav>

		</aside>
		
		<main 
		
			class="section__main relative flex flex-wrap flex-1 {% render 'class-settings' prefix:'main_class' settings:section.settings %}"
			style="{%- render 'style-settings' prefix:'main_styles' settings:section.settings -%}">

	      {% liquid

			for block in section.blocks

				render 'account-block' settings:block.settings type:block.type

				case block.type

					when 'profile'
						assign fields = section.blocks | where:'type','profile_field' | map: 'settings'
						render 'account-profile' fields:fields, settings:block.settings

					when 'content-item'
						render 'content-item' settings:block.settings

					when 'subscription'
						render 'account-subscription' settings:block.settings

					when 'order-history'
						render 'order-history' customer:customer settings:block.settings
					
					when 'order-detail'
						render 'order-detail' customer:customer

					when 'wishlist'
						render 'wishlist'

					when 'addresses'
						render 'account-addresses'

					when 'password'
						render 'password-recovery'

					when 'opt-in'
						render 'opt-in'

					when 'loyalty-status'
						render 'loyalty-status'

					when 'custom-liquid'
						echo block.settings.custom_liquid

					when 'accordion'
						render 'accordion' settings:block.settings, attributes:block.shopify_attributes, block_id:block.id, section_id:section.id

				endcase

				echo '</article>'

			endfor 

			%}

		</main>

	</div>
 
</section>


 

{% schema %}
{
	"name": "Account",
	"settings": [
		{
			"type": "header",
			"content": "Navigation"
		},
		{
			"type": "link_list",
			"id": "account_nav",
			"label": "Account Nav"
		},
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_classs"
		}
	],
	"blocks": [
		{
			"type": "profile_field",
			"name": "Profile Field",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Label"
				},
				{
					"type": "text",
					"id": "key",
					"label": "Data Key"
				},
				{
					"type": "checkbox",
					"id": "save",
					"label": "Save / Edit Buttons"
				},
				{
					"type": "richtext",
					"id": "subtext",
					"label": "Subtext"
				},
				{
					"type": "text",
					"id": "legend_title",
					"label": "Fieldset Title"
				},
				{
					"type": "richtext",
					"id": "legend_subtext",
					"label": "Fieldset Subtext"
				},
				{
					"type": "radio",
					"id": "placement",
					"label": "Placement",
					"default": "profile",
					"options": [
						{
							"value": "profile",
							"label": "Profile"
						},
						{
							"value": "account",
							"label": "Account"
						},
						{
							"value": "preferences",
							"label": "Preferences"
						}
					]
				},
				{
					"type": "select",
					"id": "field_wrap_class_width",
					"label": "Field Width",
					"options": [
						{
							"value": "w-full",
							"label": "100%"
						},
						{
							"value": "w-1/3",
							"label": "33%"
						},
						{
							"value": "w-2/5",
							"label": "40%"
						},
						{
							"value": "w-[45%]",
							"label": "45%"
						},
						{
							"value": "w-1/2",
							"label": "50%"
						},
						{
							"value": "w-2/3",
							"label": "66%"
						},
						{
							"value": "w-auto",
							"label": "Auto"
						}
					]
				},
				{
					"type": "select",
					"id": "field_wrap_class_width_desktop",
					"label": "Field Width (Desktop)",
					"options": [
						{
							"value": "lg:w-full",
							"label": "100%"
						},
						{
							"value": "lg:w-1/3",
							"label": "33%"
						},
						{
							"value": "lg:w-2/5",
							"label": "40%"
						},
						{
							"value": "lg:w-[45%]",
							"label": "45%"
						},
						{
							"value": "lg:w-1/2",
							"label": "50%"
						},
						{
							"value": "lg:w-2/3",
							"label": "66%"
						},
						{
							"value": "lg:w-auto",
							"label": "Auto"
						}
					]
				},
				{
					"type": "select",
					"id": "format",
					"label": "Format",
					"options": [
						{
							"value": "text",
							"label": "Text Input"
						},
						{
							"value": "select",
							"label": "Dropdown Select"
						},
						{
							"value": "radio",
							"label": "Radio Buttons"
						},
						{
							"value": "checkbox",
							"label": "Checkboxes"
						},
						{
							"value": "swatches",
							"label": "Color Swatches"
						},
						{
							"value": "chips",
							"label": "Chips"
						}
					]
				},
				{
					"type": "textarea",
					"id": "options",
					"label": "Options",
					"info": "{{ value }}:{{ Label }}"
				},
				{
					"type": "number",
					"id": "limit",
					"label": "Input Limit",
					"default": 3,
					"info": "Applies only to Swatches and Chips"
				},
				{
					"type": "radio",
					"id": "profile_inclusion",
					"label": "Profile Edit Inclusion",
					"default": "all",
					"options": [
						{
							"value": "all",
							"label": "All"
						},
						{
							"value": "primary",
							"label": "Primary Profile"
						},
						{
							"value": "secondary",
							"label": "Secondary Profiles"
						}
					]
				},
				{
					"type": "checkbox",
					"id": "profile_summary_inclusion",
					"label": "Profile Summary Display",
					"default": false
				},
				{
					"type": "checkbox",
					"id": "primary_disabled",
					"label": "Disabled Field on Primary Profile",
					"default": false
				},
				{
					"type": "liquid",
					"id": "inclusion_js",
					"label": "Conditional Display",
					"info": "[JavaScript] If populated, an expression that evaluates to a truthy value will show the field."
				},
				{
					"type": "liquid",
					"id": "custom_liquid",
					"label": "Custom Liquid",
					"info": "Renders below field"
				}
			]
		},
		{
			"type": "profile",
			"name": "Profile",
			"settings": [
				{
					"type": "number",
					"id": "limit",
					"label": "Profile Limit",
					"default": 3
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:account_block_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include TextStack"
				}
			]
		},
		{
			"type": "subscription",
			"name": "Subscription",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:account_block_class, label_prefix:Item "
				},
				{
					"type": "text",
					"id": "title",
					"label": "Title"
				},
				{
					"type": "richtext",
					"id": "subtext",
					"label": "Subtext"
				},
				{
					"type": "select",
					"id": "field_type",
					"label": "Input Field Type",
					"default": "email",
					"options": [
						{
							"value": "email",
							"label": "Email"
						},
						{
							"value": "tel",
							"label": "Tel"
						},
						{
							"value": "text",
							"label": "Text"
						}
					]
				},
				{
					"type": "text",
					"id": "trigger_text",
					"label": "Trigger Text"
				},
				{
					"type": "select",
					"id": "button_style",
					"label": "Button Style",
					"options": [
						{
							"value": "@include ButtonStyle",
							"label": "Inclusion"
						},
						{
							"value": "button--primary",
							"label": "@global:  Primary"
						},
						{
							"value": "button--secondary",
							"label": "@global:  Secondary"
						},
						{
							"value": "button--tertiary",
							"label": "@global:  Tertiary"
						},
						{
							"value": "button--light",
							"label": "@global:  Light"
						},
						{
							"value": "button--dark",
							"label": "@global:  Dark"
						},
						{
							"value": "button--pop",
							"label": "@global:  Pop"
						},
						{
							"value": "button--highlight",
							"label": "@global:  Highlight"
						},
						{
							"value": "button--action",
							"label": "@global:  Action"
						},
						{
							"value": "button--simple",
							"label": "@global:  Simple"
						},
						{
							"value": "button--emphasis",
							"label": "@global:  Emphasis"
						},
						{
							"value": "button--light-text-link",
							"label": "@global:  Light Text Link"
						},
						{
							"value": "button--link",
							"label": "@global:  Text Link"
						},
						{
							"value": "button--micro-link",
							"label": "@global:  Micro Text Link"
						},
						{
							"value": "button--icon",
							"label": "@global:  Icon"
						},
						{
							"value": "button--primary-hover",
							"label": "@global:  Primary Hover"
						},
						{
							"value": "button--secondary-hover",
							"label": "@global:  Secondary Hover"
						},
						{
							"value": "button--tertiary-hover",
							"label": "@global:  Tertiary Hover"
						}
					]
				},
				{
					"type": "text",
					"id": "field_name",
					"label": "Field Name",
					"default": "email"
				},
				{
					"type": "text",
					"id": "placeholder_text",
					"label": "Placeholder Text"
				},
				{
					"type": "text",
					"id": "input_pattern",
					"label": "Input Pattern"
				},
				{
					"type": "liquid",
					"id": "on_input",
					"label": "JavaScript OnInput"
				},
				{
					"type": "text",
					"id": "error_message",
					"label": "Field Validation Error Text"
				},
				{
					"type": "text",
					"id": "submit_text",
					"label": "Submit Text",
					"default": "Submit"
				},
				{
					"type": "richtext",
					"id": "info_text",
					"label": "Revealed Information"
				},
				{
					"type": "richtext",
					"id": "success_text",
					"label": "Success Message"
				},
				{
					"type": "text",
					"id": "subscription_key",
					"label": "Primary Subscription Key"
				},
				{
					"type": "text",
					"id": "identity_key",
					"label": "Customer Identity Key"
				},
				{
					"type": "textarea",
					"id": "subscription_options",
					"label": "Subscription Options",
					"info": "Each line follows pattern: key:Label:Description text"
				},
				{
					"type": "text",
					"id": "edit_text",
					"label": "Edit Button Text",
					"default": "Manage Subscription"
				}
			]
		},
		{
			"type": "order-history",
			"name": "Order History",
			"settings": [
				{
					"type": "text",
					"id": "title",
					"label": "Title",
					"default": "Order History"
				},
				{
					"type": "number",
					"id": "limit",
					"label": "Orders per Page",
					"default": 5
				},
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:account_block_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include TextStack"
				}
			]
		},
		{
			"type": "order-detail",
			"name": "Order Detail",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:account_block_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include TextStack"
				}
			]
		},
		{
			"type": "addresses",
			"name": "Addresses",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:account_block_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include TextStack"
				}
			]
		},
		{
			"type": "password",
			"name": "Password Recovery",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:account_block_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include TextStack"
				}
			]
		},
		{
			"type": "wishlist",
			"name": "Wishlist",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:account_block_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include TextStack"
				}
			]
		},
		{
			"type": "content-item",
			"name": "Content Item",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:account_block_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include ContentItem"
				}
			]
		},
		{
			"type": "loyalty-status",
			"name": "Loyalty Status",
			"settings": []
		},
		{
			"type": "custom-liquid",
			"name": "Custom Liquid",
			"settings": [
				{
					"type": "liquid",
					"id": "custom_liquid",
					"label": "Custom Liquid"
				}
			]
		},
		{
			"name": "Accordion",
			"type": "accordion",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:account_block_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include SectionDisplay, label_prefix:Display, id_prefix:accordion_detail_class"
				},
				{
					"type": "paragraph",
					"content": "@include Accordion"
				}
			]
		}
	]
}
{% endschema %}
