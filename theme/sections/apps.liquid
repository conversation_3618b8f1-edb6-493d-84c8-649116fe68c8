{% liquid 
	assign section_type = 'apps'
%}

<section 
	class="section section--{{ section_type }} {% render 'class-settings' prefix:'wrapper_class' settings:section.settings %}" 
	style="{% render 'style-settings' prefix:'wrapper_style' settings:section.settings %}">

	<div 
		class="section__container {% render 'class-settings' prefix:'container_class' settings:section.settings %}" 
		style="{%- render 'style-settings' prefix:'container_styles' settings:section.settings -%}">

    {%- liquid
        for block in section.blocks 
          case block.type 
            when '@app' 
              render block 

            when 'content-item' 
              render 'content-item' settings:block.settings 
          endcase 
        endfor 
      -%}
  </div>
</section>

{% schema %}
{
	"name": "t:sections.apps.name",
	"tag": "section",
	"class": "spaced-section",
	"settings": [
		{
			"type": "paragraph",
			"content": "@include Container, id_prefix:container_class"
		},
		{
			"type": "paragraph",
			"content": "@include SectionWrapper, label_prefix:Wrapper, id_prefix:wrapper_class"
		}
	],
	"blocks": [
		{
			"type": "@app"
		},
		{
			"type": "content-item",
			"name": "Content Item",
			"settings": [
				{
					"type": "paragraph",
					"content": "@include BlockWidths, id_prefix:account_block_class, label_prefix:Item "
				},
				{
					"type": "paragraph",
					"content": "@include ContentItem"
				}
			]
		}
	],
	"presets": [
		{
			"name": "t:sections.apps.presets.name"
		}
	]
}
{% endschema %}
