<article class="article h-full bg-light" itemscope itemtype="http://schema.org/BlogPosting">
  {%- for block in section.blocks -%}
    {%- case block.type -%}
      {%- when '@app' -%}
        <div class="page-width page-width--narrow px-4">
          {% render block %}
        </div>
      {%- when 'featured_image'-%}
        {%- if block.settings.src -%}
          <div class="m-0" {{ block.shopify_attributes }}>
            <div class="block bg-gray-50 relative {{ block.settings.height_mobile }} {{ block.settings.height }}" itemprop="image">
              {% render 'image' with image: block.settings.src, class: "article__featured-image block top-0 left-0 w-full h-full object-cover object-top" %}
            </div>
          </div>
        {%- endif -%}

        {%- when 'title'-%}
          <header class="article__header container bg-light px-4 pb-4 mx-auto" {{ block.shopify_attributes }}>
            <div class="article__info">
              {%- if block.settings.blog_show_category -%}
                <span class="article__meta article__category text-sm m-0" itemprop="category">{{ blog.title }}</span>
              {%- endif -%}

              {%- if block.settings.blog_show_tags -%}
                {% for tag in article.tags -%}
                  <span class="article__meta article__tag text-sm m-0" itemprop="category">{{ tag }}</span>
                {%- endfor %}
              {%- endif -%}
              
              {%- if block.settings.blog_show_author -%}
                <span class="article__meta article__author text-sm m-0">{{ article.author }}</span>
              {%- endif -%}

              {%- if block.settings.blog_show_date -%}
                <span class="article__meta article__date text-sm m-0" itemprop="dateCreated pubdate datePublished">{{ article.published_at | date: '%b %d, %Y' | upcase }}</span>
              {%- endif -%}

              {%- if block.settings.blog_show_ttr -%}
                <span class="article__meta article__ttr text-sm m-0">{{ article.content | strip_html | size | divided_by: 1200 }} min read</span>
              {%- endif -%}
            </div>
            
            <h1 class="article__title type-headline my-0" itemprop="headline">{{ article.title | escape }}</h1>

            {%- if block.settings.blog_show_share -%}
              {% render 'social-sharing' %}   
            {%- endif -%}
          </header>

        {%- when 'content'-%}
          <div class="article__content mx-auto rte" itemprop="articleBody" {{ block.shopify_attributes }}>
            {{ article.content }}
          </div>   

        {%- when 'back-button'-%}
          <div class="flex justify-center align-center">
            <a href="{{ blog.url }}" class="article__link link flex items-center gap-1 p-4">
              {% render 'icon' icon: 'chevron-left' width:16 height:16 strokeWidth:2 %}
              {{ 'blogs.article.back_to_blog' | t: blog: blog.title }}
            </a>
          </div>
          
    {%- endcase -%}
  {%- endfor -%}

  <div class="article__footer"></div>

  {%- if blog.comments_enabled? -%}
    <div class="article__comment-wrapper background-secondary py-8">
      <div id="comments" class="page-width page-width--narrow px-4">
        {% form 'new_comment', article %}
          {%- liquid
            assign post_message = 'blogs.article.success'
            if blog.moderated? and comment.status == 'unapproved'
              assign post_message = 'blogs.article.success_moderated'
            endif
          -%}
          <h2 class="f2 text-2xl poppins leading-normal mb-4 mt-0 capitalize">{{ 'blogs.article.comment_form_title' | t }}</h2>
          {%- if form.errors -%}
            <div class="form__message" role="alert">
              <h3 class="form-status caption-large" tabindex="-1" autofocus>
                {% render 'icon' icon:'alert-circle' %} {{ 'templates.contact.form.error_heading' | t }}
              </h3>
            </div>
            <ul class="form-status-list caption-large">
              {%- for field in form.errors -%}
                <li>
                  <a href="#CommentForm-{{ field }}" class="link">
                    {%- if form.errors.translated_fields[field] contains 'author' -%}
                      {{ 'blogs.article.name' | t }}
                    {%- elsif form.errors.translated_fields[field] contains 'body'-%}
                     {{ 'blogs.article.message' | t }}
                    {%- else -%}
                      {{ form.errors.translated_fields[field] }}
                    {%- endif -%}
                    {{ form.errors.messages[field] }}
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          {%- elsif form.posted_successfully? -%}
            <div class="form-status-list form__message" role="status">
              <h3 class="form-status" tabindex="-1" autofocus>{% render 'icon-success' %} {{ post_message | t }}</h3>
            </div>
          {%- endif -%}

          <div {% if blog.moderated? == false %} class="article__comments-fields"{% endif %}>
            <div class="article__comment-fields">
              <div class="field field--with-error mt-2">
                <label class="field__label form--label" for="CommentForm-author">{{ 'blogs.article.name' | t }} <span aria-hidden="true">*</span></label>
                {%- if form.errors contains 'author' -%}
                  <small id="CommentForm-author-error">
                    <span class="form__message">{% render 'icon' icon:'alert-circle' %}{{ 'blogs.article.name' | t }} {{ form.errors.messages['author'] }}.</span>
                  </small>
                {%- endif -%}
                <input
                  type="text"
                  name="comment[author]"
                  id="CommentForm-author"
                  class="field__input form--input"
                  autocomplete="name"
                  value="{{ form.author }}"
                  aria-required="true"
                  {% if form.errors contains 'author' %}
                    aria-invalid="true"
                    aria-describedby="CommentForm-author-error"
                  {% endif %}
                  placeholder="{{ 'blogs.article.name' | t }}"
                >
              </div>
              <div class="field field--with-error mt-2">
                <label class="field__label form--label" for="CommentForm-email">{{ 'blogs.article.email' | t }} <span aria-hidden="true">*</span></label>
                {%- if form.errors contains 'email' -%}
                  <small id="CommentForm-email-error">
                    <span class="form__message">{% render 'icon' icon:'alert-circle' %}{{ 'blogs.article.email' | t }} {{ form.errors.messages['email'] }}.</span>
                  </small>
                {%- endif -%}
                <input
                  type="email"
                  name="comment[email]"
                  id="CommentForm-email"
                  autocomplete="email"
                  class="field__input form--input"
                  value="{{ form.email }}"
                  autocorrect="off"
                  autocapitalize="off"
                  aria-required="true"
                  {% if form.errors contains 'email' %}
                    aria-invalid="true"
                    aria-describedby="CommentForm-email-error"
                  {% endif %}
                  placeholder="{{ 'blogs.article.email' | t }}"
                >
              </div>
            </div>
            <div class="field field--with-error mt-2">
              <label class="form__label field__label form--label" for="CommentForm-body">{{ 'blogs.article.message' | t }} <span aria-hidden="true">*</span></label>
              <textarea
                rows="5"
                name="comment[body]"
                id="CommentForm-body"
                class="text-area field__input form--input"
                aria-required="true"
                {% if form.errors contains 'body' %}
                  aria-invalid="true"
                  aria-describedby="CommentForm-body-error"
                {% endif %}
                placeholder="{{ 'blogs.article.message' | t }}"
              >{{ form.body }}</textarea> 
            </div>
            {%- if form.errors contains 'body' -%}
              <small id="CommentForm-body-error">
                <span class="form__message">{% render 'icon' icon:'alert-circle' %}{{ 'blogs.article.message' | t }} {{ form.errors.messages['body'] }}.</span>
              </small>
            {%- endif -%}
          </div>
          {%- if blog.moderated? -%}
            <p class="article__comment-warning caption">{{ 'blogs.article.moderated' | t }}</p>
          {%- endif -%}
          <input type="submit" class="btn bg-primary" value="{{ 'blogs.article.post' | t }}">
        {% endform %}

        {%- if article.comments_count > 0 -%}
          {%- assign anchorId = '#Comments-' | append: article.id -%}

          <h2 id="Comments-{{ article.id }}" class="sr-only">{{ 'blogs.article.comments' | t: count: article.comments_count }}</h2>
          {% paginate article.comments by 5 %}
            <div class="article__comments">
              {%- if comment.status == 'pending' and comment.content -%}
                <article id="{{ comment.id }}" class="article__comments-comment">
                  {{ comment.content }}
                  <footer class="right">
                    <span class="circle-divider caption-with-letter-spacing">{{ comment.author }}</span>
                  </footer>
                </article>
              {%- endif -%}

              {%- for comment in article.comments -%}
                <article id="{{ comment.id }}" class="article__comments-comment py-6 mt-6 border-t">
                  <span class="text-md font-normal block ">{{ comment.author }}</span>
                  <span class="text-xs lg:text-sm mt-0 block">{{ comment.created_at | time_tag: format: 'month_year' }}</span>
                  <span class="block pt-4">{{ comment.content }}</span>
                </article>
              {%- endfor -%}
              {% render 'pagination', paginate: paginate, anchor: anchorId %}
            </div>
          {% endpaginate %}
        {%- endif -%}
      </div>
    </div>
  {%- endif -%}
</article>

{% schema %}
{
  "name": "t:sections.main-article.name",
  "tag": "section",
  "settings": [
    {
      "type": "html",
      "id": "static_schema",
      "label": "Static / Dynamic Schema",
      "info": "If a value is found in this field, it will be rendered as the schema for this article. This may be configured to come from a metafield using Dynamic Sources."
    },
    {
      "type": "liquid",
      "id": "liquid_schema",
      "label": "Liquid Schema",
      "info": "If a value is found in this field and no Static / Dynamic Schema is defined, it will be rendered as the schema for this article"
    },
    {
      "type": "paragraph",
      "content": "If no value is found in either of the above fields, default Article schema spec will be used."
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "featured_image",
      "name": "t:sections.main-article.blocks.featured_image.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "height",
          "label": "Image Height (Desktop)",
          "options": [
            {
              "value": "lg:h-100v",
              "label": "Full height"
            },
            {
              "value": "lg:h-70v",
              "label": "Three quarter height"
            },
            {
              "value": "lg:h-50v",
              "label": "Half height"
            },
            {
              "value": "lg:h-20v",
              "label": "Quarter height"
            },
            {
              "value": "lg:h-auto",
              "label": "Text content height"
            },
            {
              "value": "h-auto-img-l",
              "label": "Image height"
            }
          ],
          "default": "lg:h-100v"
        },
        {
          "type": "select",
          "id": "height_mobile",
          "label": "Image Height (Mobile)",
          "options": [
            {
              "value": "h-100v",
              "label": "Full height"
            },
            {
              "value": "h-70v",
              "label": "Three quarter height"
            },
            {
              "value": "h-50v",
              "label": "Half height"
            },
            {
              "value": "h-20v",
              "label": "Quarter height"
            },
            {
              "value": "h-auto",
              "label": "Text content height"
            },
            {
              "value": "h-auto-img",
              "label": "Image height"
            },
            {
              "value": "h-content-below",
              "label": "Image top / Content below"
            }
          ],
          "default": "h-100v"
        },
        {
          "type": "image_picker",
          "id": "src",
          "label": "Image Source"
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.main-article.blocks.title.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "blog_show_category",
          "default": true,
          "label": "Show category"
        },
        {
          "type": "checkbox",
          "id": "blog_show_tags",
          "default": true,
          "label": "Show tags"
        },
        {
          "type": "checkbox",
          "id": "blog_show_author",
          "default": false,
          "label": "t:sections.main-article.blocks.title.settings.blog_show_author.label"
        },
        {
          "type": "checkbox",
          "id": "blog_show_date",
          "default": true,
          "label": "t:sections.main-article.blocks.title.settings.blog_show_date.label"
        },
        {
          "type": "checkbox",
          "id": "blog_show_ttr",
          "default": false,
          "label": "Show time estimate"
        },
        {
          "type": "checkbox",
          "id": "blog_show_share",
          "default": false,
          "label": "Show share links",
          "info": "Your social media links can be configured under Theme Settings > Social Media"
        }
      ]
    },
    {
      "type": "content",
      "name": "t:sections.main-article.blocks.content.name",
      "limit": 1
    },
    {
      "type": "back-button",
      "name": "Back Button",
      "limit": 1
    }
  ]
}
{% endschema %}
