<script>

  window.offers = window.offers || []

  {% for block in section.blocks %}

  {% unless block.settings.inclusion == blank %}
    // if(Util.express((offer.conditions || 'true'), {{ block.settings | json}})) 
    if({{ block.settings.inclusion_js | default: 'true'}}) {   
    
    offers.push({{ block.settings | json}});
   
  }
 
  {% endunless %}
  {% endfor %}

  /*
  {
    id:"example",
    group:'example',
    title:"Example Offer",
    approach:`Getting close... {{ balance }} away`,
    success:`Success!`,
    delay:1000,
    type:'',
    conditions:[
      '!cart.items.map(i=>i.id).includes(32139621793907)',
      'cart.total_price > 1000',
      '!!customer'
    ],
    actions:[
    ],
    products:[],
    exclusive:true,
    automatic:true,
    accepted:false,
    declined:false,
    eligible:false,
    in_cart:false,
    storage:'local' // || session || cart
  }
  */
</script> 

{% schema %}
{
  "name": "Offers",
  "blocks": [
    {
      "type": "offer",
      "name": "Open Offer",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "info": "Internal reference only"
        },
        {
          "type": "text",
          "id": "key",
          "label": "Key *"
        },
        {
          "type": "checkbox",
          "id": "combinable",
          "label": "Combinable",
          "info": "Whether to display combinable offers as one progress bar"
        }, 
        {
          "type":"header",
          "content":"Inclusion Settings"
        },
        {
          "type": "liquid",
          "id": "inclusion",
          "label": "Liquid Inclusion Logic *",
          "info": "[Liquid] A value other than blank will register the offer",
          "default":"1"
        },
	{
	  "type": "liquid",
	  "label": "Javascript Logic Inclusion",
	  "id": "inclusion_js",
	  "info": "Insert any javascript logic that evaluates to true to display the section"
	},  
        {
          "type": "liquid",
          "id": "conditions",
          "label": "Eligibility Conditions *",
          "info": "[JavaScript] An expression that produces a true value will mark the offer as eligible for presentation."
        },
        {
          "type": "liquid",
          "id": "progress_numerator",
          "label": "Progress Numerator"
        },
        {
          "type": "liquid",
          "id": "progress_denominator",
          "label": "Progress Denominator"
        },
        {
          "type":"header",
          "content":"Action Settings"
        },
        {
          "type": "liquid",
          "id": "action_filter",
          "label": "Action Logic",
          "info": "Constrains Actions",
          "info": "[JavaScript] If populated, An expression that produces a true value will allow"
        },
        {
          "type": "number",
          "id": "delay",
          "label": "Delay",
          "info": "Seconds"
        },
        {
          "type": "liquid",
          "id": "actions",
          "label": "Actions",
          "info": "[JavaScript] Present the offer. Will be executed when offer is eligible and Action Logic evalues to a truthy value"
        },
        {
          "type": "liquid",
          "id": "revoke",
          "label": "Revoke",
          "info": "[JavaScript] Will be executed when offer is non-eligible but has already been accepted"
        },
        {
          "type": "checkbox",
          "id": "automatic",
          "label": "Automatically mark as Accepted"
        },
        {
          "type": "select",
          "id": "storage",
          "label": "Persist Across",
          "default":"",
          "options": [
            {
              "label":"None",
              "value":""
            },
            {
              "label":"Session",
              "value":"session"
            },
            {
              "label":"Browser",
              "value":"local"
            }
          ]
        },
        {
          "type":"header",
          "content":"Messaging Settings"
        },
        {
          "type": "liquid",
          "id": "approach",
          "label": "Approach Messaging",
          "info": "[JavaScript] With Template literals against the offer."
        },
        {
          "type": "liquid",
          "id": "success",
          "label": "Success Messaging",
          "info": "[JavaScript] With Template literals against the offer."
        },
        {
          "type": "header",
          "content": "Combined Progress Bar Settings"
        },
        {
          "type": "liquid",
          "id": "threshold_icon",
          "label": "Threshold Icon (SVG)"
        },
        {
          "type": "text",
          "id": "threshold_label",
          "label": "Threshold Label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Offers",
      "category": "Advanced",
      "settings": {},
      "blocks": []
    }
  ]
}
{% endschema %}
