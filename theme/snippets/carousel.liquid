{% if open %}
<div class="frame-carousel" x-data="{swiper: null }" x-init="$nextTick(()=>{swiper = Carousels.create($refs.container)})">
  <div class="swiper-container swiper !overflow-visible" swiper x-ref="container">
    <script type="swiper/config">
      { 
        breakpoints:{
          1:{
            slidesPerView: {{ settings.slides_per_view | default: 1 }}
          },
          1025:{
            slidesPerView: {{ settings.slides_per_view_desktop | default: 1 }}
          }
        },
        {% if settings.carousel_pagination != blank %}
        pagination: {
          el: '.swiper-pagination',
          type: '{{ settings.carousel_pagination }}',
          clickable: true
        }
        {% endif %}
      }
    </script>
    <div class="swiper-pagination swiper-pagination--{{ settings.carousel_pagination }}"></div>
      <div class="swiper-wrapper">

  {% elsif close %}
              
    </div>

  </div>
</div>

{% endif %}
