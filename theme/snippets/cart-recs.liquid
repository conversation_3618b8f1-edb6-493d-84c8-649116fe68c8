{% if _settings.url != blank %}
  <div class="slider-cart__recs" {% if _settings.inclusion_js %} x-show="{{ _settings.inclusion_js }}" {% endif %}
  x-data="{ 
    sliderId: $id('slider-cart'),
    products: [],
    swiper: null,
    currentSlide: 0,
    loading: true,

    getSwiperConfig() {
      return {
        breakpoints: {
          '1': {
            slidesPerView: {{ _settings.slides_per_view }},
            spaceBetween: {{ _settings.slides_space_between }},
            slidesPerGroup: 1 
          },
          '1024': {
            slidesPerView: {{ _settings.slides_per_view_desktop }},
            spaceBetween: {{ _settings.slides_space_between_desktop }},
            slidesPerGroup: 1 
          }
        },
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        },
        {% if _settings.loop %}loop: true,{% endif %}
        keyboard: { enabled: true, onlyInViewport: true },
        speed: 500,
        mousewheel: {
          enabled: true,
          forceToAxis: true,
          thresholdDelta: 8
        },
        on: {
          init: (swiper) => {
            if (this.currentSlide > 0) {
              swiper.slideTo(this.currentSlide, 0, false);
            }
          }
        }
      };
    },

    initSwiper() {
      $nextTick(() => {
        if (this.swiper) {
          this.currentSlide = this.swiper.activeIndex;
          this.swiper.destroy(true, true);
        }
        this.swiper = new Swiper(`#${this.sliderId}`, this.getSwiperConfig());
      });
    },
    
    async load() {
      $data.loading = true;
      try {
        let products = [];

        if (!{{ _settings.include_cart_recs }} || (!$store.cart?.items?.length && {{ _settings.include_cart_recs }})) {
          const response = await fetch(Util.literal('{{ _settings.url }}', {}));
          const data = await response.json();
          {% if _settings.map != blank %}
          products = Util.map(data, {{_settings.map}}).products || [];
          {% else %}
          products = data.products || [];
          {% endif %}
          products.forEach(product => {
            product.sku = product.variants[0].sku;
          });
        } else {
          const cartProductIds = $store.cart.items.map(i => i.product_id);
          const recommendationPromises = cartProductIds.map(productId => 
            fetch(`${window.Shopify.routes.root}recommendations/products.json?product_id=${productId}&limit=5&intent=related`)
              .then(response => response.json())
              .then(data => data.products || [])
              .catch(() => [])
          );

          const results = await Promise.all(recommendationPromises);
          const cartVariantIds = $store.cart.items.map(item => item.variant_id);
          
          products = results.flat().reduce((unique, product) => {
            if (product.variants.every(variant => cartVariantIds.includes(variant.id)) || 
                unique.some(p => p.id === product.id)) {
              return unique;
            }
            
            if (product.variants?.length) {
              product.sku = product.variants[0].sku;
            }
            
            unique.push(product);
            return unique;
          }, []);
        }

        {% if _settings.include_swatches %}
          if (window.Util?.hooks?.process) {
            products = await Util.hooks.process('Products:enhance', products);
          }
        {% endif %}
        
        $data.products = products;
        await $nextTick();
        this.initSwiper();
        $data.loading = false;
      } catch (error) {
        console.error('Error loading products', error);
        $data.products = [];
      }
    }
  }" 
  x-init="load();
  {% if _settings.include_cart_recs %}
    $watch('$store.cart.items.length', async (newLength, oldLength) => {
        if (newLength !== oldLength) {
          $data.loading = true;
          const swiperEl = $refs.container;
          if (swiperEl?.swiper) {
            {% if settings.enable_logs -%}console.log('Resetting swiper position');{%- endif %}
            swiperEl.swiper.slideTo(0);
          }
          await load();
        }
      })
  {% endif %}
  "
>
  <h4 class="slider-cart__recs-title">{{ _settings.title }}</h4>
  <div class="flex relative">

    {% if _settings.arrows %}
      <button class="slider-cart__recs-prev" onclick="this.nextElementSibling.swiper.slidePrev()">
        {% render 'icon' icon:'chevron-left' %}
      </button>
    {% endif %}

    <div class="swiper swiper-container slider-cart-swiper" :id="sliderId" swiper x-ref="container">
      <div class="swiper-wrapper">
        <template x-for="product in products">
          <template x-if="!$store.cart.items.map(i=>i.product_id).includes(product.id) && product.variants.some(variant => variant.available)">
            <div class="swiper-slide">
              {% render 'upsell-item' source_name:'slidercart' _settings:_settings %}
            </div>
          </template>
        </template>
      </div>
    </div>

    {% if _settings.arrows %}
      <button class="slider-cart__recs-next" onclick="this.previousElementSibling.swiper.slideNext()">
        {% render 'icon' icon:'chevron-right' %}
      </button>
    {% endif %}

  </div>
</div>
{% endif %}
