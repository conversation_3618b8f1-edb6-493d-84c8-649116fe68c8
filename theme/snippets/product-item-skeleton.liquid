<article class="product-item__contents relative flex-grow rounded-sm">
  
  <div class="bg-gray-50 relative">
    <div class="aspect-w-9 aspect-h-10"></div>
    <div class="flex justify-between bottom-0 w-full px-4 py-3" style="
        margin-bottom: 2px;
    ">
      <div class="bg-gray-100 h-4 w-1/3"></div>
      <div class="bg-gray-100 h-4 w-1/6"></div>
    </div>
  </div>
  <div class="flex justify-between py-4 mt-1">
    <div class="bg-gray-100 h-4 w-1/2"></div>
  </div>
  <div class="flex justify-start pb-4" style="
        margin-bottom: 1px;
    ">
    {% for i in(1..4) %}
    <div class="bg-gray-100 h-12 w-12 mr-2"></div>
    {% endfor %}
  </div>
</article>
{% comment %}
<div class="product-item__contents relative flex-grow rounded-sm">
        
        <div class="relative bg-gray-100 product-item__image-bg group">
          <a id="product_link_4678650462263" class="product-item__link" aria-label=", $75, go to product page" href="/collections/mens/products/06993-473-m">
            <figure class="relative m-0 product-item__image-wrapper bg-gray-warm-dark aspect-h-1 aspect-w-1 overflow-hidden" data-product-item-image="">

              
              

              <img class="product-item__image--main bg-white opacity-90 absolute object-contain inset-0 " aria-label="" loading="lazy" src="//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab.jpg?v=1646074132" x-srcset="" id="img_4678650462263">

              

              
                
                <div class="product-image__hover bg-gray-100 product-item__image-bg flex absolute transition-opacity opacity-0 group-hover:opacity-100   inset-0 ">
                  
                   
                  <img class="object-contain w-full h-full" srcset="//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab_e6444c54-ecf9-454e-855a-d34968e4e05e.jpg?v=1646074132 180w 135h,//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab_e6444c54-ecf9-454e-855a-d34968e4e05e.jpg?v=1646074132 360w 270h,//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab_e6444c54-ecf9-454e-855a-d34968e4e05e.jpg?v=1646074132 540w 405h,//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab_e6444c54-ecf9-454e-855a-d34968e4e05e.jpg?v=1646074132 720w 540h,//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab_e6444c54-ecf9-454e-855a-d34968e4e05e.jpg?v=1646074132 900w 675h,//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab_e6444c54-ecf9-454e-855a-d34968e4e05e.jpg?v=1646074132 1080w 810h,//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab_e6444c54-ecf9-454e-855a-d34968e4e05e.jpg?v=1646074132 1296w 972h,//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab_e6444c54-ecf9-454e-855a-d34968e4e05e.jpg?v=1646074132 1512w 1134h,//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab_e6444c54-ecf9-454e-855a-d34968e4e05e.jpg?v=1646074132 1728w 1296h,//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab_e6444c54-ecf9-454e-855a-d34968e4e05e.jpg?v=1646074132 1944w 1458h,//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab_e6444c54-ecf9-454e-855a-d34968e4e05e.jpg?v=1646074132 2160w 1620h,//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab_e6444c54-ecf9-454e-855a-d34968e4e05e.jpg?v=1646074132 2376w 1782h,//cdn.shopify.com/s/files/1/0006/9969/5159/products/06993-473-0e79e4134d202f6384a26da843eb96102cb886ab_e6444c54-ecf9-454e-855a-d34968e4e05e.jpg?v=1646074132 2400w 1800h">
                  
                </div>
              

            </figure>

            <div class="px-4 py-1 bg-secondary product-item__meta">
              <div class="text-left">
                
                
                <h3 class="my-1 leading-snug product-title product-item__title">
                  CROWN 2000
                </h3>
              </div>

              <div class="leading-snug product-item__price flex space-x-4">  
                
                

                  
                    $75
                  

                
              </div>
            </div>
          </a>

          <div class="product-item__tools absolute flex opacity-100 lg:top-2 top-1 group-hover:opacity-100 lg:right-2 right-1 lg:opacity-0"><button class="product-item__button--wishlist button button--tertiary p-1 lg:p-3 rounded-full hover:bg-white">
              
<svg class="icon   icon-heart" style="" width="16" height="16" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
  <use xlink:href="#icon-def-heart"></use>
</svg>
<span class="sr-only">heart</span>

              <span class="hidden">Wishlist</span>
            </button>
          </div>
        </div>
      


        <div class="product-item__info flex flex-col px-4 pt-1 pb-4 text-left">
          <div class="product-item__badges absolute top-0 left-0 flex flex-col items-start mt-3 ml-3 space-y-1 text-sm brand-semibold mb-0-5">
            

            

          </div>

          <div class="product-item__footer -mx-4"><div class="product-item__siblings overflow-x-hidden" x-neptune-swiper="{slidesPerView:4.5}">
              <p class="mb-0 mt-4 text-xs">Color: OUTER SPACE/WHITE</p>
              <div class="swiper-wrapper items-center">
                
                <div class="swiper-slide flex h-20 w-20">
                  <div class="relative h-20 w-20 overflow-hidden swatch" neptune-engage="{on: 'mouseenter', targets: [
                    {selector:'_parent .swatch .border-2' classes:remove:active},
                    {selector:'_self .border-2' classes:add:active},
                    {selector:'#img_4678650462263' attributes:[
                      {att:src set:},
                      {att:srcset set:}
                    ]},
                    {selector:'#product_link_4678650462263' attributes:[{att:href set:}]}
                  ]}">
                    <img class="object-contain h-full" src="https://cdn.shopify.com/s/files/1/0006/9969/5159/products/03377-080-original.jpg?v=1645562936">
                  </div>
                </div>
                
                <div class="swiper-slide flex h-20 w-20">
                  <div class="relative h-20 w-20 overflow-hidden swatch opacity-50" neptune-engage="{on: 'mouseenter', targets: [
                    {selector:'_parent .swatch .border-2' classes:remove:active},
                    {selector:'_self .border-2' classes:add:active},
                    {selector:'#img_4678650462263' attributes:[
                      {att:src set:},
                      {att:srcset set:}
                    ]},
                    {selector:'#product_link_4678650462263' attributes:[{att:href set:}]}
                  ]}">
                    <img class="object-contain h-full" src="https://cdn.shopify.com/s/files/1/0006/9969/5159/products/03377-080-original.jpg?v=1645562936">
                  </div>
                </div>
                
                <div class="swiper-slide flex h-20 w-20">
                  <div class="relative h-20 w-20 overflow-hidden swatch opacity-50" neptune-engage="{on: 'mouseenter', targets: [
                    {selector:'_parent .swatch .border-2' classes:remove:active},
                    {selector:'_self .border-2' classes:add:active},
                    {selector:'#img_4678650462263' attributes:[
                      {att:src set:},
                      {att:srcset set:}
                    ]},
                    {selector:'#product_link_4678650462263' attributes:[{att:href set:}]}
                  ]}">
                    <img class="object-contain h-full" src="https://cdn.shopify.com/s/files/1/0006/9969/5159/products/03377-080-original.jpg?v=1645562936">
                  </div>
                </div>
                
                <div class="swiper-slide flex h-20 w-20">
                  <div class="relative h-20 w-20 overflow-hidden swatch opacity-50" neptune-engage="{on: 'mouseenter', targets: [
                    {selector:'_parent .swatch .border-2' classes:remove:active},
                    {selector:'_self .border-2' classes:add:active},
                    {selector:'#img_4678650462263' attributes:[
                      {att:src set:},
                      {att:srcset set:}
                    ]},
                    {selector:'#product_link_4678650462263' attributes:[{att:href set:}]}
                  ]}">
                    <img class="object-contain h-full" src="https://cdn.shopify.com/s/files/1/0006/9969/5159/products/03377-080-original.jpg?v=1645562936">
                  </div>
                </div>
                
                <div class="swiper-slide flex h-20 w-20">
                  <div class="relative h-20 w-20 overflow-hidden swatch opacity-50" neptune-engage="{on: 'mouseenter', targets: [
                    {selector:'_parent .swatch .border-2' classes:remove:active},
                    {selector:'_self .border-2' classes:add:active},
                    {selector:'#img_4678650462263' attributes:[
                      {att:src set:},
                      {att:srcset set:}
                    ]},
                    {selector:'#product_link_4678650462263' attributes:[{att:href set:}]}
                  ]}">
                    <img class="object-contain h-full" src="https://cdn.shopify.com/s/files/1/0006/9969/5159/products/03377-080-original.jpg?v=1645562936">
                  </div>
                </div>
                
                <span class="swiper-slide text-xs whitespace-nowrap">+ 4 more Colors</span> 
              </div>
            </div>

            

            <div class="product-item__quickadd hidden active:block">
              <p class="mb-2 mt-0 text-xs">Select Size</p>
              <div class="grid grid-cols-5 gap-2">
                
                  <button class="swatch product-option__swatch swatch--disabled cursor-not-allowed text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick=""> 
                    
                    
                      
                    
                      
                    
                      
                    
                     6.5
                  </button>
                
                  <button class="swatch product-option__swatch cursor-pointer text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick="Neptune.cart.add({id:32513057488951})"> 
                    
                    
                      
                    
                      
                    
                      
                    
                     7
                  </button>
                
                  <button class="swatch product-option__swatch cursor-pointer text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick="Neptune.cart.add({id:32513057521719})"> 
                    
                    
                      
                    
                      
                    
                      
                    
                     7.5
                  </button>
                
                  <button class="swatch product-option__swatch cursor-pointer text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick="Neptune.cart.add({id:32513057554487})"> 
                    
                    
                      
                    
                      
                    
                      
                    
                     8
                  </button>
                
                  <button class="swatch product-option__swatch cursor-pointer text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick="Neptune.cart.add({id:32513057587255})"> 
                    
                    
                      
                    
                      
                    
                      
                    
                     8.5
                  </button>
                
                  <button class="swatch product-option__swatch cursor-pointer text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick="Neptune.cart.add({id:32513057620023})"> 
                    
                    
                      
                    
                      
                    
                      
                    
                     9
                  </button>
                
                  <button class="swatch product-option__swatch cursor-pointer text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick="Neptune.cart.add({id:32513057652791})"> 
                    
                    
                      
                    
                      
                    
                      
                    
                     9.5
                  </button>
                
                  <button class="swatch product-option__swatch cursor-pointer text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick="Neptune.cart.add({id:32513057685559})"> 
                    
                    
                      
                    
                      
                    
                      
                    
                     10
                  </button>
                
                  <button class="swatch product-option__swatch cursor-pointer text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick="Neptune.cart.add({id:32513057718327})"> 
                    
                    
                      
                    
                      
                    
                      
                    
                     10.5
                  </button>
                
                  <button class="swatch product-option__swatch cursor-pointer text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick="Neptune.cart.add({id:32513057751095})"> 
                    
                    
                      
                    
                      
                    
                      
                    
                     11
                  </button>
                
                  <button class="swatch product-option__swatch cursor-pointer text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick="Neptune.cart.add({id:32513057783863})"> 
                    
                    
                      
                    
                      
                    
                      
                    
                     11.5
                  </button>
                
                  <button class="swatch product-option__swatch cursor-pointer text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick="Neptune.cart.add({id:32513057816631})"> 
                    
                    
                      
                    
                      
                    
                      
                    
                     12
                  </button>
                
                  <button class="swatch product-option__swatch cursor-pointer text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick="Neptune.cart.add({id:32513057849399})"> 
                    
                    
                      
                    
                      
                    
                      
                    
                     13
                  </button>
                
                  <button class="swatch product-option__swatch cursor-pointer text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick="Neptune.cart.add({id:32513057882167})"> 
                    
                    
                      
                    
                      
                    
                      
                    
                     14
                  </button>
                
                  <button class="swatch product-option__swatch swatch--disabled cursor-not-allowed text-center block border border-light rounded text-dark font-subheading hover:border-dark active:bg-dark active:border-dark active:text-light p-2" onclick=""> 
                    
                    
                      
                    
                      
                    
                      
                    
                     15
                  </button>
                
              </div>
            </div>

            <button class="button button--primary group flex items-center justify-center mb-10 mt-5 w-full lg:hidden" neptune-engage="{targets: [{selector: '_self', classes:toggle:active}, {selector: '_parent .product-item__quickadd', classes:toggle:active}]}">
              <span class="mr-2">Quick Add to Bag</span>
              
                
<svg class="icon  group-active:hidden icon-plus" style="" width="14" height="14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <use xlink:href="#icon-def-plus"></use>
</svg>
<span class="sr-only">plus</span>

                
<svg class="icon  hidden group-active:block icon-minus" style="" width="14" height="14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <use xlink:href="#icon-def-minus"></use>
</svg>
<span class="sr-only">minus</span>

              
            </button>

          </div>
        </div>
      </div>

      {% endcomment %}