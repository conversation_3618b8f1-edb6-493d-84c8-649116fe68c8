{%- if source contains '((' -%}
  {%- liquid
    assign parts = source | split: '(('
    if buttons
      assign _buttons = buttons | split: ','
    endif 
    assign icon_index = -1
  -%}

  {%- for part in parts -%}
    {%- if part contains '))' -%}
      {%- liquid
        assign icon_index = icon_index | plus: 1 
        assign icon_ref = part | remove: '))' | split: ' ' | first
        assign dims = part | split: '))' | first | remove: icon_ref | strip | split: 'x' 
      -%}
      {%- capture icon -%}
        {% if buttons %}
          <button onclick="{{ _buttons[icon_index] }}">
        {% endif %}
        {% render 'icon' icon:icon_ref, fill:'currentColor', strokeWidth:0, width:dims[0], height:dims[1] class:icon_classes %}
        {% if buttons %}
          </span>
        {% endif %}
      {%- endcapture -%}

      {{ icon }}

    {% else %}

      {{ part }} 

    {% endif %}

    {%- assign bits = part | split: '))' -%}
    {%- if bits.size > 1 -%}
      <span>{{ part | split: '))' | last }}</span>
    {%- endif -%}
  {% endfor %}
{%- else -%}
  {{ source }}
{%- endif -%}