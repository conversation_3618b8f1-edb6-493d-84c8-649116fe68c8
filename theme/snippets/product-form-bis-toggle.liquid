<template x-if="product">
  
  <div class="w-full">

    {%- assign onclick = "`BackInStock.items.toggle({ product:'${product.handle}',variant:${product.variant ? product.variant.id : false}}); event.preventDefault(); return false;`" -%}
    {%- assign attributes = ':onclick="' | append: onclick | append: '"' -%}
    <template x-if="
    !!product.variant &&
    $store.backInStock.lists[0].items.map(i=>i.variant.id).includes(product.variant.id)">
      {% render 'button' tag:'button' attributes:attributes style:'light' content:'Remove Notification' class:'w-full justify-center' %}
    </template>

    <template x-if="
    !!product.variant &&
    !$store.backInStock.lists[0].items.map(i=>i.variant.id).includes(product.variant.id)">
      {% render 'button' tag:'button' attributes:attributes style:'primary' content:'Notify Me When Available' class:'w-full justify-center' %}
    </template>
          
  </div>

</template>
