{% assign nsA = 'checkout-pobox' %}

<div id="pobox_modal" class="{{ nsA }}">
  <div class="{{ nsA }}__container" style="">
    <div class="{{ nsA }}__inner" style="">
        <h1 class="{{ nsA }}__title">Unfortunately we do not ship to P.O. Boxes.</h1>
        <button
          onclick="document.querySelector('#checkout_shipping_address_address1').value='';
		  document.querySelector('#checkout_shipping_address_address2').value='';
		  document.querySelector('#pobox_modal').style.display = 'none'; document.querySelector('#checkout_shipping_address_address1').focus(); return false;"
          class="{{ nsA }}__button"
          data-btn-alt-text="Clear Address"
        >
          <span>Clear Address</span>
        </button>
    </div>
  </div>
</div>
<script>
	let addressField1 = document.querySelector('#checkout_shipping_address_address1');
	let addressField2 = document.querySelector('#checkout_shipping_address_address2');
	let pobox_modal = document.querySelector('#pobox_modal')

	if (addressField1 != null && addressField2 != null) {
		addressField2.onchange = function () {
			if (addressField2.value ) {
				if(addressField2.value.toLowerCase().replace(/ /g, '').replace(/[^a-z,0-9]/g, '').replace(/\./g, '').indexOf('pobox') > -1 || addressField2.value.toLowerCase().replace(/[^a-z,0-9]/g, '**').replace(/ /g, '**').replace(/\./g, '**').indexOf('box**') > -1){
					pobox_modal.style.display = 'block';
				}
			}
		};
		addressField1.onchange = function () {
			if (addressField1.value) {
				if(addressField1.value.toLowerCase().replace(/ /g, '').replace(/[^a-z,0-9]/g, '').replace(/\./g, '').indexOf('pobox') > -1){
					pobox_modal.style.display = 'block';
				}
			}
		};

		addressField2.addEventListener('input', function () {
			if (addressField2.value.toLowerCase().replace(/[^a-z,0-9]/g, '').replace(/ /g, '').replace(/\./g, '').indexOf('pobox') > -1 || addressField2.value.toLowerCase().replace(/[^a-z,0-9]/g, '**').replace(/ /g, '**').replace(/\./g, '**').indexOf('box**') > -1) {
				pobox_modal.style.display = 'block'
			}
		})

		addressField1.addEventListener('input', function () {
			if (addressField1.value && addressField1.value.toLowerCase().replace(/[^a-z,0-9]/g, '').replace(/ /g, '').replace(/\./g, '').indexOf('pobox') > -1) {
				pobox_modal.style.display = 'block'
			}
		})
	}

  setTimeout(function () {
    var address_fields = [
        document.querySelector('#checkout_shipping_address_address1'),
        document.querySelector('#checkout_shipping_address_address2')
    ]
    var pobox_modal = document.querySelector('#pobox_modal')
    address_fields.forEach(function(address_field , i) {
      if(address_field) {
		if (i==1 && address_field.value){
			if (address_field.value.toLowerCase().replace(/[^a-z,0-9]/g, '**').replace(/ /g, '**').replace(/\./g, '**').indexOf('box**') > -1 || address_field.value && address_field.value.toLowerCase().replace(/[^a-z,0-9]/g, '').replace(/ /g, '').replace(/\./g, '').indexOf('pobox') > -1) {
				pobox_modal.style.display = 'block'
			}
		}

		if (i==0){
			if (address_field.value && address_field.value.toLowerCase().replace(/[^a-z,0-9]/g, '').replace(/ /g, '').replace(/\./g, '').indexOf('pobox') > -1) {
				pobox_modal.style.display = 'block'
			}
		}
      }
    })
  }, 1000)
</script>
