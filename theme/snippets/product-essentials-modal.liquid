<div id="" class="product-essential-modal no-scrollbar fixed hidden z-50 inset-0 bg-gray-900 bg-opacity-60 overflow-y-auto h-full w-full px-[1px] lg:px-4" onclick="if(event.target.classList.contains('product-essential-modal')){this.style.display = 'none';document.querySelector('body').classList.remove('overflow-hidden');}">
    <div class="relative my-[0px] lg:my-[0px] mx-auto shadow-xl bg-transparent max-w-[800px] flex flex-row-reverse" x-data>
        <div class="flex justify-end lg:sticky top-0 my-[10px] h-[48px] w-[48px] fixed">
            <button type="button" aria-label="close-modal" onclick="this.closest('.product-essential-modal').scrollTo(0,0);this.closest('.product-essential-modal').style.display = 'none';document.querySelector('body').classList.remove('overflow-hidden');"
                class="product-essentials__modal-close text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-full text-sm p-1.5 ml-auto inline-flex items-center h-8 outline-1">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
        <div class="pt-0 text-center flex flex-col">
          <template x-for="(media, index) in $store.products[window.document.location.pathname.split('/').at(-1)].media">
          {% if hide_model_image_check and image_hide_alt_txt != blank %} 
              <template x-if="media.media_type == 'image' && ( media.alt !== null && !('{{image_hide_alt_txt}}'.split(',').some(value => media.alt.includes(value))) || media.alt === null)">                
                    <img
                    class="w-full h-full object-center object-contain my-[10px] bg-white"
                    onclick=""
                    draggable="false"
                    :src="image.format(media.preview_image.src, { width: 750 })" 
                    :alt="media.alt"
                    loading="eager" 
                    :data-media-id="media.id"
                >  
              </template>
              {% else  %}
                <template x-if="media.media_type == 'image' ">
                    
                        <img
                        class="w-full h-full object-center object-contain my-[10px] bg-white"
                        onclick=""
                        draggable="false"
                        :src="image.format(media.preview_image.src, { width: 750 })" 
                        :alt="media.alt"
                        loading="eager" 
                        :data-media-id="media.id"
                    >  
                </template>
            {% endif %}
          </template>
        </div>
    </div>
</div>
