<div id="uav-popup">
	<div class="address_verification">
		<svg xmlns="http://www.w3.org/2000/svg" width="129.814" height="30" viewBox="0 0 607.1 125.02" class="address-logo"><defs><style>.cls-1{fill:#001630;}</style></defs><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><rect class="cls-1" x="253.15" y="2.25" width="29.3" height="120.19"/><path class="cls-1" d="M183.3,0c-35.61,0-60.4,25.84-60.4,62.5S147.69,125,183.3,125c35.9,0,61-25.84,61-62.49S219.2,0,183.3,0Zm0,97.2c-18,0-30.8-14.27-30.8-34.55s12.77-34.86,30.8-34.86c18.47,0,31.39,14.43,31.39,34.86S201.77,97.2,183.3,97.2Z"/><path class="cls-1" d="M323,87.89v-53H293.72V95.7c0,18.33,9.76,28.84,25.09,28.84,8.56,0,15.32-3.75,17.87-9.61L345,95.7c-4.06,2.85-8.27,4.35-11.87,4.35C328.27,100.05,323,98,323,87.89Z"/><path class="cls-1" d="M536.94,44.5A33.53,33.53,0,0,0,512.61,34c-21.19,0-38.46,19.83-38.46,45.22s15.92,45.37,35.3,45.37c7.36,0,13.52-3.75,16.23-9.61l8.86-19.23a26.57,26.57,0,0,1-13.37,4.35c-9.77,0-17.43-9.61-17.43-21.93,0-10.67,7.21-18.93,16.38-18.93s16.82,8.41,16.82,18v45.22h29.3V34.85h-29.3Z"/><rect class="cls-1" x="577.81" y="34.85" width="29.3" height="87.59"/><rect class="cls-1" x="577.81" y="2.25" width="29.3" height="24.49"/><polygon class="cls-1" points="486.31 2.25 452.21 2.25 417.51 59.64 417.51 2.25 388.21 2.25 388.21 122.44 417.51 122.44 417.51 65.5 446.65 122.44 480.15 122.44 446.95 61.59 486.31 2.25"/><rect class="cls-1" x="347.35" y="34.85" width="29.3" height="87.59"/><path class="cls-1" d="M46,108.9c-18.42,0-32.24-21.43-24.44-45.17C26.66,49.38,35.87,35.39,39.59,29c2.83-4.78,1.24-7.09,1.24-10.47,0-2.83,1.77-4.07,4.6-4.07h8.15c3.9,0,4.43,3,3,5.13-2.31,3-6.56,3.73-9.57,8.69C43.13,34.84,37.82,48,35.69,60c-2.83,15,3.19,28.69,13.64,29.58,11.69.89,17.36-11.52,17.36-25,0-6.55-2.13-14.35-4.07-19.49-.18-.35,0-.52.17-.35a35.43,35.43,0,0,1,11,11,44.91,44.91,0,0,1,6.73,19.13c0,.17-.18.35-.36.17-1.77-2.3-8-9.56-7.79-5.31C73.42,88.35,64.74,108.9,46,108.9ZM46.85,125C78,125,98.93,100.58,92.39,67.1l-6.2-31.54C82.12,15,74,2.26,52.17,2.26H41.53C19.75,2.26,10.89,15,7,35.56L1.15,67.1C-5.23,101.47,15.67,125,46.85,125Z"/></g></g></svg>


		<h2>Verify Your Shipping Address</h2>

		<p>We weren’t able to verify your shipping address. Please confirm or edit your address. You can also use one of the suggested addresses below.</p>

		<div class="addressList" id="address-container">
			<ul></ul>
			<button type="button" class="product__add-to-bag" data-address-button="original" onclick="doNothing();"><span>KEEP THIS ADDRESS</span></button>
			<button type="button" class="product__add-to-bag" data-address-button="suggested" onclick="updateAddress();" style="display: none"><span>USE THIS ADDRESS</span></button>
		</div>

		<button type="button" data-address-verification-close class="address-verification__close">
			<svg xmlns="http://www.w3.org/2000/svg" width="22.121" height="22.121" viewBox="0 0 22.121 22.121" aria-hidden="true">
				<g id="Group_1" data-name="Group 1" transform="translate(-20.967 -144.821)">
					<path id="Path_1635" data-name="Path 1635" d="M1532.027,145.881l20,20" transform="translate(-1510)" fill="none" stroke="#30231f" stroke-width="3"/>
					<path id="Path_1636" data-name="Path 1636" d="M1552.027,145.881l-20,20" transform="translate(-1510)" fill="none" stroke="#30231f" stroke-width="3"/>
				</g>
			</svg>

			<span class="visually-hidden">Close</span>
		</button>
	</div>
</div>

<div id="uav-popup-loading">
	<div class="add_verify">
		<p><span class="btn--loading"><svg class="icon-svg icon-svg--size-18 btn__spinner icon-svg--spinner-button" aria-hidden="true" focusable="false"> <use xlink:href="#spinner-button"></use> </svg></span> Verifying Address …</p>
	</div>
</div>
<script>
{% if settings.address_validation_timeout != blank %}
	window.addressValidation_timeout = {{settings.address_validation_timeout}};
{% endif %}
let uvaCheckFlag = false;
$(document).ready(function() {
	if(Shopify.Checkout.step == 'contact_information'){

	$('body').on('click', '[data-address-item]', function() {
		$(this).addClass('selected').siblings().removeClass('selected')

		if ($(this).data('address-item') === 'suggested') {
			$('[data-address-button="suggested"]').show()
			$('[data-address-button="original"]').hide()
		} else {
			$('[data-address-button="suggested"]').hide()
			$('[data-address-button="original"]').show()
		}
	})

	$('body').on('click', '[data-address-verification-close]', function() {
		$('body').css("overflow","auto");
		$("#continue_button").removeClass("btn--loading");
		$('#uav-popup').hide();
	})

	$(document).on("click","#continue_button",function( event ) {
		$("#continue_button").addClass("btn--loading");
		let checkAddressFields = true;
		if($.trim($('#checkout_shipping_address_last_name').val()) == '' || $.trim($('#checkout_shipping_address_address1').val()) == '' || $.trim($('#checkout_shipping_address_city').val()) == '' || $.trim($('#checkout_shipping_address_zip').val()) == '' || $.trim($('[id*=checkout_email]').val()) == '' || $.trim($('#checkout_shipping_address_province').val()) == '' || $('#checkout_shipping_address_province').val() == null || $('#checkout_shipping_address_country').val() == null ){
			checkAddressFields = false;
		}

		{% if customer %} 
		
		{% else %}
		let cartId = getCookie('cart');
		var identify = {};		
		var customerEmail = $.trim($('[id*=checkout_email]').val());
		var customerPhone = $.trim(formatPhone($('#checkout_shipping_address_phone').val()));

		if(customerEmail){
			if (cartId) {
				identify['cart_id'] = cartId;
			}	
			
			identify['email_id'] = customerEmail;

			if (customerPhone) {
				identify['phone_id'] = customerPhone;
			}
			// just for testing
			//identify['custom'] = "information....";

      {%- comment -%}
			exponea.identify(
				identify
			);
      {%- endcomment -%}
		}
			 
		
		{% endif %}

		if(!uvaCheckFlag && checkAddressFields) {
			event.preventDefault();

			let maxResponseAddress = 1;
			let firstName = $('#checkout_shipping_address_first_name').val();
			let lastName = $('#checkout_shipping_address_last_name').val();
			let fullName = firstName+' '+lastName;
			fullName = fullName.trim();
			let shippingAddressOne = $('#checkout_shipping_address_address1').val().toUpperCase();
			let shippingAddressTwo = $('#checkout_shipping_address_address2').val().toUpperCase();
			let shippingAddressCity = $('#checkout_shipping_address_city').val().toUpperCase();
			let shippingAddressCountry = $('#checkout_shipping_address_country>[selected=selected]').attr('data-code');
			let shippingAddressProvince = $('#checkout_shipping_address_province option:selected').text();
			let shippingAddressProvinceCode = $('#checkout_shipping_address_province').val();
			let shippingAddressZip = $('#checkout_shipping_address_zip').val().toUpperCase();

			let fullShippingAddress = fullName+'<br>'+shippingAddressOne+'<br>'+shippingAddressCity+', '+shippingAddressProvinceCode+' '+shippingAddressZip;
			if(shippingAddressTwo != '' && shippingAddressTwo != null){
				fullShippingAddress = fullName+'<br>'+shippingAddressOne+'<br>'+shippingAddressTwo+'<br>'+shippingAddressCity+', '+shippingAddressProvinceCode+' '+shippingAddressZip;
			}
			let myKeyVals = {'maxResponseAddress':maxResponseAddress,'fullName':fullName,'shippingAddressOne':shippingAddressOne,'shippingAddressTwo':shippingAddressTwo,'shippingAddressCity':shippingAddressCity,'shippingAddressCountry':shippingAddressCountry,'shippingAddressProvince':shippingAddressProvince,'shippingAddressProvinceCode':shippingAddressProvinceCode,'shippingAddressZip':shippingAddressZip};
			let originalAddressTemplate = '<li class="selected" data-address-item="original"><div class="radio-icon"></div><h4>Original Address<span class="popup-edit-address" onclick="removeSuggestions();">Edit Address</span></h4><p>'+fullShippingAddress+'</p></li>';
			$('#address-container ul').replaceWith('<ul>'+originalAddressTemplate+'</ul>');
			var suggestedAddressTemplate = '';
			//console.log(window.addressValidation_timeout);
			
			$.ajax({
				type: "POST",
				dataType: "json",
				url: "https://wixy8sprqg.execute-api.us-west-2.amazonaws.com/default/Fedex_Address_Validation",
				data: JSON.stringify(myKeyVals),
				timeout: window.addressValidation_timeout,
				success: function(resultData) {
					$.each(resultData, function(i, itemObject) {
						let shippingCandidateAddressOne = '';
						let shippingCandidateAddressTwo = '';
						let addressLine = [];
						let postcodePrimaryLow = '';
						let numberOfResults = 10;
						if(itemObject.Candidate === undefined){
							$('body').css("overflow","visible");
							uvaCheckFlag = true;
							$("#continue_button").click();
						} else {
							let suggestedAddressText = '<h4>Suggested Address</h4>';
							$.each(itemObject.Candidate, function(i, candidateObject) {
								numberOfResults = Object.keys(itemObject.Candidate).length;
								if(candidateObject.AddressKeyFormat !== undefined){
									shippingCandidateAddressOne = candidateObject.AddressKeyFormat.PoliticalDivision1;
									shippingCandidateAddressTwo = candidateObject.AddressKeyFormat.PoliticalDivision2;

									if(typeof(candidateObject.AddressKeyFormat.AddressLine) != 'string'){
										addressLine = candidateObject.AddressKeyFormat.AddressLine.join("---");
									} else {
										addressLine = candidateObject.AddressKeyFormat.AddressLine;
									}
									postcodePrimaryLow = candidateObject.AddressKeyFormat.PostcodePrimaryLow
								} else {
									shippingCandidateAddressOne = candidateObject.PoliticalDivision1;
									shippingCandidateAddressTwo = candidateObject.PoliticalDivision2;
									if(typeof(candidateObject.AddressLine) != 'string'){
										addressLine = candidateObject.AddressLine.join("---");
									} else {
										addressLine = candidateObject.AddressLine;
									}
									postcodePrimaryLow = candidateObject.PostcodePrimaryLow
								}

								suggestedAddressTemplate = '<li data-address-item="suggested" data-address="'+shippingCandidateAddressOne+'|'+shippingCandidateAddressTwo+'|'+addressLine+'|'+postcodePrimaryLow+'"><div class="radio-icon"></div>'+suggestedAddressText+'<p>'+fullName+'<br>'+addressLine.replace("---", "<br>")+'<br>'+shippingCandidateAddressTwo+', '+shippingCandidateAddressOne+' '+postcodePrimaryLow+'</p><!--<button type="button" class="product__add-to-bag" onclick="changeAddress(\''+shippingCandidateAddressOne+'\',\''+shippingCandidateAddressTwo+'\',\''+addressLine+'\',\''+postcodePrimaryLow+'\');" ><span>USE THIS ADDRESS</span></button>--></li>';
								$('#address-container ul').append(suggestedAddressTemplate);
								suggestedAddressText = '';
							});
							if(shippingCandidateAddressOne.toLowerCase() == shippingAddressProvinceCode.toLowerCase() && shippingCandidateAddressTwo.toLowerCase() == shippingAddressCity.toLowerCase() && addressLine.replace("---", " ").toLowerCase() == $.trim(shippingAddressOne+' '+shippingAddressTwo).toLowerCase() && postcodePrimaryLow.toLowerCase() == shippingAddressZip.toLowerCase() && numberOfResults == 1){
								$('body').css("overflow","visible");
								uvaCheckFlag = true;
								$("#continue_button").click();
							} else {
								$('body').css("overflow","hidden");
								$("#continue_button").addClass("btn--loading");
								$('#uav-popup').css('display', 'flex');
							}

						}
					});
				},
				error: function(xhr, textStatus, errorThrown) {
					//if (textStatus == 'timeout') {
						//console.log("Error : Timeout for this call!");
						$('body').css("overflow","visible");
						uvaCheckFlag = true;
						$("#continue_button").click();
					//}
				}
			});

		} else { $("#continue_button").removeClass("btn--loading"); }
  }});
	}
});

function getCookie(t){var n,e,r=t+"=",i=document.cookie.split(";"),o=i.length;for(n=0;n<o;n+=1){for(e=i[n];" "===e.charAt(0);)e=e.substring(1);if(-1!==e.indexOf(r))return e.substring(r.length,e.length)}return!1}

function formatPhone(phone) {
    if (!phone) {
      return null;
    } 

    phone = phone.split(' ').join('');
    phone = phone.split('(').join('');
    phone = phone.split(')').join('');
    phone = phone.split('-').join('');

    if (phone.length == 10) {
      return '001' + phone;
    } else if (phone.startsWith('+1')) {
      return phone.replace('+1', '001');
    } else if (phone.startsWith('001')) {
      return phone;
    } else if (phone.startsWith('+')) {
      return phone.replace('+', '00');
    } else {
      return null;
    }
  }

function capitalize(s){
	return s;
};

function updateAddress() {
	const $selected = $('[data-address-item].selected')
	const address = $selected.data('address').split('|')
	const addressOne = address[0]
	const addressTwo = address[1]
	let addressLine = address[2]
	const postcodePrimaryLow = address[3]

	uvaCheckFlag = true;
	addressLine = addressLine.split('---');

	$('#checkout_shipping_address_address1').val(capitalize(addressLine[0]));

	if(addressLine[1] != undefined){
		$('#checkout_shipping_address_address2').val(capitalize(addressLine[1]));
	}

	$('#checkout_shipping_address_city').val(capitalize(addressTwo));
	$('#checkout_shipping_address_zip').val(postcodePrimaryLow);
	$('#checkout_shipping_address_province').val(addressOne)
	$('#uav-popup').hide();
	$("#continue_button").removeClass("btn--loading");
	$('body').css("overflow","visible");
	$("#continue_button").click();
}

function doNothing(){
	uvaCheckFlag = true;
	$('#uav-popup').hide();
	$("#continue_button").removeClass("btn--loading");
	$('body').css("overflow","visible");
	$("#continue_button").click();
}

function removeSuggestions(){
	uvaCheckFlag = false;
	$('#uav-popup').hide();
	$("#continue_button").removeClass("btn--loading");
	$('body').css("overflow","visible");
}
</script>
<style>
#uav-popup,
#uav-popup-loading {
	background: rgba(0,0,0,0.7);
	display: none;
	height: 100%;
	position: fixed;
	width: 100%;
	z-index: 999;
}

#uav-popup {
	align-items: center;
	justify-content: center;
}

.address-logo {
	display: block;
	margin-bottom: 35px;
}

.address-verification__close {
	position: absolute;
	right: 40px;
	top: 32px;
}

.address_verification {
	background: #FCF9F3;
	margin: 0 auto;
	max-height: 100%;
	max-width: 415px;
	overflow-y: auto;
	padding: 30px 40px 42px;
	position: relative;
}

@media (max-width: 992px) {
	.address_verification {
		top: 0;
		padding: 36px 20px 24px;
	}
}

.address_verification h2 {
	color: #231F20;
	font-family: "GTA-Medium", Arial, sans-serif;
	font-size: 18px;
	line-height: 22px;
	margin-bottom: 2px;
}

.address_verification p {
	color: #231F20;
	font-family: "GTA-Regular", Arial, sans-serif;
	font-size: 12px;
	line-height: 16px;
}

.address_verification > p {
	margin-bottom: 24px;
}

.addressList .radio-icon {
	border: 1px solid #F5E9D8;
	border-radius: 50%;
	height: 18px;
	left: 15px;
	position: absolute;
	top: 23px;
	width: 18px;
}

.addressList .radio-icon:after {
	background-color: #fff;
	content: "";
	height: 4px;
	left: 50%;
	opacity: 0;
	position: absolute;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 4px;
}

.addressList .selected .radio-icon {
	border-color: #ff4438;
	background-color: #ff4438;
}

.addressList .selected .radio-icon:after {
	opacity: 1;
}

.addressList ul {
	background-color: #fff;
	border: 1px solid #F5E9D8;
	border-radius: 3px;
}

.addressList ul li {
	border-bottom: 1px solid #F5E9D8;
	cursor: pointer;
	padding: 23px 15px 16px 48px;
	position: relative;
}

.addressList ul li h4 {
	color: #797979;
	font-family: "GTA-Regular", Arial, sans-serif;
	font-size: 11px;
	line-height: 13px;
	margin: 0 0 7px;
}

.addressList .address_innerList p {
	color: #231F20;
	font-family: "GTA-Regular", Arial, sans-serif;
	font-size: 14px;
	line-height: 17px;
	margin: 0 0 16px;
}

.addressList button.product__add-to-bag {
	align-items: center;
	background-color: #ff4438;
    border: 1px solid #ff4438;
	color: #fff;
	display: flex;
	justify-content: center;
	font-family: "GTA-Bold", Arial, sans-serif;
	font-size: 14px;
	height: 55px;
	letter-spacing: 0.04em;
	line-height: 18px;
	margin: 24px auto 0;
	max-width: 300px;
	padding: 0 25px;
	text-transform: uppercase;
	width: 100%;
}

.addressList ul li:last-child {
	border-bottom: none;
}

.add_verify {
	max-width: 268px;
	background:#a32e0a;
	margin: 0px auto;
	padding: 31px 19px;
	position: relative;
	top: calc(50% - 53px);
}

.add_verify p{
	font-family: "GTA-Regular", Arial, sans-serif;
	font-weight:400;
	font-size: 18px;
	line-height: 21px;
	color: #fff;
	display: flex;
	align-items: center;
}

.add_verify p span{
	margin-right:21px;
}

.add_verify p span .btn__spinner{
	position:static;
	margin:0;
	opacity:1;
	-webkit-transform: translateX(0);
	transform: translateX(0);
	-webkit-transition: -webkit-transform 400ms ease-out;
	transition: -webkit-transform 400ms ease-out;
	transition: transform 400ms ease-out;
	transition: transform 400ms ease-out, -webkit-transform 400ms ease-out;
}

.popup-edit-address {
	color: #ff4438;
	cursor: pointer;
	float: right;
	font-family: "GTA-Regular", Arial, sans-serif;
	font-size: 11px;
	line-height: 13px;
	text-decoration: none;
}
</style>
