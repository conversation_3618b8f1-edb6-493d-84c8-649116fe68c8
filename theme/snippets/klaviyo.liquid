<script>    
{% if customer %}
    {% if customer.email %}
        klaviyo.identify({"$email" : "{{customer.email}}"})
    {% elsif customer.phone %}
        klaviyo.identify({"$phone_number" : "{{customer.phone}}"})
    {% endif %}

    // window.addEventListener('load', function() {
        
        let customer_email = '{{customer.email}}';
        let data = {};
        data.store_domain = window.store.domain;
        data.action = 'get_profile';
        if (customer_email) data.email = customer_email;
        {% if settings.enable_logs -%}console.log("data... ", data){%- endif %}
    
        fetch(`https://363wd7s9c1.execute-api.us-west-2.amazonaws.com/default/Update_customer_data_klaviyo`, {
            method:'POST',
            headers: {
            "Content-Type": "application/json"
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(responseData => {
            // Check if the response has a status code of 200
            if (responseData.statusCode === 200) {
                // Extract the body value
                const bodyValue = responseData.body;
                    {% if settings.enable_logs -%}console.log("bodyValue", bodyValue){% endif %}              

                    let customer_birthdate = Util.format.date(bodyValue, 'y/m/d')
                    if (bodyValue) {
                        window.customer.birthday = true;
                        window.customer.birthday_date = Util.format.date(bodyValue, 'y/m/d');
                        {% if settings.enable_logs -%}
                            console.log("formatDate(bodyValue)", convertAndValidateDate(bodyValue))
                            console.log(Util.format.date(bodyValue, 'y/m/d'));
                        {%- endif %} 
                        const customerString = localStorage.getItem('customer');
                        let customerObject = JSON.parse(customerString);
                        customerObject.birthday_date = Util.format.date(bodyValue, 'y/m/d');
                        {% if settings.enable_logs -%}console.log("localStorage.getItem('customer')",customerObject);{%- endif %}
                        {% if settings.enable_logs -%}console.log("window.customer.birthday_date",window.customer.birthday_date);{%- endif %}
                        let birthdate_added = {
                            email: window.customer.account.email,
                            birthdate: window.customer.birthday_date,
                            timestamp: (new Date()).toISOString()
                        }
                        if(customer_birthdate != window.customer.birthday_date){
                            _learnq.push(['track', 'Birthdate Added', birthdate_added]);
                            customer_birthdate = window.customer.birthday_date
                        }
                    }else {
                        window.customer.birthday = false;
                        window.customer.birthday_date = '';
                    }
                
            } else {
                // Handle non-200 responses
                console.error('Request failed with status:', responseData.statusCode);
            }
        })
        .catch(error => {
            // Handle errors that occur during the fetch
            console.error('Fetch error:', error);
        });

    // })

    window.addEventListener('Customer:profileEdit', e => {
        // const { customer } = e; 
    })
    
    
    window.addEventListener('Customer:profileUpdate', e => {
        {% if settings.enable_logs -%}console.log('Customer:profileUpdate klaviyo.', e);{%- endif %}
        const { customer } = e.detail; 
        let shopcustomer_email = '{{customer.email}}';
        {% if settings.enable_logs -%}console.log('Customer Info :', customer.profiles[0].birthday);{%- endif %}
        // klaviyo.push(['identify', {'email': customer.profiles[0].birthday}, (result) => console.log('Identify result ' + result)])    
        let data = {};
        data.store_domain = window.store.domain;
        data.action = 'profile_update';
        if (shopcustomer_email) data.email = shopcustomer_email;                
        if (customer.profiles[0].birthday) data.birthday = customer.profiles[0].birthday;
        {% if settings.enable_logs -%}console.log("data...", data){%- endif %}
        
      
        fetch(`https://363wd7s9c1.execute-api.us-west-2.amazonaws.com/default/Update_customer_data_klaviyo`, {
            method:'POST',
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(data)
        }).then(response => {
            // Check if the response status code is OK (200-299)
            if (!response.ok) {
                // Handle HTTP errors (response status outside 200-299 range)
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            // Parse JSON if response is OK
            return response.json();
        })
        .then(responseData => {
            {% if settings.enable_logs -%}console.log('responseData', responseData);{%- endif %}

            // Extract the body value from the responseData
            const bodyValue = responseData.body;
            {% if settings.enable_logs -%}console.log('bodyValue', bodyValue);{%- endif %}
            let customer_birthdate = window.customer.birthday_date
            if (window.customer) {
                if (bodyValue) {
                    window.customer.birthday = true;
                    profile_birthdate = bodyValue['update_response']['data']['attributes']['properties']['birthdate']
                    window.customer.birthday_date = Util.format.date(profile_birthdate, 'm/d/y');
                    {% if settings.enable_logs -%}
                        console.log("formatDate(bodyValue)", convertAndValidateDate(bodyValue));
                        console.log("profile_birthdate ", profile_birthdate);
                    {%- endif %}
                    let birthdate_added = {
                        email: window.customer.account.email,
                        birthdate: window.customer.birthday_date,
                        timestamp: (new Date()).toISOString()
                    }
                    if(customer_birthdate != window.customer.birthday_date){
                        _learnq.push(['track', 'Birthdate Added', birthdate_added]);
                        customer_birthdate = window.customer.birthday_date
                    }
                } else {
                    window.customer.birthday = false;
                }
            }

        })

    });
    
{% else %}
    window.addEventListener('load', function() {
        var _learnq = window._learnq || [];
        const klaviyo_email = localStorage.getItem('klaviyo_email');
        {% if settings.enable_logs -%}console.log("localStorage", klaviyo_email){%- endif %}
        _learnq.push(['identify', {
            '$email': klaviyo_email 
        }]);
        setTimeout(function() {
            {% if settings.enable_logs -%}console.log("localStorage", klaviyo_email, _learnq){%- endif %}
            localStorage.removeItem("klaviyo_email");
        }, 3000);
        

    })
{% endif %}	
{% if product %}
    window.addEventListener('load', function() {
        var _learnq = window._learnq || [];

        function addedToCart(item) {
            fetch(`${window.location.origin}/cart.js`)
            .then(res => res.json())
            .then(data => {
                var cart = {
                    total_price: data.total_price / 100,
                    $value: data.total_price / 100,
                    total_discount: data.total_discount,
                    original_total_price: data.original_total_price / 100,
                    items: data.items
                };

                if (item) {
                    cart = Object.assign(cart, item);
                }
                {% if settings.enable_logs -%}console.log('added to', cart);{%- endif %}

                _learnq.push(['track', 'Added to Cart', cart]);
            });
        }

        function handleAddToCartButton(button) {
            button.addEventListener("click", function(e) {
                var itemAddedListener = function(edata) {
                    {% if settings.enable_logs -%}console.log('edata', edata);{%- endif %}
                    var item = {
                        name: edata.detail.product_title,
                        product_ID: edata.detail.id,
                        url: `https://{{ shop.domain }}/${edata.detail.url}`,
                        image_URL: edata.detail.image,
                        brand: '{{ shop.name }}',
                        Price: `$${edata.detail.price / 100}`
                    };
                    addedToCart(item); 

                    window.removeEventListener('Cart:itemAdded', itemAddedListener);
                };

                window.addEventListener('Cart:itemAdded', itemAddedListener);
            });
        }

        var atcButtons = document.querySelectorAll('.button.product-item__quick-add, form[action*="/cart/add"] button[type="submit"]');
        atcButtons.forEach(handleAddToCartButton);

        var pdpAtcButton = document.querySelector('button.button.button--primary.button--large .button__text')?.closest('button');
        if (pdpAtcButton) {
            handleAddToCartButton(pdpAtcButton);
        }

        var additionalButtons = [
            document.querySelector('.product-form__actions button.button.button--primary'),
            document.querySelector('.product-gate__actions button.button.button--primary')
        ];
        
        additionalButtons.forEach(button => {
            if (button) {
                handleAddToCartButton(button);
            }
        });

    });

    window.addEventListener('Wishlist:add', e => {
        let items = e.detail.list.items
        items = JSON.stringify(items)
        items = items.replace(/<\/?[^>]+(>|$)/g, "");
        items = JSON.parse(items);
        let wishlist = {
            "action": "adds to wishlist",
            "product_id": e.detail.item.product?.id,
            "handle": e.detail.item.product?.handle,
            "variant id": e.detail.item.variant?.id,
            "title": e.detail.item.product?.title,
            "url": '{{ canonical_url | replace: ".com/", ".com"}}',
            "items": items,
            "price": `${e.detail.item.variant?.price/100}`,
            "item count": e.detail.list.items.length
        }
        _learnq.push(['track', 'Favorited Item - Web', wishlist]);
    })

    window.addEventListener('Wishlist:remove', e => {
        let items = e.detail.list.items
        items = JSON.stringify(items)
        items = items.replace(/<\/?[^>]+(>|$)/g, "");
        items = JSON.parse(items);
        let wishlist = {
            "action": "removes from wishlist",
            "product_id": e.detail.items[0].product?.id,
            "handle": e.detail.items[0].product?.handle,
            "variant id": e.detail.items[0].variant?.id,
            "title": e.detail.items[0].product?.title,
            "url": '{{ canonical_url | replace: ".com/", ".com"}}',
            "items": items,
            "price": `${e.detail.items[0].variant?.price/100}`,
            "item count": e.detail.list.items.length
        }
        _learnq.push(['track', 'Favorited Item - Web', wishlist]);
    })
{% endif %}
{% unless product %}
    var _learnq = window._learnq || [];
    var klAjax = true;
    // var atcButtons = document.querySelectorAll(".field__button.quick-add__field-button");
    {% if settings.enable_logs -%}
        console.log("atcButtonss.length", atcButtons.length);
        console.log('Add to cart button clicked');
    {%- endif %}
    if (klAjax) {
        window.addEventListener('Cart:itemAdded', function(e) {
            {% if settings.enable_logs -%}
                console.log('Cart:itemAdded',e); 
                console.log('window.cart', window.cart);
            {%- endif %}
            var cart = {
                total_price: window.cart.total_price / 100,
                $value: window.cart.total_price / 100,
                total_discount: window.cart.total_discount,
                original_total_price: window.cart.original_total_price / 100,
                items: window.cart.items,
                name: e.detail.product_title,
                product_ID: e.detail.product_id,
                compare_at_price: e.detail.properties._compare_at_price,
                url: '{{ canonical_url | replace: ".com/", ".com"}}'+e.detail.url,
                image_URL: e.detail.featured_image.url,
                brand: '{{ shop.name }}',
                Price: `$${e.detail.price / 100}`
            }
            {% if settings.enable_logs -%}console.log('Tracking added to cart event...', cart);{%- endif %}
            // Adjust the event data as needed
            _learnq.push(['track', 'Added to Cart', cart]);
            klAjax = false;
        });
        window.addEventListener('Collection:init', e => {
            klaviyo.track("Viewed Collection", {
                title: collection.title,
                url: window.location.href,
                id: collection.id
            });
        })
    }
    {% endunless %}
</script>