{% if settings.text_text != blank or text_text != blank or settings.text_liquid != blank or settings.text_attr_x_text != blank %}
{% assign element = text_element | default: settings.text_element | default: 'p' %}
<{{ element }} {% render 'attributes' _settings:settings, prefix:'text_attr' %} {{ text_attributes }} class="{% render 'class-settings' prefix:'text_class' settings:settings %} {{ text_classes }} my-0"  style="{% render 'style-settings' prefix:'text_style' settings:settings %}">{{ text_text | default: settings.text_text }}{{ settings.text_liquid }}</{{ element}}>
{% endif %}
