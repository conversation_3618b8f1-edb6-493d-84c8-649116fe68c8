<script type="text/javascript">
  // Configures the SDK. Note the settings below for isDevelopmentMode
  // and logLevel.
        
  function m_getCookie(cname) {
    var name = cname + '=';
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
      var c = ca[i];
      while (c.charAt(0) == ' ') {
        c = c.substring(1);
      }
      if (c.indexOf(name) == 0) {
        return c.substring(name.length, c.length);
      }
    }
    return undefined;
  }
      
  {% if settings.enable_logs %}console.log("new settings ",'{{ settings.is_development_mode }}', '{{ settings.logLevel }}', '{{ settings.logout_function }}'){% endif %}
  var is_development_mode = {{ settings.is_development_mode }};
  var m_exponea_etc = m_getCookie('__exponea_etc__');
  {% if settings.enable_logs %}console.log( "m_exponea_etc", m_exponea_etc){% endif %}
  var m_domain_name = '{{ settings.mparticle_domain_name }}';
  var m_store_name = '';

  window.mParticle = {
      config: {
          logLevel: '{{ settings.logLevel }}',
          isDevelopmentMode: {{ settings.is_development_mode }},
          {{ settings.mparticle_config }} 
          identifyRequest: {
              userIdentities: {
                // leave empty
              },
          },
            onCreateBatch: (batch) => {
            batch.events = batch.events.map((event) => {
                if (event.event_type === "session_start") {
                    const modifiedEvent = Object.assign({}, event);
                    // Function to get UTM parameters
                    const getUtmParameter = (param) => {
                        const urlParams = new URLSearchParams(window.location.search);
                        return urlParams.get(param);
                    };
                    // Create custom attributes with only existing UTM values
                    const customAttributes = {};
                    const utmParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term'];
                    utmParams.forEach(param => {
                        const value = getUtmParameter(param);
                        if (value) {
                            customAttributes[param] = value; // Only add if value exists
                        }
                    });
                    if(new URLSearchParams(window.location.search).size){
                      // Add additional attributes
                      customAttributes.location = window.location.href;
                      customAttributes.referrer = document.referrer || null;
                    }
                    // Set custom attributes in the modified event
                    modifiedEvent.data.custom_attributes = Object.assign({}, event.data.custom_attributes, customAttributes);
                    
                    return modifiedEvent;
                }
                return event;
            });
            return batch;    
        },
          identityCallback: function(result) {
            /* add callback login call */
            var identityRequest2 = {
              userIdentities: {
                //other3: String(m_cart_id),
                other4: String(m_exponea_etc)
              }
            }
            var identityCallback2 = function(result) {
            // leave empty
            
            }
            {% if settings.enable_logs %}console.log("result. ",result.body.matched_identities.other4);{% endif %}
            {% if settings.enable_logs %}console.log("mParticle.Identity.getCurrentUser().isLoggedIn() ", mParticle.Identity.getCurrentUser().isLoggedIn());{% endif %}
            
            if(m_exponea_etc != undefined){
              if(!result.body.matched_identities.other4){
                  mParticle.Identity.modify(identityRequest2, identityCallback2);         //perform login after initialization
              }
            }
        
            {% if settings.enable_logs %}console.log("identityCallback function");{% endif %}
            
            
          },
          
      },
  };
    
  (
        function(e){window.mParticle=window.mParticle||{};window.mParticle.EventType={Unknown:0,Navigation:1,Location:2,Search:3,Transaction:4,UserContent:5,UserPreference:6,Social:7,Other:8};window.mParticle.eCommerce={Cart:{}};window.mParticle.Identity={};window.mParticle.config=window.mParticle.config||{};window.mParticle.config.rq=[];window.mParticle.config.snippetVersion=2.3;window.mParticle.ready=function(e){window.mParticle.config.rq.push(e)};var i=["endSession","logError","logBaseEvent","logEvent","logForm","logLink","logPageView","setSessionAttribute","setAppName","setAppVersion","setOptOut","setPosition","startNewSession","startTrackingLocation","stopTrackingLocation"];var n=["setCurrencyCode","logCheckout"];var t=["identify","login","logout","modify"];i.forEach(function(e){window.mParticle[e]=o(e)});n.forEach(function(e){window.mParticle.eCommerce[e]=o(e,"eCommerce")});t.forEach(function(e){window.mParticle.Identity[e]=o(e,"Identity")});function o(i,n){return function(){if(n){i=n+"."+i}var e=Array.prototype.slice.call(arguments);e.unshift(i);window.mParticle.config.rq.push(e)}}var r,c,a=window.mParticle.config,s=a.isDevelopmentMode?1:0,l="?env="+s,w=window.mParticle.config.dataPlan;if(w){r=w.planId;c=w.planVersion;if(r){if(c&&(c<1||c>1e3)){c=null}l+="&plan_id="+r+(c?"&plan_version="+c:"")}}var d=window.mParticle.config.versions;var m=[];if(d){Object.keys(d).forEach(function(e){m.push(e+"="+d[e])})}var f=document.createElement("script");f.type="text/javascript";f.async=true;if(m_domain_name){m_store_name = ("https:"==document.location.protocol?"https://mparticle":"http://mparticle")+"."+m_domain_name+"/tags/JS/v2/"+e+"/mparticle.js";}else {m_store_name = ("https:"==document.location.protocol?"https://jssdkcdns":"http://jssdkcdn")+".mparticle.com/js/v2/"+e+"/mparticle.js"+l+"&"+m.join("&");}f.src=m_store_name;var p=document.getElementsByTagName("script")[0];p.parentNode.insertBefore(f,p)}
  )
  // Insert your API key below
  ("{{ settings.key_mparticle }}");
</script>

{% if customer %}
  {% if settings.mparticle_enable %}
    <script>
      function m_getCookie(cname) {
        var name = cname + '=';
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
          var c = ca[i];
          while (c.charAt(0) == ' ') {
            c = c.substring(1);
          }
          if (c.indexOf(name) == 0) {
            return c.substring(name.length, c.length);
          }
        }
        return undefined;
      }

      function formatNumber(input, template) {
        // Remove all non-digit characters from the input
        const digitsOnly = input.toString().replace(/\D/g, '');
        
        // Format the number according to the template
        let formattedNumber = '';
        let digitIndex = 0;
        for (let i = 0; i < template.length; i++) {
          if (template[i] === '#') {
            // Replace '#' with the next digit from the input
            formattedNumber += digitsOnly[digitIndex] || '';
            digitIndex++;
          } else {
            // Keep non-numeric characters as is
            formattedNumber += template[i];
          }
        }
        
        return formattedNumber;
      }


      let emailFieldValue = localStorage.getItem('emailFieldValue');
      let phoneFieldValue = localStorage.getItem('phoneFieldValue');
      let referrerUrl = localStorage.getItem('referrerUrl');
      let customer__id = '{{customer.id}}';
      //debugger;
      if (emailFieldValue && (referrerUrl.includes("/account/login") || referrerUrl.includes("/account/register"))) {
        let cust_id = customer__id;
        let cust_email = emailFieldValue;
        let m_cart_id = m_getCookie('cart');
        let m_exponea_etc = m_getCookie('__exponea_etc__');
        
        var identityRequest = {
          userIdentities: {
            email: String(cust_email),
            customerid: String(cust_id),
            other3: String(m_cart_id),
            other4: String(m_exponea_etc)
          }
        };
        {% if settings.enable_logs %}console.log("identityRequest1",identityRequest);{% endif %}
        if (referrerUrl.includes("/account/register")) {
            identityRequest.userIdentities.mobile_number = String(formatNumber(phoneFieldValue, '##########'));
        }
        {% if settings.enable_logs %}console.log("identityRequest2",identityRequest);{% endif %}
        var identityCallback = function(result) {
          {% if settings.enable_logs %}console.log("result....",result);{% endif %}
          if (result.getUser()) { 
            //  Add/Set User Attributes post-login	
            if (result.body.matched_identities.email) { 			
              result.getUser().setUserAttribute("email", result.body.matched_identities.email)
            }
            if (result.body.matched_identities.customerid) { 	
              result.getUser().setUserAttribute("customerid", result.body.matched_identities.customerid)
            }
            if (result.body.matched_identities.customerid) {
              result.getUser().setUserAttribute("mP_mpid", result.body.mpid)
            }
            if (referrerUrl.includes("/account/register")) {
              result.getUser().setUserAttribute("phone", result.body.matched_identities.mobile_number)
            }
            localStorage.removeItem('emailFieldValue');
            localStorage.removeItem('phoneFieldValue');
            localStorage.removeItem('referrerUrl');
          } 
          
          // Copy attributes from previous user to current user
          result.getUser().setUserAttributes(result.getPreviousUser().getAllUserAttributes());
        
          var previousUser = result.getPreviousUser();
        
          // If the user is anonymous, create and send the alias request
          if (previousUser && Object.keys(previousUser.getUserIdentities().userIdentities).length === 0) {      
            var aliasRequest = mParticle.Identity.createAliasRequest(result.getPreviousUser(), result.getUser());
            {% if settings.enable_logs %}console.log("aliasRequest....",aliasRequest);{% endif %}
            mParticle.Identity.aliasUsers(aliasRequest);
          }
        }
        mParticle.Identity.login(identityRequest, identityCallback);
        
      }

    </script>

    {% if settings.logout_function %}
      <script>  
        document.addEventListener('DOMContentLoaded', function() {
          let cust_id = '{{customer.id}}';
          let cust_email = String('{{customer.email}}');
          const logoutLink = document.querySelector('.sidebar__link[href="/account/logout"]');

          if (logoutLink) {
            logoutLink.addEventListener('click', function(event) {
              // Prevent default link behavior
              // event.preventDefault(); 

              var identityCallback = function(result) { 
                if (result.getUser()) { 
                  // Proceed with logout 
                  // Redirect to the logout link
                  window.location.href = logoutLink.href;
                } 
              };

              // Perform logout operation
              mParticle.Identity.logout({}, identityCallback);
            });
          }
        });
        
      </script>
    {% endif %}

  {% endif %}
{% endif %}
