<!-- Segment Visitors -->
<script type="text/javascript">
  {% if customer %}
    if (!{{ settings.visitor_storage }}Storage.getItem('visitorType')) {
      {{ settings.visitor_storage }}Storage.setItem('visitorType', 'returning');
    }
    document.addEventListener('DOMContentLoaded', () => {
      if (!customer.segmentation.visitorType) {
        Customer.segment('visitorType', 'returning', {reload:true})
      }
    })
  {% else %}
    // Check if the "visitorType" key exists in session storage
    if ({{ settings.visitor_storage }}Storage.getItem('visitorType')) {
      const firstVisitDate = new Date({{ settings.visitor_storage }}Storage.getItem('visitorDate'));

      if (firstVisitDate.getTime() + ({{ settings.visitor_timeout }} * 60 * 60 * 1000) < new Date().getTime()) {
        {{ settings.visitor_storage }}Storage.setItem('visitorType', 'returning');

        document.addEventListener('DOMContentLoaded', () => {
          if (!customer.segmentation.visitorType) {
            Customer.segment('visitorType', 'returning', {reload:true})
          }
        })
      }

    } else {
      // Set the "visitorType" key for the session
      {{ settings.visitor_storage }}Storage.setItem('visitorType', 'new');
      {{ settings.visitor_storage }}Storage.setItem('visitorDate', new Date().toISOString());
    }
  {% endif %}
</script>
<!-- /Segment Visitors -->
