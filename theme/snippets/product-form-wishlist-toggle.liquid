<button 
  class="wishlist-toggle {% render 'class-settings' settings:settings, prefix:'button_class' %}" 
  :onclick="`Wishlist.items.toggle(
    { 
      product:'${(product.variant && product.variant.product) || product.handle}',
      variant:${product.variant ? product.variant.id : false }
    },0,{
      login:{
        title:'{{ 'wishlist.customer_request.login.title' | t }}',
        prompt:'{{ 'wishlist.customer_request.login.prompt' | t }}'
      },
      register:{
        title:'{{ 'wishlist.customer_request.register.title' | t }}',
        prompt:'{{ 'wishlist.customer_request.register.prompt' | t }}'
      },
      quickadd:{
        title:'{{ 'wishlist.quick_add.selection.title' | t }}',
        prompt:'{{ 'wishlist.quick_add.selection.prompt' | t }}',
        success:{
          title:'{{ 'wishlist.quick_add.success.title' | t }}',
          button:{
            text:'{{ 'wishlist.quick_add.success.button_text' | t }}',
            link:'{{ settings.wishlist_url | default:'/account?view=wishlist' }}'
          }
        }
      }
    }
  ); 
  event.preventDefault(); 
  return false;`"
  x-data="{
      product:$store.products['{{ product.handle }}'],
      variant:$store.products['{{ product.handle }}'].variant,
    }"
    x-init="
      $alpine.listen('Product', () => {
        $data.product = $store.products['{{ product.handle }}']
        $data.variant = $store.products['{{ product.handle }}'].variant
      })
    ">
  
  <template x-if="
  !!product.variant &&
  $store.wishlist.lists[0].items.map(i=>i.variant.id).includes(product.variant.id)">
    {% render 'icon' icon:'heart' class:'active' width:20 height:20 strokeWidth:2 %}
  </template>

  <template x-if="
  !!product.variant &&
  !$store.wishlist.lists[0].items.map(i=>i.variant.id).includes(product.variant.id)">
    {% render 'icon' icon:'heart' width:20 height:20 strokeWidth:2 %}
  </template>

  <template x-if="
  !product.variant">
    {% render 'icon' icon:'heart' width:20 height:20 strokeWidth:2 %}
  </template>

</button>

