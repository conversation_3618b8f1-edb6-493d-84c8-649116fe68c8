{% if settings.inclusion_js %}

<div
  x-data='{ show: {{ settings.inclusion_js }} }'
  x-bind:hidden="!show"
  :class="{ 'lg:border-0': !show  }"
>
  <template x-if="show">
  

  {% endif %}
   
<details {{ attributes }} 
  class="mini-form w-full no-close {{ classes | default: settings.classes }}" 
  x-data 
  {% if on_button_click != blank or settings.on_button_click != blank %}
    onclick="this.closest('.mini-form').open=false;{{ on_button_click | default: settings.on_button_click }}"
  {% endif %}
>
  <summary class="flex justify-end summary_form_success">
    <span class="mini-form__trigger button {{ button_style | default: settings.button_style | default: 'button--primary' }} w-full absolute pointer-events-none">
      {% liquid
        if trigger_text != blank
          render 'inline-icon-text' source: trigger_text
        else
          render 'inline-icon-text' source: settings.trigger_text | default: 'Sign up'
        endif
      %}
    </span>

    <div class="mini-form__success">
      {{ success_text | default: settings.success_text }}
    </div>

  </summary>
  <div>
    <form action="{{ form_action | default: settings.form_action }}" method="{{ form_method | default: settings.form_method }}" class="relative w-full" onsubmit="this.closest('.mini-form').classList.add('mini-form--submitted'); this.closest('.mini-form').open=false;{% if settings.prevent_default %}event.preventDefault();{% endif %} {{ on_submit | default: settings.on_submit }}">
      <div class="flex w-full items-stretch">
        <label><input required type="{{ field_type | default: settings.field_type | default: 'email'}}" class="w-full h-full" placeholder="{{ placeholder_text | default: settings.placeholder_text | default: 'Sign up...'}}" name="{{ field_name | default: settings.field_name | default: 'email'}}" error-message="{{ error_message | default: settings.error_message }}" oninput="this.setAttribute('valid', this.validity.valid); {{ on_input | default: settings.on_input }}; " {% if input_mask != blank or settings.input_mask != blank %}x-mask="{{ input_mask | default: settings.input_mask }}"{% endif %} data-footer-newsletter-input="email" aria-label="{% if field_type == 'tel' %}phone-number{% else %}email-address{% endif %}" role="textbox"></label>
        <button class="button {{ button_class | default: settings.button_class | default: 'button--primary' }} newsletter__button flex-shrink-0" role="button" aria-label="{{ submit_text | default: settings.submit_text | default: 'Sign up' | downcase | replace: ' ', '-' }}">{{ submit_text | default: settings.submit_text | default: 'Sign up'}}</button>
      </div>
      {% if settings.activate_email_validation %}
        <div class="newsletter__error">
            {{ settings.error_message }}
        </div>     
      {% endif %}
      <div class="mini-form__info">
        {{ info_text | default: settings.info_text }}
      </div>
    </form>
  </div>
</details>
{% if settings.inclusion_js %}

</template>
</div>
{% endif %}

<!-- <pre>{{ settings | json }}</pre> --> 
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
{% if settings.activate_email_validation %}
<script>
  $('.newsletter__button').click((e) => {
    e.preventDefault()
    const input = $('[data-footer-newsletter-input]')
    const value = input.val()
    {% if settings.enable_logs %}console.log('value',value){% endif %}
    let registered = false
    $.ajax({
      url: 'https://eiu6am83df.execute-api.us-west-2.amazonaws.com/sms',
      type: 'POST',
      contentType: 'application/json',
      data: JSON.stringify({
        payloadData: value
      }),
      success: data => {
        if (data && typeof data.result !== 'undefined' && data.result === 'true') {
          registered = true
          if(registered == true){
            $('.newsletter__error:eq(0)').slideDown();
            $('.newsletter__error:eq(1)').hide();
            {% if settings.enable_logs %}console.log('data',data){% endif %}
          }          
        }else{
          $('.section__mini-forms details:eq(0)').removeClass('mini-form w-full no-close').addClass('mini-form w-full no-close mini-form--submitted');
          $('form:eq(2)').hide();
        }       
      },
      error: (xhr, status, error) => {
        console.error('Error:', error);
      }   
    })
  }); 
</script>
{% endif %}