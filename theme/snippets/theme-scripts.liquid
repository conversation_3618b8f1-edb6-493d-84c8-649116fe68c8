<script type="text/javascript">
  window.addEventListener("DOMContentLoaded", function() {
    window.addEventListener('Cart:itemAdded', e => {
      try {
        {{ settings.script_cart_add }}
      } catch(err) {
        console.info('Theme Scripts Error', 'Cart:itemAdded', err)
      }
    })
  
    window.addEventListener('Cart:itemRemoved', e => {
      try {
        {{ settings.script_cart_remove }}
      } catch(err) {
        console.info('Theme Scripts Error', 'Cart:itemRemoved', err)
      }
    })
    
    window.addEventListener('Customer:identified', e => {
      try {
        {{ settings.script_customer_identify }}
      } catch(err) {
        console.info('Theme Scripts Error', 'Customer:identified', err)
      }
    })
    
    window.addEventListener('submit', e => {
      try {
        {{ settings.script_form_submit }}
      } catch(err) {
        console.info('Theme Scripts Error', 'submit', err)
      }
    })
  });
</script>