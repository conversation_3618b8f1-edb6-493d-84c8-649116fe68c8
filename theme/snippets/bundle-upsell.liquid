{% if product.metafields.custom.bundle_parent.value %}
<div class="bundle-upsell flex items-center space-x-2 mt-8">
  {% for upsellProduct in product.metafields.custom.bundle_parent.value %}
    {% assign upsellProductHandle = upsellProduct.handle %}
    {% assign upsellProductURL = upsellProduct.url %}

    <div class="bundle-upsell__message flex items-center space-x-2">
      <img src="{{ block.settings.icon }}" loading="lazy" width="24" height="24"/>
      <p class="capitalize text-dark">{{ block.settings.message }}</p>
    </div>
    <span class="bundle-upsell__separator">|</span>
    <a href="{% if block.settings.link_url != blank %}{{ block.settings.link_url }}{% else %}{{ upsellProductURL }}{% endif %}" class="bundle-upsell__link underline text-secondary">{{ block.settings.link_label }}</a>
  {% endfor %}
</div>
{% endif %}