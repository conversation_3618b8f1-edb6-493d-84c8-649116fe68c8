<script>
  var DISCOUNT_CODE = ["CSSHIP", "CSC20", "CSX50","CSEXC","CSW50","SORRY"];
  function updateWarningMessage(){
    let warningMessage = false;
    let checkout_reduction_code = document.getElementById('checkout_reduction_code').value;
    checkout_reduction_code = checkout_reduction_code.toUpperCase();
    if(document.querySelector("#order-summary .notice--warning") !== 'null'){
    if(document.querySelector("#order-summary .notice--warning .notice__content .notice__text") != null){
      warningMessage = document.querySelector("#order-summary .notice--warning .notice__content .notice__text").innerHTML;
      warningMessage = warningMessage.toUpperCase();
      let exist_flag;
        for (i = 0; i < DISCOUNT_CODE.length; i++) {
          if(warningMessage.indexOf(DISCOUNT_CODE[i]) != -1){
            exist_flag = true;
            warningMessageContainer = document.querySelector("#order-summary .notice--warning");
            warningMessageContainer.remove();
            if(document.getElementById('error-for-reduction_code') == null ){
              let node = document.createElement("p");
              let textnode = document.createTextNode("Enter a valid discount code or gift card");
              node.appendChild(textnode);
              node.setAttribute("class", "field__message field__message--error"); 
              node.setAttribute("id", "error-for-reduction_code"); 
              node.style.display = "block";
              document.querySelector("#order-summary .field").appendChild(node);
            }
          }
        }
        if(exist_flag !== true){ document.querySelector("#order-summary .notice--warning").style.display = "table";  }
      }
    } 
  }
  $(document).on('click','[data-trekkie-id="apply_discount_button"]',function(){
    let counter = 0;
    var intervalId = window.setInterval(function(){
      if(document.querySelector("#order-summary .notice--warning") !== null){
        updateWarningMessage();
        clearInterval(intervalId);
      }
      if(counter > 800){
        clearInterval(intervalId);
      }
      counter = counter + 1;
    }, 10);
  });
  document.addEventListener("DOMContentLoaded", function(event) { 
    updateWarningMessage();
  });
  </script>
  <style>#order-summary .notice--warning{ display:none; }</style>