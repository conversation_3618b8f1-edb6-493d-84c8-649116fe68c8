{% comment %}

  Alpine Component State Variables:
    shouldLoad - default: false,
    isVisible - default: false,
    isHovered - default: false,
    isPlaying - default: false,
    timeoutId - default: null,
    isMobileView - default: false,

{% endcomment %}

{% liquid
  assign source = source | default: settings.source
  assign source_mobile = source_mobile | default: settings.source_mobile
  assign loading_method = loading_method | default: settings.loading_method | default: 'scroll'
  assign loading_delay = loading_delay | default: settings.loading_delay | default: 0
  assign poster = poster | default: settings.poster | default: ''
  assign poster_mobile = poster_mobile | default: settings.poster_mobile | default: ''
  assign autoplay = autoplay | default: settings.autoplay
  assign muted = muted | default: settings.muted 
  assign controls = controls | default: settings.controls
  assign loop = loop | default: settings.loop
  assign playsinline = playsinline | default: settings.playsinline
  assign play_button = play_button | default: settings.play_button
%}

{% if source or source_mobile %}
  <div x-data="asyncVideo({
      source: '{{ source }}',
      sourceMobile: '{{ source_mobile }}',
      poster: '{% if poster %}{{ poster | image_url }}{% endif %}',
      posterMobile: '{% if poster_mobile %}{{ poster_mobile | image_url }}{% endif %}',
      loadingMethod: '{{ loading_method }}',
      loadingDelay: {{ loading_delay | times: 1000 }},
      autoplay: {{ autoplay | default: false }},
      controls: {{ controls | default: false }},
      muted: {{ muted | default: false }},
      loop: {{ loop | default: false }},
      playsinline: {{ playsinline | default: false }},
      playButton: {{ play_button | default: false }}
    })"
    x-init="initVideo()"
    class="async-video w-full"
    :class="{'video-loaded': shouldLoad}"
  >

    <!-- Desktop Video -->
    <template x-if="config.source">
      <video 
        x-ref="desktopVideo"
        class="video--desktop w-full {{ classes }} {% render 'class-settings' prefix:'video_class' settings:settings %}"
        :class="config.source && config.sourceMobile ? 'hidden lg:block' : ''"
        :poster="config.poster"
        {% if autoplay %}autoplay{% endif %}
        {% if controls %}controls{% endif %}
        {% if muted %}muted{% endif %}
        {% if loop %}loop{% endif %}
        {% if playsinline %}playsinline{% endif %}
        {% if play_button %}@click="isPlaying ? pauseVideo() : playVideo()"{% endif %}
        @play="handlePlay($event)"
        @pause="handlePause($event)"
        @ended="handleEnded($event)"
      >
        <template x-if="shouldLoad && (!isMobileView || !config.sourceMobile)">
          <source :src="config.source || config.sourceMobile" type="video/mp4">
        </template>
      </video>
    </template>
      
    <!-- Mobile Video -->
    <template x-if="config.sourceMobile">
      <video 
        x-ref="mobileVideo"
        class="video--mobile w-full {{ classes }} {% render 'class-settings' prefix:'video_class' settings:settings %}"
        :class="config.source && config.sourceMobile ? 'lg:hidden' : ''"
        :poster="config.posterMobile || config.poster"
        {% if autoplay %}autoplay{% endif %}
        {% if controls %}controls{% endif %}
        {% if muted %}muted{% endif %}
        {% if loop %}loop{% endif %}
        {% if playsinline %}playsinline{% endif %}
        {% if play_button %}@click="isPlaying ? pauseVideo() : playVideo()"{% endif %}
        @play="handlePlay($event)"
        @pause="handlePause($event)"
        @ended="handleEnded($event)"
      >
        <template x-if="shouldLoad && isMobileView">
          <source :src="config.sourceMobile || config.source" type="video/mp4">
        </template>
      </video>
    </template>

    <!-- Play Button -->
    <template x-if="config.playButton">
      <button 
        x-show="!isPlaying"
        @click="playVideo()"
        class="absolute top-1/2 left-1/2 bg-white aspect-square rounded-full p-lg transform -translate-x-1/2 -translate-y-1/2 transition-opacity"
      >
        {% render 'icon' icon:'play' class:"translate-x-[2px]" %}
      </button>
    </template>
  </div>
{% endif %}
