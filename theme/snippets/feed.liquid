{%- comment -%}

  Brief: Ultimate Search feed for AJAX load of products onto collections
  Author: @SLTWTR
  Features:
    1.  Inclusive filtering
      Pass filter[key]=value, allows products tagged with key:value to be included
    2.  Pseudo Pagination
      Pass limit=n, allows only that number of products to be included in output
      Pass offset=n, offsets allowed products 
    3.  Compatible with @Neptune /sections/collection-products and @Neptune sections/collection-filters
  Notes:
    1.  Modify product item to include only what is needed for the theme to keep output as small as possible. Only use variants and metafields if absolutely necessary.
    2.  For large collections, recommended to pass at least one exclusive tag in url to collection to reduce size
    3.  May require multiple trips to server, if pagination.collection.items > pagination.collection.page_size 
    4.  Useful for related products

    5. Does not handle product type constraint by url yet, need to begin passing key + field + product_type constraint in key of each filter item

{%- endcomment -%}
 
{% liquid 

  assign page_size = 500
  if search.performed
    assign page_size = 500
  endif
  assign limit = 100
  assign offset = 0
  assign params = content_for_header | split: '?view=feed\u0026' | last | split: '"' | first | split: '\u0026'
  assign filter_string = ''
  assign include_description = false
  assign include_variants = false

%}

{
  "params": [
    {%- for param in params -%}
    {%- liquid 
      assign key = param | split: '=' | first | url_decode
      assign value = param | split: '=' | last | replace: '%20', ' '
      if key == 'limit' 
        assign limit = value | plus: 0
      endif
      if key == 'offset'
        assign offset = value | plus: 0
      endif
      if key == 'page_size'
        assign page_size = value | plus: 0 
      endif
      if key == 'include_description'
        assign include_description = true
      endif
      if key == 'include_variants'
        assign include_variants = true 
      endif
      if key == 'include_inventory'
        assign include_inventory = true
      endif
      if key == 'include_swatches'
        assign include_swatches = true
      endif
      if key contains 'filter['
        assign filterkey = key | split: 'filter[' | last | split: ']' | first
        assign filter_string = filter_string | append: filterkey | append: '>>>' | append: value | append: '|'
      endif
      assign filters = filter_string | split: '|'
    -%}
    {
      "key": {{key | json }},
      "value": {{value | json }}
    }{%- unless forloop.last  -%},{%- endunless -%}
    {%- endfor -%}
  ],
  {% comment %}server-side complex filtering{% endcomment -%}
  "filters": [
    {% for filter in filters -%}
    {%- liquid 
      assign field = 'tags'
      assign key = filter | split: '>>>' | first
      if key contains '~'
        assign field = key | split: '~' | last
        assign key = key | split: '~' | first
      endif
      assign values = filter | split: '>>>' | last | url_decode | split: ','
    -%}
    {
      "field": {{field | json }},
      "key":{{key | json }},
      "values": {{values | json }},
      "inclusion": "any"
    }{%- unless forloop.last -%},{%- endunless -%}
      {%- endfor %}
    ],
  "products":[
  
  {%- liquid 
    assign product_index = 0
    assign render_index = 0
    if search.performed 
      paginate search.results by page_size
        assign products = search.results | where: 'object_type', 'product'
        assign pagination = paginate
      endpaginate
    else
      paginate collection.products by page_size
        assign products = collection.products
        assign pagination = paginate
      endpaginate
    endif

  -%}

    {%- for product in products -%}
    
    {%- comment -%}Core Business Logic for rendering of products{%- endcomment -%}

    {% assign render_product = true %}


    {%- comment -%}End core business logic{%- endcomment -%}
    
    {%- if filters.size > 0 -%}
    {%- for filter in filters -%}
      {%- assign passes_set = false -%}
      {%- assign field = 'tags' -%}
      {% comment %}
      {%- assign key = filter | split: '>>>' | first -%}
      {% if key contains '~' %}
        {%- assign field = key | split: '~' | last -%}
        {%- assign key = key | split: '~' | first -%}
      {% endif %}
      {%- assign values = filter | split: '>>>' | last | url_decode | split: ',' -%}
      {% endcomment %}
      {%- for value in values -%}
        {%- if product[field] contains value -%}
          {%- assign passes_set = true -%}
        {%- endif -%}
        {% comment %}
          {% assign value = value | strip %}
          {%- if key != blank and field == 'tags' -%}
            {%- assign filter_tag = key | append: value -%}
          {%- else -%}
            {%- assign filter_tag = value -%}
          {%- endif %}
          {%- if product[field] contains filter_tag -%}
            {%- assign passes_set = true -%}
          {%- endif -%}
        {% endcomment %}
      {%- endfor -%}
      {%- unless passes_set -%}
        {%- assign render_product = false -%}
      {%- endunless -%}
    {%- endfor  -%}
    {%- endif -%}
    {%- if render_product -%}
    {%- assign product_index = product_index | plus: 1  -%}
    {%- if product_index < offset -%}{%- continue -%}{%- endif -%}
    {%- assign render_index = render_index | plus: 1  %}
      {
        "index": {
          "true": {{forloop.index}},
          "processed": {{ product_index}},
          "rendered": {{render_index}}
        },
        {% render 'product-item-data' product:product include_description:include_description, include_variants:include_variants, include_inventory:include_inventory, include_swatches:include_swatches %}
      },
    {%- if render_index == page_size -%}{%- break -%}{%- endif -%}
    {%- endif -%}
    {%- endfor -%}
    false
  ],
  "pagination": {
    "total_rendered": {{render_index}},
    "total_processed": {{ product_index}},
    "limit": {{limit}},
    "page_size": {{page_size}},
    "collection": {{pagination|json }}
  }
}

