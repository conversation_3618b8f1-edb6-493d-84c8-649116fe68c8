{%- if zone == 'head' and position == 'end' -%}
  <script>
    window.mparticle_enable = {{ settings.mparticle_enable }};
  </script>

  {% if settings.mparticle_enable %}
    {% render 'mparticle-init' %}
  {% endif %}

  {% render 'segment-visitors' %}

  <!-- Start of Shoplift scripts -->
  {% render 'shoplift' %}
  <!-- End of Shoplift scripts -->
{%- endif -%}

{%- if zone == 'body' and position == 'end' -%}
<script>
  window._Bundle = () => {
    cart.bundles = Util.unique(cart.items.map(i=>i.properties.Bundle)).map(b=>{
        return Util.unique(cart.items.filter(i=>i.properties.Bundle==b).map(i=>{
            return {...i,...{title:i.product_title.split(' - ')[0]}}
        }),'title').map(i=>{
            return {
                title:i.title,
                bundle:i.properties.Bundle,
                count:i.properties._bundle_size,
                gift:i.price==0,
                id:i.id,
                quantity:Util.math.sum(cart.items.filter(j=>j.product_title.split(' - ')[0]==i.title && j.properties.Bundle==i.properties.Bundle).map(j=>j.quantity))
            }
        })
    })

    cart.updates = cart.updates || {}
    cart.bundles.forEach(b=>{
        var gift = b.find(i=>i.gift)
        var min = Math.min(...b.map(j=>j.quantity))
        if(!!gift && b.length < gift.count) min = 0;
        if ( !!gift && gift.quantity > min){
            cart.updates[cart.items.find( i=>i.id==gift.id && gift.bundle==i.properties.Bundle ).key] = min;
            
        }
    })

    if(Object.keys(cart.updates).length>0){
        Cart.update(cart.updates);
        delete cart.updates
    }
  }
  
  window.addEventListener('Cart:set', e => {_Bundle();})
  window.addEventListener('DOMContentLoaded', e => {_Bundle();})
</script>

{% render 'klaviyo' %}
{%- endif -%}