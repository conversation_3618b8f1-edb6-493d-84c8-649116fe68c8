{% if settings.gift_card_form %}
  {% comment %}
    Renders gift card recipient form.
    Accepts:
    - product: {Object} product object.
    - form: {Object} the product form object.
    - section: {Object} section to which this snippet belongs.
  
    Usage:
    {% render 'gift-card-recipient-form', product: product, form: form, section: section %}
  {% endcomment %}
  <div class="customer">  
    
    {%- assign gift_card_recipient_control_flag = 'properties[__shopify_send_gift_card_to_recipient]' -%}
    {% comment %} <script src="{{ 'recipient-form.js' | asset_url }}" defer="defer"></script> {% endcomment %}
    <recipient-form
      class="recipient-form"
      data-section-id="{{ section.id }}"
      data-product-variant-id="{{ product.selected_or_first_available_variant.id }}"
      
    >
      <input
        id="Recipient-checkbox-{{ section.id }}"
        type="checkbox"
        name="{{ gift_card_recipient_control_flag }}"
        @click="const checkbox = event.target; // Reference to the checkbox
          let recipientEmailField = document.querySelector('#Recipient-email-{{ section.id }}');
          let recipientNameField = document.querySelector('#Recipient-name-{{ section.id }}');
          let messageField = document.querySelector('#Recipient-message-{{ section.id }}');
          let sendOn = document.querySelector('#Recipient-send-on-{{ section.id }}');
          let sendOnErrorElement = document.querySelector('#RecipientForm-send_on-error-{{ section.id }}');
          const offsetInput = document.querySelector('#Recipient-timezone-offset-{{ section.id }}');
          const timezoneOffset = new Date().getTimezoneOffset();
  
          if (offsetInput) {
              offsetInput.value = timezoneOffset.toString();
          }
          if (!checkbox.checked) {
              // Reset the input fields
              recipientEmailField.value = '';
              recipientNameField.value = '';
              messageField.value = '';
              sendOn.value = '';
  
              // Reset styles
              updateFieldStyle(recipientEmailField, true);
              updateFieldStyle(recipientNameField, true);
              updateFieldStyle(messageField, true);
              updateFieldStyle(sendOn, true);
  
              // Optionally hide any error messages
              sendOnErrorElement.classList.add('hidden');
          }
  
          function updateFieldStyle(field, isValid) {
              if (isValid) {
                  field.classList.remove('invalid');
              } else {
                  field.classList.add('invalid');
              }
          }
      "
      >
      <label class="recipient-checkbox" for="Recipient-checkbox-{{ section.id }}">
        <svg
          width="1.6rem"
          height="1.6rem"
          viewBox="0 0 16 16"
          aria-hidden="true"
          focusable="false"
        >
          <rect width="16" height="16" stroke="currentColor" fill="none" stroke-width="1"></rect>
        </svg>
        <svg
          aria-hidden="true"
          class="icon icon-checkmark"
          width="1.1rem"
          height="0.7rem"
          viewBox="0 0 11 7"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M1.5 3.5L2.83333 4.75L4.16667 6L9.5 1" stroke="currentColor" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <span>I want to send this as a gift</span>
      </label>
      
      <p
        id="Recipient-fields-live-region-{{ section.id }}"
        class="visually-hidden"
        role="status"
      ></p>
      <div class="recipient-fields">
        
        <div class="recipient-fields__field">
          <div class="field">            
            <input
              class="field__input"
              id="Recipient-email-{{ section.id }}"
              type="email"
              placeholder=""
              name="properties[Recipient email]"
              value="{{ form.email }}"
              required
                          
            >
            <label class="field__label" for="Recipient-email-{{ section.id }}">
              <span class="recipient-email-label required">Recipient email*</span>
            </label>
            
          </div>
  
          
        </div>
  
        <div class="recipient-fields__field">
          <div class="field">
            
            <input
              class="field__input"
              autocomplete="name"
              type="text"
              id="Recipient-name-{{ section.id }}"
              name="properties[Recipient name]"
              placeholder=""
              value="{{ form.name }}"
              maxlength="255"
              required
              
            >
            <label class="field__label" for="Recipient-name-{{ section.id }}">Recipient name*</label>
          </div>
  
          
        </div>
  
        <div class="recipient-fields__field">
          {%- assign max_chars_message = 200 -%}
          {%- assign max_chars_message_rendered = '200 characters max' -%}
          
          <div class="field">
            
            <textarea
              rows="10"
              id="Recipient-message-{{ section.id }}"
              class="text-area field__input"
              name="properties[Message]"
              maxlength="{{ max_chars_message }}"
              placeholder=""
              aria-label="{{ message_label_rendered }} {{ max_chars_message_rendered }}"
              required
              
            >{{ form.message }}</textarea>
            <label class="field__label" for="Recipient-message-{{ section.id }}">Gift Message*</label>
          </div>
  
          <label class="form__label recipient-form-field-label recipient-form-field-label--space-between">
            <span>{{ max_chars_message_rendered }}</span>
          </label>
  
          
        </div>
  
        <div class="recipient-fields__field">
          <div class="field">
            {% comment %} <input
              class="field__input text-body"
              autocomplete="send_on"
              type="date"
              id="Recipient-send-on-{{ section.id }}"
              name="properties[Send on]"
              placeholder="Send on (optional)"
              pattern="\d{4}-\d{2}-\d{2}"
              value="{{ form.send_on }}"
              
            > {% endcomment %}
            
            <input 
              type="text" 
              id="Recipient-send-on-{{ section.id }}"
              name="properties[Send on]" 
              placeholder="MM-DD-YYYY" 
              maxlength="10" 
              class="field__input text-body"             
              x-mask="99-99-9999"             
            >
            <label class="field__label" for="Recipient-send-on-{{ section.id }}">Send on</label>         
          </div>
          <div id="RecipientForm-send_message-{{ section.id }}" class="form__message">
            <span class="recipient_form_send_message type-micro">This gift card will be emailed to the recipient on the date filled above. If left blank, this gift card is to be emailed to recipient right after purchase.</span>
          </div>
          <div id="RecipientForm-send_on-error-{{ section.id }}" class="form__message hidden">
            <span class="error-msg">Send on must be within 90 days from now.</span>
          </div>       
  
          
        </div>
      </div>
      <input
        type="hidden"
        name="{{ gift_card_recipient_control_flag }}"
        value="if_present"
        id="Recipient-control-{{ section.id }}"
      >
      <input
        type="hidden"
        name="properties[__shopify_offset]"
        value=""
        id="Recipient-timezone-offset-{{ section.id }}"
        disabled
      >
    </recipient-form>
  </div> 
  {% endif %}
