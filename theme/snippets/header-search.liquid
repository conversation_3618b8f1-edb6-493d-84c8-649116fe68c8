<div class="w-full header-bar__search-field">
  <form action="/search" method="GET" id="SearchModalInput" role="search">
    <label for="header_search" class="flex items-center relative overflow-hidden" x-data aria-describedby="header_search" @click="$el.classList.add('active')" @click.outside="if ($el.classList.contains('active')) { $el.querySelector('.header-search__close').click(); } $el.classList.remove('active');return true;">
      {% render 'icon' icon:'search' %}
      <input name="q" id="header_search" type="search" placeholder="Search" value="" aria-label="search" role="searchbox" {% if settings.input_focus %}onfocus="{{ settings.input_focus }}"{% endif %} @keydown.esc="$el.closest('label').classList.remove('active')" />
      <label class="hidden"><input type="submit" class="sr-only" style="width:0;" aria-label="submit-for-search" role="button"></label>
      <button class="header-search__close" x-show="$store.search.query && $store.search.query.length > 0 && $store.search.form == 'SearchModalInput'" onclick="event.preventDefault(); Search.clear(); this.previousElementSibling.previousElementSibling.value=''; Util.wait().then(() => this.closest('label').classList.remove('active')); this.blur(); return false;">
        {% render 'icon' icon:'x' %}
      </button>
      {% assign search_suggestion = settings.search_suggestions | split: ',' %}
      {% if search_suggestion.size > 0 %}
        <div class="search-suggestions-slides swiper w-auto absolute" style="height:40px;font-family:inherit;" aria-live="polite" x-init="$nextTick(()=>{
          new Swiper($el, {
						loop: true,
            direction: 'vertical',
            freeMode: true,
            spaceBetween: {{ settings.search_terms_inbetween_distance }},
            grabCursor: false,
            slidesPerView: 1,
            autoplay: {
              delay: {{ settings.search_terms_animation_delay }},
              disableOnInteraction: true
            },
            speed: {{settings.search_terms_animation_speed | times: 1000}},
            freeModeMomentum: false,
					});
        })">
          <div class="swiper-wrapper swiper-wrapper-smooth-transition" style="display:flex;" x-data>
            {% for term in search_suggestion %}
              <div class="swiper-slide {{ settings.search_terms_classes }}"><span class="flex items-center h-full">{{- term -}}</span></div>
            {% endfor %}
          </div>
        </div>
      {% endif %}
    </label>
  </form>
  <style>
    .swiper-wrapper-smooth-transition{
      display:flex;
      -webkit-transition-timing-function:linear !important; 
      -o-transition-timing-function:linear !important;
      transition-timing-function:linear !important; 
      transition: transform 0.01s ease-in-out;
    }
    .order-1, .order-2, .order-3 {}
    .order-none, .max-lg\:order-2 {}  
    #header_search[value=""] + button { display:none; }
    .search-suggestions-slides {
      position:absolute;
    }
    @media only screen and (min-width: 768px) {
      .search-suggestions-slides {
        left:{{settings.search_terms_position_tablet}}px;
      }
    }
    @media only screen and (min-width: 1023px) {
      .search-suggestions-slides {
        left:{{settings.search_terms_position}}px;
      }
    }
    
    @media only screen and (max-width: 767px) {
      .search-suggestions-slides {
        left:{{settings.search_terms_position_mobile}}px;
      }
    }
    .search-suggestions-slides .swiper-slide span , #header_search::-webkit-input-placeholder, #header_search::placeholder{
      color:{{ settings.search_terms_color }};
      opacity:{{settings.search_terms_color_opacity | divided_by: 100.0 }};
    }
  </style>
</div>
