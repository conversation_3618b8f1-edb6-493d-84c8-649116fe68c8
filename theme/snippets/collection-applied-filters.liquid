<section 
  class="applied-filters flex flex-wrap items-start"
  x-data="{ collection: Alpine.reactive(window.collection)}" 
  x-init="window.collection = Alpine.reactive(window.collection||{});"
  :view="collection.view"
>


  <template x-for="key in Object.keys(collection.filters.applied)">
    <template x-for="value in collection.filters.applied[key]">
      <template x-if="key != 'pf_c_collection'" >

      <div class="field field__chip">
            <span class="" x-text="window.collection.filters.all.find(option => option.label === 'Collection')?.options?.find(subOption => subOption.value === value)?.handle ?? value"></span>

        <button @click="Collection.filter(key, value)">
          {% render 'icon', icon: 'x' width:16 height:16 strokeWidth:1.5 %}
        </button>
      </div>
    </template>

    </template>
  </template>
</section> 