{% assign siblings = settings.sibling_products | default: collections[settings.siblings_collection].products| default: false %}

{% for sibling in siblings %}
	{% unless sibling.handle == product.handle %}
	{% render 'product-data' product:sibling, script_tag:true  include:'variants,inventory,media,shipment_date' preselect_variant:false, history_state:false %}
	<script type="text/javascript">
		products['{{ product.handle }}'].siblings = products['{{ product.handle }}'].siblings || []
		products['{{ product.handle }}'].siblings.push('{{ sibling.handle }}')
		products['{{ product.handle }}'].variants.push(...products['{{sibling.handle}}'].variants)
		window.addEventListener('DOMContentLoaded', e => {
      if (Shopify.designMode) return false	
			window.addEventListener('Products:optionSelected',Util.debounce(e=>{
				let handle = e.detail.values.find(v=>v.value==e.detail.selected_value).product 
				if(products[e.detail.product].variant){
					handle = products[e.detail.product].variant.product
				} 
				if(!!products[e.detail.product].history_state && document.location.pathname.split('/').at(-1) != handle){					
          {% unless settings.history_state == false %}window.history.replaceState(window.history.state,null,handle){% endunless %}
					Util.events.dispatch('Products:siblingChange', handle)
					Util.events.dispatch('Products:refresh')
					if(document.querySelector('.product-essentials__media .swiper')){
						document.querySelector('.product-essentials__media .swiper').swiper.slideTo(0)
					}
				}
			},100))
		})

	</script>
	{% for image in sibling.images %}
	<link rel="preload" as="image" href="{{ image.src | image_url: width:750 }}">
	{% endfor %}
	{% endunless %}
{% endfor %}
