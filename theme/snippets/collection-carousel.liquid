{% unless settings.collection.products.size == 0 %}
<div class="carousel-outer collection-carousel" {% render 'carousel-config' settings:settings namespace:block.id %}>
	{% liquid 

		if settings.title_product != blank 
	 		render 'carousel-product-title' settings:settings, price:false inline:true
		endif
					 
		assign slide_width_class = ' w-1/' | append: settings.slides_per_view_mobile | append: ' lg:w-1/' | append:settings.slides_per_view 
	%}
	<div class="w-full h-full relative group/carousel" aria-live="polite">
		<div swiper class="w-full swiper {% if settings.outer_slides %}swiper--overflow-visible{% endif %}">
			
      <div class="swiper-wrapper items-stretch" 
				style="--gap:{{ settings.spacebetween }}px; --gap-mobile: {{ settings.spacebetween_mobile }}px;" 
				{% if settings.product_item_show_swatches %}
					x-init="$nextTick(async () => {
						await Util.hooks.process('Products:enhance', Util.array($el.querySelectorAll('.product-item')).map(item => item._x_dataStack?.[0]?.product));
					})"
				{% endif %}
			>
				{% for product in settings.collection.products limit:settings.limit %}
					{% assign loop_count =  forloop.length | plus:1 %}
					{% if settings.content_position != blank and forloop.index == settings.content_position and settings.image != blank %}
						<div class="swiper-slide relative {{ slide_width_class }}{%- if loop_count < settings.slides_per_view_mobile  -%} collection-hidden-arrow-mob{% endif %}" :data-index="{{ forloop.index }}">
							{% render 'content-item' settings:settings, index: forloop.index0 %}
						</div>
					{% endif %}

					<div class="swiper-slide relative {{ slide_width_class }}{%- if loop_count < settings.slides_per_view_mobile -%} collection-hidden-arrow-mob{% endif %}" :data-index="{{ forloop.index }}">
						{% render 'product-item' product:product, settings:settings, _settings:_settings location:'carousel', index: forloop.index0 %}
					</div>
				{% endfor %}
				{% if settings.shop_all_text != blank %}
					<div class="swiper-slide relative {{ slide_width_class }}">
						<div class="content-carousel__show-all w-full h-full flex items-center justify-center aspect-[1/1]">
							<a href="{{ settings.collection.url }}" class="button button--primary">
								 {{ settings.shop_all_text }}
							 </a>
						</div>
					</div>
				{% endif %}
			</div>
			{% if settings.arrows or settings.arrows_mobile or settings.show_arrows_on_hover %}
				{% capture arrow_visibility_classes %}
					{% if settings.arrows_mobile and settings.arrows %}
						opacity-100
					{% elsif settings.arrows_mobile %}
						opacity-100 lg:opacity-0
					{% elsif settings.arrows %}
						opacity-0 lg:opacity-100
					{% endif %}
					{% if settings.show_arrows_on_hover %}
						opacity-0 lg:opacity-0 lg:group-hover/carousel:opacity-100
					{% endif %}
				{% endcapture %}
				
				<button class="top-1/2 transform -translate-y-1/2 absolute left-6 {{ arrow_visibility_classes }} {% if settings.arrows_mobile %}show-arrow-mob{% endif %} swiper-button-prev btn-control" tabindex="0" aria-label="Next slide">
					{% render 'icon' icon:'chevron-left' width:30 height:30 strokeWidth:2 %}
				</button>
				<button class="top-1/2 transform -translate-y-1/2 absolute right-6 {{ arrow_visibility_classes }} {% if settings.arrows_mobile %}show-arrow-mob{% endif %} swiper-button-next btn-control" tabindex="0" aria-label="Next slide">
					{% render 'icon' icon:'chevron-right' width:30 height:30 strokeWidth:2 %}
				</button>
			{% endif %}
		</div>
	</div>

	{% if settings.button_1_text != blank or button_1_text != blank %}
		<button {% if settings.button_1_onclick != blank %}onclick="{{ settings.button_1_onclick }};" {% else %}onclick="window.location='{{ button_1_link | default:settings.button_1_link }}'" {% endif %} class="button-mobile button collection-title-shop-all {{ settings.button_1_class_style }} {{ settings.button_1_class_size }} ">
			<span>
				{{ button_1_text | default: settings.button_1_text }}
			</span>
		</button>
	{% endif %}
</div>
{% style %}
	.swiper-button-disabled{opacity:0;}
	{% if settings.arrows_mobile %}
	@media only screen and (max-width:1023px) {}
		button.show-arrow-mob {
			opacity: 1;
			pointer-events: auto;
		}
	}
	.swiper-wrapper:has(.swiper-slide-active:first-of-type) + button.swiper-button-prev {
		opacity: 0;
	}
	{% endif %}
{% endstyle %}
{% endunless %}
