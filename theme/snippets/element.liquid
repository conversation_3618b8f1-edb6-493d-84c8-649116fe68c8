{% comment %}<!-- <pre>{{ _settings | json }}</pre> -->{% endcomment %}
{% liquid 
	assign tag = tag | default: _settings.tag
	assign class_prefix = prefix | append: '_class'
	assign style_prefix = prefix | append: '_style'
	assign attribute_prefix = prefix | append: '_attr'
	assign content_prefix = prefix | append: '_content'
%}
<{{ tag }} {% if name != blank %}name="{{ name }}" {% endif %} class="{% render 'classes' _settings:_settings prefix:class_prefix class:class %}" {% render 'styles' _settings:_settings item:item, prefix:style_prefix, att:true %} {% render 'attributes' _settings:_settings item:item, prefix:attribute_prefix %} {{ attributes }} {{ _settings.attributes }} {% if tag == 'a' %}role="link"{% elsif tag == 'button' %}role="button"{% elsif tag == 'summary' %}{% elsif tag == 'details' %}{% elsif tag == 'ul' %}role="list"{% elsif tag == 'li' %}role="listitem"{% endif %} aria-label="{% if link_url %}{{ link_url | replace: '-', ' ' | split: '/' | slice: -2, 2 | join: ' ' }}{% else %}{{ content | strip_html | escape | downcase }}{% endif %}">{% render 'content' content:content _settings:_settings item:item, prefix:content_prefix %}{{ content }}
{%- unless open -%}</{{ tag }}>{%- endunless -%}
