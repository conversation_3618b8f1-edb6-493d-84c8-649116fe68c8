<div id="uav-popup">
	<div class="address_verification">
		<h2>Verify Your Shipping Address</h2>
		<p>We weren’t able to verify your shipping address. Please confirm or edit your address. You can also use one of the suggested addresses below.</p>
		<div class="addressList" id="address-container">
			<ul></ul>
		</div>
	</div>
</div>

<div id="uav-popup-loading">
	<div class="add_verify">
		<p><span class="btn--loading"><svg class="icon-svg icon-svg--size-18 btn__spinner icon-svg--spinner-button" aria-hidden="true" focusable="false"> <use xlink:href="#spinner-button"></use> </svg></span> Verifying Address …</p>
	</div>
</div>
<script>
{% if settings.address_validation_timeout != blank %}
	window.addressValidation_timeout = {{settings.address_validation_timeout}};
{% endif %}
let uvaCheckFlag = false;
$(document).ready(function() {
	if(Shopify.Checkout.step == 'contact_information'){
	$('body').on("click","#continue_button",function( event ) {
		$("#continue_button").addClass("btn--loading");
		let checkAddressFields = true;
		if($.trim($('#checkout_shipping_address_last_name').val()) == '' || $.trim($('#checkout_shipping_address_address1').val()) == '' || $.trim($('#checkout_shipping_address_city').val()) == '' || $.trim($('#checkout_shipping_address_zip').val()) == '' || $.trim($('#checkout_email').val()) == '' || $.trim($('#checkout_shipping_address_province').val()) == '' || $('#checkout_shipping_address_province').val() == null || $('#checkout_shipping_address_country').val() == null ){
			checkAddressFields = false;
		}
		if(!uvaCheckFlag && checkAddressFields) {
			event.preventDefault();
			//$('#uav-popup-loading').show();
			//$('body').css("overflow","hidden");
			setTimeout( function(){
				if($('#uav-popup').is(':hidden')){
					//uvaCheckFlag = true;
				}
				//$('#uav-popup-loading').hide();
				if($('#uav-popup').is(':hidden')){
					//$('body').css("overflow","visible");
				}
			}  , 10000 );
			let maxResponseAddress = 1;
			let firstName = $('#checkout_shipping_address_first_name').val();
			let lastName = $('#checkout_shipping_address_last_name').val();
			let fullName = firstName+' '+lastName;
			fullName = fullName.trim();
			let shippingAddressOne = $('#checkout_shipping_address_address1').val().toUpperCase();
			let shippingAddressTwo = $('#checkout_shipping_address_address2').val().toUpperCase();
			let shippingAddressCity = $('#checkout_shipping_address_city').val().toUpperCase();
			let shippingAddressCountry = $('#checkout_shipping_address_country>[selected=selected]').attr('data-code');
			let shippingAddressProvince = $('#checkout_shipping_address_province option:selected').text();
			let shippingAddressProvinceCode = $('#checkout_shipping_address_province').val();
			let shippingAddressZip = $('#checkout_shipping_address_zip').val().toUpperCase();

			let fullShippingAddress = fullName+'<br>'+shippingAddressOne+'<br>'+shippingAddressCity+', '+shippingAddressProvinceCode+' '+shippingAddressZip;
			if(shippingAddressTwo != '' && shippingAddressTwo != null){
				fullShippingAddress = fullName+'<br>'+shippingAddressOne+'<br>'+shippingAddressTwo+'<br>'+shippingAddressCity+', '+shippingAddressProvinceCode+' '+shippingAddressZip;
			}
			let myKeyVals = {'maxResponseAddress':maxResponseAddress,'fullName':fullName,'shippingAddressOne':shippingAddressOne,'shippingAddressTwo':shippingAddressTwo,'shippingAddressCity':shippingAddressCity,'shippingAddressCountry':shippingAddressCountry,'shippingAddressProvince':shippingAddressProvince,'shippingAddressProvinceCode':shippingAddressProvinceCode,'shippingAddressZip':shippingAddressZip};
			let originalAddressTemplate = '<li><h4>Original Address<span class="popup-edit-address" onclick="removeSuggestions();"> EDIT ADDRESS</span></h4><div class="address_innerList"><div class="left"><p>'+fullShippingAddress+'</p></div><div class="right"><button type="button" class="product__add-to-bag" onclick="doNothing();" ><span>KEEP THIS ADDRESS</span></button></div></div></li>';
			$('#address-container ul').replaceWith('<ul>'+originalAddressTemplate+'</ul>');
			var suggestedAddressTemplate = '';

			$.ajax({
				type: "POST",
				dataType: "json",
				url: "https://wixy8sprqg.execute-api.us-west-2.amazonaws.com/default/Fedex_Address_Validation",
				data: JSON.stringify(myKeyVals),
				timeout: window.addressValidation_timeout,
				success: function(resultData) {
					$.each(resultData, function(i, itemObject) {
						let shippingCandidateAddressOne = '';
						let shippingCandidateAddressTwo = '';
						let addressLine = [];
						let postcodePrimaryLow = '';
						let numberOfResults = 10;
						if(itemObject.Candidate === undefined){
							//$('#uav-popup-loading').hide();
							$('body').css("overflow","visible");
							uvaCheckFlag = true;
							$("#continue_button").click();
						} else {
							let suggestedAddressText = '<h4>Suggested Address</h4>';
							$.each(itemObject.Candidate, function(i, candidateObject) {
								numberOfResults = Object.keys(itemObject.Candidate).length;
								if(candidateObject.AddressKeyFormat !== undefined){
									shippingCandidateAddressOne = candidateObject.AddressKeyFormat.PoliticalDivision1;
									shippingCandidateAddressTwo = candidateObject.AddressKeyFormat.PoliticalDivision2;

									if(typeof(candidateObject.AddressKeyFormat.AddressLine) != 'string'){
										addressLine = candidateObject.AddressKeyFormat.AddressLine.join("---");
									} else {
										addressLine = candidateObject.AddressKeyFormat.AddressLine;
									}
									postcodePrimaryLow = candidateObject.AddressKeyFormat.PostcodePrimaryLow
								} else {
									shippingCandidateAddressOne = candidateObject.PoliticalDivision1;
									shippingCandidateAddressTwo = candidateObject.PoliticalDivision2;
									if(typeof(candidateObject.AddressLine) != 'string'){
										addressLine = candidateObject.AddressLine.join("---");
									} else {
										addressLine = candidateObject.AddressLine;
									}
									postcodePrimaryLow = candidateObject.PostcodePrimaryLow
								}

								suggestedAddressTemplate = '<li>'+suggestedAddressText+'<div class="address_innerList"><div class="left"><p>'+fullName+'<br>'+addressLine.replace("---", "<br>")+'<br>'+shippingCandidateAddressTwo+', '+shippingCandidateAddressOne+' '+postcodePrimaryLow+'</p></div><div class="right"><button type="button" class="product__add-to-bag" onclick="changeAddress(\''+shippingCandidateAddressOne+'\',\''+shippingCandidateAddressTwo+'\',\''+addressLine+'\',\''+postcodePrimaryLow+'\');" ><span>USE THIS ADDRESS</span></button></div></div></li>';
								$('#address-container ul').append(suggestedAddressTemplate);
								suggestedAddressText = '';
							});
							if(shippingCandidateAddressOne.toLowerCase() == shippingAddressProvinceCode.toLowerCase() && shippingCandidateAddressTwo.toLowerCase() == shippingAddressCity.toLowerCase() && addressLine.replace("---", " ").toLowerCase() == $.trim(shippingAddressOne+' '+shippingAddressTwo).toLowerCase() && postcodePrimaryLow.toLowerCase() == shippingAddressZip.toLowerCase() && numberOfResults == 1){
								//$('#uav-popup-loading').hide();
								$('body').css("overflow","visible");
								uvaCheckFlag = true;
								$("#continue_button").click();
							} else {
								//$('#uav-popup-loading').hide();
								$('body').css("overflow","hidden");
								$("#continue_button").addClass("btn--loading");
								$('#uav-popup').show();
							}
						}
					});
					},
				error: function(xhr, textStatus, errorThrown) {
					//if (textStatus == 'timeout') {
						//console.log("Error : Timeout for this call!");
						$('body').css("overflow","visible");
						uvaCheckFlag = true;
						$("#continue_button").click();
					//}
				}
			});
		} else { $("#continue_button").removeClass("btn--loading"); }
	});
	}
});

function capitalize(s){
    //return s.toLowerCase().replace( /\b./g, function(a){ return a.toUpperCase(); } );
	return s;
};

function changeAddress(addressOne, addressTwo, addressLine, postcodePrimaryLow){
	uvaCheckFlag = true;
	addressLine = addressLine.split('---');
	$('#checkout_shipping_address_address1').val(capitalize(addressLine[0]));
	if(addressLine[1] != undefined){
		$('#checkout_shipping_address_address2').val(capitalize(addressLine[1]));
	}
	$('#checkout_shipping_address_city').val(capitalize(addressTwo));
	$('#checkout_shipping_address_zip').val(postcodePrimaryLow);
	$('#checkout_shipping_address_province').val(addressOne)
	$('#uav-popup').hide();
	$("#continue_button").removeClass("btn--loading");
	$('body').css("overflow","visible");
	$("#continue_button").click();
}

function doNothing(){
	uvaCheckFlag = true;
	$('#uav-popup').hide();
	$("#continue_button").removeClass("btn--loading");
	$('body').css("overflow","visible");
	$("#continue_button").click();
}

function removeSuggestions(){
	uvaCheckFlag = false;
	$('#uav-popup').hide();
	$("#continue_button").removeClass("btn--loading");
	$('body').css("overflow","visible");
}
</script>
<style>
	#uav-popup,
	#uav-popup-loading {
		background: rgba(0,0,0,0.5);
		width: 100%;
		height: 100%;
		position: fixed;
		z-index: 999;
		display: none;
	}

	.address_verification {
		max-width:491px;
		background:#fff;
		margin:0px auto;
		padding:32px 36px;
		overflow-y: auto;
		height: 100%;
		max-height: 564px;
		position: relative;
		top: calc(50% - 282px);
	}

	.address_verification h2 {
		font-family: 'Avenir Book', Arial, sans-serif;
		font-size: 22px;
		line-height: 27px;
		letter-spacing: -0.44px;
		color: #000000;
		margin: 0 0 16px;
	}

	.address_verification p {
		font-family: 'Avenir Book', Arial, sans-serif;
		font-weight: 400;
		font-size: 13px;
		line-height: 19px;
		letter-spacing: -0.07px;
		color: #000000;
		margin:0 0 24px;
	}

	.address_verification #address-container {
		border: 1px solid #D9D9D9;
	}

	.addressList ul li {
		border-bottom: 1px solid #D9D9D9;
		padding: 24px;
	}

	.addressList ul li h4 {
		font-family: 'Avenir Book', Arial, sans-serif;
		font-weight:500;
		font-size: 13px;
		line-height: 18px;
		color: #000000;
		margin: 0 0 16px;
		text-transform: uppercase;
	}

	.addressList .address_innerList p {
		font-family: 'Avenir Book', Arial, sans-serif;
		font-weight:400;
		margin: 0 0 16px;
		color: #717171;
	}

	.addressList button.product__add-to-bag {
		font-family: 'Avenir Book', Arial, sans-serif;
		font-weight: 700;
		font-size: 15px;
		line-height: 18px;
		letter-spacing: 1.13px;
		width: 100%;
		color: #000;
		text-transform: uppercase;
		height: 40px;
		padding: 0 25px;
		border: 2px solid #000;
	}

	.addressList button.product__add-to-bag:hover {
		background-color: #000;
		color:#fff;
	}

	.addressList ul li:last-child {
		border-bottom: none;
	}

	.add_verify {
		max-width: 268px;
		background:#a32e0a;
		margin: 0px auto;
		padding: 31px 19px;
		position: relative;
		top: calc(50% - 53px);
	}

	.add_verify p {
		font-family: 'Avenir Book', Arial, sans-serif;
		font-weight:400;
		font-size: 18px;
		line-height: 21px;
		color: #fff;
		display: flex;
		align-items: center;
	}

	.add_verify p span {
		margin-right: 21px;
	}

	.add_verify p span .btn__spinner {
		position:static;
		margin:0;
		opacity:1;
		-webkit-transform: translateX(0);
		transform: translateX(0);
		-webkit-transition: -webkit-transform 400ms ease-out;
		transition: -webkit-transform 400ms ease-out;
		transition: transform 400ms ease-out;
		transition: transform 400ms ease-out, -webkit-transform 400ms ease-out;
	}

	.popup-edit-address {
		font-size: 10px;
		line-height: 19px;
		color: #000000;
		letter-spacing: -0.05px;
		font-family: 'Avenir Book', Arial, sans-serif;
		font-weight:400;
		cursor: pointer;
		text-decoration: underline;
		text-transform: uppercase;
		float:right;
	}

	.addressList ul li h4 {
		font-family: 'Avenir Book', Arial, sans-serif;
		font-weight:500;
		font-size: 13px;
		line-height: 15px;
		color: #000000;
		margin: 0 0 16px;
		text-transform: uppercase;
	}

	.addressList ul li {
		border-bottom: 1px solid #D9D9D9;
		padding: 23px;
	}

	.addressList .address_innerList p {
		font-family: 'Avenir Book', Arial, sans-serif;
		font-weight:400;
		margin: 0 0 16px;
		color: #717171;
		line-height: 18px;
	}

	.address_verification {
		max-width: 491px;
		background: #fff;
		margin: 0px auto;
		padding: 31px 36px;
		overflow-y: auto;
		height: 100%;
		max-height: 564px;
		position: relative;
		top: calc(50% - 282px);
	}

	@media (max-width: 992px) {
		.address_verification {
			top:0;
			padding: 36px 20px 24px;
		}
	}
</style>
