{% comment %}
  IMPORTANT: This snippet is automatically generated by vite-plugin-shopify.
  Do not attempt to modify this file directly, as any changes will be overwritten by the next build.
{% endcomment %}
{% assign path = vite-tag | replace: '~/', '/' | replace: '@/', '/' %}
{% liquid
  assign file_url = path | prepend: 'http://localhost:5173/components/'
  assign file_name = path | split: '/' | last
  if file_name contains '.'
    assign file_extension = file_name | split: '.' | last
  endif
  assign css_extensions = 'css|less|sass|scss|styl|stylus|pcss|postcss' | split: '|'
  assign is_css = false
  if css_extensions contains file_extension
    assign is_css = true
  endif
  assign modules_path = ''
  if file_extension == blank and modules_path != blank and file_url contains modules_path
    assign module_name = path | split: '/' | last
    assign file_url = file_url | append: '/' | append: module_name | append: '.js'
  endif
%}
{% if is_css == true %}
  {{ file_url | stylesheet_tag }}
{% else %}
  <script src="{{ file_url }}" type="module" crossorigin="anonymous"></script>
{% endif %}
