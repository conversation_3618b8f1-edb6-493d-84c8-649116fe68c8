<nav class="pagination">
{% capture prev %}{% render 'icon' icon:'chevron-left' %}{% endcapture %}
{% capture next %}{% render 'icon' icon:'chevron-right' %}{% endcapture %}
{{ paginate | default_pagination: next: next, previous: prev }}
</nav>
{% if type == 'endless' %}
{% if paginate.next %}
<button onclick="Util.remote.get('{{ paginate.next.url }}' {select:'.article-grid'}).then(grid=>{
	document.querySelectorAll('.article-grid').after(grid)
})"></button>
{% endif %}
{% endif %}