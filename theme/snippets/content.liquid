{%- liquid
  for setting in _settings
    if setting contains prefix and _settings[setting] != blank 
      unless _settings[setting] contains '@include'
        if setting contains 'icon'
					if forloop.first
						assign icon_class = 'leading'
					else
						assign icon_class = 'trailing'
					endif 
          render 'icon' icon:_settings[setting] class:icon_class
        elsif setting contains 'link_list'
          render 'link-list'  linklist:linklists[_settings[setting]]
        else
          echo _settings[setting] | append: ' '
        endif
      endunless
    endif
  endfor
-%}
