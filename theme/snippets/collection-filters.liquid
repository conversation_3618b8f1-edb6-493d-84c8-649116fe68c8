{% if section.settings.enable_clear_all_filter %}
  <template x-if="Object.keys(collection.filters.applied).filter(key => key !== 'pf_c_collection').length > 0 "> 
  <button  class="desk-clear-all flex items-center" onclick="Collection.clear()" role="button" aria-label="clear-all-filters">
    <span class="my-0 clear-all-text">Clear All Filters</span>
  </button>
  </template>
  {% endif %}
  <div class="collection-filters w-full">
       
    <template x-for="(set, index) in collection.filters.all">
      <template x-if="set.options.length" >      
        <details class="collection-filters__accordion" x-bind:open="collection.innerWidth > 1024 ? (set.isCollapsePC ? null : true) : (set.isCollapseMobile ? null : true)" :aria-label="'expand-collapse-'+set.label.replace(/\s/g, '-').toLowerCase()+'-options'">
          <summary class="collection-filters__accordion-header flex w-full items-center justify-between cursor-pointer" aria-controls="'filter-section-mobile-'+index" :aria-label="'expand-collapse-'+set.label.replace(/\s/g, '-').toLowerCase()+'-options'">
            <span class="collection-filters__accordion-title" x-text="set.label"></span>
            <span class="collection-filters__accordion-icon">
              {% assign icon = section.settings.filter_accordion_icon | default: 'chevron-up' %}
              {% render 'icon', icon: icon width:12 height:12 strokeWidth:4 %}
            </span>              
          </summary>
          
          <div class="collection-filters__content field w-full">
  
            <template x-if="set.format == 'list' && set.label != 'Collection'" >
              <template x-for="option in set.options">
                <template x-if="option.count > 0" >
  
                <label class="field__checkbox">
                  <input x-bind:value="set.key" type="checkbox" @input="Collection.filter(set.key,option.value,option.handle)" x-model="option.active" :aria-label="set.label.replace(/\s/g, '-').toLowerCase()+'-'+option.label.split(':').at(-1).toLowerCase()" role="checkbox" tabindex="0">
                  <template x-if="set.label != 'Collection'" >
                    <span x-text="option.label.split(':').at(-1)" :aria-label="set.label.replace(/\s/g, '-').toLowerCase()+'-'+option.label.split(':').at(-1).toLowerCase()"></span>
                  </template>
  
                  <template x-if="set.label == 'Collection' && option.count > 0" >
                    <span x-text="option.handle"></span>
                  </template>
                </label>
              </template>
  
              </template>
            </template>
  
            <template x-if="set.format == 'list' && set.label == 'Collection'" >
              <template x-for="option in set.options">

                <template x-if="option.count > 0" >
  
                <label class="field__radio">

                  <input x-bind:value="set.key" type="radio" :name="'radio-group-' + set.key" :checked="option.active !== undefined ? option.active : false" @change="Collection.filter(set.key, option.value, option.handle, true)" :aria-label="set.label.replace(/\s/g, '-').toLowerCase()+'-'+option.label.split(':').at(-1).toLowerCase()" role="radio" tabindex="0">

                  <template x-if="set.label != 'Collection'" >

                    <span x-text="option.label.split(':').at(-1)" :aria-label="set.label.replace(/\s/g, '-').toLowerCase()+'-'+option.label.split(':').at(-1).toLowerCase()"></span>

                  </template>
  
                  <template x-if="set.label == 'Collection'" >
                    <span x-text="option.handle"></span>
                  </template>
                </label>
              </template>
  
              </template>
            </template>
  
            <template x-if="set.format == 'grid'" >
              <div class="field__buttons">
                <template x-for="option in set.options">
                  <label class="field__button" tabindex="-1">
                    <input class="sr-only-hide" x-bind:value="set.key" type="checkbox" @input="Collection.filter(set.key,option.value)" x-model="option.active" role="checkbox" :aria-label="set.label.replace(/\s/g, '-').toLowerCase()+'-'+option.label.split(':').at(-1).toLowerCase()" tabindex="0">
                    <span class="field__button-text" x-text="option.label.split(':').at(-1)" :aria-label="set.label.replace(/\s/g, '-').toLowerCase()+'-'+option.label.split(':').at(-1).toLowerCase()"></span>
                  </label>
                </template>
              </div>
            </template>
  
            <template x-if="set.format == 'swatches'" >
              <div class="field__colors" x-data="{ getImageUrl: function(colorName) { return window.swatch_settings[colorName.toLowerCase()]?.imageUrl || ''; } }">
                <template x-for="option in set.options">
                  <label class="field__color" tabindex="-1">
                    <input class="sr-only-hide" x-bind:value="set.key" type="checkbox" @input="Collection.filter(set.key,option.value)" x-model="option.active" role="checkbox" :aria-label="set.label.replace(/\s/g, '-').toLowerCase()+'-'+option.label.split(':').at(-1).toLowerCase()" tabindex="0">
                    <span class="field__color-swatch" :style="`background: ${option.value}; border-color: ${option.value.includes('White') || option.value.includes('Multi') ? '#E5E7EB' : 'transparent'}; background-image: url('${getImageUrl(option.value)}'); background-size: contain;`" :aria-label="set.label.replace(/\s/g, '-').toLowerCase()+'-'+option.label.split(':').at(-1).toLowerCase()"></span>
                    <span class="field__color-label" x-text="option.label.split(':').at(-1)" :aria-label="set.label.replace(/\s/g, '-').toLowerCase()+'-'+option.label.split(':').at(-1).toLowerCase()"></span>
                  </label>
                </template>
              </div>
            </template>
  
          </div>
        </details>
      </template>
    </template>
  
  </div>
