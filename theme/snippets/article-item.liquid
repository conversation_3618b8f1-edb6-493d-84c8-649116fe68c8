<!-- <pre>{{ settings | json | replace: ',', '<br/>'}}</pre> -->
{%- liquid 
	assign date = article.published_at | date: "%B %d, %Y"
	assign meta = '<span class="article-item__category">' | append: blog.title | append: '</span>' | append: '<time class="article-item__date">' | append: date | append: '</time>' 
	assign excerpt = article.excerpt_or_content | strip_html | truncatewords: 12, '...' 

  if layout
    assign layout = 'content-item__article--' | append: layout
  endif

  assign item_classes = 'content-item--article ' | append: layout
  assign url = article.url

-%}
  {%- capture header -%}
    <span 
      class="{%- 
        render 'class-settings' 
        prefix:'text_item_1_class' 
        settings:settings 
      %} content-item__meta hidden content-item__meta--top"
    >{{ meta }}</span>
  {%- endcapture -%}
  {% 	
	render 'content-item' 
  url:url
	item_classes:item_classes
	text_item_1_element:'span'
	text_item_2_element:'h4'
	text_item_3_element:'p'
	text_item_1_text: meta
	text_item_2_text: article.title
	text_item_3_text: excerpt 
	text_item_1_classes:'content-item__meta content-item__meta--text-stack'
	text_item_2_classes:'content-item__title type-item my-0'
	text_item_3_classes:'content-item__excerpt'
  header:header
	image: article.image
	content_classes:'flex-col'
	text_stack_classes:'flex-col'
	media_classes:'content-item__media-container'
	button_1_text: 'Read More'
	button_1_classes: 'button--light'
	settings:settings
%}

