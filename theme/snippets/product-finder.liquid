<section product-finder="{{ section.settings.tracking_id }}" class="product-finder" data-state="grid">

  <aside class="product-finder__quiz" filter-quiz style="--current-pane:1;--content-color:{{ section.settings.content_color }}">

    <div class="product-finder__decoration"></div>

    <header class="product-finder__quiz-header">

      <div class="product-finder__quiz-progress">
        <div class="product-finder__quiz-progress-bar" style="width:calc(100% / {{ section.blocks.size }} * var(--current-pane));"></div>
      </div>

      <div class="product-finder__quiz-navigation">

        <nav>
          {% for block in section.blocks %}
            <button class="transitional-element" data-index="{{ forloop.index | plus: 1 }}" onclick="productFinder.pane({{ forloop.index }}); productFinder.clear('{{ block.settings.id }}');" aria-label="{{ block.settings.title | strip_html | downcase | replace: ' ', '-'}}">
              <svg version="1.1" x="0px" y="0px" width="24" height="24" viewBox="0 0 100 100" xml:space="preserve"><path d="M30,50l35,35l5-10L45,50l25-25l-5-10L30,50z" fill="currentColor"></path></svg>
              <span>{{ block.settings.title }}</span>
              </button>
          {% endfor %}
        </nav>

        <div class="product-finder__quiz-pagination">
          <span class="product-finder__quiz-pagination-number product-finder__quiz-pagination-number--current" style="position:relative;width:1rem;text-align:right;">
            {% for block in section.blocks %}
            <div class="page transitional-element {% if forloop.first %}active{% endif %}" data-index="{{ forloop.index }}" style="position:absolute;top:0;left:0;width:100%;">{{ forloop.index }}</div>
            {% endfor %}
          </span>
          <span class="product-finder__quiz-pagination-number product-finder__quiz-pagination-number--delimiter"> of </span>
          <span class="product-finder__quiz-pagination-number product-finder__quiz-pagination-number--total">{{ section.blocks.size }}</span>
        </div>

      </div>

    </header>

    <main class="product-finder__quiz-panes">
    {% for block in section.blocks %}
      
      {% liquid
       assign index = forloop.index0 
       assign previous = forloop.index0 | minus: 1 
       assign next = forloop.index0 | minus: 1 
       if previous > -1 
        assign previous_step = section.blocks[previous] 
       endif 
      %}

      <article class="product-finder__quiz-pane transitional-element {% if block.settings.style_class != blank %}{{block.settings.style_class}}{% endif %} {% if forloop.first %}active{% endif %} {{block.type}} {% if block.type == 'size_plus_calculation' %}finding-perfect-fit{% endif %}" filter-set="{{ block.settings.id }}" data-limit="{{ block.settings.limit }}" data-index="{{ forloop.index }}">
          {% if block.type == "initial-slide" %}
            <div class="product-finder__decoration-melin"></div>
          {% endif %}

          {% if block.settings.question != blank %}
            <p class="product-finder__question">{{ block.settings.question }}</p>
          {% endif %}

          {% if block.settings.initial_slide_title != blank %}
            <p class="product-finder__question">{{ block.settings.initial_slide_title }}</p>
          {% endif %}

          {% if block.settings.instruction != blank %}
          <p class="product-finder__instruction">{{ block.settings.instruction }}</p>
          {% endif %}

          {% if block.settings.initial_slide_copy != blank %}
          <p class="product-finder__instruction">{{ block.settings.initial_slide_copy }}</p>
          {% endif %}
          {% if block.settings.initial_slide_continue != blank %}
            <a class="product-finder__continue" onclick="productFinder.pane(2)">{{ block.settings.initial_slide_continue
          }}</a>
          {% endif %}

          {% if block.settings.prompt != blank %}
            <p class="product-finder__prompt">{{ block.settings.prompt }}</p>
          {% endif %}
          
          {% comment %}
          {% if block.settings.options != blank %}
          {% assign options = block.settings.options | newline_to_br | split: '<br />' %}
          <div class="product-finder__options {% if block.settings.grid == 'compact' %}product-finder__options--compact{% endif %}">  
            {% for option in options %}
              {% liquid 
                assign label = option | split: '>' | first | strip
                assign value = option | split: '>' | last | split: '(' | first | strip
                assign constraint = false
                if option contains '('
                  assign constraint = option | split: '(' | last | split: ')' | first | strip
                endif
              %}
              <input type="checkbox" {% if constraint %}data-constraint="{{ constraint }}"{% endif %} {% if block.settings.constrain_to_available %}data-available="true"{% endif %} data-key="{{ block.settings.id }}" data-value="{{ value }}" id="{{ block.settings.id }}__{{ label | handle }}{% if constraint %}--{{ constraint | handle}}{% endif %}" onchange="productFinder.filter('{{ block.settings.id }}','{{ value }}',event)"> 
              <label onkeyup="
              if(event.which==32){
                console.log(event.which);
                this.previousElementSibling.checked = !this.previousElementSibling.checked;
                this.previousElementSibling.onchange();
                event.stopPropagation();
              }" tabindex="1" for="{{ block.settings.id }}__{{ label | handle }}{% if constraint %}--{{ constraint | handle}}{% endif %}">
                {{ label }}
              </label>
            {% endfor %}
          </div>
          {% endif %}
          {% endcomment %}

          {% if block.settings.options != blank %}
          {% assign options = block.settings.options | newline_to_br | split: '<br />' %}
          <div
            class="product-finder__options {% if block.settings.grid == 'compact' %}product-finder__options--compact{% endif %}">
            {% for option in options %}
            {% assign option_icon_id = "option_icon" | append: forloop.index %}
            {% assign option_icon_src = block.settings[option_icon_id] %}
            {% liquid
            assign label = option | split: '>' | first | strip
            assign value = option | split: '>' | last | split: '(' | first | strip
            assign constraint = false
            if option contains '('
            assign constraint = option | split: '(' | last | split: ')' | first | strip
            endif
            %}
            {% if value == 'not_sure' %}
            <a class="product-finder__not_sure" onclick="productFinder.showCalculation()">Not Sure?</a>
            {% else %}
            <input type="checkbox" {% if constraint %}data-constraint="{{ constraint }}" {% endif %} {% if
              block.settings.constrain_to_available %}data-available="true" {% endif %} data-key="{{ block.settings.id }}"
              data-value="{{ value }}"
              id="{{ block.settings.id }}__{{ label | handle }}{% if constraint %}--{{ constraint | handle}}{% endif %}"
              onchange=" productFinder.filter('{{ block.settings.id }}','{{ value }}',event)">
            <label onkeyup="
                                    console.log(event);
                                    if(event.which==32){
                                      console.log(event.which);
                                      this.previousElementSibling.checked = !this.previousElementSibling.checked;
                                      this.previousElementSibling.onchange();
                                      event.stopPropagation();
                                    }" tabindex="1"
              for="{{ block.settings.id }}__{{ label | handle }}{% if constraint %}--{{ constraint | handle}}{% endif %}">
              {% if block.type == "question_icon" and option_icon_src != blank %}
              <img alt="icon-{{ forloop.index }}" src="{{ option_icon_src | img_url: 'compact' }}" />
              {% endif %}
              <p>{{ label }}</p>
            </label>
            {% endif %}
            {% endfor %}
          </div>
          {% if value == 'not_sure' %}
          <div class="finding-perfect-fit-wrap">
            {% if block.settings.finding_fit_title != blank %}
            <div class="product-finder__title">{{ block.settings.finding_fit_title }}</div>
            {% endif %}
            {% if block.settings.finding_fit_info != blank %}
            <div class="product-finder__info">{{ block.settings.finding_fit_info }}</div>
            {% endif %}
            <div class="perfect-fit">
              <div class="perfect-fit-model">
                <img src="https://cdn.shopify.com/s/files/1/1175/0278/files/Fit_measue_image.png?v=1680501437"
                  alt="model-size">
              </div>
              <div class="perfect-fit-detail">
                <h3>My Circumference</h3>
                <div class="cal_form">
                  <div class="form-control">
                    <label>
                      <input type="text" id="size-number" placeholder="60" onchange="productFinder.headSizeInputChange()"
                      oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');">
                    </label>
                    <label>
                      <select name="measure-type" id="measure-size" placeholder="cm" aria-label="select-size">
                        <option value="cm">cm</option>
                        <option value="in">in</option>
                      </select>
                    </label>
                  </div>
                  <button disabled
                    onclick="productFinder.calButtonClick()" aria-label="calculate">calculate</button>
                  <p class="cal_error" style="display:none;"></p>
                </div>
              </div>
            </div>
            <div class="rec-calc-size" style="display:none;">
              <p>Your Size Recommendation</p>
              <h2 class="calRecommendedSize"></h2>
              <p class="availability_error" style="display: none;">{{ section.settings.size_recommendation_error }}</p> 
              {% comment %}
              <button onclick="productFinder.pane({{ forloop.index | plus: 1 }})">continue</button>
              {% endcomment %}
              <input type="checkbox" data-key="size" data-value="" id="" onchange="" aria-label="size">
              <label onkeyup="{% if settings.enable_logs %}console.log(event);{% endif %}
                              if(event.which==32){
                                {% if settings.enable_logs %}console.log(event.which);{% endif %}
                                this.previousElementSibling.checked = !this.previousElementSibling.checked;
                                this.previousElementSibling.onchange();
                                event.stopPropagation();
                              }" tabindex="1" for="">
                <p>continue</p>
              </label>
            </div>
          </div>
          <script>
            window.measurement = {{ block.settings.cal_measurement }};
          </script>
          {% endif %}
          {% endif %}

          {% if block.settings.placeholder != blank %}
            <form onsubmit="
            {% if block.settings.additional_script_liquid != blank %}
              {{ block.settings.additional_script_liquid | escape }}
            {% endif %} 

            if(!(/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/).test(this.querySelector('#productFinderEmail').value)){return false;}else{
              var olukai_email_custom_source = '{{block.settings.klaviyo_source_name}}';
              var olukai_email_public_api = '{{block.settings.klaviyo_public_api}}';
              var olukai_email_list_id = '{{block.settings.klaviyo_email_list_id}}';
              var olukai_newsletter_list_id = '{{block.settings.klaviyo_news_list_id}}';
              var email = this.querySelector('#productFinderEmail').value;


              if((email != '')&&(olukai_email_custom_source != '-')&&(olukai_email_public_api != '-')){

                  var myHeaders = new Headers();
                  myHeaders.append('revision', '2024-02-15');
                  myHeaders.append('Content-Type', 'application/json');
                  if(olukai_email_list_id != '-'){
                    var raw = JSON.stringify({'data': {'type': 'subscription','attributes': {'custom_source': olukai_email_custom_source,'profile': {'data': {'type': 'profile','attributes': {'email': email}}}},'relationships': {'list': {'data': {'type': 'list','id': olukai_email_list_id}}}}});
                    var requestOptions = {method: 'POST',headers: myHeaders,body: raw,redirect: 'follow'};
                    fetch('https://a.klaviyo.com/client/subscriptions/?company_id='+olukai_email_public_api, requestOptions).then((response) => response.text()).then((result) => console.log('result',result)).catch((error) => console.error('error',error));
                  }
                  if(olukai_newsletter_list_id != '-'){
                    var olukai_email_list_id = olukai_newsletter_list_id;
                    var raw = JSON.stringify({'data': {'type': 'subscription','attributes': {'custom_source': olukai_email_custom_source,'profile': {'data': {'type': 'profile','attributes': {'email': email}}}},'relationships': {'list': {'data': {'type': 'list','id': olukai_email_list_id}}}}});
                    var requestOptions = {method: 'POST',headers: myHeaders,body: raw,redirect: 'follow'};
                    fetch('https://a.klaviyo.com/client/subscriptions/?company_id='+olukai_email_public_api, requestOptions).then((response) => response.text()).then((result) => console.log('result',result)).catch((error) => console.error('error',error));
                  }
              }
            }

            var email_custom_source = '{{block.settings.klaviyo_source_name}}';
              var email_public_api = '{{block.settings.klaviyo_public_api}}';
              var email_list_id = '{{block.settings.klaviyo_email_list_id}}';
              var newsletter_list_id = '{{block.settings.klaviyo_news_list_id}}';
              var email = this.querySelector('#productFinderEmail').value;
              klaviyo.identify({'$email' : email});

              if((email != '')&&(email_public_api != '-')){
                  var tracking = window.tracking;

                  var myHeaders = new Headers();
                  myHeaders.append('revision', '2024-02-15');
                  myHeaders.append('Content-Type', 'application/json');

                  var event_tracking = {method: 'POST',headers: myHeaders,body: JSON.stringify({data: {type: 'event',attributes: {metric: {data: {type: 'metric', attributes: {name: 'Product Finder'}}},properties: {'Question': Object.entries(tracking).filter(([key]) => key !== 'ProductID' && key !== 'ProductName' && key !== 'SKU').map(([key, value]) => ({ [key]: value })),Recommendations: [{ProductID: tracking.ProductID,ProductName: tracking.ProductName,SKU: tracking.SKU}]},profile: {data: {type: 'profile',attributes: {email: email}}}}}})};

                  var requestOptions = {method: 'POST',headers: event_tracking.headers,body: event_tracking.body,redirect: 'follow'};
                  fetch('https://a.klaviyo.com/client/events/?company_id=' + email_public_api, requestOptions).then(response => response.text()).then(result => console.log('event_tracking_result', result)).catch(error => console.error('event_tracking_error', error));
                                    
                  if(email_custom_source != '-'){                  
                    if(email_list_id != '-'){
                      var raw = JSON.stringify({'data': {'type': 'subscription','attributes': {'custom_source': email_custom_source,'profile': {'data': {'type': 'profile','attributes': {'email': email}}}},'relationships': {'list': {'data': {'type': 'list','id': email_list_id}}}}});
                      var requestOptions = {method: 'POST',headers: myHeaders,body: raw,redirect: 'follow'};
                      fetch('https://a.klaviyo.com/client/subscriptions/?company_id='+email_public_api, requestOptions).then((response) => response.text()).then((result) => console.log('result',result)).catch((error) => console.error('error',error));
                    }
                    if(newsletter_list_id != '-'){
                      var email_list_id = newsletter_list_id;
                      var raw = JSON.stringify({'data': {'type': 'subscription','attributes': {'custom_source': email_custom_source,'profile': {'data': {'type': 'profile','attributes': {'email': email}}}},'relationships': {'list': {'data': {'type': 'list','id': email_list_id}}}}});
                      var requestOptions = {method: 'POST',headers: myHeaders,body: raw,redirect: 'follow'};
                      fetch('https://a.klaviyo.com/client/subscriptions/?company_id='+email_public_api, requestOptions).then((response) => response.text()).then((result) => console.log('result',result)).catch((error) => console.error('error',error));
                    }
                  }
              }

            {%- comment -%}
            exponeaTrack.doubleOptIn({
              shop_url: '{{ shop.url }}', 
              shopify_id: '{{ customer.id }}', 
              opt_in_type: 'email', 
              email: customerEmail, 
              source: '{{ section.settings.tracking_id }} Email Signup', 
              marketing_signup: true, 
              loyalty_signup: false
            });
            {%- endcomment -%}

            this.classList.add('submitted'); return false;">
              <input id="productFinderEmail" type="email" placeholder="{{ block.settings.placeholder }}" oninput="if((/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/).test(this.value)){this.nextElementSibling.removeAttribute('disabled')}" aria-label="email">
              <button type="submit" disabled>{{ block.settings.submit }}</button>
              {% if block.settings.success != blank %}
                <p class="product-finder__success">{{ block.settings.success }}</p>
              {% endif %}
            </form>
          {% endif %}

          {% if block.settings.disclaimer != blank %}
            <p class="product-finder__disclaimer">{{ block.settings.disclaimer }}</p>
          {% endif %}

          {% if block.settings.restart != blank %}
            <a class="product-finder__restart" onclick="productFinder.reset()">{{ block.settings.restart }}</a>
          {% endif %}

      </article>
    {% endfor %}
    </main>

  </aside>

  <style>
    .product-finder__sticky-header nav div {
      color: #381300;  /* #000000 */
      font-size: 16px;
      line-height: 1;
      position: absolute;top: 0;left: 0;
      font-family: GTA-Bold,Arial,Helvetica,sans-serif;
      letter-spacing: 0.3px;
    }
    .product-finder__to-top button{
      display: flex;
      align-items: center;
      height: 35px;
      padding: 0 10px 0 14px;
      border-radius:1000px;
      border:1px solid #D3C7C1;
      font-size: 13px;
      color:#381300;  /* #000000 */
    }
    .product-finder__to-top button span {
      margin-bottom: -1px;
    }
    .product-finder__to-top svg {
      margin-left: 5px;
    }
  </style>

  <main results class="product-finder__products">

    <header class="product-finder__sticky-header">
      <div class="" style="width:60%;">
        <nav style="position:relative;">
          {% for block in section.blocks %}
            <div class="transitional-element {% if forloop.first %}active{% endif %}" data-index="{{ forloop.index }}">
              <span>{{ block.settings.title }}</span>
            </div>
          {% endfor %}
        </nav>
      </div>
      <span class="product-finder__to-top">
        <button style="" onclick="document.querySelector('[product-finder] [filter-quiz]').scrollIntoView({behavior:'smooth'});productFinder.scrollTo=false;return false;" aria-label="back-to-quiz">
          <span>Back to Quiz</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="18 15 12 9 6 15"></polyline></svg>
        </button>
      </span>
    </header>

    <header class="product-finder__recommendation-header" style="color: {{ section.settings.recommendation_header_text_color }}; background-color:{{ section.settings.recommendation_header_bg_color }};">
      {{ section.settings.recommendation_header }}
    </header>

    <header class="product-finder__selection-header">
      <button class="product-finder__back" onclick="productFinder.detail(false)" aria-label="{{ section.settings.back_to_results | strip_html | downcase | replace: ' ', '-' }}">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-grid">
          <rect x="3" y="3" width="6" height="6"></rect>
          <rect x="14" y="3" width="6" height="6"></rect>
          <rect x="14" y="14" width="6" height="6"></rect>
          <rect x="3" y="14" width="6" height="6"></rect></svg> 
          <span>{{ section.settings.back_to_results }}</span>
      </button>
    </header>

    <section product-detail class="product-finder__product-detail" style="min-height:100vh;"></section>

    {% if section.settings.no_results %}
      <section class="no-results-message" x-data="console.log($store.productFinder.products)"><div x-show="$store.productFinder.products.length === 0">{{section.settings.no_results}}</div></section>
    {% endif %}
    
    <section product-grid class="product-finder__product-grid" x-data>
      <template x-for="product in $store.productFinder.products">
        {% render 'product-item' attributes: '@click.prevent="productFinder.detail($el.href)"' settings:section.settings %}
      </template>
    </section>
    
  </main>

</section>
<script>
  window.collection = {
    products: [
      {% paginate collection.products by 12 %}
      {% for product in collection.products %}
      {
      {% render 'product-item-data' product:product include_variants:true %}
      }{% unless forloop.last %},{% endunless %}
      {% endfor %}
      {% endpaginate %}
    ],
    fill: () => {
      return fetch(`${document.location.pathname}?view=item-data`).then(r=>r.json()).then(d=>{
        collection.products=d.products.map(p=>{p.tags=[...p.tags, ...p.sizes]; return p; })
      })
    }
  }

  document.addEventListener('alpine:init', function() {
    Alpine.store('productFinder', { products: window.collection.products })
  })
  
  window.productFinderConfig = {{ section.blocks | where: 'type','question' | map: 'settings' | json }}
  
</script>
 
<style>
.product-finder__product-detail.loading {
  opacity: 0;
}
.product-finder__product-detail.loading.loaded {
  transition: opacity 300ms ease;
  opacity: 1;
}
 .template-collection main {
    overflow: visible;
  }
  .collection-pinned-filters {
    display: none !important;
  }
</style>
