<script>
  window.addEventListener('DOMContentLoaded', ()=>{
 
    {% if settings.enable_logs -%}console.log("sku", '{{product.variants[0].sku}}'){%- endif %}
    let is_prefix_sku_enable = false;
    {% if isPrefixSkuEnable %}
      is_prefix_sku_enable = true;
    {% endif %}
    {% if settings.enable_logs -%}console.log("is_prefix_sku_enable ", is_prefix_sku_enable){%- endif %}

    let config = {};
    let requestbody = {};
    let fetch_config; 
    try {
      
      fetch_config = {{ fetch_config | default:'false' }}

    }catch(err){ console.error('Recs: invalid fetch_config') }

    if(typeof fetch_config != 'undefined' && fetch_config) config = fetch_config

    {% if settings %}
      let settings = {{ settings | json }};
      let ruleBasedFlag = false
      Object.keys(settings).forEach(key=>{
        if (key.includes('config_bodyPart_') && !!settings[key]){
          if(key == 'config_bodyPart_ruleBased') {
            ruleBasedFlag = settings['config_bodyPart_ruleBased']
            delete settings[key];
          } else if(key == 'config_bodyPart_type' || key == 'config_bodyPart_status'){
            // requestbody[key.replace('config_bodyPart_','')] = Util.literal(settings[key], settings)
            if(ruleBasedFlag) {
              {% if settings.enable_logs -%}
                console.log('config hereeeee')
                console.log(requestbody["ruleBased"][0])
              {%- endif %}
              if(requestbody["ruleBased"] === undefined){ 
                {% if settings.enable_logs -%}console.log('sadfasfd'){% endif %}
                requestbody["ruleBased"] = [{}]
              }
              if(settings[key] && settings[key] != 'none') {
                requestbody["ruleBased"][0][key.replace('config_bodyPart_','')] = Util.literal(settings[key], settings)
              }
            }
            delete settings[key];
          } else if(key == 'config_bodyPart_productIds'){
            if(settings[key] && settings[key] != 'none'){
              requestbody[key.replace('config_bodyPart_','')] = settings[key].split(",")
            }
            requestbody['debug'] = true
          } else {
            if(settings[key] && settings[key] != 'none') {
              requestbody[key.replace('config_bodyPart_','')] = Util.literal(settings[key], settings)
            }
          }
          delete settings[key];
        }
      })
      {% if settings.enable_logs -%}console.log("config config len",Object.keys(requestbody).length){%- endif %}
      if(Object.keys(requestbody).length > 0 && !settings.hasOwnProperty('config_body')){
        {% if settings.enable_logs -%}console.log("config config heree"){%- endif %}
        settings['config_body'] = JSON.stringify(requestbody);
      }
      Object.keys(settings).forEach(key=>{
        if (key.includes('config_') && !!settings[key])
          config[key.replace('config_','')] = Util.literal(settings[key], settings)
      })
      if(!!config.headers && typeof config.headers == 'string')
        config.headers = JSON.parse(config.headers)
    {% endif %}


    {% if settings.enable_logs -%}console.log("config config ",config);{%- endif %}
    
    let isSplitTabs = {{ split_tabs | json }}
    fetch('{{ source | default: settings.remote_url }}', config)
    .then(response => response.json())
    .then(data => {

      let section = sections.find(s => s.id == '{{ id }}')

      if(!section) return false;
      if (isSplitTabs) {
        let blockId = {{ block_id | json }}
        {% if settings.enable_logs -%}console.log("Block ID in carousel-product-data:", blockId){%- endif %}

        window.tabsProducts = window.tabsProducts || {}
        window.tabsProducts[blockId] = Util.map(data, {{ map | default:settings.map | default: 'false' }}).products
        {% if settings.enable_logs -%}console.log('carousel-product-data : window.tabsProducts - BLOCKS',window.tabsProducts){%- endif %}

        window.dispatchEvent(new CustomEvent('productsUpdated', {
          detail: { blockId: blockId }
        }))
        

      } else {     
        // section.data.products = [
        //   ...(section.data.products || []),
        //   ...Util.map(data, {{ map | default:settings.map | default: 'false' }}).products.map(product => ({
        //       ...product
        //   })),
        // ].filter(p=>{
        //   return !document.location.pathname.includes(p.handle)
        // });
        {% if settings.enable_logs -%}console.log('carousel-product-data : section.data.products',section.data.products){%- endif %}
        if(is_prefix_sku_enable){

            let current_product_sku = '{{ product.variants[0].sku }}';
            current_product_sku = current_product_sku.split('-')[0];
            {% if settings.enable_logs -%}console.log("current_product_sku", current_product_sku){%- endif %}

            section.data.products = [
              ...(section.data.products || []),
              ...Util.map(data, {{ map | default:settings.map | default: 'false' }}).products.map(product => ({
                  ...product
              })),
            ].filter(p => {
              // Filter out products with SKU prefixes
              const skuPrefixes = [current_product_sku]; // Add your SKU prefixes here
              {% if settings.enable_logs -%}
              console.log("skuPrefixes...", skuPrefixes);
              console.log("p...", p);
              console.log("skuPrefixes.some", skuPrefixes.some(prefix => p.variants[0].sku.startsWith(prefix)));
              {%- endif %}
              return !skuPrefixes.some(prefix => p.variants[0].sku.startsWith(prefix));
            }).filter(p=>{
              {% if settings.enable_logs -%}console.log("p.handle", p.handle){%- endif %}
              return !document.location.pathname.includes(p.handle)
            });
        
        }else {           
          section.data.products = [
            ...(section.data.products || []),
            ...Util.map(data, {{ map | default:settings.map | default: 'false' }}).products.map(product => ({
                ...product
            })),
          ].filter(p=>{
            return !document.location.pathname.includes(p.handle)
          });

          {% if settings.enable_logs -%}console.log('carousel-product-data : section.data.products',section.data.products);{%- endif %}
          section.data.products.forEach(item => {
            let urlToFetch = `https://${window.location.hostname}/products/${item.handle}?view=metaData`;
            const myHeaders = new Headers();
            myHeaders.append("content-type", "application/json");
        
            const requestOptions = {
              method: "GET",
              headers: myHeaders,
              redirect: "follow"
            };
        
            fetch(urlToFetch, requestOptions)
              .then(response => response.json())
              .then(result => {
                item.subtitle = result.subtitle;
                {% if settings.enable_logs -%}console.log('itemmm Product data fetched', item);{%- endif -%}
              })
              .catch(error => {
                console.error('itemmm Error fetching product data', error);
              });
          });

        }
      }
      
    });
    
  });
  if(typeof window.productMetafields === 'undefined'){
    window.productMetafields = []
  }
  {%- if collection_handle -%}
    {% paginate collections[collection_handle].products by 1000 %}
      {% for product in collections[collection_handle].products %}
        if(typeof window.productMetafields["style"] === 'undefined'){
          window.productMetafields["style"] = []
          window.productMetafields["style"]["{{product.handle}}"] = []
          window.productMetafields["style"]["{{product.handle}}"]["subtitle"] = []
        } else if(typeof window.productMetafields["style"]["{{product.handle}}"] === 'undefined'){
          window.productMetafields["style"]["{{product.handle}}"] = []
          window.productMetafields["style"]["{{product.handle}}"]["subtitle"] = []
        } 
        window.productMetafields["style"]["{{product.handle}}"]["subtitle"].push("{{- product.metafields.product.style.value.subtitle -}}");
      {% endfor %}
    {% endpaginate %}
  {%- endif -%}
</script>
