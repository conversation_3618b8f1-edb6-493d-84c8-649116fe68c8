{% liquid
  assign product = settings.product | default: product 
  assign price = settings.price
  assign reviews = settings.reviews
  assign type = settings.type

  if settings.price_dynamic == "True"
    assign price = true
  endif
  if settings.type_dynamic == "True"
    assign type = true
  endif
  if settings.reviews_dynamic == "True"
    assign reviews = true
  endif

  if settings.price_dynamic == "False"
    assign price = false
  endif
  if settings.type_dynamic == "False"
    assign type = false
  endif
  if settings.reviews_dynamic == "False"
    assign reviews = false
  endif
%}
<div class="product-header" x-data="{ product: false }" x-init="() => { $data.product = null; 
        if (window.products && window.products['{{ product.handle }}'] !== undefined) {
            $data.product = window.products['{{ product.handle }}'];
        }}">
  <div class="product-header__top flex justify-between items-center">
    {% assign default_title = product.title | split: ' - ' | first %}
    <{{ title_element | default: settings.title_element | default: 'h1' }} class="product-header__title type-headline my-0" {% if settings.display_dynamic_product_title %}x-text="$store.products[window.document.location.pathname.split('/').at(-1)].title.split(' - ')[0]"{% endif %}>{{ settings.title_source | default: default_title }}</{{ title_element | default: settings.title_element | default: 'h1' }}>    
  {% if price %}
    <p class="product-header__prices">
      <template x-if="$store.products.hasOwnProperty(window.document.location.pathname.split('/').at(-1)) && $store.products[window.document.location.pathname.split('/').at(-1)].compare_at_price">
        <s class="product-header__compare-at-price type-item" :class="{'hidden': !$store.products[window.document.location.pathname.split('/').at(-1)].compare_at_price || $store.products[window.document.location.pathname.split('/').at(-1)].compare_at_price === 0}" x-text="(() => {
            const product = $store.products[window.document.location.pathname.split('/').at(-1)];
            return money.format(product.compare_at_price);
        })()"></s>
      </template>
      {% comment %}
        <span class="product-header__price type-item" x-text="money.format(product.price).split('.')[0]"></span>
      {% endcomment %}
      <span class="product-header__price type-item" x-bind:class="{ 'product-item__price_with_compare': $store.products[window.document.location.pathname.split('/').at(-1)]?.compare_at_price }" x-text="(() => {
        const product = $store.products[window.document.location.pathname.split('/').at(-1)];
        if (product && product.product_type === 'Gift Card') {
            return `${money.format(product.price_min)} - ${money.format(product.price_max)}`;
        } else {
          if(product && product.price){
            return money.format(product.price);
          }
        }
        })()"></span>
    </p>
    {% endif %}
  </div>
  {% if type or reviews %}
    <div class="product-header__bottom flex justify-between items-center">
    {% if settings.meta_subtitle_check %}
      <h2 class="product-header__type type-subline my-0" {% if settings.metafield_subtitle_value != '' %}aria-hidden="true" tabindex="-1"{% endif %}>{{ settings.metafield_subtitle_value }}</h2>
    {% else %}
      <h2 class="product-header__type type-subline my-0">{{ product.type }}</h2>
    {% endif %}
    {% if reviews %}
      {% render 'review-summary' product:product, sku_path:'product.variants[0]?.sku' %}
    {% endif %}
    </div>
  {% endif %}
</div>
