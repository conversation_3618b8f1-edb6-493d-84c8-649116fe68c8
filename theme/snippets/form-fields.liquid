{%- liquid
  assign row_depth = 0

  for block in blocks offset:offset

    assign attributes = ''

		assign _offset = offset | plus: forloop.index

		if block.type == 'row' or block.type == 'row-break'
      if nested
        break
      else
        assign row_depth = 0
      endif
		endif

    if row_depth > 0 
      continue
    endif

    case block.type
      when 'row'
        assign row_depth = row_depth | plus: 1
        render 'form-row' blocks:blocks offset:_offset nested:true

      when 'rich-text'
        echo '<div class="mb-4 '
          render 'class-settings' settings:block.settings prefix:'text_class'
        echo '">'
        echo block.settings.text
        echo '</div>'

      when 'text-field'
        if block.settings.field_required
          assign attributes = attributes | append: 'required ' 
        endif
        if block.settings.pattern != blank
          assign attributes = attributes | append: 'pattern="' | append: block.settings.pattern | append: '" ' 
        endif
        if block.settings.mask != blank
          assign attributes = attributes | append: 'x-mask="' | append: block.settings.mask | append: '" ' 
        endif
        render 'field' name:block.settings.field_name type:block.settings.field_type style:'floating' required:block.settings.required attributes:attributes settings:block.settings

      when 'multi-options'
        if block.settings.field_required
          assign attributes = attributes | append: 'required ' 
        endif
        assign options = block.settings.field_options | newline_to_br | split: '<br />' | json | remove: '"' | remove: '[' | remove: ']' | remove: '\n' 
        render 'field' name:block.settings.field_name type:block.settings.field_type options:options required:block.settings.required attributes:attributes default:block.settings.default_value

      when 'submit'
        assign content = 'Submit'
        assign attributes = ''
        if block.settings.button_text
          assign content = block.settings.button_text
        endif
        if block.settings.onclick
          assign attributes = 'onclick="event.preventDefault();' | append: block.settings.onclick | append: '"'
        endif

        echo '<div class="flex gap-4">'

        render 'button' tag:'button' content:content class:block.settings.button_class_style attributes:attributes

          if block.settings.reset_button
            assign content = 'Reset'
            assign attributes = 'onclick="event.preventDefault(); this.parentNode.parentNode.querySelectorAll(`input`).forEach(function(input) {input.value = ``})"'
            if block.settings.reset_button_text != ''
              assign content = block.settings.reset_button_text
            endif

            render 'button' tag:'button' content:content class:block.settings.reset_button_class_style attributes:attributes

          endif

        echo '</div>'

    endcase
  endfor
-%}
