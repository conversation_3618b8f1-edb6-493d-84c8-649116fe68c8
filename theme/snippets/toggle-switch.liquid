{% assign _options = options | default: settings.options | newline_to_br | split: '<br />' %}
<div x-data="{
  default_image_type: Alpine.reactive(window.collection?.settings?.default_image_type || 'produt_image'),
  {%- for option in _options -%}
  option_{{ forloop.index }}:'{{ option | strip_newlines }}'.split(':').pop().split('|'){% if forloop.last %}{% else %},{% endif %}
  {%- endfor -%}
}" x-init="$nextTick(() => {if(default_image_type == 'model_image'){[option_1, option_2] = [option_2, option_1]} })" class="field__toggle" role="radiogroup" aria-label="toggle">
  {%- for option in _options -%}

    {%- assign pair_options = option | split: ':' | last | split: '|' -%}

  <label role="radio" data-value="{{ option | split: ':' | first | strip }}" {% if settings.toggle_interaction contains "hover" %}onmouseover{% else %}onclick{% endif %}="Util.express('{{ onchange | default:settings.onchange }}', this.dataset)">
        <input type="radio" class="sr-only" name="toggle-{{ name | default: block_id }}" value="{{ option | split: ':' | first | strip }}" x-model="{{ model | default: settings.model }}">
        <span class="toggle__label toggle__label--selected" x-text="option_{{forloop.index}}[0]">{{ pair_options | first | strip }}</span>
        <span class="toggle__label toggle__label--unselected" x-text="option_{{forloop.index}}[1]">{{ pair_options | last | strip }}</span>
      </label>
   
  {%- endfor -%}
</div>

