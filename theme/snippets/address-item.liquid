<article class="address-item group {% unless address %}address-item__new{% endunless %}">
	
	<!-- <pre>{{ address | json }}</pre> -->
	
	<header class="text-sm group-active:hidden">
		<div class="address-item__role h-4 text-xs">
		{% if address == customer.default_address %}
		{{ 'customer.addresses.default' | t }}
		{% endif %}
		</div>
		<div class="flex">
			<span class="text-sm address-item__name">{{ address.first_name }} {{ address.last_name }}</span>
			<span class="ml-auto address-item__actions">
				<button class="address-item__action button button--link" onclick="this.closest('.group').classList.toggle('active')">{{ 'customer.addresses.edit' | t }}</button>
				<!-- <button class="address-item__action">{{ 'customer.addresses.delete' | t }}</button> -->

				<form class="inline-block" method="post" action="/account/addresses/{{ address.id }}" onsubmit="if(!confirm('{{ 'customer.addresses.delete_confirm' | t }}')){return false;}">
            	<input type="hidden" name="_method" value="delete"/>
                    <button class="address-item__action button button--link" type="submit">
                      {{ 'customer.addresses.delete' | t }}
                    </button>
                  </form>

			</span>
		</div>
	</header>

	<address class="text-xs not-italic group-active:hidden">
		{{ address | format_address | remove:address.first_name | remove:address.last_name | remove_first:'<br>'}}
	</address>

	<div class="address-item__edit">
		
		{% assign _address = address | default: customer.new_address %}
		
		{% form 'customer_address', _address %}

			<div class="lg:flex lg:gap-md">

				{% assign label = 'customer.addresses.first_name' | t  %}
				{% render 'field'type:'text' name:'address[first_name]' label:label, attributes:'autocapitalize="words"' style:"field--floating-label lg:w-1/2" value:address.first_name %}
			
				{% assign label = 'customer.addresses.last_name' | t  %}
				{% render 'field'type:'text' name:'address[last_name]' label:label, attributes:'autocapitalize="words"' style:"field--floating-label lg:w-1/2" value:address.last_name %}
			</div>
		
			{% assign label = 'customer.addresses.address1' | t  %}
			{% render 'field'type:'text' name:'address[address1]' label:label, attributes:'autocapitalize="words"' style:"field--floating-label" value:address.address1 %}
		
			{% assign label = 'customer.addresses.address2' | t  %}
			{% render 'field'type:'text' name:'address[address2]' label:label, attributes:'autocapitalize="words"' style:"field--floating-label" value:address.address2 %}
		
			{% assign label = 'customer.addresses.city' | t  %}
			{% render 'field'type:'text' name:'address[city]' label:label, attributes:'autocapitalize="words"' style:"field--floating-label" value:address.city %}

			{% assign label = 'customer.addresses.set_default' | t  %}
			{% unless address == customer.default_address %}
			{% render 'field' type:'checkbox' name:'address[default]' input_label:label style:'pb-sm' %}
			{% endunless %}

			<div class="lg:flex lg:gap-md">

				{% assign label = 'customer.addresses.country' | t  %}
				{% render 'field'type:'select' name:'address[country]' label:label, attributes:'autocapitalize="words"' style:"field--floating-label lg:w-1/3" default:address.country option_html:country_option_tags %}

				{% assign label = 'customer.addresses.province' | t  %}
				{% render 'field'type:'select' name:'address[province]' label:label, attributes:'autocapitalize="words"' style:"field--floating-label lg:w-1/3" value:address.province option_html:country_option_tags %}
			
				{% assign label = 'customer.addresses.zip' | t  %}
				{% render 'field'type:'text' name:'address[zip]' label:label, attributes:'autocapitalize="words"' style:"field--floating-label lg:w-1/3" value:address.zip %}
			</div>

			<footer class="flex gap-8">
				<button class="button button--simple button--w-icon" onclick="this.closest('.group').classList.toggle('active'); return false;">
					{% render 'icon' icon:'chevron-left' height:'16' width:'16' strokeWidth:2 %} {{ 'customer.addresses.cancel' | t }}
				</button>
				<button class="button button--primary" >
					{% if address %}
						{{ 'customer.addresses.update' | t }}
					{% else %}
						{{ 'customer.addresses.add' | t }}
					{% endif %}
				</button>
			</footer>
			
		{% endform %}
	</div>
	

</article>