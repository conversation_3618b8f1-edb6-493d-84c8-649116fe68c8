<article name="Menu Item" class="menu-item flex {% render 'class-settings' prefix:'item_class' settings:settings %} {{ item_classes }}" style="{% render 'style-settings' prefix:'item_style' settings:settings %}" {{ attributes }}>

	<div class="menu-item__media {% render 'class-settings' prefix:'media_class' settings:settings %} {{ media_classes }}">
  
	  <!-- media items with position settings (absolute, relative), media size settings (width, height, aspect ratio) -->
	  {% if image != blank or settings.image !=blank%}
	  <img src="{{ image | default: settings.image | image_url }}" class="content-item__media aspect-wide object-cover {% render 'class-settings' prefix:'image_' settings:settings %} {{ image_classes }}" alt="{{ image.alt | default: settings.image.alt}}">
	  {% endif %}
	</div>

  <!-- content container with flex settings (padding, gap, direction) defaults to section settings -->
  <div name="Menu" class="menu-item__menu">
    <ul class="{% render 'class-settings' prefix:'menu_class' settings:settings %} {{ menu_classes }}" style="{% render 'style-settings' prefix:'menu_style' settings:settings %}">
      {% for link in linklists[settings.link_list].links %}
      {% assign open_menus = settings.default_open_mobile | newline_to_br | split: '<br />' %}
      <li class="group{%- liquid
        if open_menus contains link.title
          echo ' menu-active'
        endif
      -%}">
      <a href="{%  if link.url == '#' %}javascript:void(0){% else %}{{ link.url }}{% endif %}" class="w-full" {%  if link.url == '#' %}onclick="if (window.innerWidth < 1024) { event.preventDefault(); toggleMenu(this, '{{ settings.menu_item_interaction }}'); }"{% endif %}>
          {{ link.title }}
          {% if link.links.size > 0 %}
            <button class="lg:hidden ml-auto" onclick="{%  if link.url != '#' %}toggleMenu(this.parentNode, '{{ settings.menu_item_interaction }}');{% endif %}event.preventDefault();return false;" aria-label="toggle-menu">
            {% render 'icon' icon:'plus' class:'group-[.menu-active]:hidden'%}
            {% render 'icon' icon:'minus' class:'hidden group-[.menu-active]:block' %}
          </button>
          {% endif %}
        </a>
        {% if link.links.size > 0 %}
        <ul class="grid max-lg:max-h-0 overflow-hidden group-[.menu-active]:max-h-[60vh] transition-[max-height] {% if settings.columns %}lg:grid-cols-{{settings.columns}}{% endif %}">
          {% if section_settings.menu_text %}
            {% assign menu_items = section_settings.menu_text | split: ',' %}
          {% else %}
            {% assign menu_items = "" %}
          {% endif %}      
          {% for sub in link.links %}
            {% assign is_highlighted = false %}
            {% for item in menu_items %}
              {% if sub.title == item %}
                {% assign is_highlighted = true %}
                {% break %}
              {% endif %}
            {% endfor %}
            <li>
              <a href="{% if sub.url == '#' %}javascript:void(0){% else %}{{ sub.url }}{% endif %}"
                 style="{% if is_highlighted %}color: {{ section_settings.color_selector | default: '#000000' }}{% endif %}">
                {{ sub.title }}
              </a>
            </li>
          {% endfor %}
        </ul>
      {% endif %}
      
      </li>
      {% endfor %}
    </ul>
    
  </div>
</article>

<script>
  function toggleMenu(element, interaction) {
    if (window.innerWidth < 1024) {
      const currentGroup = element.closest('.group');
      const allGroups = document.querySelectorAll('.menu-item__menu .group');

      if (interaction === 'grouped') {
        allGroups.forEach(group => {
          if (group !== currentGroup) {
            group.classList.remove('menu-active');
          }
        });
      }

      currentGroup.classList.toggle('menu-active');
    }
  }
</script>
