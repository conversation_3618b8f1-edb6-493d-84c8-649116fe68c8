{% if settings.inclusion_liquid != blank %}
	{% if settings.inclusion_js != blank %}
    		<template x-data x-if="{{ settings.inclusion_js }}">
	{% endif %}
		<a href="{{ link | default:settings.link }}" class="nav-item relative flex flex-col {% render 'class-settings' prefix:'item_class' settings:settings %} {{ item_classes }}" style="{% render 'style-settings' prefix:'item_style' settings:settings %}" {{ attributes }}>
		
			{% if settings.item_bg_image %}
				<img src="{{ settings.item_bg_image | image_url }}" alt="{{ settings.item_bg_image.alt }}" class="_media absolute inset-0 w-full h-full object-cover" />
			{% endif %}
		
		  <div class="z-10">
		    {% if image != blank or settings.image != blank%}
		      <img src="{{ image | default: settings.image | image_url }}" class="nav-item__media object-cover {% render 'class-settings' prefix:'image_' settings:settings %} {{ image_classes }}" alt="{{ image.alt | default: settings.image.alt}}">
		    {% endif %}
		
		    {% if settings.title != blank or title != blank %}
		    <p class="nav-item__text">{{ title | default: settings.title}}</p>
		    {% endif %}
		
		    {% if settings.description != blank or description != blank %}
		    <div class="nav-item__description">{{ description | default: settings.description}}</div>
		    {% endif %}

		  </div>

		</a>
	{% if settings.inclusion_js != blank %}
		</template>
	{% endif %}
{% endif %}
