<!-- content item article with flex settings (padding, gap, direction), theme settings (select from presets with element overrides) -->
{% if debug %}
<pre>{{ settings | json | replace: ',', '<br/>'}}</pre>
{% endif %}
{% liquid 
  assign element_open = 'article'
  assign element_close = 'article'
  if settings.link != blank or url != blank or settings.liquid_link != blank
    assign item_url = url
    if settings.link != blank
      assign item_url = settings.link
    endif
    if settings.liquid_link != blank
      assign item_url = settings.liquid_link
    endif

    assign element_open = 'a href="' | append: item_url | append: '"'
    assign element_close = 'a' 
  endif

%}

<{{ element_open }} name="Content Item" class="content-item relative flex {% render 'class-settings' prefix:'item_class' settings:settings %} {{ item_classes }}" style="{% render 'style-settings' prefix:'item_style' settings:settings %}" {{ attributes }} {% if collection_grid %}:style="{minHeight: this.productHeight || '0px'}"{% endif %}>

	{% if header != blank  or settings.header != blank %}
	<header class="content-item__header">
		{{ header | default: settings.header }}
	</header>
	{% endif %}

  {%- if image != blank or settings.image != blank or settings.video != blank or settings.videomobile != blank -%}
    <div class="content-item__media {% render 'class-settings' prefix:'media_class' settings:settings %} {{ media_classes }}">
  
	  <!-- media items with position settings (absolute, relative), media size settings (width, height, aspect ratio) -->
    {%- liquid
      assign priority_index = 0
      case settings.image_loading
        when 'eager'
          assign priority_index = 0
        when 'lazy'
          assign priority_index = 3
      endcase
    -%}

    {%- capture image_attr -%}
      x-data="{
        isPriority() {
          const el = this.$el.closest(`[data-index]`);
          const index = el ? parseInt(el.dataset.index) : {{ priority_index }};
          const priority = window.innerWidth >= 1024 ? index < 3 : index < 2;
          return priority
        }
      }"
      :data-priority="$data.isPriority()"
      :fetchpriority="$data.isPriority() ? `high` : `low`"
      :loading="$data.isPriority() ? `eager` : `lazy`"
      :decoding="$data.isPriority() ? `sync` : `async`"
    {%- endcapture -%}

	  {% if image != blank or settings.image != blank %}
      {% capture img_classes -%}
        content-item__media w-full object-cover {% render 'class-settings' prefix:'image_class' settings:settings %} {{ image_classes }}
      {%- endcapture %}
      {% if image_desktop != blank or settings.image_desktop != blank %}
        {% assign img_classes = img_classes | append: 'lg:hidden' %}
      {% endif %}

      {% liquid
        # Map Tailwind classes to percentages
        assign width_map = 'w-full:100,w-1/2:50,w-1/3:33,w-2/3:66,w-1/4:25,w-3/4:75,w-1/5:20,w-2/5:40,w-3/5:60,w-4/5:80,w-1/6:16,w-5/6:83'
        assign container_width = settings.content_class_width | default: 'w-full'
        
        # Convert Tailwind to percentage
        assign width_pairs = width_map | split: ','
        assign mobile_width = '100'
        
        for pair in width_pairs
          assign class_value = pair | split: ':'
          if class_value[0] == container_width
            assign mobile_width = class_value[1]
          endif
        endfor
        
        # Create responsive sizes
        assign sizes = mobile_width | append: 'vw'
      %}

      {% assign image_source = image | default: settings.image %}

      {% render 'image'
        image: image_source
        widths: '200,400,600,800,1200,1600,2000,2400,2800,3200'
        class: img_classes
        loading: settings.image_loading
        sizes: sizes
        attributes: image_attr
      %}
	  {% endif %}

    {% if image_desktop != blank or settings.image_desktop != blank %}
      {% capture img_classes -%}
        content-item__media w-full object-cover hidden lg:block {% render 'class-settings' prefix:'image_class' settings:settings %} {{ image_classes }} 
      {%- endcapture %}

      {% liquid
        assign container_width_desktop = settings.content_class_width_desktop | default: container_width | remove: 'lg:'
        assign desktop_width = '100'
        
        for pair in width_pairs
          assign class_value = pair | split: ':'
          if class_value[0] == container_width_desktop
            assign desktop_width = class_value[1]
          endif
        endfor
        
        # Create responsive sizes for desktop image
        assign sizes = '(min-width: 1024px) ' | append: desktop_width | append: 'vw, 100vw'
      %}

      {% assign image_desktop_source = image_desktop | default: settings.image_desktop %}

      {% render 'image'
        image: image_desktop_source
        widths: '200,400,600,800,1200,1600,2000,2400,2800,3200'
        class: img_classes
        loading: settings.image_loading
        sizes: sizes
        attributes: image_attr
      %}
    {% endif %}

    {% if settings.video != blank or settings.videomobile != blank %}
      {% render 'async-video'
        settings: settings
        source: settings.video
        source_mobile: settings.videomobile
        poster: settings.posterDesktop
        poster_mobile: settings.posterMobile
        autoplay: settings.video_attr_autoplay
        muted: settings.video_attr_muted
        loop: settings.video_attr_loop
        controls: settings.video_attr_controls
        playsinline: settings.video_attr_playsinline
        classes: 'media object-cover'
      %}
    {% endif %}

    {% if settings.button_media_text != blank or button_media_text != blank %}
      {% if product_item_show_button == 'show' %}
      <div class="content-item__media-button ">
        {% liquid 
          assign media_button_el = 'button' 
          if button_media_link != blank and url == blank
            assign media_button_el = 'a'
          endif 
        %}
        <{{ media_button_el }} {% if button_media_link != blank %}href="{{ button_media_link | default:settings.button_media_link }}"{% endif %} class="button {% render 'class-settings' prefix:'button_media_class', settings:settings%} {{ button_media_classes }}">{{ button_media_text | default: settings.button_media_text }}</{{ media_button_el }}>      
      </div>
      {% endif %}
    {% endif %}

	</div>
  {%- endif -%}

  <!-- content container with flex settings (padding, gap, direction) defaults to section settings -->
  <div name="Content" class="content-item__content relative flex {% render 'class-settings' prefix:'content_class' settings:settings %} {{ content_classes }}" style="{% render 'style-settings' prefix:'content_style' settings:settings %}">

    {% comment %}{% render 'text-stack' settings:settings %}{% endcomment %}
     <!-- text stack container with flex settings (padding, gap, direction) defaults to container settings, section settings -->

    {% if settings.text_item_1_text != blank or text_item_1_text != blank or text_item_1_attributes or settings.title_image != blank or title_image != blank or settings.svg != blank or svg != blank or settings.text_item_2_text != blank or text_item_2_text != blank or text_item_2_attributes or settings.text_item_3_text != blank or text_item_3_text != blank or text_item_3_attributes %}
      <div name="Text Stack" class="content-item__text-stack flex {% render 'class-settings' prefix:'text_stack_class' settings:settings %} {{ text_stack_classes }} {% if same_height_review %}same_height_review{% endif %}">
      
        {% if settings.title_image != blank or title_image != blank or settings.svg != blank or svg != blank %}
          <div class="content-item__text-stack-media flex {% render 'class-settings' prefix:'title_image_class' settings:settings %} {{ text_stack_classes }}">

            {% if settings.title_image != blank or title_image != blank %}
              {% liquid
                # Map Tailwind classes to percentages
                assign width_map = 'w-full:100,w-1/2:50,w-1/3:33,w-2/3:66,w-1/4:25,w-3/4:75,w-1/5:20,w-2/5:40,w-3/5:60,w-4/5:80,w-1/6:16,w-5/6:83'
                assign container_width = settings.content_class_width | default: 'w-full'
                assign container_width_desktop = settings.content_class_width_desktop | default: container_width | remove: 'lg:'
                
                # Convert Tailwind to percentage
                assign width_pairs = width_map | split: ','
                assign mobile_width = '100'
                assign desktop_width = '100'
                
                for pair in width_pairs
                  assign class_value = pair | split: ':'
                  if class_value[0] == container_width
                    assign mobile_width = class_value[1]
                  endif
                  if class_value[0] == container_width_desktop
                    assign desktop_width = class_value[1]
                  endif
                endfor
                
                # Create responsive sizes
                assign sizes = '(min-width: 1024px) ' | append: desktop_width | append: 'vw, ' | append: mobile_width | append: 'vw'
              %}

              {% assign title_image = title_image | default: settings.title_image %}
              {% render 'image'
                image: title_image
                alt: title_image.alt
                loading: 'lazy'
                class: 'w-full'
                sizes: sizes
                attributes: '
                  decoding="async"
                  fetchpriority="low"
                '
              %}
            {% endif %}

            {% if settings.svg != blank or svg != blank %}
              <div class="content-item__custom-svg {{ svg_classes }}">
                {{ svg | default: settings.svg }}
              </div>
            {% endif %}

          </div>
        {% endif %}
        
        <!-- text items with text content and format settings -->
        
        {% if settings.text_item_1_text != blank or text_item_1_text != blank or text_item_1_attributes %}
        {% assign element = text_item_1_element | default: settings.text_item_1_element | default: 'p' %}

        {% if text_item_1_link != blank %}
        <a href="{{ text_item_1_link }}">
        {% endif %}
        <{{ element }} class="{% render 'class-settings' prefix:'text_item_1_class' settings:settings %} {{ text_item_1_classes }}" {{ text_item_1_attributes }} style="{% render 'style-settings' settings:settings prefix:'text_item_1_style' %}">{{text_item_1_text | default: settings.text_item_1_text}}</{{ element}}>
        {% endif %}
        {% if text_item_1_link != blank %}
        </a>
        {% endif %}
        
        {% if settings.text_item_2_text != blank or text_item_2_text != blank or text_item_2_attributes %}
        {% assign element = text_item_2_element | default: settings.text_item_2_element | default: 'p' %}
        <{{ element }} class="{% render 'class-settings' prefix:'text_item_2_class' settings:settings %} {{ text_item_2_classes }}" {{ text_item_2_attributes }} style="{% render 'style-settings' settings:settings prefix:'text_item_2_style' %}">{{text_item_2_text | default: settings.text_item_2_text}}</{{ element}}>
        {% endif %}
        
        {% if settings.text_item_3_text != blank or text_item_3_text != blank or text_item_3_attributes %}
        {% assign element = text_item_3_element | default: settings.text_item_3_element | default: 'p' %}
        <{{ element }} class="{% render 'class-settings' prefix:'text_item_3_class' settings:settings %} {{ text_item_3_classes }}" {{ text_item_3_attributes }} style="{% render 'style-settings' settings:settings prefix:'text_item_3_style' %}">{{text_item_3_text | default: settings.text_item_3_text}}</{{ element}}>
        {% endif %}
        
      </div>
    {% endif %}

    {% if settings.liquid != blank or liquid != blank%}
    <div class="content-item__custom-liquid {% render 'class-settings' prefix:'text_stack_class' settings:settings %} {{ text_stack_classes }}">
      {{ liquid | default: settings.liquid }} 
    </div>
    {% endif %}
    
    <!-- button set container with flex settings (padding, gap, direction) defaults to container settings, section settings -->
    {% if settings.button_1_text != blank or button_1_text != blank or settings.button_2_text != blank or button_2_text != blank or settings.button_3_text != blank or button_3_text != blank %}
      <div name="Button Set" class="content-item__button-set {% render 'class-settings' prefix:'buttons', settings:settings%} {{ button_stack_classes }}">
        
        <!-- button items with button content and format settings -->
        {% if settings.button_1_text != blank or button_1_text != blank %}
        <button {% if settings.button_1_onclick != blank %}onclick="{{ settings.button_1_onclick }};" {% else %}onclick="window.location='{{ button_1_link | default: settings.button_1_liquid_link | default:settings.button_1_link }}'" {% endif %} class="button {% if desk_hide_button1 %} button-desktop {% endif %}{% render 'class-settings' prefix:'button_1_class', settings:settings%} ">
          {% if settings.button_1_leading_icon != blank %}
            {% render 'icon' icon:settings.button_1_leading_icon class:'button__icon button__icon--trailing ml-2' %}
          {% endif %}
          <span>
            {{ button_1_text | default: settings.button_1_text }}
          </span>
          {% if settings.button_1_trailing_icon != blank %}
            {% render 'icon' icon:settings.button_1_trailing_icon class:'button__icon button__icon--trailing ml-2' %}
          {% endif %}
        </button>
        {% endif %}

        {% if settings.button_2_text != blank or button_2_text != blank %}
        <button {% if settings.button_2_onclick != blank %}onclick="{{ settings.button_2_onclick }};" {% else %}onclick="window.location='{{ button_2_link | default: settings.button_2_liquid_link | default:settings.button_2_link }}'" {% endif %} class="button {% render 'class-settings' prefix:'button_2_class', settings:settings%} ">
          {% if settings.button_1_leading_icon != blank %}
            {% render 'icon' icon:settings.button_1_leading_icon class:'button__icon button__icon--trailing ml-2' %}
          {% endif %}
          <span>
            {{ button_2_text | default: settings.button_2_text }}
          </span>
          {% if settings.button_2_trailing_icon != blank %}
            {% render 'icon' icon:settings.button_2_trailing_icon class:'button__icon button__icon--trailing ml-2' %}
          {% endif %}
        </button>
        {% endif %}

        {% if settings.button_3_text != blank or button_3_text != blank %}
          <button {% if settings.button_3_onclick != blank %}onclick="{{ settings.button_3_onclick }};" {% else %}onclick="window.location='{{ button_3_link | default: settings.button_3_liquid_link | default:settings.button_3_link }}'" {% endif %} class="button {% render 'class-settings' prefix:'button_3_class', settings:settings%} ">
            <span>
              {{ button_3_text | default: settings.button_3_text }}
            </span>
          </button>
        {% endif %}

      </div>
    {% endif %}
    
  </div>

  {% render 'hotspots' blocks:blocks offset:offset %}
  {% render 'overlay' blocks:blocks offset:offset %} 

</{{ element_close }}>

