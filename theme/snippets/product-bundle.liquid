<div 
	x-data="{
    bundle:{{ blocks | where: 'type','bundle-component' | map: 'settings' | map: 'product' | map : 'handle' | json | replace: '"', "'"}}.map(handle=>window.products[handle]),
	}"
	x-init="
	  $alpine.listen('Product', () => {
	    $data.bundle = {{ blocks | where: 'type','bundle-component' | map: 'settings' | map: 'product' | map : 'handle' | json | replace: '"', "'"}}.map(handle=>window.products[handle])
	    window.bundle = $data.bundle;
  })">

	<template x-if="bundle.some(product=>!product.variant && product.selected_options.length < product.options.length)">
	  {% render 'button' tag:'button' attributes:'disabled' style:'disabled' content:settings.button_select_options class:'w-full justify-center button--large' %}
	</template>

	<template x-if="bundle.some(product=>!product.variant && product.selected_options.length === product.options.length)">
	  {% render 'button' tag:'button' attributes:'disabled' style:'disabled' content:settings.button_select_options class:'w-full justify-center button--large' %}
	</template>

	<template x-if="bundle.some(product=>!!product.variant && !product.variant.available)">
	  {% render 'button' tag:'button' attributes:'disabled' style:'disabled' content:settings.button_out_of_stock class:'w-full justify-center button--large' %}
	</template>

	<template x-if="bundle.every(product=>!!product.variant && product.variant.available)">
	{% assign click = '@click="Cart.add(bundle.map(p=>{ return { id:p.variant.id, properties:{ Bundle:`bundle_name`, _bundle_size:bundle.length } } } ))"' | replace: 'bundle_name', settings.bundle_name %}
	  {% render 'button' tag:'button' attributes:click, style:'primary', content:settings.button_add_to_cart, class:'w-full justify-center button--large' %}
		}
	</template>

</div>

<style>	
	.shopify-product-form button { display:none !important; } 
</style>

<script>
  window.addEventListener('DOMContentLoaded', () => {
    window.setTimeout(() => {
      var reversedProducts = {};var keys = Object.keys(window.products).reverse();var i = 0;
      keys.forEach(key => {
          reversedProducts[i] = window.products[key];i++;
      });

      Object.values(reversedProducts).forEach(product => {
          product.options.forEach(option => {
              if (option.name === 'Color' || option.name === 'Size' || product.product_type === 'Gift') {
                  try {
                      {% if settings.enable_logs -%}console.log(product.handle, option.name, option.values[0].value){%- endif %}
                      Products.select.option(product.handle, option.name, option.values[0].value)
                  } catch (error) {
                      console.log('error',error)
                  }
              }
          });
      });

    }, 100)
  })
</script>
