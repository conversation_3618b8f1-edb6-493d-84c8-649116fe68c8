<script> 
if (Shopify.Checkout.step == 'contact_information') {
setTimeout(function(){
    {%- assign sms_legal_header = settings.roark_sms_legal_consent_copy1 -%}
    {%- assign sms_legal_copy = settings.roark_sms_legal_consent_copy2 -%}
    var d1 = document.querySelector(".section--shipping-address");
    var dc1 = d1.querySelector('.section__content');
    var dc2 = dc1.querySelector('.fieldset');
    dc2.insertAdjacentHTML('beforeend', `<div class="field sms-checkbox"><div class="checkbox-wrapper"><div class="checkbox__input"> <input size="30" type="hidden" name="checkout[sms-checkbox]"> <input name="checkout[sms-checkbox]" type="hidden" value="0"><input class="input-checkbox" data-backup="sms-checkbox" type="checkbox" value="1" name="checkout[sms-checkbox]" id="checkout_sms-checkbox"></div> <label class="checkbox__label" for="checkout_sms-checkbox">
        {%- if sms_legal_header != blank -%}
            <b>{{ sms_legal_header }}</b>
        {%- endif -%}    
        {%- if sms_legal_copy != blank -%}
            <p>{{ sms_legal_copy }}</p> 
        {%- endif -%}</label></div></div>`);

      var x_phone = document.getElementById("checkout_shipping_address_phone"); 
      x_phone.setAttribute("maxlength", 14);
      x_phone.addEventListener("focus", myFocusFunction, true);
      var sms_checkbox = document.querySelector(".sms-checkbox");
      function myFocusFunction() {
        sms_checkbox.style.display = "block";
      }
      var myInput = document.getElementById("checkout_shipping_address_phone");

      if (myInput && myInput.value) {
        sms_checkbox.style.display = "block";
      }

    $('[data-step="contact_information"] form').on('submit', (e) => {
        var checkBox = document.getElementById("checkout_sms-checkbox");
		if(document.getElementById("checkout_buyer_accepts_marketing") || document.getElementById("checkout_sms-checkbox")){
			if(document.getElementById('checkout_buyer_accepts_marketing').checked || document.getElementById('checkout_sms-checkbox').checked){
				localStorage.setItem('roark_member', 'true')
			}
			if(document.getElementById('checkout_buyer_accepts_marketing').checked){
				localStorage.setItem('roark_member_email', document.getElementById('checkout_email_or_phone').value)
			}
			if(document.getElementById('checkout_sms-checkbox').checked){
				if(document.getElementById('checkout_shipping_address_phone').value != ''){
					let phoneNum = document.getElementById('checkout_shipping_address_phone').value
					phoneNum = phoneNum.replace(/\D+/g, '')
					localStorage.setItem('roark_member_phone', phoneNum)
				}
			}
		}
        if (checkBox.checked == true){
          if (typeof exponeaTrack !== 'undefined') {
            exponeaTrack.doubleOptIn({
              shop_url: '{{ shop.url }}', 
              shopify_id: '{{ customer.id }}', 
              opt_in_type: 'sms', 
              phone: document.getElementById("checkout_shipping_address_phone").value, 
              source: 'Checkout SMS Signup', 
              marketing_signup: false, 
              loyalty_signup: false
            });
          }

      // Function to format the number to 001 format instead of +1
      function formatPhone(phone) {
            phone = phone.split(' ').join('');
            phone = phone.split('(').join('');
            phone = phone.split(')').join('');
            phone = phone.split('-').join('');
            if (phone.length == 10) {
              return '001' + phone;
            } else
              if (phone.startsWith('+1')) {
                return phone.replace('+1', '001');
              } else
                if (phone.startsWith('001')) {
                  return phone;
                } else
                  if (phone.startsWith('+')) {
                    return phone.replace('+', '00');
                  } else return null;
          }
        }
    });
 }, 3000);
}
</script>