<div class="{{ class }} {{ settings.cart_footer_classes }}" x-data>
 
  <div class="cart__checkout-button">
    {% assign checkout = 'sections.cart.checkout' | t %}
    {% assign attributes = 'href="' | append: routes.root_url | append: 'checkout"' %}
    {% render 'button' tag:'a', content: checkout, style: settings.checkout_button_style, attributes:attributes, class: settings.checkout_button_classes %}
  </div>

  <div class="cart__continue-button">
    {% assign continue = 'sections.cart.continue' | t %}
    {% render 'button' content:continue, style: settings.continue_button_style, attributes: '@click="$event.preventDefault(); Modal.close();"', class: settings.continue_button_classes %}

  </div>

</div>
