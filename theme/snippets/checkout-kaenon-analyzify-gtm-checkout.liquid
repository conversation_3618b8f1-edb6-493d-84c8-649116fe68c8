<!-- Added with Analyzify V2.3 - Dec 21, 2022 11:35 am -->

<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','{{ settings.kaenon_gtm_container_id }}');</script>
<!-- End Google Tag Manager -->
<script>
document.addEventListener("DOMContentLoaded", function() {
window.dataLayer = window.dataLayer || [];
window.analyzify = window.analyzify || {};

if (analyzify.hasOwnProperty("g_feed_region") == false){
  analyzify["g_feed_region"] = "US";
}
function checkout_event(event_name, option_value = ''){
  fetch('/cart.js')
      .then(response => response.json())
      .then(cart => {
          if(cart.items.length > 0){
              var cartId = cart.token;
              var cartTotalValue = cart.total_price;
              var cartTotalQuantity = cart.item_count;
              var cartCurrency = cart.currency;
              var cartItemsName = [];
              var cartItemsBrand = [];
              var cartItemsType = [];
              var cartItemsSku = [];
              var cartItemsId = [];
              var cartItemsVariantId = [];
              var cartItemsVariantTitle = [];
              var cartItemsPrice = [];
              var cartItemsQuantity = [];
              var cartItemsQuantity = [];
              var cartGProductIds = [];

              cart.items.map((val, index) => {
                  cartItemsName.push(val.title);
                  cartItemsBrand.push(val.vendor);
                  cartItemsType.push(val.product_type);
                  cartItemsSku.push(val.sku);
                  cartItemsId.push(val.product_id);
                  cartItemsVariantId.push(val.variant_id);
                  cartItemsVariantTitle.push(val.title);
                  cartItemsPrice.push(parseFloat(parseInt(val.original_price)/100));
                  cartItemsQuantity.push(val.quantity);
                  cartGProductIds.push("shopify_"+ analyzify.g_feed_region +"_"+val.product_id+"_"+val.variant_id);
              });

              params = {
                  event: event_name,
                  page_type: 'checkout',
                  product_name: cartItemsName,
                  product_brand: cartItemsBrand,
                  product_type: cartItemsType,
                  product_sku: cartItemsSku,
                  product_id: cartItemsId,
                  variant_id: cartItemsVariantId,
                  variant: cartItemsVariantTitle,
                  product_price: cartItemsPrice,
                  quantity: cartItemsQuantity,
                  cart_id: cart.token,
                  currency: cartCurrency,
                  totalValue: parseFloat(cart.total_price)/100,
                  totalQuantity: cart.item_count,
                  g_product_id: cartGProductIds
              }

              if(option_value != ''){
                  params['option_value'] = option_value;
              }

              window.dataLayer.push(params);
              console.log(event_name+'==>', window.dataLayer);
          }
      });
}

if(Shopify.Checkout.step == 'contact_information'){
  checkout_event('ee_checkout_contact_information');
}else if(Shopify.Checkout.step == 'shipping_method'){
  const checkedShippingRate = document.querySelector('[name="checkout[shipping_rate][id]"]:checked');
  if(checkedShippingRate.value != ''){
      shipping_method_option = decodeURIComponent(checkedShippingRate.value);
      if(typeof shipping_method_option != 'undefined' && shipping_method_option != 'undefined'){
          checkout_event('ee_checkout_shipping_method');
          checkout_event('ee_checkout_shipping_method_option', shipping_method_option);
      }
  }
}else if(Shopify.Checkout.step == 'payment_method'){
  checkout_event('ee_checkout_payment_method');
  setTimeout(function(){
      const checkedPaymentGateway = document.querySelector('[name="checkout[payment_gateway]"]:checked');
      checkout_event('ee_checkout_payment_method_option', checkedPaymentGateway.closest('.radio-wrapper').getAttribute('data-gateway-name'));
  }, 1000)

}else if(Shopify.Checkout.step == 'review'){
  checkout_event('ee_checkout_review');
}

if(Shopify.Checkout.step == 'shipping_method'){
  document.querySelectorAll('[name="checkout[shipping_rate][id]').forEach(el => el.addEventListener('change', (event) => {
      const checkedShippingRate = document.querySelector('[name="checkout[shipping_rate][id]"]:checked');
      if(checkedShippingRate.value != ''){
          shipping_method_option = decodeURIComponent(checkedShippingRate.value);
          if(typeof shipping_method_option != 'undefined' && shipping_method_option != 'undefined'){
              checkout_event('ee_checkout_shipping_method_option', shipping_method_option);
          }
      }
  }));
} else if (Shopify.Checkout.step == 'payment_method') {
  document.querySelectorAll('[name="checkout[payment_gateway]"]').forEach(el => el.addEventListener('change', (event) => {
      const checkedPaymentGateway = document.querySelector('[name="checkout[payment_gateway]"]:checked');
      if(checkedPaymentGateway.closest('.radio-wrapper').getAttribute('data-gateway-name')){
          checkout_event('ee_checkout_payment_method_option', checkedPaymentGateway.closest('.radio-wrapper').getAttribute('data-gateway-name'));
      }
  }));
};
});
</script>