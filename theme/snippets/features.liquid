{% liquid 

  assign features = settings.features_list | split: ',' | uniq 

  assign _icons = settings.icon_map | newline_to_br | remove: '\n' | split: '<br />'
  for feature in features

    assign text = feature
    assign words = feature | split: ' '
    if forloop.last and words[0]=='and'
      assign text = text | remove_first: 'and'
    endif 

    assign _feat = feature | replace: '&amp;', '&' | handle

    assign icon = ''

    for _icon in _icons
      assign key = _icon | split:':' | first
      assign key_handle = key | handle
      if _feat contains key_handle or key == '*'
        assign icon = _icon | split:':' | last | strip
        break
      endif
    endfor

    render 'element' tag:'article' prefix:'feature' open:true _settings:settings class:'feature-item items-center justify-center flex'
      render 'icon' icon:icon class:"feature-item__icon"
      render 'element' tag:'div' prefix:'text-stack' open:true _settings:settings class:'feature-item__text gap-1 flex flex-col'
        

        assign _text_1 = text | split: ' ' | first



        assign text_1 = _text_1 | replace: '_', ' '
        render 'element' tag:settings.text_1_element prefix:'text_1' content:text_1 _settings:settings
        
        assign text_2 = text | remove: _text_1
        render 'element' tag:settings.text_2_element prefix:'text_2' content:text_2 _settings:settings
    echo '</div></article>'
  endfor
%}
