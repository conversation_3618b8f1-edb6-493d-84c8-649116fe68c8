{%- liquid
  
  assign id = id | default:settings.id | default:block_id
  assign label = label | default: settings.label 
  assign type = type | default: settings.type | default: 'text'
  assign placeholder = placeholder | default: settings.placeholder | default: label

  if error != blank
    assign attributes = attributes | append: ' error-message="' | append: error | append: '"'
  endif

  assign options = options | default: settings.options | newline_to_br | replace: ',', '<br />' | split: '<br />'

-%}

<div class="field {{ style }} {% render 'class-settings' settings:settings, prefix:'field_class' %} w-full">
  
  {% if label != blank %}
    {%- if type == 'checkbox' or type == 'radio' or type == 'toggle' -%}
      <p class="{{ label_classes }}">{{ label }}</p>
    {%- else -%}
      <label for="{{ id }}" class="{{ label_classes }}" aria-describedby="{{ id }}">{{ label | default: settings.label }}</label>
    {%- endif -%}
  {%- endif -%}

  {%- case type -%}
    {%- when 'toggle' -%}
      <div class="field__toggle">
        {%- for option in options -%}
          {%- assign pair_options = option | split: '|' | last | split: ':' -%}
          {%- if pair_options.size > 1 -%}
            <label>
              <input type="radio" class="sr-only" name="{{ name }}" value="{{ option | split: '|' | first }}" {% liquid
                if forloop.first
                  echo 'checked'
                endif
              %} {{ attributes }} role="radio" aria-label="{{ option | split: '|' | first | downcase | replace: ' ', '-'}}">
              <span class="toggle__label">{{ pair_options[0] }}</span>
              <span class="toggle__label">{{ pair_options[1] }}</span>
            </label>
          {%- else -%}
            <label>
              <input type="radio" class="sr-only" name="{{ name }}" value="{{ option | split: '|' | first }}" {% liquid
                if forloop.first
                  echo 'checked'
                endif
              %} {{ attributes }} role="radio" aria-label="{{ option | split: '|' | first | downcase | replace: ' ', '-'}}">
              <span class="toggle__label">{{ option | split: '|' | last }}</span>
            </label>
          {%- endif -%}
        {%- endfor -%}
      </div>
    {%- when 'button' -%}
      <div class="field__buttons">
        {%- for option in options -%}
          {% if option == blank %}{% continue %}{% endif %}
          <label class="field__{{type}}" for="{{ id }}" aria-describedby="{{ id }}">
            <input id="{{ id }}" class="sr-only" name="{{ name }}" value="{{ option }}" type="checkbox" {{ attributes }} role="checkbox" aria-label="{{ option | downcase | replace: ' ', '-'}}" />
            <span class="field__button-text">{{ option }}</span>
          </label>
        {%- endfor -%}
        {{ content }}
      </div>
    {%- when 'color' -%}
      <div class="flex gap-4">
        {%- for option in options -%}
          {%- liquid
            assign color = option
            assign option_val = option
            if option == blank
              continue
            endif
            if option contains ':'
              assign color = option | split: ':' | last
              assign option_val = option | split: ':' | first
            endif
          -%}
          <label class="field__{{type}}" for="{{ id }}" aria-describedby="{{ id }}">
            <input id="{{ id }}" class="sr-only" name="{{ name }}" value="{{ option_val }}" type="checkbox" {% if option_val == default %}checked{% endif %} {{ attributes }} role="checkbox" aria-label="{{ option_val | downcase | replace: ' ', '-'}}" />
            <span class="field__color-swatch" style="background: {{ color }};"></span>
            <span class="field__color-label">{{ option_val }}</span>
          </label>
        {%- endfor -%}
        {{ content }}
      </div>
    {%- when 'checkbox' or 'radio' -%}
      {% for option in options %}
        {% if option == blank %}{% continue %}{% endif %}
        <label class="field__{{type}}">
          <input id="{{ id }}" name="{{ name }}" value="{{ option | split: '|' | first }}" type="{{ type }}" {% if option== default %}checked{% endif %} {{ attributes }} />
          <span>{{ option | split: '|' | last }}</span>
        </label>
      {% endfor %}
      {{ content }}
    {%- when 'checkbox' -%}

      <label class="field__{{type}} {% render 'class-settings' settings:settings, prefix:'label_class' %}" for="{{ id }}" aria-describedby="{{ id }}">
        <input id="{{ id }}" name="{{ name | default: settings.name }}" type="{{ type }}" {% if checked or settings.checked %}checked{% endif %} {{ attributes }} {% if required or settings.required %}required aria-required="true"{% endif %} aria-label="{{ name | default: settings.name | downcase | replace: ' ', '-'}}" role="{% if type == 'checkbox' %}checkbox{% else %}radio{% endif %}" />
        <span>
          {% if input_label != blank or settings.input_label != blank %}
            <div class="text-sm">{{ input_label | default: settings.input_label }}</div>
          {% endif %}
          {% if description != blank or settings.description != blank %}
            <div class="text-xs">{{ description | default: settings.description }}</div>
          {% endif %}
        </span>
      </label>

    {%- when 'select' -%}
      <label for="{{ id }}" class="hidden"></label>
      <select id="{{ id }}" class="field__select" name="{{ name | default: settings.name }}" {{ attributes }} {% if required or settings.required %}required aria-required="true"{% endif %} {% if value!=blank %}value="{{ value }}"{% endif %} role="menu" aria-label="select-a-value">
      {% if placeholder and default == blank %}
      <option disabled selected value="">{{ placeholder }}</option>
      {% endif %}
      {{ option_html }}
      {% for option in options %}
        <option {% if option == default %}selected{% endif %} value="{{ option | split: ':' | first }}" role="menuitem">{{ option | split: ':' | last }}</option>
      {% endfor %}
        {{ content }}
      </select>
    {%- when 'textarea' -%}
      <textarea id="{{ id }}" class="field__textarea" name="{{ name }}" {{ attributes }} aria-label="{{ name | downcase | replace: ' ', '-' }}" role="textbox" autocomplete="off"></textarea>
    {%- else -%}
      <label for="{{ id }}" class="hidden"></label>
      <input id="{{ id }}" class="field__input" name="{{ name | default: settings.name }}" type="{{ type }}" {{ attributes }} placeholder="{{ placeholder }}" {% if mask != blank or settings.mask != blank %} x-data x-mask="{{ mask | default: settings.mask }}"{% endif %} {% if pattern != blank or settings.pattern != blank %} pattern="{{ pattern | default: settings.pattern }}"{% endif %} {% if required or settings.required %}required aria-required="true"{% endif %} {% if value!=blank %}value="{{ value }}"{% endif %} role="{% if type == 'tel' or type == 'text' or type == 'email' or type == 'password' %}textbox{% elsif type == 'radio' %}radio{% elsif type == 'checkbox' %}checkbox{% endif %}" autocomplete="{% if type == 'email' %}email{% elsif type == 'tel' %}tel-national{% elsif type == 'text' %}on{% elsif type == 'password' %}off{% else %}on{% endif %}" aria-label="{{ value | default:'text-input' | downcase | replace: ' ', '-' }}">
  {%- endcase -%}


  {% if description != blank or settings.description != blank %}
  {% unless type == 'checkbox' %}
  <div class="field__description text-xs {% render 'class-settings' settings:settings, prefix:'description_class' %}">
    {{ description | default: settings.description }}
  </div>
  {% endunless %} 
  {% endif %} 

</div> 
