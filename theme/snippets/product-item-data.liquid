"id":{{ product.id }},
"handle":{{ product.handle | json }},
"title":{{ product.title | json }},
"available":{{ product.available | json }},
{%- if include_description %}
  "description":{{ product.description | json }},
{%- endif %}
{%- if include_swatches -%}
  "swatches": [
  {% for product in product.metafields.product.style.value.collection.value.products %}
  {{ product | json }}{% unless forloop.last %},{% endunless %}
  {% endfor %}
  ],
{%- endif -%}
"tags":{{ product.tags | json }},
"type":{{ product.type | json }},
"options":{{ product.options | json }},
"options_by_name": {{ product.options_by_name | json }},
"options_with_values": {{ product.options_with_values | json }},
"price":{{ product.price }},
"price_min":{{ product.price_min }},
"price_max":{{ product.price_max }},
"compare_at_price":{{ product.compare_at_price | json }},
{%- if include_variants -%}
  {%- if include_inventory -%}
    "variants": [
    {%- for variant in product.variants -%}
      {%- assign inv_string = variant.inventory_quantity | prepend: '"inventory_quantity":' | append: ',"inventory_management"' -%}
      {{ variant | json | replace: '"inventory_management"', inv_string }}{%- unless forloop.last -%},{%- endunless -%}
    {%- endfor -%}
    ],
  {%- else -%}
    "variants": {{ product.variants | json }},
  {%- endif -%}
  "sizes":{{ product.variants | where: 'available' | map: 'option2' | json }},
{%- endif -%}
"featured_image": {{ product.featured_image.src | json }},
"featured_image_aspect_ratio": {{ product.images[0].aspect_ratio | json }},
"featured_image_width": {{ product.images[0].width | json }},
"featured_image_height": {{ product.images[0].height | json }},
"featured_image_id": {{ product.featured_image.id | json }},
"featured_image_alt": {{ product.featured_image.alt | json }},
"hover_image": {{ product.images[1].src | json }},
"hover_image_alt": {{ product.images[1].alt | json }},
"media":{{ product.media | json }},
"sku": {{ product.selected_or_first_available_variant.sku | json }}


