collection.settings.remote_url = 'https://services.mybcapps.com/bc-sf-filter/{% if search.performed %}search{% else %}filter{% endif %}?shop={{ shop.permanent_domain }}&page=${page}&limit=24&sort=${sort}&country={{ localization.country.iso_code | downcase }}&currency={{ localization.country.currency.iso_code }}&display=grid{% if collection %}&collection_scope= ${collection.id} {% endif %}{% if search.performed %}&q={{ search.terms }}{% endif %}&product_available={% if show_oos == 'true' %}true{% else %}false{% endif %}&variant_available={% if show_oos == 'true' %}true{% else %}false{% endif %}&build_filter_tree=true${Object.keys(filters).length?`&_=pf${Object.keys(filters).map(key=>`${collection.filters.applied[key].map(value=>`&${key}[]=${value}`).join(``)}`).join(``)}`:``}';

collection.settings.map = {
  filters: {
    from:'filter.options',
    each:{
      label:'label',
      key:'filterOptionId',
      isCollapseMobile:'isCollapseMobile',
      isCollapsePC:'isCollapsePC',
      selectType:'selectType',
      format: 'displayType|this.replace(`box`,`grid`)|this.replace(`swatch`,`swatches`)',
      options:{
        from: 'values',
        each: {
          value: 'key',
          label: 'key',
          handle: 'label',
          count: 'doc_count'   
        }
      }
    }
  },
  products:{
    from:'products',
    each:{
      title:'title',
      images:{ from:'images_info'},
      price: {% if cart.currency.iso_code == shop.currency %}'price_max|*100'{% else %}'price_max_{{ cart.currency.iso_code | downcase }}|*100'{% endif %},
      compare_at_price : {% if cart.currency.iso_code == shop.currency %}'compare_at_price_max|*100'{% else %}'compare_at_price_max_{{ cart.currency.iso_code | downcase }}|*100'{% endif %},
      handle:'handle',
      type:'product_type',
      tags:'tags',
      id:'id',
      variants:{ 
        from:'variants', 
        each:{ 
          id:'id', 
          title:'title', 
          price: {% if cart.currency.iso_code == shop.currency %}'price|*100'{% else %}'price_{{ cart.currency.iso_code | downcase }}|*100'{% endif %},
          available:'available', 
          sku:'sku', 
          image:{ 
            from:'image', 
            each:{ 
              src:'src', alt:'alt'
            }
          }
        }
      }
    }
  },
  total_products:'total_product',
  suggestions: {
    from: 'suggestions'
  }
};
