<script>
  {% if settings.roark_cn_modal_enabled %}
    {% if checkout.customer.id %}
      $(document).on(`page:load page:change`, function() {
        if ($('.cn_modal')[0]){}else{cn_modal_enable()}
      });

      jQuery(document).on('click','#cn_modal_button', (e) => {
        sessionStorage.setItem("accepts_marketing", "1");
        e.preventDefault();
        jQuery('#cn_modal_button').addClass('btn--loading');
        jQuery('#cn_modal_button').html("");
        jQuery('#cn_modal_button').html("<span></span>");
        jQuery('#cn_modal_button span').addClass('cn_loader');
        let customer_id = jQuery('#cn_modal_button').attr('customer_id');
        let store_name = jQuery('#cn_modal_button').attr('store_name');
        jQuery.ajax({
          url: 'https://kvt6fvnrq3.execute-api.us-west-2.amazonaws.com/default/Olukai_Free_Shipping_Newsletter/resource',
          type: 'post',
          data: JSON.stringify({'customer_id': customer_id,'store_name': store_name,'query': 'put',"accepts_marketing" : "","accepts_marketing_status" : ""}),
          headers: {'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*'},
          crossDomain: true,
          async: false,
          success: function(data) {
            //let data_customer_raw = JSON.parse(data);
            let data_customer = data.body;
            if (data_customer.customer.accepts_marketing) {
              location.reload();
            } else {
              location.reload();
            }
          }
        })
      });

      $(document).ready(function(){
        $('#checkout_email').attr('status','notupdate');
        console.log('status',$('#checkout_email').attr('update'));
        let customer_id = '{{checkout.customer.id}}';
        let store_name = '{{shop.domain}}';
        if(customer_id != ''){
          jQuery.ajax({
            url: 'https://kvt6fvnrq3.execute-api.us-west-2.amazonaws.com/default/Olukai_Free_Shipping_Newsletter/resource',
            type: 'post',
            data: JSON.stringify({'customer_id': customer_id,'store_name': store_name,'query': 'get',"accepts_marketing" : "","accepts_marketing_status" : ""}),
            async: false,
            headers: {'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*'},
            crossDomain: true,
            success: function(data) {
            //let data_customer_raw = JSON.parse(data);
            let data_customer = data.body;
              //console.log(data_customer.customer.accepts_marketing);
              if (data_customer.customer.accepts_marketing) {
                $('#checkout_buyer_accepts_marketing').prop('checked', true);
              }else{
                $('#checkout_buyer_accepts_marketing').prop('checked', false);
              }
            }
          })
        }
      });

      $('#checkout_email').change(function() {
        $(this).attr('status','updated');
        if($('#checkout_buyer_accepts_marketing').prop('checked')){
          $('input[name="checkout[buyer_accepts_marketing]"]').val('1');
        }else{
          $('input[name="checkout[buyer_accepts_marketing]"]').val('0');
        }
      });

      $('#checkout_buyer_accepts_marketing').change(function() {
        let customer_id = '{{checkout.customer.id}}';
        let store_name = '{{shop.domain}}';
        let accepts_marketing = "0";
        if(this.checked){
          accepts_marketing = "1";
          $('input[name="checkout[buyer_accepts_marketing]"]').val('1');
        }else{
          $('input[name="checkout[buyer_accepts_marketing]"]').val('0');
        }
        //console.log('status',$('#checkout_email').attr('status'));
        let status = $('#checkout_email').attr('status');
        if((customer_id != '') && (status == 'notupdate')){
          jQuery("#continue_button").addClass("btn--loading");
          jQuery.ajax({
            url: 'https://kvt6fvnrq3.execute-api.us-west-2.amazonaws.com/default/Olukai_Free_Shipping_Newsletter/resource',
            type: 'post',
            headers: {'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*'},
            crossDomain: true,
            data: JSON.stringify({'customer_id': customer_id,'store_name': store_name,'query': 'put',"accepts_marketing" : accepts_marketing,"accepts_marketing_status" : "1"}),
            async: false,
            success: function(data) {
              jQuery("#continue_button").removeClass("btn--loading");
              //let data_customer_raw = JSON.parse(data);
              let data_customer = data.body;
              //console.log('accepts_marketing',data_customer.customer.accepts_marketing);
              if (data_customer.customer.accepts_marketing) {
                $('input[name="checkout[buyer_accepts_marketing]"]').val('1');
              }else{
                $('input[name="checkout[buyer_accepts_marketing]"]').val('0');
              }
            },
            error: function() {
              console.log('error in api response');
              jQuery("#continue_button").removeClass("btn--loading");
            }
          });
        }
      });

      function cn_modal_enable(){
        let customer_id = '{{checkout.customer.id}}';
        let store_name = '{{shop.domain}}';
        jQuery.ajax({
          url: 'https://kvt6fvnrq3.execute-api.us-west-2.amazonaws.com/default/Olukai_Free_Shipping_Newsletter/resource',
          type: 'post',
          headers: {'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*'},
          crossDomain: true,
          data: JSON.stringify({'customer_id': customer_id,'store_name': store_name,'query': 'get',"accepts_marketing" : "","accepts_marketing_status" : ""}),
          async: false,
          success: function(data) {
            //let data_customer_raw = JSON.parse(data);
            let data_customer = data.body;
            if (data_customer.customer.accepts_marketing) {
              $('form').append('<input class="custom_buyer_accepts_marketing" name="checkout[buyer_accepts_marketing]" type="hidden" value="1">');
            } else {
              setTimeout(function(){
                //console.log('.');
                jQuery('.section--shipping-method .content-box').css("margin-top", "0px");
                jQuery('.section--shipping-method .content-box').css("border-radius", "0px 0px 5px 5px");
              }, 500);

              let cn_modal = `<div class="cn_modal" style="background:{{settings.roark_cn_background_color}};"><div class="cn_icon">{% if settings.roark_cn_icon %}<img src="{{settings.roark_cn_icon  | img_url: '20x20'}}" alt="locker">{% else %}<img src="https://cdn.shopify.com/s/files/1/0068/6404/4145/files/lock_fd706657-db4c-43cd-aa21-5a23aff83c51_1.svg?v=1589836027" alt="locker"/>{% endif %}</div><div  class="cn_message"><div class="cn_superscript">{{settings.roark_cn_superscript}}</div><div class="cn_heading">{{settings.roark_cn_heading}}</div><div class="cn_heading cnHeading_mobile">{% if settings.roark_cn_icon %}<img src="{{settings.roark_cn_icon  | img_url: '20x20'}}" alt="locker">{% else %}<img src="https://cdn.shopify.com/s/files/1/0068/6404/4145/files/lock_fd706657-db4c-43cd-aa21-5a23aff83c51_1.svg?v=1589836027" alt="locker"/>{% endif %}{{settings.roark_cn_heading}}</div><div class="cn_description">{{settings.roark_cn_description}}</div></div><div class="cn_btn"><button id="cn_modal_button" customer_id="{{checkout.customer.id}}" store_name="{{shop.domain}}" style="background-color:{{settings.roark_cn_button_color}};color:{{settings.roark_cn_btn_text_color}};">{{settings.roark_cn_btn_text}}<span></span></button></div></div>`;
              jQuery(cn_modal).insertBefore('[data-shipping-methods]');
            }
          }
        })
      }
    {% endif %}
  {% endif %}
</script>