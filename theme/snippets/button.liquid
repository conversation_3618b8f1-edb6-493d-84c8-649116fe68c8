{% if settings.inclusion_js %}
<template x-if="{{ settings.inclusion_js }}">
{% endif %}

{%- liquid 
  assign tag = tag | default: 'button'
  if link != blank or settings.link != blank
    assign tag = 'a'
  endif
-%}
<{{ tag }}
  class="button {% unless style contains 'button--' or settings.style contains 'button--' %}button--{% endunless %}{{ style | default: settings.style }} {{ class }} {{ classes }} {% render 'class-settings' settings:settings prefix:'button_class'%}" 
  {% if link != blank or settings.link != blank %}href="{{ link | default: settings.link }}"{% endif %}  
  {% if onclick != blank or settings.onclick != blank %}{{ settings.onclick_type | default: 'x-data @' }}click="event.preventDefault(); {{ onclick | default: settings.onclick }}"{% endif %}  
  {% if onhover != blank or settings.onhover != blank %}{{ settings.onhover_type | default: 'x-data @' }}mouseenter="{{ onhover | default: settings.onhover }}"{% endif %}
  {% if disabled or settings.disabled %}disabled{% endif %} {% if form_validity or settings.form_validity %}disabled{% endif %}
  {% render 'attributes' _settings:settings, prefix:'button_attr' %}
  {{ attributes }} {% if tag == 'a' %}role="link"{% elsif tag == 'button' %}role="button"{% endif %} aria-label="{{ content | default: button_text | default: settings.button_text | strip_html | escape | downcase | replace: ' ', '-' }}" >
  {% liquid 
    if settings.leading_icon != blank
      render 'icon' icon:settings.leading_icon class:'button__icon button__icon--leading mr-2'
    endif
    if leading_icon != blank
      render 'icon' icon:leading_icon class:'button__icon button__icon--leading mr-2'
    endif 
  %}
  {% if content != blank or settings.button_text != blank %}
    <span class="button__text">{{ content | default: button_text | default: settings.button_text }}</span>
  {% endif %}
  {% liquid 
    if settings.trailing_icon != blank
      render 'icon' icon:settings.trailing_icon class:'button__icon button__icon--trailing ml-2'
    endif
    if trailing_icon != blank
      render 'icon' icon:trailing_icon class:'button__icon button__icon--trailing ml-2'
    endif 
  %}
</{{ tag }}>
{% if form_validity or settings.form_validity %}
<script type="text/javascript">
  const button = document.currentScript.previousElementSibling;
  const form = button.closest('form');
  window.addEventListener('input', () => {
    if(Util.form.valid(form)){
      button.removeAttribute('disabled')
    } else {
      button.setAttribute('disabled','disabled')
    }
  })
</script>
{% endif %}
{% if settings.inclusion_js %}
</template>
{% endif %}

