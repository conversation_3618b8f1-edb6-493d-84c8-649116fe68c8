<dialog 
  data-modal="login" 
  class="modal modal--login modal--center"
  tabindex="0"
>
  <button class="absolute p-4 right-0 top-0" onclick="Modal.close()">
    {% render 'icon' icon:'x' %}
  </button>

  <header x-data="Alpine.reactive(window.customer.login.prompt)" x-init="window.customer.login.prompt = Alpine.reactive(window.customer.login.prompt)">
    <h2 x-text="`${customer.login.prompt.title || `Login`}`">Login</h2>
    <p x-text="`${customer.login.prompt.motivator || ``}`">All the aloha in one place</p>
  </header>

  <main>
    <form data-action="account/login">
      {% render 'field' name: 'Email' type: 'email' %}
      {% render 'field' name: 'Password' type: 'password' %}
      <input type="submit" value="Login" class="button button--primary">
    </form>
  </main>

</dialog>