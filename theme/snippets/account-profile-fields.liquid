<fieldset class="flex flex-wrap account-fields account-fields--{{ placement | handle }}">

{% assign fields = fields | where: 'placement', placement %}

{% for field in fields %}

  {% if field.inclusion_js != blank %}
  <template x-if="{{ field.inclusion_js }}">
  {% endif %}

  <div class="{% render 'class-settings' prefix:'field_wrap_class_' settings:field %}">
    
    {% case field.profile_inclusion %}

      {% when 'primary' %}<template x-if="$store.customer.edit === 0">
      {% when 'secondary' %}<template x-if="$store.customer.edit > 0">

    {% endcase %}
    
    {% if field.legend_title != blank or field.legend_subtext != blank %}
      <legend class="w-full">
        {% if field.legend_title != blank %}<h4>{{ field.legend_title }}</h4>{% endif %}
        {% if field.legend_subtext != blank %}{{ field.legend_subtext }}{% endif %}
      </legend>
    {% endif %}

    {% unless field.profile_inclusion == 'all' %}
      </template>
    {% endunless %}

    {% case field.profile_inclusion %}

      {% when 'primary' %}<template x-if="$store.customer.edit === 0">
      {% when 'secondary' %}<template x-if="$store.customer.edit > 0">
      
    {% endcase %}

      <div class="account-field account-field--{{field.format | handle}} account-field--{{field.key | handle}}">

        {% case field.format %}

          {% when 'radio' %}

          <div class="flex">
            {% assign options = field.options | newline_to_br | split: '<br />' %}
            {% for option in options %}
            {% assign value = option | split: ':' | first | strip %}
            {% assign label = option | split: ':' | last | strip %}
            <div class="field">
              <label class="field__radio">
                <input id="{{ field.key }}" name="{{ field.key }}" value="{{value }}" type="radio" x-model="profile.{{ field.key }}" x-model="profile.{{ field.key }}">
                <span>{{ label }}</span>
              </label>
            </div>
            {% endfor %}

          </div>

          {% when 'checkbox' %}

          <div class="flex">

            {% assign options = field.options | newline_to_br | split: '<br />' %}
            {% for option in options %}
            {% assign value = option | split: ':' | first | strip %}
            {% assign label = option | split: ':' | last | strip %}
            <div class="field">
              <label class="field__checkbox">
                <input id="{{ field.key }}" name="{{ field.key }}" value="{{value }}" type="checkbox" x-model="profile.{{ field.key }}">
                <span>{{ label }}</span>
              </label>
            </div>
            {% endfor %}

          </div>

          {% when 'chips' %}

          <div

          class="flex flex-wrap fields--{{ field.key | handle }}"
          :class="{
            'hide-disabled':$data.collapsed
          }"

          x-data="{
            limit:{{ field.limit | default: 3 }},
            profile:profile,
            selected:profile.{{ field.key }} || [],
            editable:false,
            editing:false,
            saveable:false,
            collapsed:false,
            full:false,
            check:() => {
              
              $nextTick(()=>{

                $data.remote = customer.profiles[customer.edit||0].{{ field.key }} || [];

                $data.local = $data.profile.{{ field.key }} || [...$data.remote];

                $data.full = $data.local.length == $data.limit;
                
                $data.saveable = $data.remote.sort().join() != profile.{{ field.key }}.sort().join();

                $data.editable = $data.full && !$data.editing

                $data.collapsed = $data.full
                
              })

            }
          }" 

          x-init="check()">       
            {% assign options = field.options | newline_to_br | split: '<br />' %}
            {% for option in options %}
            {% assign value = option | split: ':' | first | strip %}
            {% assign label = option | split: ':' | last | strip %}

            <div class="field field--chip">
              <label class="field__chip">
                <input id="{{ field.key }}" name="{{ field.key }}" value="{{ value }}" type="checkbox" x-init="profile.{{ field.key }} = profile.{{ field.key }} || []" x-model="(profile.{{ field.key }})" class="sr-only" :disabled="$data.full && !profile.{{ field.key }}.includes('{{ value }}')" @input="$data.editing=true; check()">
                <span>{{ label }}</span>
              </label>
            </div>
            {% endfor %}

            {% if field.save %}
              <template x-if="$data.editable">
                <button class="button button--primary" @click.prevent="$data.editing=true; check()">Edit</button>
              </template>

              <template x-if="$data.saveable">
                <div class="basis-full">
                  <button class="button button--primary" @click.prevent="Customer.profile.save(Util.form.values(Util.select('.account-profile-editor .fields--{{ field.key | handle }}')[0])); $data.editing=false; check();">Save</button>
                </div>
              </template>
            {% endif %}

          </div>

          <style>
            .hide-disabled {
              .field:has([disabled]){display:none;}
            }
          </style>

          {% when 'swatches' %}

          <div 

          class="flex flex-col flex-wrap fields--{{ field.key | handle }}"
          :class="{
            'hide-disabled':$data.collapsed
          }"
        
          x-data="{
            limit:{{ field.limit | default: 3 }},
            profile:profile,
            selected:profile.{{ field.key }} || [],
            editable:false,
            editing:false,
            saveable:false,
            collapsed:false,
            full:false,
            check:() => {
              
              $nextTick(()=>{

                $data.remote = customer.profiles[customer.edit||0].{{ field.key }} || [];

                $data.local = $data.profile.{{ field.key }} || [...$data.remote];

                $data.full = $data.local.length == $data.limit;
                
                $data.saveable = $data.remote.sort().join() != profile.{{ field.key }}.sort().join();

                $data.editable = $data.full && !$data.editing

                $data.collapsed = $data.full
                
              })

            }
          }" 

          x-init="check()">
          <div class="flex flex-wrap gap-x-2">
            {% assign options = field.options | newline_to_br | split: '<br />' %}
            {% for option in options %}
            {% assign bits = option | split: ':' %}
            {% assign value = bits | first | strip %}
            {% assign label = bits | last | strip %}
            {% assign hex = bits[1] | strip %}

            <div class="field field--swatch">
              <label class="field__color">
                <input id="{{ field.key }}" name="{{ field.key }}" value="{{ value }}" type="checkbox" x-init="profile.{{ field.key }} = profile.{{ field.key }} || []" x-model="(profile.{{ field.key }})" class="sr-only" :disabled="$data.full && !profile.{{ field.key }}.includes('{{ value }}')" @input="$data.editing=true; check()">
                <span class="field__color-swatch" style="background: {{ hex }}"></span>
                <span class="field__color-label">{{ label }}</span>
              </label>
            </div>
            {% endfor %}
          </div>

            {% if field.save %}
              <div>
                
                <template x-if="$data.editable">
                  <button class="button button--primary" @click.prevent="$data.editing=true; check()" aria-label="edit">Edit</button>
                </template>

                <template x-if="$data.saveable">
                  <button class="button button--primary" @click.prevent="Customer.profile.save(Util.form.values(Util.select('.account-profile-editor .fields--{{ field.key | handle }}')[0])); $data.editing=false; check();" aria-label="save">Save</button>
                </template>

              </div>
            {% endif %}

          </div>

          <style>
            .hide-disabled {
              .field:has([disabled]){display:none;}
            }
          </style>  

          {% when 'old-swatches' %}

          <div class="flex max-checked-3">

            {% assign options = field.options | newline_to_br | split: '<br />' %}
            {% for option in options %}
            {% assign bits = option | split: ':' %}
            {% assign value = bits | first | strip %}
            {% assign label = bits | last | strip %}
            {% assign hex = bits[1] | strip %}
            <div class="field">
              <label class="field__color">
                <input id="{{ field.key }}" name="{{ field.key }}" value="{{value }}" type="checkbox" x-model="profile.{{ field.key }}" class="sr-only">
                <span class="field__color-swatch" style="background: {{ hex }}"></span>
                <span class="field__color-label">{{ label }}</span>
              </label>
            </div>
            {% endfor %}

          </div>

          {% when 'select' %}

          <div class="field field--floating-label">
            <label for="{{ field.key }}" class="">{{ field.title }}</label>
            <select id="{{ field.key }}" class="field__select" name="{{ field.key }}" x-model="profile.{{ field.key }}" {% if field.disabled %}disabled{% endif %}>
              <option value="" disabled selected>{{ field.title }}</option>
              {% assign options = field.options | newline_to_br | split: '<br />' %}
              {% for option in options %}
                {% assign value = option | split: ':' | first | strip %}
                {% assign label = option | split: ':' | last | strip %}
                <option value="{{ value }}">{{ label }}</option>
              {% endfor %}
            </select>
          </div>

          {% when 'text' %}

          {% assign extras = field.options | newline_to_br | split: '<br />' %}
          <div class="field floating-label w-full" >
            <label for="{{ field.key }}" class="">{{ field.title }}</label>             
            {% if field.key == 'birthday' %}
            <input 
              type="text"
              name="{{ field.key }}" 
              {% comment %} placeholder="MM/DD/YYYY"  {% endcomment %}
              placeholder="{{ field.title }}"
              maxlength="10" 
              class="field__input "
              x-model="$store.customer.birthday_date"
              x-ref="dateInput"
              @input="validateAndFormatDate"
              :value="profile.{{ field.key }}"
              :class="{ 'invalid': !isValid && date.trim() !== '' }"
              {% if extras[0] %}x-mask="{{ extras[0] }}"{% endif %} 
              :disabled="$store.customer.birthday == true"
              id="{{ field.key }}"
              >
              <div x-init="{% if settings.enable_logs %}console.log('$store.customer.birthday_date ', $store.customer.birthday_date){% endif %}" x-show="!isValid && date.trim() !== ''" class="text-red-500 text-sm mt-1">
                  Please enter a valid date in MM/DD/YYYY format.
              </div>
              {% else %}
              <input id="{{ field.key }}" class="field__input " name="{{ field.key }}" type="{% if field.key contains 'tel' or field.key contains 'phone' %}tel{% else %}text{% endif %}" placeholder="{{ field.title }}" {% if extras[0] %}x-mask="{{ extras[0] }}"{% endif %} {% if extras[1] %}pattern="{{ extras[1] | strip }}"{% endif %} x-model="profile.{{ field.key }}" {% if field.primary_disabled %}:disabled="$store.customer.edit === 0"{% endif %}>
              {% endif %}
          </div>

        {% endcase %}

        {{ field.subtext | replace: '<p>', '<p class="field__subtext rte">' }}
        {% if field.custom_liquid %}
          <div class="field__liquid rte">
            {{ field.custom_liquid }}
          </div>
        {% endif %}

      </div>

    {% unless field.profile_inclusion == 'all' %}
      </template>
    {% endunless %}
  </div>

  {% if field.inclusion_js != blank %}
    </template>
  {% endif %}
{% endfor %}
