<div class="slider-cart__header {{ settings.header_classes }} grid grid-cols-12 p-4 items-center" x-data>
  {% if settings.close %}
    <button 
      type="button" 
      class="slider-cart__header-content slider-cart__header-content--close {{ settings.close_button_classes }} col-span-1"
      @click="Modal.close()"
      aria-label="close-modal"
    >
    {% unless settings.display == 'text' %}{% render 'icon' icon:'x' width:settings.icon_size height:settings.icon_size %}{% endunless %}
    {% unless settings.display == 'icon' %}{{ 'sections.header.tools.cart' | t }}{% endunless %}
    </button>
  {% endif %}

  <div class="slider-cart__header-content slider-cart__header-content--announcements {{ settings.header_text_classes }} col-span-10 text-center text-sm">
    {{ settings.content }}
    {% render 'announcements' settings:settings blocks:blocks index:index %}
  </div>

</div>
