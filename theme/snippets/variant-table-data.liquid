(() => {
  try {
    const metaObject = JSON.parse($el.querySelector('[data-size-matrix]').textContent)

    return metaObject.sizes.reduce((sizeMatrix, size) => { 
    if (!sizeMatrix.settings) sizeMatrix.settings = metaObject.settings

     sizeMatrix.data[size.value] = size.data.split('\n').reduce((arr, cell) => { 
       const cellData = cell.split(':')
       const header = cellData[0].trim()
       const data = cellData.slice(-1)[0].trim();

       if (!sizeMatrix.headers.includes(header)) sizeMatrix.headers.push(header)
       arr.push(data); 

       return arr 
     }, []); 
     return sizeMatrix 
   }, { data: {}, headers: [] })
   } catch (err) {
    console.error('Error parsing JSON or processing data:', err);
    return null;
   }
 })()
