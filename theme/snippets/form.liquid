{% unless close %}
  {% if settings.shopify != blank %}

    {% capture _form %}
    {% form settings.shopify %} 
    {{ form.errors | default_errors }}
    {% endform %}
    {% endcapture %}
    {{  _form | remove:'</form>' }}
  
  {% else %} 

    <form accept-charset="UTF-8" {{ attributes }} x-data 
      {% if form_action != blank or settings.form_action != blank %}
      action="{{ form_action | default: settings.form_action }}"
      {% endif %}
      {% if form_method != blank or settings.form_method != blank %}
      method="{{ form_method | default: settings.form_method }}"
      {% endif %}
      {% if form_onsubmit != blank or settings.form_onsubmit != blank %}
      onsubmit="{% if settings.prevent_default %}event.preventDefault();{% endif %}{{ form_onsubmit | default: settings.form_onsubmit }}"
      {% endif %}
      >
     {% assign _fields = hidden_fields | default: settings.hidden_fields | newline_to_br | split: '<br />' %}
     {% for field in _fields %}
     <input type="hidden" name="{{ field | split: ':' | first | strip }}" value="{{ field | split: ':' | last | strip }}">
     {% endfor %}

    {% endif %}

{% else %}

  </form>

{% endunless %}
