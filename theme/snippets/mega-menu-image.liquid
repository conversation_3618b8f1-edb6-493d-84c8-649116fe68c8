<div class="mega__image_text lg:mb-0 mb-4 {{block.settings.width}} {{block.settings.desktop_width}} {{ block.settings.padding_vertical_desktop }} {{ block.settings.padding_horizontal_desktop }} {% if block.settings.show_desktop == true and block.settings.show_mobile == true %} block {% elsif block.settings.show_desktop == true and block.settings.show_mobile == false %} lg:block hidden {% elsif block.settings.show_desktop == false and block.settings.show_mobile == true %} lg:hidden block {% endif %}">

  {% liquid 
    assign overlay_text_wrapper_classes = ""
    assign overlay_text_classes = ""
    
    if block.settings.desktop_layout == "overlay_text" and block.settings.layout == "overlay_text"
      assign overlay_text_wrapper_classes = 'relative'
      assign overlay_text_classes = "absolute inset-0 flex flex-col"
      assign overlay_text_classes = overlay_text_classes | append: ' ' | append: block.settings.content_position
      assign overlay_text_classes = overlay_text_classes | append: ' ' | append: block.settings.content_position_desktop
    endif

    if block.settings.desktop_layout == "overlay_text" and block.settings.layout != "overlay_text"
      assign overlay_text_wrapper_classes = 'lg:relative'
      assign overlay_text_classes = "lg:absolute lg:inset-0 lg:flex lg:flex-col"
      assign overlay_text_classes = overlay_text_classes | append: ' ' | append: block.settings.content_position_desktop
    endif

    if block.settings.desktop_layout != "overlay_text" and block.settings.layout == "overlay_text"
      assign overlay_text_wrapper_classes = 'relative lg:static'
      assign overlay_text_classes = "absolute inset-0 flex flex-col lg:relative lg:inset-auto lg:block"
      assign overlay_text_classes = overlay_text_classes | append: ' ' | append: block.settings.content_position
    endif

    if block.settings.text_color contains 'light'
      assign button_class = 'button--tertiary'
    endif

    if block.settings.text_color contains 'dark'
      assign button_class = 'button--primary'
    endif

    if block.settings.text_color_desktop contains 'light'
      assign button_class = button_class | append: ' lg:button--tertiary'
    endif

    if block.settings.text_color_desktop contains 'dark'
      assign button_class = button_class | append: ' lg:button--primary'
    endif

    assign overlay_text_classes = overlay_text_classes | append: ' ' | append: block.settings.text_align
    assign overlay_text_classes = overlay_text_classes | append: ' ' | append: block.settings.text_align_desktop
    assign overlay_text_classes = overlay_text_classes | append: ' ' | append: block.settings.text_width
    assign overlay_text_classes = overlay_text_classes | append: ' ' | append: block.settings.text_width_desktop
  %}

  <a class="flex items-center {{ overlay_text_wrapper_classes }} {{ block.settings.layout | split: '|' | first }} {{ block.settings.desktop_layout | split: '|' | first }}" href="{{ block.settings.link }}">
    
    <div class="image-text--wrapper w-full">
      <div class="{{ block.settings.aspect_ratio }} {{ block.settings.aspect_ratio_mobile }} relative">
        {% render 'image' image: block.settings.image, image_class: "block absolute top-0 left-0 max-w-full w-full h-full object-cover object-center p-0" %}
      </div>
    </div>

    {% if block.settings.title != blank or block.settings.text != blank %}
      <div class="py-2 w-full {{ overlay_text_classes }} {% if block.settings.desktop_layout contains 'lg:flex-col' %}px-0{% else %}px-4{% endif %}">
        {% if block.settings.title != blank %}
          <p class=" block m-0 p-0 {{ block.settings.text_color }} {{ block.settings.text_color_desktop }}">
            {{ block.settings.title }}
          </p>
        {% endif %}
        {% if block.settings.addtl_text != blank %}
          <p class="whitespace-normal m-0 p-0 mb-4 {{ block.settings.text_color }} {{ block.settings.text_color_desktop }}">
            {{ block.settings.addtl_text }}
          </p>
        {% endif %}
        {% if block.settings.text != blank %}
          <p class="button {{ button_class }} inline-block text-xs focus:opacity-100 animate leading-normal whitespace-normal">
            {{ block.settings.text }}
          </p>
        {% endif %}
      </div>
    {% endif %}
  </a>
  </div>
