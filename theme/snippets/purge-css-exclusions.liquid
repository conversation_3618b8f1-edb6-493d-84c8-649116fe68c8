lg:mr-6
lg:pl-12
pt-12
pt-8
lg:pt-8
lg:top-12
w-full lg:w-4/5 lg:p-5 mt-6 grid grid-cols-2 gap-sm lg:gap-y-10 lg:gap-x-6 lg:grid-cols-3 xl:gap-x-8
lg:top-16
text-xs text-2xs text-sm text-md text-base text-lg text-xl text-2xl text-3xl text-4xl text-5xl text-6xl text-7xl
lg:absolute lg:transform lg:-translate-x-1/2 lg:left-1/2
lg:hidden lg:group-active:flex lg:flex justify-center w-full
max-lg:hidden max-lg:group-active:flex max-lg:flex justify-center w-full lg:hidden
group-active:max-lg:flex
group-active:lg:flex 
.zoomable {}
.swiper-slide-visible .zoomable {}

aspect-square


.pagination {
  @apply text-center flex items-center justify-center p-2;
  .pagination-bullet, .swiper-pagination-bullet {
    @apply bg-white w-3 h-3 rounded-full mx-3 cursor-pointer relative opacity-100;
    &.active, &.swiper-pagination-bullet-active {
      @apply opacity-100 bg-dark;
      &:before {
        @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 z-10 bg-center bg-no-repeat;
        content: "";
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24.1328 28'%3E%3Cpolygon points='24.133 19.178 23.678 8.126 12.238 0 3.476 6.903 0 11.486 0.975 20.023 3.469 22.373 14.635 28 13.577 25.012 24.133 19.178' style='fill:%23fff'/%3E%3C/svg%3E");
      }
      &:after {
        content: "";
        @apply bg-dark w-3 h-3 rounded-full cursor-pointer absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20;
      }
    }
  }
  &.pagination-dash {
    .pagination-bullet, .swiper-pagination-bullet {
      @apply bg-dark w-20 h-1 rounded-none mx-3;
      &.active, &.swiper-pagination-bullet-active {
        @apply opacity-100 bg-light;
      }
    }
  }
  &.swiper-pagination-progressbar.swiper-pagination-horizontal {
    height: 2px;
    @apply relative p-0 bg-gray-100;
    .swiper-pagination-progressbar-fill {
      @apply bg-dark;
    }
  }
}

order-1
order-2
order-3
order-4
order-5
order-6
order-7
order-8
order-9
order-10
order-11
order-12
lg:order-1
lg:order-2
lg:order-3
lg:order-4
lg:order-5
lg:order-6
lg:order-7
lg:order-8
lg:order-9
lg:order-10
lg:order-11
lg:order-12
max-lg:order-1
max-lg:order-2
max-lg:order-3
max-lg:order-4
max-lg:order-5
max-lg:order-6
max-lg:order-7
max-lg:order-8
max-lg:order-9
max-lg:order-10
max-lg:order-11
max-lg:order-12

pt-2xs
pt-xs
pt-sm
pt-md
pt-lg
pt-xl
pt-2xl
pt-3xl
pt-4xl
pt-5xl
pt-6xl
pt-7xl
pt-8xl

pb-2xs
pb-xs
pb-sm
pb-md
pb-lg
pb-xl
pb-2xl
pb-3xl
pb-4xl
pb-5xl
pb-6xl
pb-7xl
pb-8xl

py-2xs
py-xs
py-sm
py-md
py-lg
py-xl
py-2xl
py-3xl
py-4xl
py-5xl
py-6xl
py-7xl

px-2xs
px-xs
px-sm
px-md
px-lg
px-xl
px-2xl
px-3xl
px-4xl
px-5xl
px-6xl
px-7xl
px-8xl

-mx-2xs
-mx-xs
-mx-sm
-mx-md
-mx-lg
-mx-xl
-mx-2xl
-mx-3xl
-mx-4xl
-mx-5xl
-mx-6xl
-mx-7xl
-mx-8xl

mx-0
mx-2xs
mx-xs
mx-sm
mx-md
mx-lg
mx-xl
mx-2xl
mx-3xl
mx-4xl
mx-5xl
mx-6xl
mx-7xl
mx-8xl

lg:mx-0
lg:mx-2xs
lg:mx-xs
lg:mx-sm
lg:mx-md
lg:mx-lg
lg:mx-xl
lg:mx-2xl
lg:mx-3xl
lg:mx-4xl
lg:mx-5xl
lg:mx-6xl
lg:mx-7xl
lg:mx-8xl
