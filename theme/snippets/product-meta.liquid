<div class="flex gap-4 justify-start items-start mb-5">
  {% if show_image %}
    <div class="h-24 w-24 bg-tertiary">
      {% render 'image' 
      attributes: ':src="product.featured_image || product.images[0].src || product.images[0]" :alt="product.title" style="mix-blend-mode:multiply;"' 
        class: 'h-24 w-24 object-contain object-center opacity-100'
        sizes: '(min-width: 768px) 20vw, 50vw'
      %}
    </div>
  {% endif %}

  <div class="product-meta flex justify-between flex-col">
    <h3 class="product-meta__title type-item m-0" x-text="{{ settings.product_item_title_source }}"></h3>
    <h4 class="product-meta__type type-subline m-0" x-text="product.type"></h4> 
    {% render 'review-summary' product:product %}
    <div class="product-meta__footer">
      <span class="product-meta__color" x-text="product.variants[0].option1"></span>
      
      <div class="flex gap-2 items-center">
        <p class="product-item__prices">
          <template x-if="!!product.compare_at_price">
            <s class="product-item__compare-at-price type-item {{ settings.classes_product_item_price }}" x-text="money.format(product.compare_at_price).split('.')[0]"></s>
          </template>
          <span class="product-item__price type-item {{ settings.classes_product_item_price }}" x-text="money.format(product.price).split('.')[0]"></span>
        </p>
      </div>
    </div>
  </div>
</div>
