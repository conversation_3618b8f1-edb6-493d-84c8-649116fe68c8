<!-- <pre>{{ settings | json | replace: ',', '<br/>'}}</pre> -->
{% liquid
	assign product = product | default: settings.product
	assign review = settings.review | strip_html | truncatewords: 12, '...' 
%}
{% capture header %}
<div class="flex items-end justify-between review-item">
	<div class="w-full">
		<h3 class="m-0 truncate review-item__product">{{ product.title | split: ' - ' | first}}</h3>
		<div class="flex {% if product_type_mob_class and product_type_mob_class != blank %}{{ product_type_mob_class }}{% else %}items-center{% endif %} justify-between justify-flex-start">
			<h4 class="m-0 review-item__type">{{ product.type }}</h4>
			<div class="review-item__rating">
				{% render 'review-summary' product:product %}
			</div>
		</div>
	</div>
</div>
{% endcapture %}
{% 	
	render 'content-item' 
	item_classes:'content-item--review flex-col'
	header:header 
	same_height_review:same_height_review
	product_item_show_button:product_item_show_button
	text_item_1_text: settings.review
	text_item_1_classes:'content-item__review'
	text_item_2_text: settings.author
	text_item_2_classes:'content-item__author type-subline type--sm'
	text_item_2_element:'span'
	content_classes:'flex-col'
	text_stack_classes:'flex-col'
	button_media_text: 'Shop Now'
	button_media_classes: 'content-item__button-media button--secondary'
	
	button_1_text: 'Shop Now'
	button_1_classes: 'button--link'
	
	url:product.url 
	settings:settings
%}
