<svg class="allbrands hidden icon-library">
  <defs>

    <symbol id="icon-def-hamburger" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
    </symbol>

    <symbol id="icon-def-sidebar" viewBox="0 0 24 24">
      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="9" y1="3" x2="9" y2="21"></line>
    </symbol>

    <symbol id="icon-def-account-1" viewBox="0 0 24 24"><path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></symbol>

    <symbol id="icon-def-cart" width="24" height="24" viewBox="0 0 17.467 20.712">
    <g id="Group_37347" data-name="Group 37347" transform="translate(-334.406 -105.681)" fill="currentColor">
      <g id="ico-cart" transform="translate(-1490.594 49.431)">
        <path id="Path_1633" data-name="Path 1633" d="M7.486,7.991V6.743A3.743,3.743,0,0,0,0,6.743V7.991" transform="translate(1829.99 54)" fill="none" stroke="currentColor" stroke-width="1.5"></path>
        <g id="Path_1634" data-name="Path 1634" transform="translate(1826 60.743)" fill="none">
          <path d="M.248,0H15.219l1.248,12.476a3.743,3.743,0,0,1-3.743,3.743H2.743A3.743,3.743,0,0,1-1,12.476Z" stroke="none"></path>
          <path d="M 1.*************** 1.*************** L 0.*************** 12.************** C 0.**************** 13.************** 1.*************** 14.************** 2.*************2 14.************** L 12.72400951385498 14.************** C 13.93885898590088 14.************** 14.93122863769531 13.************** 14.96597766876221 12.************** L 13.86179351806641 1.*************** L 1.*************** 1.*************** M 0.2476396560668945 3.814697265625e-06 L 15.21927928924561 3.814697265625e-06 L 16.4669189453125 12.47637271881104 C 16.4669189453125 14.54352283477783 14.79115962982178 16.21928215026855 12.72400951385498 16.21928215026855 L 2.*************2 16.21928215026855 C 0.6757593154907227 16.21928215026855 -1 14.54352283477783 -1 12.47637271881104 L 0.2476396560668945 3.814697265625e-06 Z" stroke="none" fill="currentColor"></path>
        </g>
      </g>
    </g>
  </symbol>

  <symbol id="icon-def-shipping" xmlns="http://www.w3.org/2000/symbol" xmlns:xlink="http://www.w3.org/1999/xlink"  viewBox="0 0 50 50">
    <defs>
      <clipPath id="clip-path">
        <rect id="Rectangle_383" data-name="Rectangle 383" width="50" height="50" fill="#fff" stroke="#707070" stroke-width="1"/>
      </clipPath>
    </defs>
    <g id="ico-shipping" clip-path="url(#clip-path)">
      <g id="ico-free_shipping" transform="translate(1.875 2.5)">
        <path id="Path_8275" data-name="Path 8275" d="M18.6,0H12.162a1.846,1.846,0,0,0-1.6.925L.246,18.846a1.846,1.846,0,0,0,0,1.842L10.562,38.61a1.846,1.846,0,0,0,1.6.925H32.792a1.846,1.846,0,0,0,1.6-.925L44.711,20.688a1.846,1.846,0,0,0,0-1.842L34.392.925A1.846,1.846,0,0,0,32.792,0H26.631" transform="translate(7.059 44.957) rotate(-90)" fill="none" stroke="#797979" stroke-linecap="round" stroke-width="2"/>
        <path id="Path_8276" data-name="Path 8276" d="M10.318,0,0,18.944,10.261,37.784" transform="translate(7.883 21.863) rotate(-90)" fill="none" stroke="#797979" stroke-linecap="round" stroke-width="2"/>
        <path id="Path_8277" data-name="Path 8277" d="M17.75,0,7.056,19.179H0" transform="translate(16.875 23.997) rotate(-90)" fill="none" stroke="#797979" stroke-linecap="round" stroke-width="2"/>
        <path id="Path_8279" data-name="Path 8279" d="M5.3,0,0,10.734" transform="translate(0 24.501) rotate(-90)" fill="none" stroke="#797979" stroke-linecap="round" stroke-width="2" stroke-dasharray="4 3"/>
        <path id="Path_8280" data-name="Path 8280" d="M3.775,0,0,7.43" transform="translate(2.064 20.195) rotate(-90)" fill="none" stroke="#797979" stroke-linecap="round" stroke-width="2" stroke-dasharray="4 3"/>
        <path id="Path_8282" data-name="Path 8282" d="M1.949,0,0,4.046" transform="translate(4.842 26.962) rotate(-90)" fill="none" stroke="#797979" stroke-linecap="round" stroke-width="2" stroke-dasharray="4 3"/>
        <path id="Path_8284" data-name="Path 8284" d="M22.431,0H0" transform="translate(26.826 44.294) rotate(-90)" fill="none" stroke="#797979" stroke-linecap="round" stroke-width="2"/>
        <path id="Path_8278" data-name="Path 8278" d="M3.483,0,0,6.4" transform="translate(17.257 30.654) rotate(-90)" fill="none" stroke="#797979" stroke-linecap="round" stroke-width="1"/>
        <path id="Path_8281" data-name="Path 8281" d="M2.177,0,0,4" transform="translate(19.656 32.499) rotate(-90)" fill="none" stroke="#797979" stroke-linecap="round" stroke-width="1"/>
        <path id="Path_8283" data-name="Path 8283" d="M0,3.6,1.959,0H5.673L3.688,3.6Z" transform="translate(20.055 27.577) rotate(-90)" fill="none" stroke="#797979" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
      </g>
    </g>
  </symbol>

  <symbol id="icon-def-account" width="24" height="24" viewBox="0 0 24 24">
  <g id="Group_37347" data-name="Group 37347" transform="translate(-2668 7191)">
    <g id="Subtraction_24" data-name="Subtraction 24" transform="translate(2668 -7191)" fill="none">
      <path d="M12,24A12,12,0,0,1,3.515,3.515,12,12,0,1,1,20.485,20.485,11.922,11.922,0,0,1,12,24ZM12,5.5A3.5,3.5,0,1,0,15.5,9,3.5,3.5,0,0,0,12,5.5Z" stroke="none"></path>
      <path d="M 11.************** 22.************** C 14.************** 22.************** 17.************** 21.************** 19.************** 19.************** C 21.************** 17.************** 22.************** 14.************** 22.************** 11.************** C 22.************** 9.*************** 21.************** 6.*************** 19.************** 4.*************** C 17.************** 2.*************** 14.************** 1.*************** 11.************** 1.*************** C 9.*************** 1.*************** 6.*************** 2.*************** 4.*************** 4.*************** C 2.*************** 6.*************** 1.*************** 9.*************** 1.*************** 11.************** C 1.*************** 14.************** 2.*************** 17.************** 4.*************** 19.************** C 6.*************** 21.************** 9.*************** 22.************** 11.************** 22.************** M 11.************** 3.999900579452515 C 14.756760597229 3.999900579452515 16.99979972839355 6.242940902709961 16.99979972839355 9.000000953674316 C 16.99979972839355 11.75706100463867 14.756760597229 14.00010108947754 11.************** 14.00010108947754 C 9.242640495300293 14.00010108947754 6.999600887298584 11.75706100463867 6.999600887298584 9.000000953674316 C 6.999600887298584 6.242940902709961 9.242640495300293 3.999900579452515 11.************** 3.999900579452515 M 11.************** 24.00030136108398 C 8.79469108581543 24.00030136108398 5.781370639801025 22.75200080871582 3.514830589294434 20.4853515625 C 1.248260736465454 18.21865081787109 6.805419729971618e-07 15.2050609588623 6.805419729971618e-07 11.************** C 6.805419729971618e-07 8.794731140136719 1.248260736465454 5.781420707702637 3.514830589294434 3.514830589294434 C 5.781420707702637 1.248260736465454 8.794731140136719 6.805419729971618e-07 11.************** 6.805419729971618e-07 C 15.2050609588623 6.805419729971618e-07 18.21865081787109 1.248260736465454 20.4853515625 3.514830589294434 C 22.75200080871582 5.781370639801025 24.00030136108398 8.79469108581543 24.00030136108398 11.************** C 24.00030136108398 15.20510101318359 22.75200080871582 18.21870040893555 20.4853515625 20.4853515625 C 18.21870040893555 22.75200080871582 15.20510101318359 24.00030136108398 11.************** 24.00030136108398 Z M 11.************** 5.499900817871094 C 10.06974029541016 5.499900817871094 8.499600410461426 7.070040702819824 8.499600410461426 9.000000953674316 C 8.499600410461426 10.92996025085449 10.06974029541016 12.50010108947754 11.************** 12.50010108947754 C 13.92966079711914 12.50010108947754 15.49980068206787 10.92996025085449 15.49980068206787 9.000000953674316 C 15.49980068206787 7.070040702819824 13.92966079711914 5.499900817871094 11.************** 5.499900817871094 Z" stroke="none" fill="currentColor"></path>
    </g>
    <g id="Intersection_1" data-name="Intersection 1" transform="translate(2668 -7191)" fill="none">
      <path d="M2.222,18.957a11,11,0,0,1,19.557,0,12,12,0,0,1-19.557,0Z" stroke="none"></path>
      <path d="M 11.99969863891602 22.************** C 13.70205783843994 22.************** 15.39193820953369 22.08397102355957 16.88666725158691 21.29631042480469 C 18.05671882629395 20.67974662780762 19.10981369018555 19.83700561523438 19.97358512878418 18.83139419555664 C 18.22975921630859 16.13779449462891 15.25300312042236 14.49960041046143 11.99969863891602 14.49960041046143 C 8.746566772460938 14.49960041046143 5.770007133483887 16.13784027099609 4.026454448699951 18.83133125305176 C 4.890154361724854 19.83712577819824 5.943126201629639 20.67989158630371 7.113128185272217 21.29646110534668 C 8.607598304748535 22.08402061462402 10.29734802246094 22.************** 11.99969863891602 22.************** M 11.99969863891602 24.00030136108398 C 7.966008186340332 24.00030136108398 4.39710807800293 22.00984001159668 2.221508264541626 18.95708084106445 C 4.049948215484619 15.41856002807617 7.742308139801025 12.99960041046143 11.99969863891602 12.99960041046143 C 16.25709915161133 12.99960041046143 19.94993782043457 15.41868019104004 21.77863883972168 18.95720100402832 C 19.60289764404297 22.00943946838379 16.03353881835938 24.00030136108398 11.99969863891602 24.00030136108398 Z" stroke="none" fill="currentColor"></path>
    </g>
  </g>
</symbol>



<symbol id="icon-def-filter" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25H12" />
</symbol>


    <symbol id="icon-def-bag" viewBox="0 0 24 24">
      <path d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
    </symbol>


    <symbol id="icon-def-check" viewBox="0 0 24 24">
     <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
    </symbol>


    <symbol id="icon-def-search" viewBox="0 0 24 24"><path  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></symbol>
    <symbol id='icon-def-cart-1' viewBox='0 0 24 24'> <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" /> </symbol>
    <symbol id="icon-def-heart" viewBox="0 0 24 24">
      <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
    </symbol>
    <symbol id="icon-def-x" fill="none" viewBox="0 0 24 24">
      <path d="M6 18L18 6M6 6l12 12" />
    </symbol>

    <symbol id='icon-def-play' viewBox='0 0 24 24'> <path d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.348a1.125 1.125 0 010 1.971l-11.54 6.347a1.125 1.125 0 01-1.667-.985V5.653z" /> </symbol>
    <symbol id='icon-def-arrow-down' viewBox='0 0 24 24'> <path d="M19.5 13.5L12 21m0 0l-7.5-7.5M12 21V3" /> </symbol>
    <symbol id='icon-def-arrow-left' viewBox='0 0 24 24'> <path d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" /> </symbol>
    <symbol id='icon-def-arrow-right' viewBox='0 0 24 24'> <path d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" /> </symbol>
    <symbol id='icon-def-arrow-up' viewBox='0 0 24 24'> <path d="M4.5 10.5L12 3m0 0l7.5 7.5M12 3v18" /> </symbol>
    <symbol id='icon-def-arrow-up-right' viewBox='0 0 24 24'>
      <line x1="7" y1="17" x2="17" y2="7"></line><polyline points="7 7 17 7 17 17"></polyline>
    </symbol>
    <symbol id='icon-def-chevron-down' viewBox='0 0 24 24'> <path d="M19.5 8.25l-7.5 7.5-7.5-7.5" /> </symbol>
    <symbol id='icon-def-chevron-left' viewBox='0 0 24 24'> <path d="M15.75 19.5L8.25 12l7.5-7.5" /> </symbol>
    <symbol id='icon-def-chevron-right' viewBox='0 0 24 24'> <path d="M8.25 4.5l7.5 7.5-7.5 7.5" /> </symbol>
    <symbol id='icon-def-chevron-up' viewBox='0 0 24 24'> <path d="M4.5 15.75l7.5-7.5 7.5 7.5" /> </symbol>
    <symbol id='icon-def-menu' viewBox='0 0 24 24'> <path d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" /> </symbol>
    <symbol id="icon-def-minus" viewBox="0 0 24 24"><line x1="5" y1="12" x2="19" y2="12"></line></symbol>
    <symbol id="icon-def-plus" viewBox="0 0 24 24"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></symbol>

    <symbol id="icon-def-arrows-width" viewBox="0 0 24 24"><polyline points="15 3 21 3 21 9" style="transform:rotate(45deg);"></polyline><polyline points="9 21 3 21 3 15"></polyline><line x1="21" y1="3" x2="14" y2="10"></line><line x1="3" y1="21" x2="10" y2="14"></line></symbol>

     <symbol id='icon-def-arrows-pointing-out'> 
      <path d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15" />
    </symbol>
   
    <symbol id='icon-def-stack'> 
      <path d="M6.429 9.75 2.25 12l4.179 2.25m0-4.5 5.571 3 5.571-3m-11.142 0L2.25 7.5 12 2.25l9.75 5.25-4.179 2.25m0 0L21.75 12l-4.179 2.25m0 0 4.179 2.25L12 21.75 2.25 16.5l4.179-2.25m11.142 0-5.571 3-5.571-3" />
    </symbol>

    <symbol id='icon-def-droplet'> 
      <path d="M12 2.69l5.66 5.66a8 8 0 1 1-11.31 0z"></path>
    </symbol>

    <symbol id='icon-def-wind'> 
      <path d="M9.59 4.59A2 2 0 1 1 11 8H2m10.59 11.41A2 2 0 1 0 14 16H2m15.73-8.27A2.5 2.5 0 1 1 19.5 12H2"></path>
    </symbol>

    <symbol id='icon-def-thermometer'>
      <path d="M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z"></path>
    </symbol>

    <symbol id='icon-def-feather'>
      <path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5z"></path><line x1="16" y1="8" x2="2" y2="22"></line><line x1="17.5" y1="15" x2="9" y2="15"></line>
    </symbol>
    
    <symbol id="icon-def-speaker" viewBox="0 0 512 512" fill="currentColor"  stroke-color="currentColor">
      <path d="M255.37,45.08a18.83,18.83,0,0,0-20.47,4L134.08,148.9H65.55a18.79,18.79,0,0,0-18.79,18.8V344.3a18.79,18.79,0,0,0,18.79,18.8h68.53L234.9,462.93a18.8,18.8,0,0,0,32-13.36V62.43A18.8,18.8,0,0,0,255.37,45.08Z"/><path d="M336.69,147.81a18.8,18.8,0,1,0-26.59,26.58,115.54,115.54,0,0,1,0,163.22,18.8,18.8,0,0,0,26.59,26.59C396.34,304.54,396.34,207.47,336.69,147.81Z"/><path d="M396,88.52a18.79,18.79,0,0,0-26.58,26.58c77.69,77.69,77.7,204.1,0,281.81A18.79,18.79,0,1,0,396,423.49C488.33,331.13,488.33,180.86,396,88.52Z"/>
    </symbol>

    <symbol id="icon-def-star" viewBox="0 0 24 24" fill="currentColor"  stroke-color="currentColor">
      <path fill-rule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clip-rule="evenodd" />
    </symbol>

    <symbol id="icon-def-mail" viewBox="0 0 20 16" fill="currentColor"  stroke-color="currentColor">
      <g id="Group_21191" data-name="Group 21191" transform="translate(-50 -586)">
      <rect id="Rectangle_10339" data-name="Rectangle 10339" width="20" height="16" rx="3" transform="translate(50 586)" fill="#fff"></rect>
      <path id="Path_19365" data-name="Path 19365" d="M905.307,592.5l6.543,4.543,6.543-4.543" transform="translate(-851.769 -2.168)" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
      </g>
    </symbol>

    <symbol id="icon-def-chat" viewBox="0 0 20 16.333" fill="currentColor"  stroke-color="currentColor">
      <g id="Group_21152" data-name="Group 21152" transform="translate(-50 -436)">
        <path id="Union_4" data-name="Union 4" d="M0,10.862V3A3,3,0,0,1,3,0H17a3,3,0,0,1,3,3v7.862a3,3,0,0,1-3,3H3.422L0,16.333Z" transform="translate(50 436)" fill="#fff"></path>
        <path id="Union_5" data-name="Union 5" d="M8.516,1.094a1.115,1.115,0,0,1,2.229,0,1.115,1.115,0,0,1-2.229,0Zm-4.258,0a1.115,1.115,0,0,1,2.229,0,1.115,1.115,0,0,1-2.229,0ZM0,1.094A1.1,1.1,0,0,1,1.115,0,1.105,1.105,0,0,1,2.23,1.094,1.1,1.1,0,0,1,1.115,2.189,1.1,1.1,0,0,1,0,1.094Z" transform="translate(54.658 441.837)" fill="none" stroke="currentColor" stroke-width="1"></path>
      </g>
    </symbol>

    <symbol  id="icon-def-gift" viewBox="0 0 18 18">
      <path fill="currentColor" stroke="none" d="M18,5c0-2.757-2.243-5-5-5h-3C9.634,0,9.295,0.106,9,0.278C8.705,0.106,8.366,0,8,0H5C2.243,0,0,2.243,0,5v3c0,0.366,0.106,0.705,0.278,1C0.106,9.295,0,9.634,0,10v3c0,2.757,2.243,5,5,5h3c0.366,0,0.705-0.106,1-0.278C9.295,17.894,9.634,18,10,18h3c2.757,0,5-2.243,5-5v-3c0-0.366-0.106-0.705-0.278-1C17.894,8.705,18,8.366,18,8V5z M8,16H5c-1.654,0-3-1.346-3-3l-0.001-3l0,0C1.999,10,2,10,2,10h4.585l-2.889,2.889l1.414,1.414L8,11.414V16z M8,6.586L5.111,3.697L3.697,5.111L6.586,8H2V5c0-1.654,1.346-3,3-3h3V6.586z M16,13c0,1.654-1.346,3-3,3h-3v-4.587l2.89,2.889l1.414-1.414L11.415,10H16V13z M16,8h-4.586l2.889-2.889l-1.414-1.414L10,6.586V2h3c1.654,0,3,1.346,3,3V8z"></path>
    </symbol>

    <symbol id="icon-def-globe"  fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
      <path stroke-linecap="round" stroke-linejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418" />
    </symbol>


  <symbol id="icon-def-shoppay" viewBox="0 0 424 102" fill="none">
      <path d="M108.665 32.87c-3.402-7.136-9.852-11.746-19.57-11.746a19.48 19.48 0 00-15.303 7.868l-.355.432V1.454a.61.61 0 00-.61-.61h-13.74a.61.61 0 00-.599.61v80.23a.598.598 0 00.598.598h14.717a.61.61 0 00.609-.598V47.475c0-6.648 4.433-11.358 11.525-11.358 7.757 0 9.718 6.383 9.718 12.888v32.679a.598.598 0 00.599.598h14.682a.609.609 0 00.61-.598v-34.63c0-1.185 0-2.349-.155-3.48a30.617 30.617 0 00-2.726-10.704zM35.184 44.76s-7.491-1.76-10.25-2.47c-2.76-.71-7.58-2.217-7.58-5.863 0-3.646 3.89-4.81 7.834-4.81 3.945 0 8.334.954 8.677 5.331a.632.632 0 00.632.576l14.505-.055a.618.618 0 00.587-.414.62.62 0 00.034-.251C48.725 22.797 36.436 17.788 25.1 17.788c-13.442 0-23.271 8.865-23.271 18.64 0 7.136 2.017 13.829 17.874 18.483 2.782.809 6.56 1.862 9.863 2.781 3.967 1.109 6.105 2.782 6.105 5.42 0 3.058-4.432 5.185-8.787 5.185-6.305 0-10.782-2.338-11.148-6.538a.632.632 0 00-.632-.554l-14.472.067a.631.631 0 00-.632.654C.665 75.145 13.431 82.27 25.332 82.27c17.73 0 25.743-9.973 25.743-19.315.022-4.388-.987-14.384-15.891-18.196zm186.611-23.658c-7.369 0-13.542 4.078-17.52 8.998v-8.422a.597.597 0 00-.587-.599h-13.763a.601.601 0 00-.599.599v78.678a.598.598 0 00.599.587h14.727a.587.587 0 00.587-.587V74.492h.222c2.338 3.568 8.732 7.846 17.087 7.846 15.714 0 28.812-13.032 28.812-30.64.011-16.9-13.021-30.596-29.565-30.596zm-1.363 46.242a15.613 15.613 0 1115.226-15.647 15.4 15.4 0 01-4.362 10.987 15.404 15.404 0 01-10.864 4.66zm-74.689-49.7c-13.73 0-20.578 4.666-26.075 8.4l-.166.11a1.364 1.364 0 00-.41 1.807l5.43 9.353a1.373 1.373 0 00.964.665 1.342 1.342 0 001.108-.3l.433-.354c2.825-2.372 7.358-5.54 18.328-6.405 6.106-.488 11.381 1.108 15.27 4.743 4.278 3.945 6.838 10.316 6.838 17.043 0 12.378-7.292 20.157-19.005 20.312-9.652-.055-16.135-5.086-16.135-12.522 0-3.945 1.785-6.516 5.264-9.087a1.349 1.349 0 00.41-1.728l-4.876-9.22a1.42 1.42 0 00-.853-.687 1.371 1.371 0 00-1.108.144c-5.474 3.247-12.19 9.186-11.824 20.6.443 14.528 12.522 25.62 28.224 26.075h1.862c18.661-.61 32.136-14.462 32.136-33.245 0-17.242-12.566-35.704-35.815-35.704z" fill="rgb(90, 49, 244)"></path>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M281.734 1.044h125.652c8.672 0 15.703 7.03 15.703 15.703V85.54c0 8.672-7.031 15.702-15.703 15.702H281.734c-8.672 0-15.702-7.03-15.702-15.702V16.747c0-8.673 7.03-15.703 15.702-15.703zm28.191 54.488c10.483 0 17.985-7.647 17.985-18.362 0-10.66-7.502-18.351-17.985-18.351h-18.506a.829.829 0 00-.831.83v50.787a.84.84 0 00.831.831h6.959a.831.831 0 00.831-.83V56.362a.83.83 0 01.832-.831h9.884zm-.532-29c5.696 0 9.896 4.498 9.896 10.638 0 6.15-4.2 10.638-9.896 10.638h-9.352a.83.83 0 01-.832-.82V27.363a.844.844 0 01.832-.831h9.352zm20.567 34.995a9.875 9.875 0 014.123-8.467c2.704-2.028 6.892-3.08 13.109-3.324l6.593-.222v-1.95c0-3.89-2.615-5.54-6.815-5.54s-6.848 1.484-7.469 3.911a.793.793 0 01-.797.576h-6.505a.816.816 0 01-.807-.588.815.815 0 01-.024-.354c.975-5.762 5.74-10.14 15.902-10.14 10.793 0 14.682 5.02 14.682 14.606v20.368a.823.823 0 01-.239.595.835.835 0 01-.592.247h-6.571a.836.836 0 01-.592-.247.833.833 0 01-.239-.595v-1.518a.619.619 0 00-.783-.65.62.62 0 00-.325.218c-1.962 2.138-5.153 3.69-10.239 3.69-7.458.022-12.412-3.879-12.412-10.616zm23.825-4.433V55.52l-8.532.444c-4.499.232-7.126 2.105-7.126 5.252 0 2.848 2.405 4.433 6.594 4.433 5.696 0 9.064-3.08 9.064-8.544v-.011zm14.772 23.626v5.928a.854.854 0 00.609.864c1.159.316 2.357.462 3.558.433 6.371 0 12.189-2.327 15.514-11.392l14.627-39.018a.847.847 0 00-.112-.753.848.848 0 00-.675-.355h-6.815a.829.829 0 00-.798.576l-8.056 24.712a.854.854 0 01-1.596 0l-9.286-24.778a.855.855 0 00-.787-.543h-6.649a.841.841 0 00-.786 1.108l13.674 35.128a.82.82 0 010 .565l-.432 1.363a7.877 7.877 0 01-7.945 5.618 16.45 16.45 0 01-3.048-.288.839.839 0 00-.918.472.826.826 0 00-.079.36z" fill="rgb(90, 49, 244)"></path>
    </symbol>

    <symbol id="icon-def-afterpay" viewBox="0 0 165.0003 31" fill="currentColor" stroke="none"><path d="M162.673,6.5013,152.103.6088a4.656,4.656,0,0,0-6.981,3.8936v.6045a1.5186,1.5186,0,0,0,.791,1.3233l1.996,1.1124a.8777.8777,0,0,0,1.317-.734V5.305a1.0105,1.0105,0,0,1,1.515-.8458l9.155,5.1051a.9583.9583,0,0,1,0,1.6891l-9.155,5.1051a1.0105,1.0105,0,0,1-1.515-.8458v-.8a4.6563,4.6563,0,0,0-6.983-3.8936l-10.57,5.8925a4.4207,4.4207,0,0,0,0,7.7872l10.57,5.8925a4.6578,4.6578,0,0,0,6.983-3.8936v-.6045a1.5216,1.5216,0,0,0-.791-1.3233l-1.996-1.115a.8777.8777,0,0,0-1.317.7341v1.5036a1.01,1.01,0,0,1-1.515.8457l-9.155-5.1051a.9609.9609,0,0,1,0-1.6915l9.155-5.1052a1.01,1.01,0,0,1,1.515.8458v.8a4.6552,4.6552,0,0,0,6.981,3.8936l10.57-5.8925A4.4164,4.4164,0,0,0,162.673,6.5013ZM36.5482,18.4136V11.0505h2.7135V7.4338H36.5482V3.3878H32.22v4.046H26.646V6.4255c0-1.3944.55-1.9252,2.0615-1.9252h.9492V1.2823H27.5742c-3.5707,0-5.2535,1.1277-5.2535,4.5768V7.4313H19.9175V11.048h2.4032V24.0165h4.328V11.048h5.5742v8.1275c0,3.3832,1.3384,4.8437,4.8407,4.8437h2.2323V20.3364h-.86C36.9584,20.3364,36.5482,19.8054,36.5482,18.4136ZM13.7016,9.5241A6.9457,6.9457,0,0,0,8.38,7.1367,8.3761,8.3761,0,0,0,0,15.7264a8.2255,8.2255,0,0,0,8.2747,8.5569,6.9716,6.9716,0,0,0,5.4243-2.4206v2.1565h4.2254V7.4338H13.7016ZM8.9924,20.4709a4.648,4.648,0,0,1-4.67-4.7445,4.6278,4.6278,0,0,1,4.67-4.7443,4.6079,4.6079,0,0,1,4.67,4.7443A4.6081,4.6081,0,0,1,8.9924,20.4709ZM49.7713,7.1341c-5.0825,0-8.9293,3.6168-8.9293,8.6254s3.639,8.5238,8.8242,8.5238c4.2911,0,7.5541-2.2555,8.5848-5.7707h-4.43a4.5864,4.5864,0,0,1-4.0518,2.1565,4.2268,4.2268,0,0,1-4.4305-3.8479H58.4929a5.3338,5.3338,0,0,0,.1026-1.0617A8.4509,8.4509,0,0,0,49.7713,7.1341ZM45.375,14.233A4.2107,4.2107,0,0,1,49.7,10.7838a4.0761,4.0761,0,0,1,4.257,3.4492Zm60.318-4.7089a6.9466,6.9466,0,0,0-5.322-2.3874,8.3756,8.3756,0,0,0-8.3772,8.59,8.2254,8.2254,0,0,0,8.2742,8.5569,6.9728,6.9728,0,0,0,5.425-2.4206v2.1565h4.223V7.4338h-4.223Zm-4.707,10.9468a4.648,4.648,0,0,1-4.67-4.7445,4.67,4.67,0,1,1,9.34,0A4.6081,4.6081,0,0,1,100.986,20.4709ZM64.9007,9.0593V7.4363H60.678V24.0217h4.3595V14.2025c-.0053-1.7322.9176-3.0275,2.3769-3.2179a5.5928,5.5928,0,0,1,3.0475.7569V7.51a4.3992,4.3992,0,0,0-1.8538-.3733A4.2715,4.2715,0,0,0,64.9007,9.0593ZM125.074,7.4338l-4.249,9.5727-4.141-9.5727h-5.096l7.078,14.3274-3.8216,8.2431h4.49L129.638,7.4338ZM82.16,7.1367A6.8889,6.8889,0,0,0,76.7355,9.59V7.4338H72.5128V30.0043h4.3595V22.0634a6.7475,6.7475,0,0,0,5.1851,2.2224,8.3779,8.3779,0,0,0,8.3772-8.5924A8.2862,8.2862,0,0,0,82.16,7.1367Zm-.7178,13.3342a4.6481,4.6481,0,0,1-4.67-4.7445,4.67,4.67,0,1,1,9.34,0A4.67,4.67,0,0,1,81.4421,20.4709Z"/></path></symbol>

    <symbol id="icon-def-klarna" viewBox="0 0 452.9 121.1" fill="currentColor"><path d="M79.7,0H57.4a57.0734,57.0734,0,0,1-23,46l-8.8,6.6L59.8,99.2H87.9L56.4,56.3A78.8433,78.8433,0,0,0,79.7,0ZM0,99.2H22.8V0H0Zm94.5,0H116V0H94.5ZM304.6,28.7c-8.2,0-16,2.5-21.2,9.6V30.6H263V99.2h20.7v-36c0-10.4,7-15.5,15.4-15.5,9,0,14.2,5.4,14.2,15.4V99.3h20.5V55.6C333.8,39.6,321.1,28.7,304.6,28.7ZM181,35a35.7109,35.7109,0,0,0-20.4-6.3,36.2,36.2,0,1,0,0,72.4A35.7109,35.7109,0,0,0,181,94.8v4.4h20.5V30.6H181ZM162.3,82.5c-10.3,0-18.6-7.9-18.6-17.6s8.3-17.6,18.6-17.6,18.6,7.9,18.6,17.6S172.6,82.5,162.3,82.5Zm71-43V30.6h-21V99.2h21.1v-32c0-10.8,11.7-16.6,19.8-16.6h.2v-20C245.1,30.6,237.4,34.2,233.3,39.5ZM397.6,35a35.7109,35.7109,0,0,0-20.4-6.3,36.2,36.2,0,1,0,0,72.4,35.7109,35.7109,0,0,0,20.4-6.3v4.4h20.5V30.6H397.6ZM378.9,82.5c-10.3,0-18.6-7.9-18.6-17.6s8.3-17.6,18.6-17.6,18.6,7.9,18.6,17.6C397.6,74.6,389.2,82.5,378.9,82.5Zm53-43.9a4.9,4.9,0,1,0-4.9-4.9A4.9736,4.9736,0,0,0,431.9,38.6Zm0-8.9a3.9273,3.9273,0,0,1,3.9,4,3.9922,3.9922,0,0,1-3.9,4,3.9273,3.9273,0,0,1-3.9-4A3.9922,3.9922,0,0,1,431.9,29.7Zm-.7,4.6h1l.8,1.9h1l-.9-2.1a1.5509,1.5509,0,0,0,.9-1.5,1.583,1.583,0,0,0-1.8-1.6h-1.9v5.2h.9Zm0-2.5h1c.6,0,.9.3.9.8s-.2.8-.9.8h-1ZM440,74.9a12.9,12.9,0,1,0,12.9,12.9A12.9315,12.9315,0,0,0,440,74.9Z"/></symbol>
   
      {{ settings.icons }}
  </defs>
</svg>

<script>
  window.addEventListener('DOMContentLoaded', e=>{
    Array.from(document.querySelectorAll('use')).filter(use=>!document.querySelector(use.href.baseVal)).forEach(use=>use.parentNode.remove())
  })
</script>


