{
method: 'POST',
    headers: {
        "Content-Type": "application/json"
    },
    mode: "cors",
    body: `{
        "store_name": "${store.name}",
        "email": "${customer.identity.email !== 'undefined' ? customer.identity.email : ''}",
        "id": "${customer.identity.id !== 'undefined' ? customer.identity.id : ''}",
        "phone": "${customer.identity.phone !== 'undefined' ? customer.identity.phone : ''}",
        "cookie": "${Util.cookies.get('__exponea_etc__') !== 'undefined' ? Util.cookies.get('__exponea_etc__') : ''}",{% if product %}
  "catalogFilter": [
  {
                  "property": "item_style",
                  "constraint": {
                      "type": "string",
                      "operator": "does not contain",
                      "operands": [{"type": "constant", "value": {{ product.title | split: ' - ' | first | json }} }]
  }
              }
          ],{% endif %}
"recommendation_id": "{{ settings.recomendation_id }}",
"recommendations_size": {{ settings.limit }}
}`
}