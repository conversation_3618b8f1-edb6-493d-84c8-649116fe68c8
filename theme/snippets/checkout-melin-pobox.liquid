{% assign nsA = 'checkout-pobox' %}

<div id="pobox_modal" class="{{ nsA }}">
  <div class="{{ nsA }}__container" style="">
    <div class="{{ nsA }}__inner" style="">
			<h1 class="{{ nsA }}__title">Unfortunately we do not ship to P.O. Boxes.</h1>
			<button
				onclick="document.querySelector('#checkout_shipping_address_address1').value='';
				document.querySelector('#checkout_shipping_address_address2').value='';
				document.querySelector('#pobox_modal').style.display = 'none';
				document.querySelector('#checkout_shipping_address_address1').focus();
				return false;"
				class="{{ nsA }}__button"
				data-btn-alt-text="{{ settings.melin-pob-button-text }}"
			>
				<span>{{ settings.melin-pob-button-text }}</span>
			</button>
    </div>
  </div>
</div>

<script>
	let addressField1 = document.querySelector('#checkout_shipping_address_address1');
	let addressField2 = document.querySelector('#checkout_shipping_address_address2');
	let pobox_modal = document.querySelector('#pobox_modal');
	addressField2.onchange = function () {
		if (addressField2.value) {
			if(addressField2.value.toLowerCase().replace(/ /g, '').replace(/[^a-z,0-9]/g, '').replace(/\./g, '').replace(/\./g, '').indexOf('pobox') > -1 || addressField2.value.toLowerCase().replace(/ /g, '**').replace(/[^a-z,0-9]/g, '**').replace(/\./g, '**').indexOf('box**') > -1){
				pobox_modal.style.display = 'block';
			}
		}
  };

	addressField1.onchange = function () {
		if (addressField1.value) {
			if(addressField1.value.toLowerCase().replace(/ /g, '').replace(/[^a-z,0-9]/g, '').replace(/\./g, '').replace(/\./g, '').indexOf('pobox') > -1){
				pobox_modal.style.display = 'block';
			}
		}
  };

	addressField1.addEventListener('input', function () {
		if (addressField1.value) {
			if(addressField1.value.toLowerCase().replace(/ /g, '').replace(/[^a-z,0-9]/g, '').replace(/\./g, '').replace(/\./g, '').indexOf('pobox') > -1){
				pobox_modal.style.display = 'block'
			}
		}
	});

	addressField2.addEventListener('input', function () {
		if (addressField2.value) {
			if(addressField2.value.toLowerCase().replace(/ /g, '').replace(/[^a-z,0-9]/g, '').replace(/\./g, '').replace(/\./g, '').indexOf('pobox') > -1 || addressField2.value.toLowerCase().replace(/ /g, '**').replace(/[^a-z,0-9]/g, '**').replace(/\./g, '**').indexOf('box**') > -1){
				pobox_modal.style.display = 'block'
			}
		}
	});

  setTimeout(function () {
    var address_fields = [
			document.querySelector('#checkout_shipping_address_address1'),
			document.querySelector('#checkout_shipping_address_address2')
    ]
    var pobox_modal = document.querySelector('#pobox_modal')
    address_fields.forEach(function(address_field, i) {
      if(address_field) {
        if (address_field.value && i == 0) {
			if(address_field.value.toLowerCase().replace(/ /g, '').replace(/[^a-z,0-9]/g, '').replace(/\./g, '').indexOf('pobox') > -1){
				pobox_modal.style.display = 'block'
			}
        }
		if (address_field.value && i == 1) {
			if(address_field.value.toLowerCase().replace(/ /g, '').replace(/[^a-z,0-9]/g, '').replace(/\./g, '').indexOf('pobox') > -1 || address_field.value.toLowerCase().replace(/ /g, '**').replace(/[^a-z,0-9]/g, '**').replace(/\./g, '**').indexOf('box**') > -1){
				pobox_modal.style.display = 'block'
			}
        }
      }
    })
  }, 1000)
</script>
<style>
	/* checkout pobox */
	.checkout-pobox {
		display: none;
		position: fixed;
		z-index: 100;
		background: rgba(0, 0, 0, 0.5);
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
	}
	.checkout-pobox__container {
		background: #fff;
		padding: 2rem;
		width: 600px;
		max-width: 100%;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		box-sizing: border-box;
	}
	.checkout-pobox__inner {
		border: 1px solid #D9D9D9;
		padding: 1.5rem;
	}
	.checkout-pobox__title {
		font-family: {{ settings.melin-pob-header-font }};
		color: {{ settings.melin-pob-text-color }};
		font-size: 24px;
		text-align: center;
		margin-bottom: 20px;
	}
	.checkout-pobox__button {
		width: 100%;
	}

	.checkout-pobox__button {
		font-family: {{ settings.melin-pob-button-font }};
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		border-radius: 0;
		display: -webkit-inline-box;
		display: -ms-inline-flexbox;
		display: inline-flex;
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		position: relative;
		-webkit-transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1);
		transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1);
		padding-left: 20px;
		padding-right: 20px;
		font-size: 13px;
		height: 40px;
		background-color: {{ settings.melin-pob-background-color }};
		color: {{ settings.melin-pob-color }};
		width: 100%;
		border: 1px solid;
		border-color: {{ settings.melin-pob-border-color }};
		font-weight: bold;
	}
	.checkout-pobox__button:hover{
		background-color: {{ settings.melin-pob-hover-background-color }};
		border-color: {{ settings.melin-pob-border-hover-color }};
		color: {{ settings.melin-pob-hover-color }};
	}

	@media only screen and (min-width: 768px) {
		.checkout-pobox__title {
			font-size: 24px;
		}
	}
</style>