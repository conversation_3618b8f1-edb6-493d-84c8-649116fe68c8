
{% liquid 
  assign handle = settings.product | default: product.handle
  if handle != product.handle
    assign product = all_products[handle]
    render 'product-data' product:product, script_tag:true, preselect_variant:false,  preselect_option:1, history_state:true, include:'variants,inventory,media,shipment_date'
  endif
%}

  <form 
    class="product-form"
    {% comment %} Special handling for products reactivity{% endcomment %}
    x-data="{
      product:$store.products['{{ product.handle }}'],
      variant:$store.products['{{ product.handle }}'].variant,
    }"
    x-init="
      $alpine.listen('Product', () => {
        $data.product = $store.products['{{ product.handle }}']
        $data.variant = $store.products['{{ product.handle }}'].variant
      })
    ">
    
    <div class="hidden">
      <label>
        <select name="id" class="w-full border p-3" aria-label="variant-selector">
          {% for variant in product.variants %}
            <option value="{{ variant.id }}" {% unless variant.available %}disabled{% endunless %}>{{ variant.title }}</option>
          {% endfor %}
        </select>
      </label>
      <label>
        <input type="submit" value="Add to Cart">
      </label>
    </div>
