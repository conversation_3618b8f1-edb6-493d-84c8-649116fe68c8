search.settings.remote_url = 'https://services.mybcapps.com/bc-sf-filter/search/suggest?q=${query}&shop={{ shop.permanent_domain }}&country={{ localization.country.iso_code | downcase }}&currency={{ localization.country.currency.iso_code }}&suggestion_limit=3&product_limit=5&collection_limit=3&page_limit=3&dym_limit=2&event_type=suggest${Object.keys(filters).length?`&_=pf${Object.keys(filters).map(key=>`${collection.filters.applied[key].map(value=>`&${key}[]=${value}`).join(``)}`).join(``)}`:``}';

search.settings.map = {
  filters: {
    from:'filter.options',
    each:{
      label:'label',
      key:'filterOptionId',
      isCollapseMobile:'isCollapseMobile',
      isCollapsePC:'isCollapsePC',
      selectType:'selectType',
      format: 'displayType|this.replace(`box`,`grid`)|this.replace(`swatch`,`swatches`)',
      options:{
        from: 'values',
        each: {
          value: 'key',
          label: 'key',
          handle: 'label',
          count: 'doc_count'   
        }
      }
    }
  },
  products:{
    from:'products',
    each:{
      title:'title',
      images:{ from:'images_info'},
      price: {% if cart.currency.iso_code == shop.currency %}'price_max|*100'{% else %}'price_max_{{ cart.currency.iso_code | downcase }}|*100'{% endif %},
      compare_at_price : {% if cart.currency.iso_code == shop.currency %}'compare_at_price_max|*100'{% else %}'compare_at_price_max_{{ cart.currency.iso_code | downcase }}|*100'{% endif %},
      handle:'handle',
      type:'product_type',
      sku:'variants.0.sku',
      tags:'tags',
      variants:{ 
        from:'variants', 
        each:{ 
          id:'id',
          available:'available',
          price: {% if cart.currency.iso_code == shop.currency %}'price|*100'{% else %}'price_{{ cart.currency.iso_code | downcase }}|*100'{% endif %},
        }
      } 
    }
  },
  total_products:'total_product',

  collections: {
    from: 'collections',
    each: {
      title:'title',
      handle:'handle'
    }
  },

  suggestions: {
    from: 'suggestions'
  }
};
