{% comment %}
  Renders a single color variant option

  Accepts:
  - settings: block settings
  - handle: product handle
  - field_buttons_colors_wrap: boolean for mobile wrapping
{% endcomment %}

{% if settings.enable_strikethrough %}
  <label
    :class="`field__button overflow-hidden ${value.availability.{{ settings.availability_model | default: 'progressive' }}.available ? 'available' : 'unavailable'} ${option.name.handle() === 'color' ? 'field__buttons--colors-only {% if settings.product_color_option_type == 'swatch' or settings.product_color_option_type == 'swatch_image' %}field__buttons--colors-swatch{% endif %}' : ''}`"
    @mouseenter="products['{{ handle }}'].options[oi].hover=value.value"
    @mouseleave="delete products['{{ handle }}'].options[oi].hover"
    x-bind:style="option.name.handle() === 'color' ? `flex: 0 0 calc((100% / ${window.variants_displayed_on_mobile}) - 10px);margin-bottom: 1px;` : ''"
    {%- if settings.variant_segment != blank and settings.variant_segment_design != 'tabs' -%}
    x-init="$nextTick(() => {
      const segment = products[value.product].metafields['{{ settings.variant_segment }}']
      const group = document.querySelector(`[variant-segment='${segment}']`)
      if (segment == '' || !group || option.name != 'Color') return false
      group.appendChild($el)
    })"
    {%- endif -%}
  >
{% else %}
  <label
    :class="`field__button overflow-hidden ${value.availability.{{ settings.availability_model | default: 'progressive' }}.available ? 'available' : 'unavailable_strikethrough'} ${option.name.handle() === 'color' ? 'field__buttons--colors-only {% if settings.product_color_option_type == 'swatch' or settings.product_color_option_type == 'swatch_image' %}field__buttons--colors-swatch{% endif %}' : ''}`"
    class="{% if settings.product_color_option_type == 'swatch' or settings.product_color_option_type == 'swatch_image' %}field__buttons--colors-swatch{% endif %}"
    @mouseenter="products['{{ handle }}'].options[oi].hover=value.value"
    @mouseleave="delete products['{{ handle }}'].options[oi].hover"
    x-bind:style="option.name.handle() === 'color' ? `flex: 0 0 calc((100% / ${window.variants_displayed_on_mobile}) - 10px);margin-bottom: 1px;` : ''"
    {%- if settings.variant_segment != blank and settings.variant_segment_design != 'tabs' -%}
    x-init="$nextTick(() => {
      const segment = products[value.product].metafields['{{ settings.variant_segment }}']
      const group = document.querySelector(`[variant-segment='${segment}']`)
      if (segment == '' || !group || option.name != 'Color') return false
      group.appendChild($el)
    })"
    {%- endif -%}
  >
{% endif %}

  <template x-if="option.name.handle() === 'size' && option.values.length === 1">
    <input
    class="sr-only"
    :value="value.value"
    type="radio"
    x-init="Products.select.option('{{ handle }}', option.name, value.value)"
    @change="Products.select.option('{{ handle }}', option.name, value.value)"
    :name="option.name"
    x-model="option.selected_value"
    role="radio"
    :aria-label="value.value.replace(/[^a-zA-Z0-9. ]/g, '')"
    />
  </template>
  <template x-if="option.name.handle() === 'size' && option.values.length > 1">
    <input
    class="sr-only"
    :value="value.value"
    type="radio"
    @change="Products.select.option('{{ handle }}', option.name, value.value)"
    :name="option.name"
    x-model="option.selected_value"
    role="radio"
    :aria-label="value.value.replace(/[^a-zA-Z0-9. ]/g, '')"
    />
  </template>
  <template x-if="option.name.handle() !== 'size' && option.values.length">
    <input
    class="sr-only"
    :value="value.value"
    type="radio"
    @change="Products.select.option('{{ handle }}', option.name, value.value)"
    :name="option.name"
    x-model="option.selected_value"
    x-bind:data-custom="removeApostrophe(value.value)"
    role="radio"
    :aria-label="value.value.replace(/[^a-zA-Z0-9. ]/g, '')"
    />
  </template>
  <span
  class="field__button-text"
  x-text="value.value"></span>

  <!-- <span class="" x-text="value.availability.{{ settings.availability_model }}.quantity"></span> -->

  {% if settings.product_color_option_type == 'swatch_image' or settings.product_color_option_type == 'swatch' %}
    {% if settings.product_color_option_type == 'swatch_image' %}
      {% render 'image'
        widths:'100,200,300,400,600'
        attributes: 'x-show="products[value.product]?.swatch_image" :src="products[value.product]?.swatch_image" :alt="value.value" style="width:-webkit-fill-available;height:-webkit-fill-available;margin: 0px auto;border-radius: 50%;position: relative;border: 1px solid rgb(211, 212, 213);"'
        sizes: '(min-width: 1024px) 10vw, 25vw'
        src: 'value.image.src'
        class: 'color_swatch'
      %}
    {% endif %}
    <div
      {% if settings.product_color_option_type == 'swatch_image' %}
      x-show="!products[value.product]?.swatch_image"
      {% endif %}
      class="color_swatch"
      x-data="{
        hasSwatchColors() {
          return products[value.product]?.swatch_color_1?.trim() ||
                products[value.product]?.swatch_color_2?.trim() ||
                products[value.product]?.swatch_color_3?.trim();
        },
        backgroundStyle() {
          const color1 = products[value.product]?.swatch_color_1?.trim();
          const color2 = products[value.product]?.swatch_color_2?.trim();
          const color3 = products[value.product]?.swatch_color_3?.trim();

          if (color1 && color2 && color3) {
            return `linear-gradient(to right, ${color1} 33%, ${color2} 33%, ${color2} 66%, ${color3} 66%) no-repeat`;
          } else if (color1 && color2) {
            return `linear-gradient(135deg, ${color1} 50%, ${color2} 50%) no-repeat`;
          } else if (color1) {
            return color1;  // Assuming swatch_color_1 is a valid color value
          } else {
            return '';  // No swatch colors
          }
        }
      }"
      :style="{ background: hasSwatchColors() ? backgroundStyle() : '{{ product_default_swatch_color_1 }}' }"
      style="width:-webkit-fill-available;height:-webkit-fill-available;margin: 0px auto;border-radius: 50%;position: relative;border: 1px solid rgb(211, 212, 213);"
    >
  </div>
  <template x-if="products[value.product]?.swatch_color_1 == '' && products[value.product]?.swatch_color_2  == '' && products[value.product]?.swatch_color_3  == '' && false">
    <img :src="value.image.src.replace('.png', '_240x.png')" :alt="value.value">
  </template>
  <style>
    .product-form__option:not(.product-form__option--color) .color_swatch {
      display: none;
    }
  </style>
  {% else %}
    {% render 'image'
      widths:'100,200,300,400,600'
      attributes: ':src="value.image.src ? value.image.src.replace(/\.(jpg|png)/i, `_128x.$1`) : ``" :alt="value.value"'
      sizes: '(min-width: 1024px) 10vw, 25vw'
      src: 'value.image.src'
    %}
  {% endif %}

  {%- if type contains 'table' -%}
    {% render 'variant-table-column' %}
  {%- endif -%}
  {% if settings.product_color_hover_info %}
    <template x-if="option.name.handle().toLowerCase() === 'color'">
      <span  class="product-swatch-color-tooltip" x-text="`Color: ${value.value}`"></span>
    </template>
  {% endif %}
</label>
