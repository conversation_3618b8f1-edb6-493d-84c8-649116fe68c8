<form id="{{ id | default: settings.id }}" class="relative">
	
	<div class="flex w-full relative">
		{% render 'icon' icon:'search' %}
		<input type="search" placeholder="{{ settings.placeholder }}" name="q" autocomplete="one-time-code">
	</div>

	{% if source or settings.source %}
	<script type="json/source">
		{{ source | default: settings.source }}
	</script>
	{% endif %}

	{% if map or settings.map %}
	<script type="json/map">
		{{ map | default: settings.map }}
	</script>
	{% endif %}
	
	{% if results == 'inline' or settings.results == 'inline' %}

	<template x-if="!!$store.search.results && $store.search.form !== 'SearchModalInput'">
		<div class="search-results absolute top-full overflow-y-scroll overscroll-contain" style="height:300px;">
		
	        <main>

	          {% if resources contains 'products' or settings.resources contains 'products' %}
		          <template x-if="$store.search.results.products">
		            {% render 'search-results' type:'products' click:click settings:settings %}
		          </template>
	          {% endif %}

	          {% if resources contains 'collections' or settings.resources contains 'collections' %}
		          <template x-if="$store.search.results.collections">
		            {% render 'search-results' type:'collections' %}
		          </template>
	          {% endif %}

	        </main>

		</div>
    </template>
	{% endif %}

	{% if settings.instruction %}
		{{ settings.instruction }}
	{% endif %}

	<script>
		window.addEventListener('click', e=>{
			if (
				search.form == '{{ id | default: settings.id }}' &&
				e.target != document.querySelector('#{{ id | default: settings.id }}') &&
				!document.querySelector('#{{ id | default: settings.id }}').contains(e.target)
			) Search.clear()
		})	
	</script>

</form>