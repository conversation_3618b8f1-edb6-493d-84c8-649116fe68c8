{% if product.metafields.custom.toggle_pdp.value %}
  <div class="flex gap-lg items-center pb-3xs lg:pb-3xs">
    <div class="tabs toggle">
      {% for toggle in product.metafields.custom.toggle_pdp.value %}
        {% if product.tags contains toggle.tag_active %}
          {% assign tag_element = 'span' %}
        {% else %}
          {% assign tag_element = 'a' %}
        {% endif %}
        <{{ tag_element }} {% if tag_element == "a" %}prefetch href="{{ toggle.link }}" onclick="togglePDP(this)"{% endif %}>{{ toggle.toggle_label }}</{{ tag_element }}>
      {% endfor %}
    </div>
  </div>

  <script>
    const currentUrl = window.location.href;

    const togglePDP = (toggle) => {
      const eventName = "pdp_toggle";
      const eventType = mParticle.EventType.Other;
      const eventData = {
        currentUrl: currentUrl,
        sisterUrl: toggle.getAttribute('href'),
      };
      mParticle.logEvent(eventName, eventType, eventData);
    }

    {% if settings.debug_prerendering %}
      if (document.prerendering) {
        document.addEventListener(
          "prerenderingchange",
          () => {
            console.log("Prerender activated after this script ran");
          },
          { once: true },
        );
      } else if (performance.getEntriesByType("navigation")[0]?.activationStart > 0) {
        console.log("Prerender activated before this script ran");
      } else {
        console.log("This page load was not via prerendering");
      }
    {% endif %}
  </script>
{% endif %}