<script>
    const originalTitle = document.title;
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            // Change title when the page is in a new tab
            document.title = '{{ settings.custom_meta_title }}';
        } else {
            // Restore the original title when the user comes back
            document.title = originalTitle;
        }
    });
</script>