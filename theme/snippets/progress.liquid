{%- if legacy == true -%}
<div class="progress-bar {{ class }}" >
  <progress id="block-{{ block.id }}" value="{{ value }}" max="{{ max }}" {{ attributes }}></progress>
  <div class="progress-bar__labels flex justify-between">
    {%- for option in options -%}
      <span class="progress-bar__label text-sm text-gray-500">
        {{ option }}
      </span>
    {%- endfor -%}
  </div>
</div>
{%- else -%}
<div class="progress-bar {{ class }}">
  <div class="flex w-full progress {{ class }}" style="{% render 'style-settings' settings:settings, prefix:'offer_progress_style'%}">
    <span class="transition-all rounded-full" style="width:{{ value | divided_by: max | times: 100 | append:'%' }};{% render 'style-settings' settings:settings, prefix:'offer_progress_bar_style'%}"></span>
  </div>
  {% if options %}
  <div class="progress-bar__labels flex justify-between">
    {%- for option in options -%}
      <span class="progress-bar__label text-sm text-gray-500">
        {{ option }}
      </span>
    {%- endfor -%}
  </div>
  {% endif %}
</div>
{%- endif -%}
