<div class="product-summary" product-summary style="position:relative;">
  <div class="product-summary__row">
    {% unless product_gallery == false %}
    <article class="desktop">
      <div class="">
        {% if multiple_images %}
        <div class="swiper-container" swiper>
          <div class="swiper-wrapper" data-partial="images|innerHTML">
            {% for image in product.images %}
            <div class="swiper-slide">
              <div class="product-summary__image product-summary__image--desktop">
                <img src="{{ image.src | product_img_url:'grande'}}" alt="{{ image.alt }}">
              </div>
            </div>
            {% endfor %}
          </div>
          <div class="swiper-pagination"></div>
          <div class="swiper-button swiper-button-prev">
            {% render 'icon-swiper-nav-prev' %}
          </div>
          <div class="swiper-button swiper-button-next">
            {% render 'icon-swiper-nav-next' %}
          </div>
        </div>
        {% else %}
        <div class="product-summary__image product-summary__image--desktop">
          <img src="{{ product.featured_image | product_img_url:'grande' }}" alt="{{ product.images[0].alt }}" data-partial="image|src" loading="lazy">
        </div>
        {% endif %}
      </div>
    </article>
    {% endunless %}
    <article>
      <header class="product-summary__header">
        <a class="product-summary__titles" href="/products/{{ product.handle }}">
          <h2 class="product-summary__title">{{ product.title | split: ' - ' | first }}</h2>
          <h3 class="product-summary__subtitle">{{ product.type }}</h3>
        </a>
        <div class="product-summary__meta">
    {% if product.compare_at_price and product.compare_at_price > product.price %}
      <p class="product-summary__price" data-partial="price|textContent">{{ product.price | money }} <s>{{ product.compare_at_price | money }}</s></p>
    {% else %}
      <p class="product-summary__price" data-partial="price|textContent">{{ product.price | money }}</p>
    {% endif %}
          <div class="product-summary__rating">
            <div class="ruk_rating_snippet ruk-rating-snippet" data-sku="{{ product.variants[0].sku | split: '-' | first }}" style="color: rgb(241, 164, 7);"></div>
          </div>
        </div>
      </header>

      {% unless product_gallery == false %}
      <div class="product-summary__image product-summary__image--mobile">
        <img src="{{ product.featured_image | product_img_url:'grande' }}" alt="{{ product.images[0].alt }}" loading="lazy" data-partial="image|src">
      </div>
    {% endunless %}

      <div class="product__colors" aria-live="polite">
        <h3 class="product__current product__current--color" data-sibling-color data-partial="subtitle|title" title="{{ product.title | split: ' - ' | last }}">{{ product.title | split: ' - ' | last }}</h3>
        <div class="product__list product__list--color">
          {%- liquid
            assign collection_products = product_collection.products

            if collection_products == blank
              assign collection_products = product_collection.bundle_products
            endif
          -%}
          {% for sibling in collection_products %}
            {{sibling.handle}}<br>
            <a href="javascript:void(0)" data-sibling="{{ sibling.handle }}" data-title="{{ sibling.title }}" {% unless sibling.tags contains 'bis-hidden' %}data-show-bis="true"{% endunless %} data-id="{{ sibling.id }}" data-handle="{{ sibling.handle }}" data-subtitle="{{ sibling.title | split: ' - ' | last }}" data-image="{{ sibling.featured_image | product_img_url:'grande' }}" data-price="{{ sibling.price | money }}" data-images="{% for image in sibling.images %}&lt;div class=&quot;swiper-slide&quot;&gt;&lt;div class=&quot;product-summary__image product-summary__image--desktop&quot;&gt;&lt;img src=\{{ image.src | product_img_url:'grande'}}&quot; alt=&quot;{{ image.alt }}&quot;&gt;&lt;/div&gt;&lt;/div&gt;{% endfor %}" data-variants="{% for variant in sibling.variants %}&lt;li role=&quot;none&quot; class=&quot;product__list-item product__list-item--size {% unless variant.available %}product__list-item--unavailable{% endunless %}{% unless sibling.tags contains 'bis-hidden' %} product__list-item--back-in-stock{% endunless %}&quot;&gt;&lt;input type=&quot;radio&quot; id=&quot;{{product.handle}}_{{ variant.option2 }}&quot; name=&quot;{{ sibling.handle }}-size&quot; class=&quot;product__radio product__radio--size&quot; value=&quot;{{ variant.id }}&quot;&gt;&lt;label for=&quot;{{product.handle}}_{{ variant.option2 }}&quot; onclick=&quot;this.previousElementSibling.checked = true&quot; title=&quot;Size: {{ variant.option2 }}&quot; aria-checked=&quot;false&quot; aria-label=&quot;Size {{ variant.option2 }}&quot; tabindex=&quot;0&quot; class=&quot;product__swatch product__swatch--size {% unless variant.available %}unavailable soldout{% endunless %} &quot;&gt;{{ variant.option2 }}&lt;/label&gt;&lt;/li&gt;{% endfor %}" data-label="{{ sibling.title | split: ' - ' | last }}" class="product__list-item product__list-item--color {% if product.handle == sibling.handle %}active{% endif %}" role="radio">
            <script type="application/json" variant-json>{{ sibling.variants | json }}</script>
            <div tabindex="0" class="product__list-item-inner">
              <picture>
                <source srcset="{{ sibling.featured_image | product_img_url: '152x'}}" media="(min-width: 1025px)">
                <img title="{{ sibling.title | split: ' - ' | last }}" src="{{ sibling.featured_image | product_img_url: '240x'}}" loading="lazy">
              </picture>
            </div>
          </a>
          <link rel="prefetch" href="/products/{{ sibling.handle }}?view=summary">
          {% endfor %}
        </div>
      </div>

      <footer class="product-summary__actions">

        {% if variant_selection %}
        
          <div class="product__sizes">
            <div>
              <div class="product__row product__row--size-size-chart">
                <p id="productSizeLabel" tabindex="-1" class="product__current product__current--size">Choose your size.</p>
              </div> 
              <ul aria-labelledby="productSizeLabel" role="radiogroup" class="product__list product__list--size" data-partial="variants|innerHTML">
                {% for variant in product.variants %}
                <li role="none" class="product__list-item product__list-item--size{% unless variant.available %} product__list-item--unavailable{% endunless %}{% unless product.tags contains 'bis-hidden' %} product__list-item--back-in-stock{% endunless %}">
                  <input type="radio" id="{{product.handle}}_{{ variant.option2 }}" name="{{ product.handle }}-size" class="product__radio product__radio--size" value="{{ variant.id }}"> 
                  <label 
                    for="{{product.handle}}_{{ variant.option2 }}" 
                    title="Size: {{ variant.option2 }}" 
                    aria-checked="false" 
                    aria-label="Size {{ variant.option2 }}" 
                    tabindex="0" 
                    class="product__swatch product__swatch--size {% unless variant.available %}unavailable soldout{% endunless %}  "
                    onclick="this.previousElementSibling.checked = true"
                  >
                    {{ variant.option2 }}
                  </label>
                </li>
                {% endfor %}
              </ul>
            </div>
          </div>
        {% else %}
          <button add-to-bag class="product-summary__button product-summary__button--primary" data-partial="handle|value" value="{{ product.handle }}" onclick="quickAdd.open(this.value, event, collection.filters.size&&collection.filters.size.length ? collection.filters.size[0] : null)">{{ 'products.product.atc_add' | t }}</button>
        {% endif %}

        {% if learn_more %}
        <button data-partial="handle|value" data-handle="{{ product.handle }}" value="{{ product.handle }}" onclick="window.location='/products/'+this.value" class="product-summary__button product-summary__button--secondary">{{ 'products.product.learn_more' | t }}</button>
        {% endif %}

      </footer>
      <div data-back-in-stock-button></div>
      <script data-bis-data>{{ product | escape | json }}</script>
    </article>
  </div>

  {% unless show_details == false %}
    {% if collapse_details %}
    <button class="product-summary__row-toggle" style="display:flex; padding:0rem 3.25rem; font-size:14px; width:100%; color:#666;" onclick="this.classList.toggle('active')">
      <span>Fit & Features</span>
      <span style="margin-left:auto;">
        <span class="toggle--plus">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
        </span>
        <span class="toggle--minus">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-minus"><line x1="5" y1="12" x2="19" y2="12"></line></svg>
        </span>
      </span>
    </button>
    {% endif %}

    <div class="product-summary__row product-summary__row--bottom">
      <article class="product-summary__fit-guide">
        {% render 'product-fit-guide' product:product %} 
      </article>
      <article class="product-summary__enunciation">
        {% render 'product-enunciation' product_description:product.description, product_fields:product.metafields, collection_fields:product_collection.metafields %}
      </article>
    </div> 
  {% endunless %}
</div>



