<script>

  class Checkout {
    constructor() {
      this.discountField = $('#checkout_reduction_code:visible')
      this.modalClose = $('.giftcard-message__button')
      this.modal = null
      this.giftOrderForm = document.querySelector('[data-checkout-gift-order]')
      this.giftOrderCheckbox = document.getElementById('gift-order-checkbox')
      this.giftOrderEmail = document.getElementById('checkout_gift_order_email')
      this.giftOrderFrom = document.getElementById('checkout_gift_order_from')
      this.giftOrderMessage = document.getElementById('checkout_gift_order_message')
      this.messageRemaining = 200
      this.giftOrder()
      this.updateGiftOrderCartAttributes()
      this.giftOrderConfirmation()
    }

    giftOrder() {
      let self = this;

      document.addEventListener('page:load', () => {
        if (true && window.Shopify.Checkout.hasOwnProperty('step') && window.Shopify.Checkout.step === 'contact_information' && document.querySelector('[data-shipping-address]')) {
          let giftOrderSection = document.createElement('div');
          giftOrderSection.setAttribute('data-checkout-gift-order', '');

          this.giftOrderForm = giftOrderSection;

          this.giftOrderForm.innerHTML = `
					<div class="section section--gift-order">
						<div class="section__header">
							<h2 class="section__title">Gift Message</h2>
						</div>
						<div class="checkout-gift-order__header">
							<div class="checkout-gift-order__checkbox">
								<input type="checkbox" id="gift-order-checkbox">
								<label for="gift-order-checkbox">
									<span>This order is a gift.</span>
								</label>
							</div>
						</div>

						<div class="checkout-gift-order__form">
							<p class="checkout-gift-order__disclaimer">This message will appear in an email along with returns information for the gift recipient. Prices will not be included on receipt. Not applicable for gift cards. </p>
							<div class="section__content">
								<div class="fieldset">
									<div class="address-fields">
										<div class="field">
											<div class="field__input-wrapper">
												<label class="field__label field__label--visible" for="checkout_gift_order_from">From</label>
												<input class="field__input" id="checkout_gift_order_from" autocorrect="off" name="checkout[gift_order][from]" placeholder="From" size="30" type="text">
											</div>
											<p class="checkout-gift-order__error"></p>
										</div>
										<div class="field">
											<div class="field__input-wrapper">
												<div class="checkout-gift-order__seperator"></div>
											</div>
										</div>
										<div class="field">
											<div class="field__input-wrapper">
												<label class="field__label field__label--visible" for="checkout_gift_order_email">Recipient's Email Address</label>
												<input class="field__input" id="checkout_gift_order_email" autocapitalize="off" autocorrect="off" name="checkout[gift_order][email]" placeholder="Recipient's Email Address" size="30" type="email">
											</div>
											<p class="checkout-gift-order__error"></p>
										</div>
										<div class="field">
											<div class="field__input-wrapper">
												<label class="field__label field__label--visible" for="checkout_gift_order_message">Gift Message</label>
												<input class="field__input field__input--gift-order-message" id="checkout_gift_order_message" name="checkout[gift_order][message]" placeholder="Gift Message" size="200" type="text">
												<small class="checkout-gift-order__characters"></small>
											</div>
											<p class="checkout-gift-order__error"></p>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				`;

          document.querySelector('[data-shipping-address]').parentNode.insertBefore(this.giftOrderForm, document.querySelector('[data-shipping-address]'));

          this.giftOrderCheckbox = document.getElementById('gift-order-checkbox');
          this.giftOrderEmail = document.getElementById('checkout_gift_order_email');
          this.giftOrderFrom = document.getElementById('checkout_gift_order_from');
          this.giftOrderMessage = document.getElementById('checkout_gift_order_message');

          this.giftOrderCheckbox.addEventListener('change', function() {
            self.checkInputs();
            self.checkBreadcrumbs();
            self.checkGiftOrderLocalStorage();
          });

          this.giftOrderEmail.addEventListener('input', function() {
            self.checkInputs();
            self.checkBreadcrumbs();
            self.checkGiftOrderLocalStorage();
          });

          this.giftOrderFrom.addEventListener('input', function() {
            self.checkInputs();
            self.checkBreadcrumbs();
            self.checkGiftOrderLocalStorage();
          });

          this.giftOrderMessage.addEventListener('input', function() {
            self.characterCounter();
            self.checkInputs();
            self.checkBreadcrumbs();
            self.checkGiftOrderLocalStorage();
          });


          let giftOrderFrom = document.querySelector('#checkout_gift_order_from');
          let giftOrderEmail = document.querySelector('#checkout_gift_order_email');
          let giftOrderMessage = document.querySelector('#checkout_gift_order_message');

          if (giftOrderFrom && giftOrderEmail && giftOrderMessage) {
            if (sessionStorage.getItem('gift_message')) {
              let giftOrderMVPStorage = JSON.parse(sessionStorage.getItem('gift_message'));
              console.log('giftOrderMVPStorage', giftOrderMVPStorage);
              this.giftOrderCheckbox.checked = giftOrderMVPStorage.include_message
              if (giftOrderMVPStorage.from !== '') {
                giftOrderFrom.value = `${
                  giftOrderMVPStorage.from
                }`;
              }
              if (giftOrderMVPStorage.email !== '') {
                giftOrderEmail.value = `${
                  giftOrderMVPStorage.email
                }`;
              }
              if (giftOrderMVPStorage.message !== '') {
                giftOrderMessage.value = `${
                  giftOrderMVPStorage.message
                }`;
              }
            }
          }
          self.checkBreadcrumbs();
        }
      });
    }

    updateGiftOrderCartAttributes() {
      window.addEventListener('load', () => {
        if (true && window.Shopify.Checkout.hasOwnProperty('step') && window.Shopify.Checkout.step !== 'contact_information' && window.Shopify.Checkout.step !== 'thank_you') {
          let isGiftOrder = false;
          let giftOrderMVPStorage = null;

          if (sessionStorage.getItem('gift_message')) {
            giftOrderMVPStorage = JSON.parse(sessionStorage.getItem('gift_message'));

            if (giftOrderMVPStorage.email && giftOrderMVPStorage.from && giftOrderMVPStorage.message) {
              isGiftOrder = true;
            }
          }

          const url = '/cart/update.js';

          const attributes = isGiftOrder
            ? {
              'Gift Order': 'Yes',
              'Gift Order From': giftOrderMVPStorage.from,
              'Gift Order Email': giftOrderMVPStorage.email,
              'Gift Order Message': giftOrderMVPStorage.message
            }
            : {
              'Gift Order': 'No'
            };

          fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({attributes})
          }).then((response) => {
            if (!response.ok) {
              throw new Error('Network response was not ok');
            }
            return response.json();
          }).then((data) => {
            console.log(data);
          }).catch((error) => {
            console.error('Fetch Error: ', error);
          });
        }

      });
    }

    giftOrderConfirmation() {
      document.addEventListener('page:load', () => {
        const giftOrderMVP = JSON.parse(sessionStorage.getItem('gift_message'))

        if (document.querySelector('.page--thank-you') && giftOrderMVP.include_message) { // if (yourElement && yourElement.getAttribute('data-step') === 'thank_you' && true) {
          const email = window.Shopify.checkout.email
          const lineItemsQty = window
            .Shopify
            .checkout
            .line_items
            .length
          const checkoutId = window
            .Shopify
            .checkout
            .order_id

  // const giftOrderMVP = JSON.parse(sessionStorage.getItem('gift_message'))

            exponea
            .identify({'email_id': email})
          exponea
          .update({'email': email})
        exponea
        .track('gift_order', {
          checkout_token: window.giftOrder.checkoutToken,
          fromName: giftOrderMVP.from,
          message: giftOrderMVP.message,
          order_name: window.giftOrder.orderName,
          order_number: checkoutId,
          order_status_url: window.giftOrder.orderStatusUrl,
          line_items: lineItemsQty,
          recipient_email: giftOrderMVP.email
        }, () => {
          sessionStorage.removeItem('gift_message')
        })
    }
  });
}

checkInputs() {
  this.giftOrderCheckbox.checked = this.giftOrderCheckbox.checked;
}

characterCounter() {
  this.messageRemaining = 200 - this.giftOrderMessage.value.length;
  document.querySelector('.checkout-gift-order__characters').textContent = this.messageRemaining + ' characters remaining';
}

checkBreadcrumbs() {
  if (this.giftOrderCheckbox.checked) {
    document.querySelector('.checkout-gift-order__form').style.display = 'block';
  } else {
    document.querySelector('.checkout-gift-order__form').style.display = 'none';
  }
}

checkGiftOrderLocalStorage() {
  let giftOrderMVP = {
    from: this.giftOrderFrom.value,
    include_message: this.giftOrderCheckbox.checked,
    email: this.giftOrderEmail.value,
    message: this.giftOrderMessage.value
  };

  sessionStorage.setItem('gift_message', JSON.stringify(giftOrderMVP));
}}new Checkout();
</script>