<ul class="flex justify-center h-full header-nav header-nav--{{ id }} {% render 'class-settings' prefix:'class', settings:settings %}" onmouseleave="if(window.innerWidth >= 1024 && typeof Details !== 'undefined') { Details.close('nav-{{ id }}') }">
  {% for link in linklists[settings.menu].links %}
    
    {% liquid 
      assign mega = false
      if link.links[0].url contains '#mega'
        assign mega = true
      endif
    %}

    {% capture active_item_removal %}
     x-data
     @click="document.querySelectorAll('.header-nav__item--active').forEach(el => el.classList.remove('header-nav__item--active'))"
    {% endcapture %}

    {% if mega %}

      <li class="header-nav__item header-nav__item--{{ link.title | handle }} {% liquid 
        if link.active or link.current or link.child_active or link.child_current
          echo 'header-nav__item--active'
        endif
      -%}"
      {%- liquid
        if settings.mega_menu_hover
          echo ' x-data @mouseenter="if (window.innerWidth >= 1024) $el.querySelector(`details`).setAttribute(`open`,``)" @mouseleave="if (window.innerWidth >= 1024) $el.querySelector(`details`).removeAttribute(`open`)"'
        endif
      -%}>
         
        <details type="tabs" group="nav-{{ id }}">
          
          <summary class="type-nav-link" {{ active_item_removal }}>
            {%- if settings.mega_menu_hover and link.url != '#' and link.url != blank -%}
              <a href="{{ link.url }}" onclick="if (window.innerWidth < 1024) { event.preventDefault(); Details.close('nav-{{ id }}'); this.closest('details').setAttribute(`open`,``); }">{{ link.title }}</a>
            {% else %}
              {{ link.title }}
            {%- endif -%}
          </summary>
          <div class="mega-menu absolute w-full lg:top-full left-0 lg:right-0" id="{{ link.links[0].url | remove: '#' }}"></div>

        </details>

      </li>

    {% else %}
      
      <li class="header-nav__item header-nav__item--{{ link.title | handle }} relative group/link {% liquid 
        if link.active or link.current or link.child_active or link.child_current
          echo 'header-nav__item--active'
        endif
      -%}">

        {% if link.links.size == 0 %}

          <a class="type-nav-link" href="{{ link.url }}">
            {{ link.title }}
          </a>

        {% else %}

          <details>
            <summary class="flex" {{ active_item_removal }}>
              <a class="type-nav-link" href="{{ link.url }}">
                {{ link.title }}
              </a>
              {% render 'accordion-controls' %}
            </summary>
            <ul class="">
            
              {% for sub in link.links %}
                <li class="">

                  {% if sub.links.size == 0 %}

                  <a href="{{ sub.url }}">
                    {{ sub.title }}
                  </a>

                  {% else %}

                  <details>
                    <summary class="flex">
                      <a href="{{ sub.url }}">
                        {{ sub.title }}
                      </a>
                      {% render 'accordion-controls' %}
                    </summary>
                    <ul class="">
                      {% for sub2 in sub.links %}
                        <li>
                          
                          <a href="{{ sub2.url }}">
                            {{ sub2.title }}
                          </a>
                          
                        </li>  
                      {% endfor %}
                    </ul>
                  </details>

                  
                  {% endif %}
                  
                </li>  
              {% endfor %}
            </ul>
          </details>

        {% endif %}

      </li>

    {% endif %}
  {% endfor %}
</ul>


<style>
  @media only screen and (max-width: 1023px) {
    {% assign hide = settings.hide_mobile | split:',' %}
    {% for item in  hide %}
    .header-nav--{{ id }} li.header-nav__item--{{ item | handle}} { display:none; }
    {% endfor %}
  }
  @media only screen and (min-width: 1025px) {
    {% assign hide = settings.hide_desktop | split:',' %}
    {% for item in  hide %}
    .header-nav--{{ id }} li.header-nav__item--{{ item | handle}} { display:none; }
    {% endfor %}
  }
</style>
