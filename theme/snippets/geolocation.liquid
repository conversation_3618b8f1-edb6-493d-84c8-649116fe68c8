<script>
	const geolocation = {
		
		services:{
			geoip:'{{ settings.geoip }}',
			flag:'{{ settings.flag }}',
		},

		location:{ /* tbd */ },

		sites: {{ sites | json }},

		available_countries: [
			{% for country in localization.available_countries %}
				{
					name:{{ country.name | json }},
					iso_code:{{ country.iso_code | json }},
					currency:{
						name:{{ country.currency.name | json }},
						iso_code:{{ country.currency.iso_code | json }},
						symbol:{{ country.currency.symbol | json }}
					}
				}
			{%- unless forloop.last %},{% endunless -%}
			{% endfor %}
		],
		
		country: {
			name:{{ localization.country.name | json }},
			iso_code:{{ localization.country.iso_code | json }},
			currency:{
				name:{{ localization.country.currency.name | json }},
				iso_code:{{ localization.country.currency.iso_code | json }},
				symbol:{{ localization.country.currency.symbol | json }}
			}
		},

		available_languages: [
			{% for language in localization.available_languages %}
				{
					name:{{ language.name | json }},
					iso_code:{{ language.iso_code | json }},
					endonym_name:{{ language.endonym_name | json }}
				}
			{%- unless forloop.last %},{% endunless -%}
			{% endfor %}
		],
		language: {{ localization.language | json }}

	}
	window.geolocation = geolocation;
</script>
<script src="{{ 'geolocation.js' | asset_url}}" defer="defer"></script>

{% capture geolocation_content %}

	<section class="geolocation" x-data>

		{% form 'localization' %}

			<header class="geolocation__header">
				<p x-text="Util.literal('{{ settings.heading }}', geolocation)">{{ settings.heading }}</p>
			</header>

			<main>

				{% if settings.site_switcher %}
				
					<template x-if="$store.geolocation.current_site.code != $store.geolocation.target_site.code">
						
						<article class="geolocation__site_switcher flex items-center" >

							<a class="geolocation__site cursor-pointer" onclick="Geolocation.bypass(); Modal.close();">
								<img :src="`${geolocation.services.flag}/${$store.geolocation.current_site.code.toLowerCase()}.png`" :alt="`${$store.geolocation.current_site.name} Flag`">
								<p x-text="$store.geolocation.current_site.name"></p>
							</a>

							<span class="geolocation__site_arrow">
								{% render 'icon' icon:'arrow-right' %}
							</span>

							<a class="geolocation__site" :href="$store.geolocation.target_site.url">
								<img :src="`${geolocation.services.flag}/${$store.geolocation.target_site.code.toLowerCase()}.png`" :alt="`${$store.geolocation.target_site.name} Flag`">
								<p x-text="$store.geolocation.target_site.name"></p>
							</a>

						</article>

					</template>

				{% endif %}

				{% if settings.site_list %}

					<article class="geolocation__site_list">
						<ul>
							{% for site in sites %}
								<li class="geolocation__site_list_item">
									<a href="{{ site.url }}">
										{% if settings.flag %}
											{% if settings.show_flag == 'yes' %}
												<img src="{{ settings.flag }}/{{ site.code | downcase }}.png" class="geolocation__site_list_flag">
											{% endif %}
										{% endif %}
										<span class="geolocation__site_list_name">
											{{ site.name }}
										</span>
									</a>
								</li>
							{% endfor %}
						</ul>

					</article>

				{% endif %}

				{% if settings.currency_selector or settings.language_selector %}

					<article class="geolocation__settings">

					  	{% if settings.currency_selector %}
					  	<div class="field">
					      <select name="country_code" id="country_code" class="field__select">
					        {% for country in localization.available_countries %}
					        <option value="{{ country.iso_code }}" {% if localization.country.iso_code == country.iso_code %}selected{% endif %}>
					        	{{ country.name }} ({{ country.currency.iso_code }} {{ country.currency.symbol }})
					        </option>
					        {% endfor %}
					      </select>
					    </div>
					    {% endif %}

					  	{% if settings.language_selector %}
					  	<div class="field">
					      <select name="language_code" id="language_code" class="field__select">
					        {% for language in localization.available_languages %}
					        <option value="{{ language.iso_code }}" {% if localization.language.iso_code == language.iso_code %}selected{% endif %}>
					        	{{ language.endonym_name | capitalize }}
					        </option>
					        {% endfor %}
					      </select>
					    </div>
					    {% endif %}

					</article>

			{% endif %}

			</main>

			{% if settings.currency_selector or settings.language_selector %}
				<footer class="geolocation__footer">
					
				    <button type="submit" class="button button--primary">
				    	{{ settings.button_text }}
				    </button>

				</footer>

			{% endif %}

		{% endform %}

	</section>

{% endcapture %}
{% render 'modal' key:'geolocation'  position:'mini-geo' content:geolocation_content %}