{%- liquid 
  assign compare_format = '%Y-%m-%d %H:%M:%S'
  assign current_date = 'now' | date: compare_format
  assign start_date = settings.start_date | date: compare_format
  assign end_date = settings.end_date | date: compare_format
  assign hide = false

  if start_date != blank and start_date > current_date
    assign hide = true
  endif

  if end_date != blank and end_date < current_date
    assign hide = true
  endif

  if settings.show_desktop == false and settings.show_mobile == false 
    assign hide = true
  endif

  if settings.show_liquid == ''
    assign hide = true
  endif

  if hide
    echo 'hide'
  else
    echo 'show'
  endif

  if settings.async
    unless content_for_header contains section.id
      echo 'hide async' 
    endunless
  endif
  
-%}
