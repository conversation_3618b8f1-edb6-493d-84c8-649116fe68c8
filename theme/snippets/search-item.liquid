<article class="search-results__item search-results__item--{{ type }} search-result-item">
  {% case type %}

    {% when 'product' %}
      <a {% if click != blank %}@click="event.preventDefault(); {{ click }}"{% endif %} :href="`${Shopify.routes.root}products/${product.handle}${ product.variants ? `?variant=${product.variants.find(v => v.available)?.id || product.variants[0].id}` : `` }`" class="flex">
        <img :src="`${image.format(product.images[0].src, {width:100,height:100})}`" :alt="product.images[0].alt" class="w-1/6 object-contain">
        <div class="w-full">
          <div class="flex flex-row justify-between">
            <p class="my-0 search-result-item__title" x-text="{{ settings.search_item_title_logic }}"></p>
            {% render 'product-badges' product:'product' location:'search' %}
          </div>
          <p class="my-0 search-result-item__type" x-text="{{ settings.search_item_type_logic }}"></p>
          <p class="my-0 search-result-item__sku" x-text="{{ settings.search_item_sku_logic }}"></p>
          <div x-data="{ 
            minPrice: null,
            maxPrice: null,
            calculatePrices: function() {
              this.minPrice = Math.min(...this.product.variants.filter(v => v.available).map(v => v.price));
              this.maxPrice = Math.max(...this.product.variants.filter(v => v.available).map(v => v.price));
            }
          }" x-init="calculatePrices()">
          <template x-if="product.type === 'Gift Card'">
            <p class="my-0 search-result-item__price" x-text="money.format(minPrice) + ' - ' + money.format(maxPrice)"></p>
           </template>
          <template x-if="product.type !== 'Gift Card'">
            <span class="my-0 search-result-item__price"  x-bind:class="{ 'product-item__price_with_compare': product.compare_at_price }" x-text="money.format(product.price)"></span>
          </template>
          <template x-if="product.compare_at_price">
            <s class="product-item__compare-at-price type-item {{ settings.classes_product_item_price }}" x-text="money.format(product.compare_at_price)"></s>
          </template> 
        </div>
        </div>
       
      </a>

    {% when 'collection' %}
      <a :href="`/collections/${collection.handle}`" class="flex">
        <p x-text="collection.title"></p>
      </a>
    {% when 'suggestion' %}
      <a :href="`/search?q=${suggestion}`" class="flex">
        <p x-text="suggestion"></p>
      </a>
 
  {% endcase %}
</article>
