<ul id="social-icons" class="list-none flex flex-row items-center {{ classes }}">
  {% if settings.social_instagram_link != blank %}
    <li>
      <a href="{{ settings.social_instagram_link }}" class="link block hover:opacity-50 no-underline mr-6" style="text-decoration: none;" target="_blank">
        {% render 'icon' icon: 'instagram' width:20 height:20 fill:'currentColor' sr:'Instagram' %}
      </a>
    </li>
  {% endif %}
  {% if settings.social_facebook_link != blank %}
    <li>
      <a href="{{ settings.social_facebook_link }}" class="link block hover:opacity-50 no-underline mr-6" style="text-decoration: none;" target="_blank">
        {% render 'icon' icon: 'facebook' width:21 height:21 fill:'currentColor' sr:'Facebook' %}
      </a>
    </li>
  {% endif %}
  {% if settings.social_twitter_link != blank %}
    <li>
      <a href="{{ settings.social_twitter_link }}" class="link block hover:opacity-50 no-underline mr-6" style="text-decoration: none;" target="_blank">
        {% render 'icon' icon: 'twitter' width:20 height:20 fill:'currentColor' sr:'Twitter' %}
      </a>
    </li>
  {% endif %}
  {% if settings.social_pinterest_link != blank %}
    <li>
      <a href="{{ settings.social_pinterest_link }}" class="link block hover:opacity-50 no-underline mr-6" style="text-decoration: none;" target="_blank">
        {% render 'icon' icon: 'pinterest' width:20 height:20 fill:'currentColor' sr:'Pinterest' %}
      </a>
    </li>
  {% endif %}
  {% if settings.social_youtube_link != blank %}
    <li>
      <a href="{{ settings.social_youtube_link }}" class="link block hover:opacity-50 no-underline mr-6" style="text-decoration: none;" target="_blank">
        {% render 'icon' icon: 'youtube' width:20 height:20 fill:'currentColor' sr:'YouTube' %}
      </a>
    </li>
  {% endif %}
  {% if settings.social_snapchat_link != blank %}
    <li>
      <a href="{{ settings.social_snapchat_link }}" class="link hover:opacity-50 no-underline mr-6 flex items-center justify-center" style="text-decoration: none;" target="_blank">
        {% render 'icon' icon: 'snapchat' width:20 height:20 fill:'currentColor' sr:'Snapchat' %}
      </a>
    </li>
  {% endif %}
</ul>
