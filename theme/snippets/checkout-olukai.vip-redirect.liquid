{% if site_mode == "vip" %}
  {% unless customer %}
    {% assign viewOrderPage = shop.id | append: '/orders/' %}
    {% unless request.path contains "account/login" or request.path contains "challenge" or request.path contains "account/register" or request.path contains "account/activate" or request.path contains "account/reset" or request.path contains "tools/emails/click" or request.path contains viewOrderPage  or request.path contains "/collections/ohana-collection" %}
      <script>
        if( (sessionStorage.getItem('key') != 1 &&  window.frameElement == null) && (sessionStorage.getItem('key') != 3 && window.frameElement == null)) {
          window.location = "/account/login";
        }
      </script>
    {% endunless %}
  {% endunless %}
{% else %}
{% assign ns = 'vip-redirect' %}
<a aria-label="VIP Program" href="#{{ ns }}" id="vip-trigger" data-lity style="display:none">VIP Program</a>
<div id="{{ ns }}" class="{{ ns }} lity-hide">
    <div class="{{ ns }}__modal">
        <p class="{{ ns }}__heading">OluKai ‘Ohana Program</p>
        <p class="{{ ns }}__message">Continue to the Olukai ‘Ohana site to unlock your exclusive offer.</p>
        <a aria-label="Continue" href="https://vip.olukai.com/" class="{{ ns }}__button" data-btn-alt-text="Continue"><span>Continue</span></a>
    </div>
</div>

<!-- {{ 'checkout-vip-redirect.scss' | asset_url | stylesheet_tag }} -->
{% endif %} 