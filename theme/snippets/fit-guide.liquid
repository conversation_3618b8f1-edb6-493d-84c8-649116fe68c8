{%- liquid
  assign categories = settings.categories_dynamic | replace: '\n', '<br />' | default: settings.categories
  assign categories = categories | newline_to_br | split: '<br />'
  assign _product = settings.product | default: product
-%} 
{% comment %} hide feature if no products tags match{% endcomment %}
{% assign has_matching_tags = false %}
{% for category in categories %}
  {% liquid
    assign title = category | strip | split: '|' | first | strip
    assign tag_prefix = title | downcase | append: ':' | strip
    assign options = category | strip | split: '|' | last | strip | split: ','
    assign values = _product.tags | where: tag_prefix
    if values.size > 0
      assign has_matching_tags = true
    endif
  %}
{% endfor %}
{% comment %} END hide feature if no products tags match{% endcomment %}
{% if has_matching_tags %}

{% if settings.collapse %}

  <details type="accordion" class="accordion group">
    <summary class="accordion-title">

      <h4 class="my-0 type-item type--lg" style="padding: 10px 0;">Fit Guide</h4>
      <span class="flex group-open:hidden accordion-control">
        {% render 'icon', icon: 'plus' width:24 height:24 %}
      </span>
      <span class="hidden group-open:flex accordion-control">
        {% render 'icon', icon: 'minus' width:24 height:24 %}
      </span>
    </summary>
    <div class="fit-guide accordion-panel">

      {% for category in categories %}
        {%- liquid
          assign title = category | strip | split: '|' | first | strip
          assign tag_prefix = title | downcase | append: ':' | strip
          assign options = category | strip | split: '|' | last | strip | split: ','
            if settings.product != blank
              assign values = settings.product.tags | where: tag_prefix
            else
              assign values = product.tags | where: tag_prefix
            endif
             assign values_size = values.size
            if values_size == 0
              assign values_size = 1
            endif
          assign value = 0
          for option in options
            assign _option = option | downcase | strip | prepend: tag_prefix
            if values contains _option
              assign value = value | plus: forloop.index | times: 1 | divided_by: values_size | minus: 0.25
              assign max = options.size
            endif
          endfor

          if value == 0.0 and values.size > 0
            assign value = values.first | remove: tag_prefix | plus: 0
            assign max = 100
          endif
        -%}
        {% if value > 0.00 %}
        <div class="grid grid-cols-5 gap-y-3 fit-guide">
          <label for="block-{{ block.id }}" class="col-span-5 fit-guide__title lg:col-span-1">{{ title | split: ':' | last }}</label>
          {% render 'progress' options: options active_options: tag_string class: 'flex flex-col gap-y-2 col-span-5 lg:col-span-4' value:value max:max block:block legacy:true %}
        </div>
        {% endif %}
      {% endfor %}

    </div>
  </details>

{% else %}
{%- if settings.title != '' -%}
  <h4 class="fit-guide__heading my-0 type-item type--lg">{{ settings.title | default: 'Fit Guide' }}</h4>
{%- endif -%}
  
  {% for category in categories %}


    {%- liquid
      assign title = category | strip | split: '|' | first | strip
      assign tag_prefix = title | downcase | append: ':' | strip
      assign options = category | strip | split: '|' | last | strip | split: ','
      assign values = _product.tags | where: tag_prefix | join | downcase
      assign total_values = values | split: ' ' | size
      assign valuee = 0
      
      for option in options
        assign _optionn = option | downcase | strip | prepend: tag_prefix    
        if values contains _optionn
          if forloop.index == 1
            assign valuee = 0.25  | plus: valuee
          endif
          if forloop.index == 2
            assign valuee = 1.5 | plus: valuee
          endif
          if forloop.index == 3
            assign valuee = 3 | plus: valuee
          endif        
          assign maxx = options.size 
        endif         
      endfor
      assign valuee = valuee | divided_by: total_values
    -%}
    {% if valuee > 0.00 %}
    <div class="grid grid-cols-5 gap-y-3 fit-guide {% if block.settings.brand_fit_guide == 'top' %}grid-cols-none{% endif %}">
      <label for="block-{{ block.id }}" class="col-span-5 fit-guide__title lg:col-span-1">{{ title | split: ':' | last }}</label>
      {% render 'progress' options: options active_options: tag_string class: 'flex flex-col gap-y-2 col-span-5 lg:col-span-4' value:valuee max:maxx block:block legacy: true %}
    </div>
    {% endif %}
  {% endfor %}
{% endif %}

{% endif %}
