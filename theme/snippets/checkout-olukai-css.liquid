<style>
.page-container,.page-container-2 {
    margin: 0 auto;
    padding: 0 20px;
    width: 100%
}

body,h1,h2,h3,h4,h5,h6,html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

a,body {
    color: #001630
}

[tabindex='-1'],button,img,input,video {
    outline: 0
}

.btn-highlight,.btn-highlight span {
    position: relative
}

.btn-highlight span:before,.btn-highlight-hollow span:before,.btn-highlight-solid span:before {
    top: 50%;
    pointer-events: none;
    content: '';
    will-change: transform,opacity,color;
    left: 50%
}

.ywa-10000,img[data-src]:not([src]),img[data-srcset]:not([srcset]) {
    visibility: hidden
}

.lity-loader,.lity-wrap {
    text-align: center
}

.btn-highlight span:before,.btn-highlight-hollow span:before,.btn-highlight-solid span:before,.lity-content:after,.lity-wrap:before,.video-container:before {
    content: ''
}

.checkout-gift-order__checkbox input,input[type=email],input[type=number],input[type=password],input[type=search],input[type=tel],input[type=text] {
    -webkit-appearance: none;
    -moz-appearance: none
}

h1,h2,h3,h4,h5,h6 {
    font-weight: 400
}

@-webkit-keyframes bounce {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    50% {
        -webkit-transform: translateX(11px);
        transform: translateX(11px)
    }

    100% {
        -webkit-transform: translateX(9px);
        transform: translateX(9px)
    }
}

@keyframes bounce {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    50% {
        -webkit-transform: translateX(11px);
        transform: translateX(11px)
    }

    100% {
        -webkit-transform: translateX(9px);
        transform: translateX(9px)
    }
}

.page-container {
    max-width: 1370px
}

.page-container-2 {
    max-width: 1220px
}

button,td,th {
    padding: 0
}

*,:after,:before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

article,aside,details,figcaption,figure,footer,header,hgroup,img,main,menu,nav,section,video {
    display: block
}

body,main {
    display: -webkit-box;
    display: -ms-flexbox;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal
}

a,abbr,acronym,address,article,aside,audio,blockquote,body,canvas,cite,code,dd,div,dl,dt,em,fieldset,figcaption,figure,footer,form,h1,h2,h3,h4,h5,h6,header,hgroup,html,iframe,img,label,li,mark,menu,nav,ol,optgroup,p,pre,q,section,span,strong,sub,sup,table,tbody,td,textarea,tfoot,th,thead,time,tr,ul,video {
    margin: 0;
    padding: 0
}

button,select {
    text-transform: none
}

button {
    border-radius: 0;
    display: inline-block;
    cursor: pointer;
    border: none;
    background: 0 0
}

button::-moz-focus-inner,input::-moz-focus-inner {
    border: 0;
    padding: 0
}

html {
    -webkit-tap-highlight-color: transparent;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    height: 100%;
    padding-bottom: 0!important
}

input,optgroup,select,span,textarea {
    border-radius: 0;
    color: inherit;
    font-family: inherit;
    font-size: 100%;
    vertical-align: baseline
}

input {
    line-height: normal;
    margin: 0
}

input[type=password] {
    font-family: arial,sans-serif
}

input[type=submit] {
    cursor: pointer;
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-appearance: none
}

fieldset,img,video {
    border: none
}

img,video {
    height: auto;
    max-width: 100%
}

table {
    border-collapse: collapse;
    border-spacing: 0
}

textarea {
    overflow: auto
}

ul {
    list-style-type: none
}

a {
    text-decoration: none
}

body {
    font-family: GTA-Regular,Arial,Helvetica,sans-serif,Arial,sans-serif;
    background-color: #fcfbf5;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    overflow-y: auto
}

body.template-index {
    background-color: #fff
}

@media (min-width: 1025px) {
    body {
        overflow:visible!important
    }
}

body.template-cart,body.template-checkout,body.template-collection,body.template-product,body.template-search {
    background: #fcfbf5
}

main {
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    overflow-y: visible
}

.btn-highlight,.btn-highlight-hollow {
    -webkit-box-align: center;
    font-family: GTA-Bold,Arial,Helvetica,sans-serif;
    font-size: 14px;
    letter-spacing: .56px;
    text-transform: uppercase;
    min-width: 112px
}

iframe[src*=localhost] {
    display: none
}

.btn-highlight {
    -ms-flex-align: center;
    align-items: center;
    background-color: #ff4438;
    border: 1px solid #ff4438;
    border-radius: 3px;
    color: #fff;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    height: 39px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    opacity: 1;
    overflow: hidden;
    padding: 0 18px;
    -webkit-transition: all .2s ease-out;
    transition: all .2s ease-out;
    z-index: 3
}

.btn-highlight span:before {
    background-color: #ff4438;
    border-radius: 50%;
    height: 400px;
    opacity: 0;
    position: absolute;
    -webkit-transform: translate(-50%,-50%) scale(0);
    transform: translate(-50%,-50%) scale(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transition: opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    transition: opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    transition: transform .25s ease,opacity .25s ease-out,color .1s ease;
    transition: transform .25s ease,opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    width: 400px
}

@media (min-width: 1025px) {
    .btn-highlight {
        background-color:transparent;
        color: #ff4438
    }

    .btn-highlight:hover {
        color: #fff
    }

    .btn-highlight:hover span:before {
        opacity: 1;
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1);
        -webkit-transition: opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        transition: opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease;
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        z-index: -1
    }
}

.btn-highlight-hollow {
    -ms-flex-align: center;
    align-items: center;
    background-color: #ff4438;
    border: 1px solid #ff4438;
    border-radius: 3px;
    color: #fff;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    height: 39px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    opacity: 1;
    overflow: hidden;
    padding: 0;
    position: relative;
    -webkit-transition: all .2s ease-out;
    transition: all .2s ease-out;
    z-index: 3
}

.btn-highlight-hollow span {
    position: relative;
    display: block;
    padding: 0 18px;
    width: 100%
}

.btn-highlight-hollow span:before {
    background-color: #ff4438;
    border-radius: 50%;
    height: 400px;
    opacity: 0;
    position: absolute;
    -webkit-transform: translate(-50%,-50%) scale(0);
    transform: translate(-50%,-50%) scale(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transition: opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    transition: opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    transition: transform .25s ease,opacity .25s ease-out,color .1s ease;
    transition: transform .25s ease,opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    width: calc(100% + 40px)
}

@media (max-width: 1024px) {
    .btn-highlight-hollow {
        background-color:transparent;
        color: #ff4438
    }
}

@media (min-width: 1025px) {
    .btn-highlight-hollow {
        background-color:transparent;
        color: #ff4438
    }

    .btn-highlight-hollow:hover {
        color: #fff
    }

    .btn-highlight-hollow:hover span:before {
        opacity: 1;
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1);
        -webkit-transition: opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        transition: opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease;
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        z-index: -1
    }
}

.btn-highlight-solid {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-color: #ff4438;
    border: 1px solid #ff4438;
    border-radius: 3px;
    color: #fff;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    font-family: GTA-Bold,Arial,Helvetica,sans-serif;
    font-size: 14px;
    height: 39px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    letter-spacing: .56px;
    min-width: 112px;
    opacity: 1;
    overflow: hidden;
    padding: 0 18px;
    position: relative;
    text-transform: uppercase;
    -webkit-transition: all .2s ease-out;
    transition: all .2s ease-out;
    z-index: 3
}

.shopify-challenge__button,.shopify-challenge__message {
    font-family: GTA-Bold,Arial,Helvetica,sans-serif;
    font-size: 13px
}

.btn-highlight-solid span {
    position: relative
}

.btn-highlight-solid span:before {
    background-color: #ff4438;
    border-radius: 50%;
    height: 400px;
    opacity: 0;
    position: absolute;
    -webkit-transform: translate(-50%,-50%) scale(0);
    transform: translate(-50%,-50%) scale(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transition: opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    transition: opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    transition: transform .25s ease,opacity .25s ease-out,color .1s ease;
    transition: transform .25s ease,opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    width: 400px
}

.image-cover,.overlay {
    height: 100%;
    top: 0;
    left: 0
}

@media (min-width: 1025px) {
    .btn-highlight-solid span:before {
        display:none
    }

    .btn-highlight-solid:hover {
        color: #fff
    }

    .btn-highlight-solid:hover span:before {
        opacity: 1;
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1);
        -webkit-transition: opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        transition: opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease;
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        z-index: -1
    }
}

.image-cover {
    -o-object-fit: cover;
    object-fit: cover;
    -o-object-position: center center;
    object-position: center center;
    position: absolute;
    width: 100%
}

.overlay {
    position: fixed;
    width: 100%;
    pointer-events: none;
    opacity: 0;
    background: rgba(0,0,0,.7);
    -webkit-transition: opacity 350ms ease;
    transition: opacity 350ms ease;
    z-index: 30
}

.overlay.active {
    opacity: 1;
    pointer-events: auto
}

.overlay--splash.active {
    opacity: 0;
    pointer-events: none
}

.shopify-challenge__container {
    min-height: 70vh;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.shopify-challenge__message {
    color: #001630;
    letter-spacing: -.1px;
    text-align: center;
    line-height: 21px
}

.shopify-challenge__button {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 0;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    -webkit-transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1),-webkit-box-shadow .3s cubic-bezier(.3,1,.45,1);
    transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1),-webkit-box-shadow .3s cubic-bezier(.3,1,.45,1);
    transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1),box-shadow .3s cubic-bezier(.3,1,.45,1);
    transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1),box-shadow .3s cubic-bezier(.3,1,.45,1),-webkit-box-shadow .3s cubic-bezier(.3,1,.45,1);
    padding-left: 20px;
    padding-right: 20px;
    letter-spacing: 1.3px;
    height: 40px;
    background-color: #0c4065;
    color: #fff
}

@media (min-width: 1024px) {
    .shopify-challenge__button {
        display:-webkit-inline-box;
        display: -ms-inline-flexbox;
        display: inline-flex
    }
}

.shopify-challenge__button svg {
    margin-left: 10px;
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-transition: -webkit-transform .4s ease-out;
    transition: -webkit-transform .4s ease-out;
    transition: transform .4s ease-out;
    transition: transform .4s ease-out,-webkit-transform .4s ease-out
}

.shopify-challenge__button span {
    line-height: 1
}

.shopify-challenge__button:hover svg {
    -webkit-transform: translateX(9px);
    transform: translateX(9px);
    -webkit-animation: bounce .4s;
    animation: bounce .4s;
    -webkit-animation-timing-function: cubic-bezier(.3,1,.45,1);
    animation-timing-function: cubic-bezier(.3,1,.45,1)
}

.shopify-challenge__button:focus,.shopify-challenge__button:hover {
    background-color: #163c57
}

.shopify-challenge__button svg .fill {
    fill: #0c4065
}

.template-page--commitment {
    background-color: #fff
}

.zopim.hide {
    z-index: 0
}

.noscroll {
    overflow: hidden!important
}

.template-index .flickity-enabled.is-draggable {
    -ms-touch-action: inherit;
    touch-action: inherit;
    -webkit-user-select: none;
    -webkit-user-drag: none
}

[data-test-id=ChatWidgetMobileButton] {
    z-index: 10!important
}

.video-container {
    position: relative
}

.video-container:before {
    display: block;
    padding-top: 56.25%
}

.video-container__video {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.lity,.lity-wrap {
    z-index: 9990;
    position: fixed;
    top: 0;
    outline: 0!important;
    bottom: 0;
    right: 0;
    left: 0
}

#onetrust-banner-sdk #onetrust-reject-all-handler,#onetrust-consent-sdk #onetrust-accept-btn-handler,#onetrust-consent-sdk #onetrust-banner-sdk,#onetrust-consent-sdk #onetrust-pc-btn-handler {
    background-color: #001630!important
}

.ywa-10000 {
    opacity: 0
}

.grecaptcha-badge {
    visibility: hidden!important
}

/*! Lity - v2.3.1 - 2018-04-20
* http://sorgalla.com/lity/
* Copyright (c) 2015-2018 Jan Sorgalla; Licensed MIT */
.lity {
    white-space: nowrap;
    background: #0b0b0b;
    background: rgba(0,0,0,.9);
    opacity: 0
}

.lity.lity-opened {
    opacity: 1
}

.lity.lity-closed {
    opacity: 0
}

.lity * {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.lity-wrap:before {
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-right: -.25em
}

.lity-loader {
    z-index: 9991;
    color: #fff;
    position: absolute;
    top: 50%;
    margin-top: -.8em;
    width: 100%;
    font-size: 14px;
    font-family: Arial,Helvetica,sans-serif;
    opacity: 0;
    -webkit-transition: opacity .3s ease;
    transition: opacity .3s ease
}

.lity-close,.lity-close:active,.lity-close:focus,.lity-close:hover,.lity-close:visited {
    text-align: center;
    padding: 0;
    font-style: normal;
    font-size: 35px;
    font-family: Arial,Baskerville,monospace;
    line-height: 35px;
    text-shadow: 0 1px 2px rgba(0,0,0,.6);
    background: 0 0;
    border: 0;
    color: #fff;
    text-decoration: none
}

.lity-loading .lity-loader {
    opacity: 1
}

.lity-container {
    z-index: 9992;
    position: relative;
    text-align: center;
    display: inline-block;
    white-space: normal;
    max-width: 100%;
    max-height: 100%;
    outline: 0!important
}

.lity-content {
    z-index: 9993;
    width: 100%
}

.lity-content:after {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    display: block;
    right: 0;
    width: auto;
    height: auto;
    z-index: -1;
    -webkit-box-shadow: 0 0 8px rgba(0,0,0,.6);
    box-shadow: 0 0 8px rgba(0,0,0,.6)
}

.lity-close {
    z-index: 9994;
    width: 35px;
    height: 35px;
    position: fixed;
    right: 0;
    top: 0;
    -webkit-appearance: none;
    cursor: pointer;
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none
}

.lity-close::-moz-focus-inner {
    border: 0;
    padding: 0
}

.lity-close:active,.lity-close:focus,.lity-close:hover,.lity-close:visited {
    outline: 0;
    -webkit-box-shadow: none;
    box-shadow: none
}

.lity-close:active {
    top: 1px
}

.lity-image img {
    max-width: 100%;
    display: block;
    line-height: 0;
    border: 0
}

.lity-facebookvideo .lity-container,.lity-googlemaps .lity-container,.lity-iframe .lity-container,.lity-vimeo .lity-container,.lity-youtube .lity-container {
    width: 100%;
    max-width: 964px
}

.lity-iframe-container {
    width: 100%;
    height: 0;
    padding-top: 56.25%;
    overflow: auto;
    pointer-events: auto;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-overflow-scrolling: touch
}

.lity-iframe-container iframe {
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    -webkit-box-shadow: 0 0 8px rgba(0,0,0,.6);
    box-shadow: 0 0 8px rgba(0,0,0,.6);
    background: #000
}

.lity-hide {
    display: none
}

.lity-active body {
    overflow: hidden
}

.checkout-rewards {
    border: 1px solid #f5e9d8;
    background-color: #fff;
    border-bottom: none;
    height: auto;
    display: block;
    padding: 16px 8px;
    width: 100%;
    overflow: hidden
}

.checkout-rewards .rewards__container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    position: relative
}

@media (min-width: 768px) {
    .checkout-rewards .rewards__container {
        -webkit-box-orient:horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row
    }

    .checkout-rewards .rewards__container .rewards__title {
        width: 210px
    }
}

@media (max-width: 767px) {
    .checkout-rewards .rewards__container {
        -webkit-box-orient:horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center
    }

    .checkout-rewards .panel {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex
    }
}

.checkout-rewards .rewards__container .rewards {
    padding: 0 18px;
    -ms-flex-item-align: left;
    -ms-grid-row-align: left;
    align-self: left
}

.checkout-rewards .rewards__container .rewards__title {
    text-align: left;
    letter-spacing: 0;
    color: #fff;
    font-family: GTA-Regular,Arial,Helvetica,sans-serif,Arial,sans-serif;
    font-size: 18px;
    font-weight: 400
}

.checkout-rewards .rewards__container h1 {
    font-family: GTA-Bold,Arial,Helvetica,sans-serif,Arial,sans-serif;
    color: #231f20;
    font-size: 18px;
    font-weight: 400
}

.checkout-rewards .panel .panel__container .input-container input.validate+label,.checkout-rewards .panel .panel__container .input-container input:not(.disable-styling),.checkout-rewards .panel p,.checkout-rewards .rewards__container h2,.checkout-rewards .submit-container [type=button] {
    font-family: GTA-Regular,Arial,Helvetica,sans-serif,Arial,sans-serif
}

@media (min-width: 768px) {
    .checkout-rewards .rewards__container h1 {
        text-align:left
    }
}

.checkout-rewards .rewards__container h2 {
    text-align: left;
    letter-spacing: .36px;
    color: #cfb87a;
    text-transform: uppercase;
    font-size: 18px;
    font-weight: 700
}

.checkout-rewards .rewards__container p {
    font-size: 13px;
    text-align: center
}

.checkout-rewards .rewards__container div.error {
    color: #ff4438;
    font-size: 11px;
    line-height: 16px;
    margin-top: 3px;
    margin-bottom: 12px
}

.checkout-rewards .rewards__container div.error.hidden {
    visibility: hidden
}

.checkout-rewards .rewards__container.rewards__auth {
    background: #fcf9f3
}

.checkout-rewards .rewards__container.rewards__auth .rewards__title {
    color: #cfb87a
}

.checkout-rewards .rewards__container.rewards__auth .rewards .rewards__title {
    width: 135px;
    position: relative
}

.checkout-rewards .rewards__container.rewards__auth .rewards .rewards__title img {
    position: absolute;
    top: -28px
}

.checkout-rewards .rewards__container.active {
    margin-bottom: 11px
}

.checkout-rewards.rewards__auth {
    background: inherit;
    border-bottom: 1px solid #f5e9d8;
    margin-top: 20px;
    overflow: hidden
}

@media (min-width: 768px) {
    .checkout-rewards .rewards__container p {
        font-size:11px;
        text-align: left
    }

    .checkout-rewards .rewards__container.rewards__auth .rewards .rewards__title {
        width: 150px
    }

    .checkout-rewards .rewards__container.rewards__auth .rewards .rewards__title img {
        position: relative;
        top: 0
    }

    .checkout-rewards.rewards__auth {
        overflow: visible
    }
}

.checkout-rewards.active {
    padding-bottom: 24px
}

.checkout-rewards .accordion {
    cursor: pointer;
    width: 100%;
    -webkit-transition: .4s;
    transition: .4s
}

.checkout-rewards .accordion:after {
    content: '\2303';
    -webkit-transform: scaleY(-1);
    transform: scaleY(-1);
    color: #231f20;
    font-weight: 700;
    float: right;
    margin-left: 0;
    -ms-flex-item-align: end;
    align-self: flex-end;
    position: absolute;
    top: 5px;
    right: 20px
}

@media (min-width: 768px) {
    .checkout-rewards .accordion:after {
        margin-left:5px;
        -ms-flex-item-align: unset;
        -ms-grid-row-align: unset;
        align-self: unset;
        position: relative;
        top: 0;
        right: 0
    }
}

.checkout-rewards .active:after {
    content: '\2303';
    -webkit-transform: scaleY(1);
    transform: scaleY(1)
}

.checkout-rewards .panel {
    padding: 0 18px;
    max-height: 0;
    overflow: hidden;
    -webkit-transition: max-height .2s ease-out;
    transition: max-height .2s ease-out
}

.checkout-rewards .panel p {
    font-size: 12px;
    font-weight: 400
}

.checkout-rewards .panel p strong {
    font-weight: 700
}

.checkout-rewards .panel .panel__container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    width: 100%
}

.checkout-rewards .panel .panel__container .input-container {
    width: 100%;
    position: relative
}

.checkout-rewards .panel .panel__container .input-container input {
    border: 1px solid #ded1be
}

.checkout-rewards .panel .panel__container .input-container input:focus {
    border: 1px solid #001630
}

.checkout-rewards .panel .panel__container .input-container input:not(.disable-styling) {
    background-color: #fff;
    border-radius: 2px;
    -webkit-box-shadow: none!important;
    box-shadow: none!important;
    color: #231f20;
    font-size: 16px;
    height: 46px;
    line-height: normal;
    margin-bottom: 12px;
    width: 100%;
    padding: 0 13px
}

.checkout-rewards .panel .panel__container .input-container input.validate+label {
    font-size: 15px;
    position: absolute;
    letter-spacing: -.1px;
    top: 15px;
    left: 0;
    padding: 0 .91667em;
    z-index: 1;
    font-weight: 400;
    color: #797979;
    margin: 0
}

.checkout-rewards .submit-container {
    width: 73px
}

@media (min-width: 768px) {
    .checkout-rewards .submit-container {
        width:93px
    }
}

.checkout-rewards .submit-container [type=button] {
    width: 100%;
    height: 46px;
    font-size: 14px;
    font-weight: 700;
    padding: .6rem
}

.checkout-rewards[data-test=dark] {
    background-image: url(/cdn/shop/files/blue_gradient-desktop.png?v=1662069601);
    background-repeat: no-repeat
}

.checkout-rewards[data-test=dark] .accordion:after,.checkout-rewards[data-test=dark] .panel p,.checkout-rewards[data-test=dark] .panel p a,.checkout-rewards[data-test=dark] .rewards__container,.checkout-rewards[data-test=dark] .rewards__container .rewards h1,.checkout-rewards[data-test=dark] .rewards__container .rewards p,.checkout-rewards[data-test=dark] .rewards__container .rewards p a {
    color: #fff
}

.checkout-rewards[data-test=dark] .panel .error {
    color: #ff4438
}

.checkout-rewards[data-test=dark] .panel .panel__container .input-container input {
    border: 1px solid transparent
}

.checkout-rewards[data-test=dark] .panel .panel__container .input-container input:focus {
    border: 1px solid #001630
}

.checkout-rewards[data-test=dark] .submit-container [type=button] {
    background-color: #cfb87a;
    color: #001630
}

.checkout-rewards[data-test=light] .panel p,.checkout-rewards[data-test=light] .panel p a {
    color: #736b67
}

.checkout-rewards[data-test=light] .submit-container [type=button] {
    background-color: #cfb87a;
    color: #001630
}

.desktop,.mobile {
    display: initial
}

@media (min-width: 768px) {
    .mobile {
        display:none
    }
}

@media (max-width: 768px) {
    .mobile--padding {
        padding:42px 8px 16px
    }
}

.checkout-breadcrumbs .breadcrumb {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-top: 0;
    padding-bottom: 0
}

@media (min-width: 1024px) {
    .template-cart .checkout-breadcrumbs .breadcrumb {
        padding-left:20px;
        padding-right: 20px
    }
}

.checkout-breadcrumbs .breadcrumb .breadcrumb__chevron-icon {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    fill: #797979;
    height: 10px;
    margin: 0 5px;
    width: 10px
}

.checkout-breadcrumbs .breadcrumb .breadcrumb-container {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    margin-top: 20px
}

.checkout-breadcrumbs .breadcrumb .breadcrumb__item {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #797979;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-family: GTA-Regular,Arial,Helvetica,sans-serif;
    font-size: 12px;
    line-height: 18.2px
}

.checkout-breadcrumbs .breadcrumb .breadcrumb__item--completed,.checkout-breadcrumbs .breadcrumb .breadcrumb__item--current {
    color: #001630
}

.checkout-breadcrumbs .breadcrumb .breadcrumb__item--completed .breadcrumb__link,.checkout-breadcrumbs .breadcrumb .breadcrumb__item--current .breadcrumb__link {
    color: inherit
}

.checkout-breadcrumbs .breadcrumb .breadcrumb__item--current {
    font-family: GTA-Bold,Arial,Helvetica,sans-serif
}

.checkout-breadcrumbs .breadcrumb .breadcrumb__item--completed {
    color: #797979;
    font-family: GTA-Medium,Arial,Helvetica,sans-serif
}

.checkout-breadcrumbs .breadcrumb .breadcrumb__link {
    color: inherit;
    text-decoration: none
}

.page--thank-you .checkout-breadcrumbs .breadcrumb,.template-checkout .checkout-breadcrumbs .breadcrumb .breadcrumb__link[href*='/cart'],.template-checkout .checkout-breadcrumbs .breadcrumb .breadcrumb__link[href*='/cart']+svg {
    display: none
}

.page--stock-problems.page--logo-main .checkout-breadcrumbs .breadcrumb {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

body {
    min-height: 100vh
}

.alternative-payment-separator {
    color: #797979;
    font-size: 12px;
    margin: 25px 0 0;
    padding-bottom: 0
}

.alternative-payment-separator:after,.alternative-payment-separator:before {
    background-color: #f5e9d8
}

.alternative-payment-separator__content {
    text-transform: lowercase
}

.alternative-payment-separator__content:first-letter {
    text-transform: uppercase
}

@media (max-width: 999px) {
    .page--thank-you.anyflexbox .content .wrap>aside {
        margin-top:-15px
    }

    .page--thank-you.anyflexbox .content .wrap .main {
        padding-top: 20px
    }

    .anyflexbox .content .wrap {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .anyflexbox .content .wrap>aside {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1;
        margin: 10px -20px 0
    }

    .anyflexbox .content .wrap .checkout__header {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }

    .anyflexbox .content .wrap .main {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3;
        padding-top: 25px
    }

    .anyflexbox .content .wrap .sidebar {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2
    }
}

.checkbox__label {
    color: #797979;
    font-family: GTA-Regular,Arial,Helvetica,sans-serif;
    font-size: 13px
}

.dynamic-checkout {
    margin-top: 0
}

.dynamic-checkout [data-shopify-buttoncontainer=true] {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.dynamic-checkout__content,.dynamic-checkout__title:after,.dynamic-checkout__title:before {
    border-color: #f5e9d8
}

.section.section--contact-information {
    padding-bottom: 15px;
    padding-top: 30px
}

.section.section--shipping-address .section__content .fieldset>.field {
    display: none
}

@media (min-width: 1000px) {
    .alternative-payment-separator {
        font-size:13px
    }

    .template-checkout .main {
        padding-left: 66px;
        padding-right: 66px;
        width: 56.11111%
    }

    .template-checkout .sidebar {
        background-color: #fff;
        padding-left: 44px;
        padding-right: 44px;
        width: 43.88889%
    }

    .template-checkout .checkout__main-inner {
        margin-left: auto;
        max-width: 575px;
        width: 100%
    }

    .template-checkout .checkout__sidebar-inner {
        max-width: 420px;
        width: 100%
    }
}

.checkout__content a,.link {
    color: #ff4438;
    text-decoration: underline
}

.checkout__content .step__footer__continue-btn {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-color: #ff4438;
    border: 1px solid #ff4438;
    border-radius: 3px;
    color: #fff;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    font-family: GTA-Bold,Arial,Helvetica,sans-serif;
    font-size: 14px;
    height: 55px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    letter-spacing: .56px;
    min-width: 112px;
    opacity: 1;
    overflow: hidden;
    position: relative;
    text-transform: uppercase;
    -webkit-transition: all .2s ease-out;
    transition: all .2s ease-out;
    z-index: 3;
    padding: 0 19px;
    text-decoration: none
}

.main h2,.section__title {
    text-transform: capitalize
}

.checkout__content .step__footer__continue-btn span {
    position: relative
}

.checkout__content .step__footer__continue-btn span:before {
    background-color: #ff4438;
    border-radius: 50%;
    content: '';
    height: 400px;
    left: 50%;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%) scale(0);
    transform: translate(-50%,-50%) scale(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transition: opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    transition: opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    transition: transform .25s ease,opacity .25s ease-out,color .1s ease;
    transition: transform .25s ease,opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    width: 400px;
    will-change: transform,opacity,color
}

@media (min-width: 1024px) {
    .checkout__content .step__footer__continue-btn span:before {
        display:none
    }

    .checkout__content .step__footer__continue-btn:hover {
        color: #fff
    }

    .checkout__content .step__footer__continue-btn:hover span:before {
        opacity: 1;
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1);
        -webkit-transition: opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        transition: opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease;
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        z-index: -1
    }
}

.checkout__content .step__footer__continue-btn:focus,.checkout__content .step__footer__continue-btn:hover {
    background-color: #ff4438
}

.checkout__header--desktop {
    display: none
}

.checkout__header--mobile {
    padding-top: 30px
}

@media (min-width: 1000px) {
    .checkout__content .step__footer__continue-btn {
        height:39px
    }

    .checkout__header--desktop {
        display: block;
        padding-bottom: 33px
    }

    .checkout__header--mobile {
        display: none
    }
}

.checkout__logo {
    display: inline-block;
    margin-bottom: 25px;
    max-width: 108px
}

.checkout__logo svg {
    display: block;
    max-width: 100%
}

.page--thank-you .template-checkout .checkout__content .content-box .content-box__row--no-border,.shipping_address_notice:empty {
    display: none
}

.floating-labels .main .field__label {
    color: #797979;
    font-size: 11px;
    padding-left: 12px;
    padding-right: 13px
}

@media (min-width: 1000px) {
    .checkout__logo {
        margin-bottom:18px;
        max-width: none
    }

    .main__header {
        padding-bottom: 0
    }
}

.stock-problem-table__header th {
    color: #000000;  /* #381300 */
}

.template-checkout .wrap {
    max-width: 100%;
    padding-left: 20px;
    padding-right: 20px;
    width: 100%
}

@media (min-width: 1000px) {
    .template-checkout .wrap {
        padding-left:0;
        padding-right: 0
    }

    .page--no-banner .main,.page--no-banner .sidebar {
        padding-top: 60px
    }
}

.template-checkout .checkout__content {
    background-color: #fcf9f3;
    font-family: GTA-Regular,Arial,Helvetica,sans-serif;
    font-size: 13px;
    letter-spacing: -.1px
}

.template-checkout .checkout__content .content-box__row:first-child .map {
    margin: -1px
}

.template-checkout .checkout__content .content-box .content-box__row {
    background-color: transparent;
    border-radius: 0;
    padding: 14px 13px
}

.template-checkout .checkout__content .content-box .content-box__row.content-box__row--no-padding {
    padding: 0
}

.template-checkout .checkout__content .content-box .content-box__row .content-box__row {
    border-radius: 0
}

.template-checkout .checkout__content .content-box .content-box__row .content-box__row--secondary {
    background-color: transparent
}

.floating-labels .field--show-floating-label .field__input--select {
    padding-top: 20px
}

.floating-labels .field__input-wrapper--flag-focus .field__input,.floating-labels .field__input:focus {
    -webkit-box-shadow: none;
    box-shadow: none
}

.main h2,.main h3 {
    color: #231f20;
    font-family: GTA-Medium,Arial,Helvetica,sans-serif
}

.main h2 {
    font-size: 18px
}

.section__header {
    color: #797979;
    margin-bottom: 15px
}

.section__header a {
    margin-left: 5px
}

.section__title {
    color: #000000;  /* #381300 */;
    font-family: GTA-Regular,Arial,Helvetica,sans-serif;
    font-size: 22px;
    letter-spacing: -.4px;
    line-height: 1.22222
}

@media (min-width: 1000px) {
    .section__title {
        font-size:27px;
        line-height: 1.27027
    }

    .section--contact-information .section__title {
        margin-top: 0
    }
}

.section__title+.layout-flex__item {
    padding-top: 0!important
}

.section--shipping-method .section__content .arrival_date {
    position: static
}

.section--shipping-method .section__content .shipping__notice {
    color: #736b67;
    font-size: 12px;
    line-height: 1.33333;
    margin: -9px auto 17px
}

.template-checkout input:-webkit-autofill,.template-checkout input:-webkit-autofill:active,.template-checkout input:-webkit-autofill:focus,.template-checkout input:-webkit-autofill:hover {
    -webkit-box-shadow: 0 0 0 30px #fcf9f3 inset!important;
    -webkit-text-fill-color: #000000 !important;  /* #381300 */
}

.template-checkout .btn--disabled {
    pointer-events: none
}

.template-checkout .content-box {
    background-color: #fff;
    border-color: #f5e9d8;
    border-radius: 3px;
    color: #231f20
}

@media (min-width: 750px) {
    .section--shipping-method .section__content .shipping__notice {
        margin-top:-11px
    }

    .template-checkout .main__content {
        padding-bottom: 50px
    }
}

.template-checkout .main__content .content-box__row~.content-box__row {
    border-top: 1px solid #f5e9d8
}

.page--thank-you .template-checkout .main__content .content-box__row~.content-box__row {
    border-top: none
}

.template-checkout .field__input--select {
    height: 50px
}

.template-checkout .field__input {
    background-color: #fff;
    border-radius: 3px;
    border: 1px solid #f5e9d8;
    color: #231f20;
    display: block;
    font-size: 14px;
    height: 46px;
    padding-left: 12px;
    padding-right: 12px
}

.template-checkout .field__input--iframe-container {
    padding-left: 1px;
    padding-right: 1px
}

@media (min-width: 1000px) {
    .template-checkout .field__input {
        border-color:#ded1be;
        font-size: 15px
    }
}

.template-checkout .field__input:focus {
    border-color: #001630;
    border-width: 2px;
    outline: 0
}

.template-checkout .field__input[placeholder]:-ms-input-placeholder {
    color: #797979;
    font-size: inherit;
    visibility: visible
}

.template-checkout .field__input[placeholder]::-webkit-input-placeholder {
    color: #797979;
    font-size: inherit;
    visibility: visible
}

.template-checkout .field__input[placeholder]::-moz-placeholder {
    color: #797979;
    font-size: inherit;
    visibility: visible
}

.template-checkout .field__input[placeholder]::-ms-input-placeholder {
    color: #797979;
    font-size: inherit;
    visibility: visible
}

.template-checkout .field__input[placeholder]::placeholder {
    color: #797979;
    font-size: inherit;
    visibility: visible
}

.template-checkout .field__input-btn {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-color: #ff4438;
    border: 1px solid #ff4438;
    border-radius: 3px;
    color: #fff;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    font-family: GTA-Bold,Arial,Helvetica,sans-serif;
    font-size: 14px;
    height: 46px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    letter-spacing: .56px;
    min-width: auto;
    opacity: 1;
    overflow: hidden;
    padding: 0 18px;
    position: relative;
    text-transform: uppercase;
    -webkit-transition: all .2s ease-out;
    transition: all .2s ease-out;
    z-index: 3
}

.giftcard-message,.redirecttovip-message,.vip-redirect {
    min-height: 2vh;
    min-width: 50%
}

.template-checkout .field__input-btn span {
    position: relative
}

.template-checkout .field__input-btn span:before {
    background-color: #ff4438;
    border-radius: 50%;
    content: '';
    height: 400px;
    left: 50%;
    opacity: 0;
    pointer-events: none;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%) scale(0);
    transform: translate(-50%,-50%) scale(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-transition: opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    transition: opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    transition: transform .25s ease,opacity .25s ease-out,color .1s ease;
    transition: transform .25s ease,opacity .25s ease-out,color .1s ease,-webkit-transform .25s ease;
    width: 400px;
    will-change: transform,opacity,color
}

@media (min-width: 1024px) {
    .template-checkout .field__input-btn span:before {
        display:none
    }

    .template-checkout .field__input-btn:hover {
        color: #fff
    }

    .template-checkout .field__input-btn:hover span:before {
        opacity: 1;
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1);
        -webkit-transition: opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        transition: opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease;
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease,-webkit-transform .4s ease-out;
        z-index: -1
    }
}

.template-checkout .field__input-btn.btn--disabled {
    background-color: #ff4438
}

.template-checkout .field__input-btn:hover:before {
    display: none
}

.template-checkout .input-checkbox:focus,.template-checkout .input-radio:focus {
    border-width: 1px;
    border-color: #d9d9d9
}

.template-checkout .input-checkbox:after,.template-checkout .input-radio:after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: scale(.2);
    transform: scale(.2);
    -webkit-transition: all .2s ease-in-out .1s;
    transition: all .2s ease-in-out .1s;
    opacity: 0
}

.template-checkout .input-radio:after {
    width: 4px;
    height: 4px;
    margin-left: -2px;
    margin-top: -2px;
    background-color: #fff;
    border-radius: 50%
}

.template-checkout .input-checkbox:checked,.template-checkout .input-radio:checked {
    -webkit-box-shadow: 0 0 0 10px #ff4438 inset;
    box-shadow: 0 0 0 10px #ff4438 inset;
    border: none
}

.template-checkout .input-checkbox:checked:after,.template-checkout .input-radio:checked:after {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1
}

.template-checkout .input-checkbox {
    border-radius: 0
}

.template-checkout .review-block~.review-block {
    border-top-color: #f5e9d8
}

.template-checkout .review-block__label {
    font-size: 11px;
    white-space: nowrap
}

.checkout__footer a,.template-checkout .step__footer__previous-link {
    font-family: GTA-Medium,Arial,Helvetica,sans-serif;
    font-size: 13px;
    text-decoration: none
}

.template-checkout .review-block__link a {
    text-decoration: none
}

.template-checkout .step__footer {
    margin-bottom: 3px;
    margin-top: 24px;
    text-align: center
}

@media (min-width: 750px) {
    .template-checkout .step__footer {
        -webkit-box-orient:horizontal;
        -webkit-box-direction: reverse;
        -ms-flex-direction: row-reverse;
        flex-direction: row-reverse;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        margin-bottom: 0;
        margin-top: 30px
    }
}

.template-checkout .step__footer__previous-link {
    color: #001630;
    display: inline-block;
    letter-spacing: 0;
    line-height: 18px;
    margin: 10px auto 0;
    padding: 10px 0
}

.template-checkout .step__footer__previous-link[href*='/cart'] {
    display: none
}

@media (min-width: 750px) {
    .template-checkout .step__footer__previous-link {
        margin:0
    }
}

.template-checkout .step__footer .previous-link__icon {
    display: inline-block
}

.page--thank-you .content-box .content-box__row:first-child h2:only-child,.page--thank-you .os-header__hanging-icon,.page--thank-you .step__footer__info {
    display: none
}

.template-checkout .step__footer .icon-svg--color-accent {
    color: inherit
}

.template-checkout .step__footer__continue-btn.btn--loading .btn__content,.template-checkout .step__footer__continue-btn.btn--loading:focus .btn__content,.template-checkout .step__footer__continue-btn.btn--loading:hover .btn__content {
    color: transparent
}

.checkout__footer {
    border-top: 1px solid #ded1be;
    padding: 20px 0
}

.checkout__footer a {
    color: #001630
}

.page--thank-you .content-box h3,.page--thank-you .os-step__title {
    font-family: GTA-Regular,Arial,Helvetica,sans-serif
}

.checkout__footer a:not(:last-of-type) {
    margin-right: 20px
}

.page--thank-you .content-box {
    color: #736b67;
    line-height: 1.6
}

.page--thank-you .content-box h3 {
    color: #231f20;
    font-size: 14px;
    margin-bottom: 0
}

.page--thank-you .content-box *+h3 {
    margin-top: 27px
}

.page--thank-you .content-box p {
    margin-top: 2px
}

.page--thank-you .content-box .address,.page--thank-you .content-box p {
    color: #797979;
    font-size: 12px;
    line-height: 1.33333
}

.page--thank-you .content-box:last-child {
    margin-top: 15px
}

@media (min-width: 1000px) {
    .template-checkout .step__footer__continue-btn.sticky__continue-btn {
        display:none
    }

    .page--thank-you .content-box:last-child {
        margin-top: 50px
    }
}

.page--thank-you .content-box:last-child .content-box__row:last-of-type {
    padding: 14px 18px
}

.page--thank-you .content-box:last-child .content-box__row--secondary {
    margin-top: 20px
}

.page--thank-you .content-box:first-child .content-box__row:last-child {
    padding: 23px 18px 24px
}

.page--thank-you .os-header__heading {
    padding-left: 55px;
    position: relative
}

.page--thank-you .os-header__heading:before {
    background-color: #ff4438;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 50 50' fill='none' stroke-width='2' class='checkmark'%3E%3Cpath class='checkmark__circle' d='M25 49c13.255 0 24-10.745 24-24S38.255 1 25 1 1 11.745 1 25s10.745 24 24 24z'%3E%3C/path%3E%3Cpath class='checkmark__check' d='M15 24.51l7.307 7.308L35.125 19' stroke='white'%3E%3C/path%3E%3C/svg%3E");
    border-radius: 50%;
    content: '';
    height: 45px;
    left: 0;
    margin-right: 10px;
    top: -2px;
    position: absolute;
    width: 45px
}

@media (min-width: 1000px) {
    .page--thank-you .os-header__heading {
        margin-bottom:5px;
        padding-left: 70px
    }
}

@media (min-width: 1000px) and (min-width:1000px) {
    .page--thank-you .os-header__heading:before {
        height:50px;
        width: 50px
    }
}

.page--thank-you .os-order-number {
    color: #736b67
}

.page--thank-you .os-step__description {
    font-size: 12px;
    line-height: 1.33333
}

.page--thank-you .os-step__special-description {
    margin-top: 1px
}

.page--thank-you .os-step__title {
    color: #231f20;
    font-size: 14px;
    margin-bottom: 0
}

.order-summary-toggle__text,.product-thumbnail__quantity {
    font-family: GTA-Medium,Arial,Helvetica,sans-serif
}

.page--thank-you .section {
    padding-top: 0
}

.page--thank-you .section__header {
    margin-bottom: 20px
}

@media (min-width: 750px) {
    .section {
        padding-top:26px
    }
}

.section--page-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

#shopify-section-footer,#shopify-section-header,#shopify-section-header+.checkout-breadcrumbs,.display-table .logged-in-customer-information__avatar-wrapper,.tags-list .reduction-code__icon {
    display: none
}

.section--page-title .exclamation-mark {
    bottom: auto;
    color: #000000;  /* #381300 */;
    height: 22px;
    left: auto;
    margin: 0 10px 0 0;
    right: auto;
    position: relative;
    top: 2px;
    width: 22px
}

.section--page-title .section__text {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%
}

.tags-list .tag {
    line-height: 1;
    padding: 11px 12px 10px
}

.tags-list .tag__text {
    font-size: 11px;
    text-transform: none
}

.tags-list .tag__button .icon-svg {
    stroke: currentColor;
    -webkit-transition: 150ms ease-in-out;
    transition: 150ms ease-in-out
}

.tags-list .tag__button:focus .icon-svg,.tags-list .tag__button:hover .icon-svg {
    stroke: #000000;  /* #381300 */
}

.label-hidden {
    position: absolute!important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    margin: -1px;
    padding: 0;
    border: 0
}

.content-box .order-summary__emphasis {
    color: #736b67
}

.content-box .payment-due-label__total,.content-box .payment-due__currency,.content-box .payment-due__price {
    color: #000000;  /* #381300 */;
    font-size: 18px;
    font-weight: inherit;
    letter-spacing: inherit;
    line-height: inherit;
    vertical-align: inherit;
    margin-right: 0
}

.total-line-table__tbody+.total-line-table__footer .total-line td::before,.total-line-table__tbody+.total-line-table__footer .total-line th::before {
    top: 10px
}

.order-summary {
    background-color: #fff
}

.order-summary .total-line td,.order-summary .total-line th {
    padding-top: 12px
}

.order-summary__currency-code {
    color: #736b67;
    font-size: 12px;
    margin-right: 15px
}

.order-summary__sections {
    color: #000000;  /* #381300 */;
    height: auto
}

.order-summary__section__content {
    padding-bottom: 10px
}

@media (min-width: 1000px) {
    .order-summary {
        background-color:transparent;
        border: none;
        margin-bottom: 50px
    }

    .page--thank-you .order-summary {
        margin-top: 80px
    }

    .order-summary__section__content {
        padding-top: 10px
    }
}

.order-summary__section--total-lines table {
    font-size: 14px
}

.order-summary-toggle {
    background-color: #fff;
    border: none;
    padding: 0;
    position: relative
}

.order-summary-toggle:after,.order-summary-toggle:before {
    background-color: #f5e9d8;
    content: '';
    height: 1px;
    left: 0;
    position: absolute;
    right: 0
}

.order-summary-toggle:after {
    bottom: 0
}

.order-summary-toggle:before {
    top: 0
}

.order-summary-toggle__inner {
    padding: 16px 20px
}

.order-summary-toggle .wrap {
    padding: 0
}

.order-summary-toggle.order-summary-toggle--hide:after {
    left: 20px;
    right: 20px
}

.order-summary-toggle:focus .order-summary-toggle__dropdown,.order-summary-toggle:hover .order-summary-toggle__dropdown {
    fill: #001630
}

.order-summary-toggle:focus .order-summary-toggle__text,.order-summary-toggle:hover .order-summary-toggle__text {
    color: #001630
}

.order-summary-toggle__dropdown {
    fill: #001630;
    margin-left: 7px;
    position: relative;
    top: -1px
}

.checkout-pobox__button svg .fill,.vip-redirect__button svg .fill {
    fill: #0c4065
}

.order-summary-toggle__icon-wrapper {
    padding-right: 14px
}

.order-summary-toggle__text {
    color: #001630;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.order-summary__section~.order-summary__section {
    border-top-color: #f5e9d8
}

.sidebar .total-line-table__tbody+.total-line-table__footer .total-line:first-child td::before,.sidebar .total-line-table__tbody+.total-line-table__footer .total-line:first-child th::before,.sidebar .total-line-table__tbody+.total-line-table__tbody .total-line:first-child td::before,.sidebar .total-line-table__tbody+.total-line-table__tbody .total-line:first-child th::before {
    background-color: #f5e9d8
}

.product td {
    padding-bottom: 10px;
    padding-top: 6px
}

.product-thumbnail,.product-thumbnail:after,.product-thumbnail__wrapper {
    border-radius: 3px
}

.product-thumbnail {
    background-color: transparent;
    height: 70px;
    isolation: isolate;
    width: 70px
}

.product-thumbnail:after {
    border: 1px solid #f5e9d8
}

.product-thumbnail__image {
    mix-blend-mode: multiply
}

.product-thumbnail__quantity {
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0;
    text-align: center;
    position: absolute;
    top: -.75em;
    right: -.75em;
    background: #ff4438;
    color: #fff;
    border-radius: 50%;
    height: 20px;
    width: 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.sidebar,.sidebar .order-summary__emphasis,.sidebar .payment-due__currency,.sidebar .product__price {
    font-family: GTA-Regular,Arial,Helvetica,sans-serif
}

@media (min-width: 1025px) {
    .product-thumbnail__quantity {
        top:-.75em;
        right: -.75em
    }
}

.product-thumbnail__quantity.hidden {
    display: none
}

.product-thumbnail__wrapper {
    background-color: #fff
}

.sidebar {
    color: #231f20;
    font-size: 14px;
    margin: 0 -20px
}

@media (min-width: 1000px) {
    .sidebar {
        margin:0
    }
}

.sidebar .icon-svg {
    color: #fff
}

.sidebar .order-summary__emphasis {
    color: #231f20;
    font-size: 14px
}

.sidebar .order-summary__small-text {
    color: #797979;
    font-size: 14px
}

.sidebar .payment-due-label__total,.sidebar .payment-due__currency,.sidebar .payment-due__price {
    color: #231f20;
    font-size: 18px;
    font-weight: inherit;
    letter-spacing: inherit;
    line-height: inherit;
    vertical-align: inherit
}

.checkout-pobox__button span,.vip-redirect__button span {
    line-height: 1
}

.sidebar .payment-due__currency {
    color: #736b67;
    font-size: 12px;
    margin-right: 10px
}

.sidebar .reduction-code__text,.template-checkout .order-summary__section .tags-list .icon-svg {
    color: inherit
}

.sidebar .product__description {
    padding-left: 18px
}

.sidebar__cart-header-link,.sidebar__cart-header-title {
    font-family: GTA-Medium,Arial,Helvetica,sans-serif
}

.sidebar .total-line-table__tbody+.total-line-table__footer .total-line td::before,.sidebar .total-line-table__tbody+.total-line-table__footer .total-line th::before {
    background-color: transparent
}

.sidebar__cart-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 24px 20px 29px
}

.sidebar__cart-header-link {
    font-size: 12px;
    position: relative;
    top: 1px
}

.sidebar__cart-header-title {
    font-size: 18px
}

.template-checkout .total-recap__final-price,.total-line-table__footer {
    font-family: GTA-Bold,Arial,Helvetica,sans-serif;
    font-size: 18px
}

@media (min-width: 1000px) {
    .sidebar__cart-header {
        padding:0 0 19px
    }
}

.template-checkout .order-summary__section {
    padding: 20px 0 0;
    position: relative
}

.template-checkout .order-summary__section--total-lines {
    border-bottom: 1px solid #f5e9d8;
    border-top: none;
    padding: 0 20px 20px
}

@media (min-width: 1000px) {
    .template-checkout .order-summary__section--total-lines {
        border-bottom:none;
        border-top: 1px solid #f5e9d8;
        padding-left: 0;
        padding-right: 0;
        padding-top: 8px
    }
}

.template-checkout .order-summary__section.order-summary__section--discount {
    border-top: none;
    padding: 0 20px
}

.template-checkout .order-summary__section.order-summary__section--discount .field {
    padding: 0
}

.template-checkout .order-summary__section.order-summary__section--discount .fieldset {
    margin: 0
}

.template-checkout .order-summary__section.order-summary__section--discount .field__input:not(:focus) {
    border-color: #ddd
}

.template-checkout .order-summary__section .tags-list {
    margin-bottom: 5px;
    margin-top: 10px
}

.template-checkout .order-summary__section .tags-list .tag {
    background-color: #ddd;
    color: #231f20;
    margin-top: 10px
}

@media (min-width: 1000px) {
    .template-checkout .order-summary__section.order-summary__section--discount {
        padding:0
    }

    .template-checkout .order-summary__section.order-summary__section--discount .fieldset {
        border-top: 1px solid #f5e9d8;
        margin-top: 10px;
        padding: 20px 0
    }

    .template-checkout .order-summary__section .tags-list {
        margin-bottom: 20px;
        margin-top: -10px
    }
}

.template-checkout .order-summary__section--product-list {
    padding: 0 20px
}

.template-checkout .order-summary__section--product-list .product-table {
    display: block;
    margin: 0;
    width: 100%
}

.template-checkout .order-summary__section--product-list .product-table tbody {
    display: block
}

@media (min-width: 1000px) {
    .template-checkout .order-summary__section--product-list {
        padding-left:0;
        padding-right: 0
    }

    .template-checkout .order-summary__section--product-list .product-table {
        margin-bottom: 0
    }
}

.template-checkout .total-recap__final-price {
    color: #001630
}

.template-checkout .total-recap__original-price {
    color: #736b67
}

.total-line-table__footer {
    color: #001630;
    letter-spacing: 0;
    line-height: 1;
    padding: 0 0 20px
}

.checkout-pobox__button,.vip-redirect__button {
    -webkit-box-align: center;
    letter-spacing: 1.3px;
    overflow: hidden
}

.total-line-table__tbody {
    color: #231f20;
    font-family: GTA-Regular,Arial,Helvetica,sans-serif;
    font-size: 14px
}

.total-line-table__tbody+.total-line-table__footer .total-line td,.total-line-table__tbody+.total-line-table__footer .total-line th {
    padding-top: 20px
}

@media (min-width: 1000px) {
    .total-line-table__tbody+.total-line-table__footer .total-line td,.total-line-table__tbody+.total-line-table__footer .total-line th {
        padding-top:20px
    }
}

.order-summary__section--product-list:after {
    display: none
}

.total-line-table__tbody+.total-line-table__footer .total-line:first-child td,.total-line-table__tbody+.total-line-table__footer .total-line:first-child th,.total-line-table__tbody+.total-line-table__tbody .total-line:first-child td,.total-line-table__tbody+.total-line-table__tbody .total-line:first-child th {
    padding-top: 41px
}

.total-line-table__tbody+.total-line-table__footer .total-line:first-child td:before,.total-line-table__tbody+.total-line-table__footer .total-line:first-child th:before,.total-line-table__tbody+.total-line-table__tbody .total-line:first-child td:before,.total-line-table__tbody+.total-line-table__tbody .total-line:first-child th:before {
    top: 20px
}

.vip-redirect {
    background-color: #fff;
    margin: 20px;
    padding: 20px
}

.vip-redirect__heading {
    font-size: 24px;
    text-align: center
}

@media (min-width: 768px) {
    .vip-redirect__heading {
        font-size:30px
    }
}

.vip-redirect__modal {
    padding: 40px;
    border: 1px solid #0c4065
}

.vip-redirect__message {
    margin: 20px 0
}

.vip-redirect__button {
    -ms-flex-align: center;
    align-items: center;
    border-radius: 0;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    font-family: GTA-Bold,Arial,Helvetica,sans-serif;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
    -webkit-transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1),-webkit-box-shadow .3s cubic-bezier(.3,1,.45,1);
    transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1),-webkit-box-shadow .3s cubic-bezier(.3,1,.45,1);
    transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1),box-shadow .3s cubic-bezier(.3,1,.45,1);
    transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1),box-shadow .3s cubic-bezier(.3,1,.45,1),-webkit-box-shadow .3s cubic-bezier(.3,1,.45,1);
    padding-left: 20px;
    padding-right: 20px;
    font-size: 13px;
    height: 40px;
    background-color: #0c4065;
    color: #fff;
    margin: 10px auto;
    width: 100%
}

@media (min-width: 1024px) {
    .vip-redirect__button {
        display:-webkit-inline-box;
        display: -ms-inline-flexbox;
        display: inline-flex
    }
}

.vip-redirect__button svg {
    margin-left: 10px;
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-transition: -webkit-transform .4s ease-out;
    transition: -webkit-transform .4s ease-out;
    transition: transform .4s ease-out;
    transition: transform .4s ease-out,-webkit-transform .4s ease-out
}

.vip-redirect__button:hover svg {
    -webkit-transform: translateX(9px);
    transform: translateX(9px);
    -webkit-animation: bounce .4s;
    animation: bounce .4s;
    -webkit-animation-timing-function: cubic-bezier(.3,1,.45,1);
    animation-timing-function: cubic-bezier(.3,1,.45,1)
}

.vip-redirect__button:focus,.vip-redirect__button:hover {
    background-color: #163c57
}

.checkout-pobox {
    display: none;
    position: fixed;
    z-index: 100;
    background: rgba(0,0,0,.5);
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh
}

.checkout-pobox__container {
    background: #fff;
    padding: 1.5rem;
    width: 600px;
    max-width: 100%;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
}

.checkout-pobox__inner {
    border: 1px solid #0c4065;
    padding: 1.5rem
}

.checkout-pobox__title {
    font-family: GTA-Regular,Arial,Helvetica,sans-serif;
    color: #000000;  /* #381300 */;
    font-size: 24px;
    text-align: center;
    margin-bottom: 20px
}

@media (min-width: 768px) {
    .checkout-pobox__title {
        font-size:30px
    }
}

.checkout-pobox__button {
    -ms-flex-align: center;
    align-items: center;
    border-radius: 0;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    font-family: GTA-Bold,Arial,Helvetica,sans-serif;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
    -webkit-transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1),-webkit-box-shadow .3s cubic-bezier(.3,1,.45,1);
    transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1),-webkit-box-shadow .3s cubic-bezier(.3,1,.45,1);
    transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1),box-shadow .3s cubic-bezier(.3,1,.45,1);
    transition: background-color .3s cubic-bezier(.3,1,.45,1),border .3s cubic-bezier(.3,1,.45,1),box-shadow .3s cubic-bezier(.3,1,.45,1),-webkit-box-shadow .3s cubic-bezier(.3,1,.45,1);
    padding-left: 20px;
    padding-right: 20px;
    font-size: 13px;
    height: 40px;
    background-color: #0c4065;
    color: #fff;
    width: 100%
}

.giftcard-message__heading,.giftcard-message__message,.modal__content p,.modal__header__title {
    font-family: GTA-Regular,Arial,Helvetica,sans-serif;
    color: #000000;  /* #381300 */
}

@media (min-width: 1024px) {
    .checkout-pobox__button {
        display:-webkit-inline-box;
        display: -ms-inline-flexbox;
        display: inline-flex
    }
}

.checkout-pobox__button svg {
    margin-left: 10px;
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-transition: -webkit-transform .4s ease-out;
    transition: -webkit-transform .4s ease-out;
    transition: transform .4s ease-out;
    transition: transform .4s ease-out,-webkit-transform .4s ease-out
}

.checkout-pobox__button:hover svg {
    -webkit-transform: translateX(9px);
    transform: translateX(9px);
    -webkit-animation: bounce .4s;
    animation: bounce .4s;
    -webkit-animation-timing-function: cubic-bezier(.3,1,.45,1);
    animation-timing-function: cubic-bezier(.3,1,.45,1)
}

.checkout-pobox__button:focus,.checkout-pobox__button:hover {
    background-color: #163c57
}

.modal-backdrop--is-closing .modal,.modal-backdrop--is-visible .modal {
    -webkit-animation: none!important;
    animation: none!important
}

.modal {
    border-radius: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    visibility: visible;
    margin: 0 auto;
    opacity: 0;
    -webkit-transition: opacity .3s ease;
    transition: opacity .3s ease;
    max-width: 900px;
    width: 100%
}

.modal-backdrop--is-visible .modal {
    opacity: 1
}

.modal__header {
    position: relative;
    border: none
}

.modal__header__title {
    font-size: 24px;
    text-align: center
}

@media (min-width: 768px) {
    .modal__header__title {
        font-size:30px
    }
}

.modal__content {
    text-align: center
}

.modal__content p {
    font-size: 14px;
    letter-spacing: 0;
    line-height: 26px
}

.cn_modal .cn_description,.cn_modal .cn_superscript {
    line-height: 15px;
    font-size: 13px
}

.modal__close {
    position: absolute;
    right: 20px;
    top: 20px
}

.giftcard-message {
    background-color: #fff;
    margin: 20px;
    padding: 20px
}

.giftcard-message__heading {
    font-size: 24px;
    text-align: center
}

@media (min-width: 768px) {
    .giftcard-message__heading {
        font-size:30px
    }
}

.giftcard-message__modal {
    padding: 40px
}

.giftcard-message__message {
    margin: 20px 0;
    text-align: center;
    display: block!important
}

.giftcard-message__button {
    font-family: GTA-Bold,Arial,Helvetica,sans-serif;
    text-transform: uppercase;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-decoration: none;
    overflow: hidden;
    position: relative;
    padding-left: 20px;
    padding-right: 20px;
    font-size: 13px;
    letter-spacing: 1.3px;
    background-color: #ff4438;
    color: #fff;
    border-radius: 0;
    height: 50px;
    width: 250px;
    margin: 10px auto
}

.giftcard-message__button:hover {
    color: #fff!important;
    background-color: #a76c0c
}

.cn_heading,.cn_superscript {
    letter-spacing: 0;
    color: #000000;  /* #381300 */;
    text-align: left;
    opacity: 1
}

.cn_modal {
    opacity: 1;
    border-radius: 3px 3px 0 0
}

.cn_heading {
    text-transform: uppercase
}

.cn_description {
    text-align: left;
    letter-spacing: -.07px;
    color: #717171;
    opacity: 1
}

#cn_modal_button {
    width: 109px;
    height: 55px;
    opacity: 1
}

.cn_icon {
    padding: 46px 15px
}

.cn_message {
    padding: 20px 30px
}

.cn_btn {
    padding: 30px
}

.cn_loader {
    border: 2px solid #fff;
    border-top: 0 solid #fff;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin .5s linear infinite;
    display: inline-block
}

@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes spin {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

.cn_modal {
    background: #f6ebd8;
    border: 1px solid #f5e9d8;
    border-bottom: none;
    height: auto;
    display: block;
    padding: 24px;
    width: 100%;
    overflow: hidden
}

.cn_modal .cn_description {
    font-family: GTA-Regular,Arial,Helvetica,sans-serif;
    letter-spacing: -.07px;
    color: #717171
}

.cn_modal .cn_icon {
    padding: 27px 20px 0 0;
    float: left
}

.cn_modal .cn_message {
    float: left;
    padding: 0
}

.cn_modal .cn_btn {
    float: right;
    padding: 6px 0 0
}

.cn_modal .cn_heading,.cn_modal .cn_superscript {
    padding: 0 0 6px;
    font-family: GTA-Bold,Arial,Helvetica,sans-serif
}

.cn_modal .cn_heading {
    font-size: 24px;
    line-height: 28px
}

.cn_modal #cn_modal_button {
    font-family: GTA-Bold,Arial,Helvetica,sans-serif;
    font-size: 15px;
    line-height: 18px;
    letter-spacing: 1.13px;
    text-transform: uppercase
}

.redirecttovip-message__heading,.redirecttovip-message__message {
    text-align: center;
    font-family: GTA-Regular,Arial,Helvetica,sans-serif;
    color: #000000;  /* #381300 */
}

.cn_modal .cnHeading_mobile.cn_heading {
    display: none
}

@media (max-width: 480px) {
    .cn_modal .cn_btn,.cn_modal .cn_message {
        float:none;
        text-align: center
    }

    .cn_modal .cnHeading_mobile.cn_heading {
        display: block;
        text-align: center
    }

    .cn_modal .cn_heading,.cn_modal .cn_icon {
        display: none
    }

    .cn_modal .cn_description,.cn_modal .cn_superscript {
        text-align: center
    }

    .cn_modal .cnHeading_mobile.cn_heading img {
        display: inline-block;
        padding-right: 8px;
        max-width: 25px
    }

    .cn_modal .cn_btn {
        padding: 16px 0 0;
        display: block
    }

    .cn_modal #cn_modal_button {
        width: 100%;
        height: 40px
    }
}

@media (min-width: 481px) {
    .cn_modal .cn_message {
        width:70%;
        padding-right: 10px
    }

    .cn_modal {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex
    }

    .cn_modal .cn_icon>img {
        height: 20px
    }
}

.redirecttovip-message {
    background-color: #fff;
    margin: 20px;
    padding: 1rem
}

.redirecttovip-message__heading {
    font-size: 24px
}

@media (min-width: 768px) {
    .redirecttovip-message__heading {
        font-size:30px
    }
}

.redirecttovip-message__modal {
    padding: 40px
}

.redirecttovip-message__message {
    margin: 20px 0;
    display: block!important
}

.redirecttovip-message__button {
    font-family: GTA-Bold,Arial,Helvetica,sans-serif;
    text-transform: uppercase;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-decoration: none;
    overflow: hidden;
    position: relative;
    padding-left: 20px;
    padding-right: 20px;
    font-size: 13px;
    letter-spacing: 1.3px;
    background-color: #ff4438;
    color: #fff;
    border-radius: 0;
    height: 50px;
    margin: 10px auto
}

.redirecttovip-message__button:hover {
    color: #fff!important;
    background-color: #a76c0c
}

#redirecttovip-message {
    margin: 0;
    min-height: auto;
    min-width: auto;
    padding: 0;
    border: 1px solid #707070;
    border-radius: 4px;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    position: fixed;
    width: 100%;
    max-width: 852px
}

#redirecttovip-message .redirecttovip-message__modal {
    padding: 99px 93px 128px
}

#redirecttovip-message .redirecttovip-message__heading {
    font-size: 36px;
    line-height: 45px;
    margin-bottom: 28px
}

#redirecttovip-message .redirecttovip-message__message {
    font-size: 18px;
    line-height: 21px;
    max-width: 664px;
    margin: 0 auto 106px
}

#redirecttovip-message .redirecttovip-message__message a {
    color: #084496;
    text-decoration: underline
}

#redirecttovip-message .redirecttovip-message__button {
    background-color: #a32e0a!important;
    font-size: 24px;
    line-height: 28px;
    letter-spacing: 2.4px;
    width: 300px;
    height: 56px;
    margin: 0
}

#redirecttovip-message .redirecttovip-message__modal .lity-close {
    font-family: GTA-Regular,Arial,Helvetica,sans-serif;
    font-size: 18px;
    line-height: 25px;
    color: #000000;  /* #381300 */;
    text-shadow: none;
    width: 12px;
    height: 21px;
    top: 29px;
    right: 32px
}

@media (max-width: 480px) {
    div#redirecttovip-message {
        margin:0;
        -webkit-transform: translate(0,0);
        transform: translate(0,0);
        top: 0;
        left: 0;
        height: 100vh
    }

    .lity-container {
        width: 95%;
        margin-right: -3px
    }
}

@media (max-width: 992px) {
    #redirecttovip-message .redirecttovip-message__modal .lity-close {
        top:18px;
        right: 17px
    }

    #redirecttovip-message .redirecttovip-message__modal {
        padding: 66px 16px 62px;
        height: 100vh;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center
    }

    #redirecttovip-message .redirecttovip-message__heading {
        margin-bottom: 26px
    }

    #redirecttovip-message .redirecttovip-message__message {
        line-height: 25px;
        margin: 0 auto 46px
    }

    #redirecttovip-message {
        border-radius: 0
    }
}

.checkout-gift-order__checkbox,.checkout-gift-order__checkbox label {
    -webkit-box-align: center;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal
}

.checkout-gift-order__characters {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    bottom: 0;
    color: #001630;
    display: block;
    font-size: 11px;
    font-weight: 400;
    margin-bottom: .7em;
    margin-left: 1px;
    overflow: hidden;
    padding: 0 12px;
    pointer-events: none;
    position: absolute;
    text-align: right;
    text-overflow: ellipsis;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    white-space: nowrap;
    width: 100%;
    z-index: 1
}

.checkout-gift-order__checkbox {
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap
}

.checkout-gift-order__checkbox input {
    appearance: none;
    border: 1px solid #707070;
    border-radius: 3px;
    height: 18px;
    margin: 0;
    padding: 0;
    position: relative;
    width: 18px
}

.checkout-gift-order__checkbox input:after {
    background-image: url("//olukai.com/cdn/shop/t/405/assets/checkbox-icon.svg?v=169392761045823659551626389905");
    background-position: center;
    background-repeat: no-repeat;
    content: '';
    display: block;
    height: 8px;
    left: 50%;
    margin-left: -5px;
    margin-top: -4px;
    opacity: 0;
    position: absolute;
    top: 50%;
    -webkit-transform: scale(.2);
    transform: scale(.2);
    -webkit-transition: all .2s ease-in-out 0s;
    transition: all .2s ease-in-out 0s;
    width: 10px
}

.checkout-gift-order__checkbox input:checked {
    border: none!important;
    -webkit-box-shadow: 0 0 0 10px #ff4438 inset!important;
    box-shadow: 0 0 0 10px #ff4438 inset!important
}

.checkout-gift-order__checkbox input:checked:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1)
}

.checkout-gift-order__checkbox label {
    -ms-flex-align: center;
    align-items: center;
    color: #231f20;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    font-size: 12px;
    height: 18px;
    padding-left: 11px;
    position: relative
}

.checkout-gift-order__checkbox label:after {
    content: '';
    height: 100%;
    position: absolute;
    right: 100%;
    top: 0;
    width: 18px
}

.checkout-gift-order__checkbox label span {
    padding-left: 7px
}

.checkout-gift-order__disclaimer {
    color: #736b67;
    font-size: 12px;
    line-height: 1.35;
    margin: 20px 0
}

.checkout-gift-order__error {
    color: #ff4438;
    margin-top: 6px
}

.checkout-gift-order__form {
    display: none
}

.checkout-gift-order__header {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #231f20;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    font-size: 12px;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding-top: 6px
}

body.gift-wrap .content-box:last-of-type .content-box__row:last-of-type .text-container h3.heading-3:first-of-type,body.gift-wrap .os-step__description,body.gift-wrap .os-step__special-description,body.gift-wrap .section__content__column--half:last-child,body.gift-wrap .sidebar,body.gift-wrap .wrap>aside,body.gift-wrap [data-order-update-options],body.gift-wrap [data-order-updates],body.gift-wrap [data-track-shop-wrapper] {
    display: none
}

.checkout-gift-order__seperator {
    border-top: 1px solid #ded1bf;
    margin: 10px 0
}

body.gift-wrap .checkout__header,body.gift-wrap.template-checkout .checkout__main-inner {
    margin-left: auto;
    margin-right: auto;
    max-width: 730px
}

.template-checkout .breadcrumb__disable {
    height: 100%;
    left: 0;
    -o-object-fit: cover;
    object-fit: cover;
    -o-object-position: center center;
    object-position: center center;
    position: absolute;
    top: 0;
    width: 100%
}

.template-checkout .breadcrumb__item {
    position: relative
}

.template-checkout .field__input.error {
    border-color: #ff4438
}

.template-checkout .field__input--gift-order-message {
    height: 64px;
    padding-bottom: 2em
}

.template-checkout .field--show-floating-label .field__input--gift-order-message {
    padding-bottom: 1.75em
}

body.gift-wrap .checkout__logo {
    margin-bottom: 15px
}

body.gift-wrap .checkout__header {
    border-bottom: 1px solid #e9dfd1;
    width: 100%
}

body.gift-wrap .content-box:last-of-type .content-box__row:last-of-type .text-container p:first-of-type {
    height: 0;
    margin-bottom: -27px;
    overflow: hidden
}

@media (min-width: 1000px) {
    body.gift-wrap .checkout__header--desktop {
        padding-bottom:0
    }

    body.gift-wrap .main__content {
        padding-top: 20px
    }

    body.gift-wrap.template-checkout .checkout__main-inner {
        max-width: 575px
    }
}


@media (min-width:1001px){
  .step__footer__continue-btn.sticky__continue-btn
  {
    display:none;
  }
}
</style>