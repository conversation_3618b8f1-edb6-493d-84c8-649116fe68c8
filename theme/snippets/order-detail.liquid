<article class="order-detail">
  <header class="order-detail__header">
    <h2 class="order-detail__title mt-0">{{ 'customer.order.order_number' | t }}: {{ order.name }}</h2>
  </header>
  
  <div class="order-detail__summary">
    <h2 class="order-detail__title mt-0">{{ 'customer.order.details' | t }}</h2>
    
    {% comment %} placeholder {% endcomment %}
    <span class="order-detail__fulfillment order-detail__meta">
    {% if order.fulfillment_status == 'unfulfilled'%}
      This order has not shipped yet
      {{ 'customer.order.unfulfilled' | t }}
    {% elsif order.fulfillment_status == 'partial'%}
    {{ 'customer.order.partial' | t }}
      This order has not shipped yet
    {% elsif order.fulfillment_status == 'fulfilled'%}
    {{ 'customer.order.fulfilled' | t }}
      This order has shipped
    {% endif %}
    </span>
  
    <span class="order-detail__name order-detail__meta">{{ 'customer.order.order_number' | t }}: {{ order.name }}</span>
    <span class="order-detail__date order-detail__meta">{{ 'customer.order.date' | t }}: {{ order.created_at | date: format:'abbreviated_date' }}</span>
  </div>
  
  

	<main class="order-detail__main flex">
    <div>
      <h2 class="order-detail__title">{{ 'customer.order.items' | t }} ({{order.line_items | size}})</h2>
      <div class="order-detail__line-items">
        {% for line_item in order.line_items %}
          <div class="line-item flex gap-x-6 py-4">
            <img class="line-item__image" src="{{ line_item.image | image_url: width:200, height:200 }}" alt="{{ line_item.image.alt }}" />
            <div class="flex flex-col">
              <h3 class="line-item__title mt-0">{{ line_item.title }}</h3>
              <span class="line-item__sku order-detail__meta">SKU #: {{ line_item.sku }}</span>
              <span class="line-item__quantity order-detail__meta">Quantity: {{ line_item.quantity }}</span>
              <span class="line-item__price order-detail__meta">{{ line_item.price | money }}</span>
            </div>
          </div>			
        {% endfor %}
      </div>
    </div>
    
    <div class="order-detail__prices">
      <h2 class="order-detail__title mt-0">{{ 'customer.order.total' | t }}</h2>
    
      <div class="order-detail__price order-detail__subtotal flex justify-between">
        <span class="order-detail__price-label">{{ 'customer.order.subtotal' | t }}</span>
        <span class="order-detail__price-value">{{ order.subtotal_price | money }}</span>
      </div>
      
      <div class="order-detail__price order-detail__tax flex justify-between">
        <span class="order-detail__price-label">{{ 'customer.order.tax' | t }}</span>
        <span class="order-detail__price-value">{{ order.tax_price | money }}</span>
      </div>

      <div class="order-detail__price order-detail__total flex justify-between py-8">
        <span class="order-detail__price-label">{{ 'customer.order.fulfilled' | t }}</span>
  
        <span class="order-detail__price-value">
          <span class="order-detail__price-symbol mr-2">{{ shop.currency }}</span>
          {{ order.total_price | money }}
        </span>
      </div>
    </div>
    
    <div class="order-detail__details flex max-lg:flex-col gap-x-4 gap-y-8 py-8">
      <div class="order-detail__detail flex flex-col flex-[2] justify-between">
        <h4 class="order-detail__title mt-0 mb-6">{{ 'customer.order.delivery' | t }}</h4>
        <span class="order-detail__addresses flex flex-col">
          <div class="order-detail__address order-detail__address--shipping flex flex-col">
            <label class="order-detail__address-label">{{ 'customer.order.shipping_address' | t }}</label>
            <div class="order-detail__address-value flex flex-col">
              <span>{{ order.shipping_address.name }}</span>
              <span>{{ order.shipping_address.address1 }}, {{ order.shipping_address.address2 }}</span>
              <span>{{ order.shipping_address.city }}, {{ order.shipping_address.province }} {{ order.shipping_address.zip }}</span>
            </div>
          </div>

          <div class="order-detail__address order-detail__address--billing flex flex-col">
            <label class="order-detail__address-label">{{ 'customer.order.billing_address' | t }}</label>
            <div class="order-detail__address-value flex flex-col">
              <span>{{ order.billing_address.name }}</span>
              <span>{{ order.billing_address.address1 }}, {{ order.billing_address.address2 }}</span>
              <span>{{ order.billing_address.city }}, {{ order.billing_address.province }} {{ order.billing_address.zip }}</span>
            </div>
          </div>
        </span>
      </div>

      <div class="order-detail__detail flex flex-col flex-1">
        <h4 class="order-detail__title mt-0 mb-6">{{ 'customer.order.payment' | t }}</h4>
        <div class="order-detail__transactions flex flex-col">
          
          {% for transaction in order.transactions %}
            <div class="order-detail__transaction flex flex-col gap-y-4">
              <div class="order-detail__payment-method flex flex-col">
                <label class="order-detail__label">{{ 'customer.order.payment_method' | t }}</label>
                <span class="order-detail__value order-detail__payment-method">{{ transaction.gateway }}</span>
              </div>
              
              <div class="order-detail__payment-status flex flex-col">
                <label class="order-detail__label">{{ 'customer.order.payment_status' | t }}</label>
                <span class="order-detail__value order-detail__payment-status">{{ transaction.status_label }}</span>
              </div>
            </div>
          {% endfor %}
        <div>
      </div>
    </div>
    
	</main>

	<a href="{{ routes.account_url }}?view=orders" class="order-detail__return link text-sm flex items-center gap-1">
    {% render 'icon' icon: 'chevron-left' width:16 height:16 strokeWidth:2 %}
    {{ 'customer.order.order_history_link' | t }}
  </a>
	
</article>

