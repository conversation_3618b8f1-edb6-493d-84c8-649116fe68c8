{% if search.performed %}
{% assign search_terms = settings.search_suggestions | escape | split: "," %}
    
<div x-data='{
            shop: { permanent_domain: "{{ shop.permanent_domain }}" }, 
            search: { terms: "{{ search.terms | url_encode }}" },
            settings: { no_search_paragraph: `{{ settings.no_search_paragraph | escape }}` },
            suggestions: [{% for term in search_terms %}"{{- term -}}"{% unless forloop.last %},{% endunless %}{% endfor %}]
        }' x-init="" class="w-full flex gap-4 justify-center justify-between flex-col text-center mb-5 lg:mb-0" x-show="{{ settings.search_suggestions_inclusion_js }}">
    {% if settings.no_search_paragraph %}
    <p x-show="true" class="w-full text-sm" x-text="settings.no_search_paragraph" style="display:none;"></p>
    {% endif %}
    <div x-show="true"  style="display:none;" class="w-full flex gap-4 justify-center">
        <template x-for="suggestion in suggestions" :key="suggestion">
            <a :href="`https://${shop.permanent_domain}/search?q=${encodeURIComponent(suggestion)}`" class="text-sm" x-text="suggestion"></a>
        </template>
    </div>
</div>
{% endif %}