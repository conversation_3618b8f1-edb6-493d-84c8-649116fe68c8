<article class="order-item">
	<header class="order-item__header">
		<h3 class="order-item__name mt-0">Order: {{ order.name }}</h3>
	</header>
	<main class="flex overflow-h-scroll">
		<div class="order-item__line-items grid grid-cols-5">
		{% for line_item in order.line_items limit:4 %}
			<div class="aspect-[1/1] h-32 max-w-32 order-item__line-item overflow-hidden w-full flex items-center">
				{% if line_item.image %}
				<img src="{{ line_item.image | image_url: width:200, height:200 }}" alt="{{ line_item.image.alt }}" class="object-cover w-full"/>
				{% else %}
				<img src="{{ 'product-not-available.jpg' | file_url }}" alt="Product Image not available" class="object-cover"/>
				{% endif %}
			</div>
		{% endfor %}
		{% if order.line_items.size > 4 %}
			<div class="aspect-[1/1] h-32 max-w-32 order-item__line-item overflow-hidden w-full flex items-center justify-center p-sm text-sm text-center">+{{ order.line_items.size | minus: 4 }} items</div>
		{% endif %}
		</div>
		<div class="order-item__view-detail order-item__view-detail--desktop hidden lg:block">
			<a class="button button--primary" href="{{ order.customer_url | split: '.com' | last }}">View Detail</a>
		</div>
	</main>
	<footer class="grid grid-cols-2 lg:grid-cols-4 order-item__footer">

		<div class="order-item__footer-item order-item__footer-item--date">
			<p class="order-item__footer-item-label">Date</p>
			<p class="order-item__footer-item-value">{{ order.created_at | date: format:'abbreviated_date' }}</p>
		</div>

		<div class="order-item__footer-item order-item__footer-item--payment-status">
			<p class="order-item__footer-item-label">Payment Status</p>
			<p class="order-item__footer-item-value">{{ order.financial_status_label }}</p>
		</div>

		<div class="order-item__footer-item order-item__footer-item--date">
			<p class="order-item__footer-item-label">Fulfillment Status</p>
			<p class="order-item__footer-item-value">{{ order.fulfillment_status_label }}</p>
		</div>

		<div class="order-item__footer-item order-item__footer-item--date">
			<p class="order-item__footer-item-label">Total</p>
			<p class="order-item__footer-item-value">{{ order.total_price | money }}</p>
		</div>
		<div class="order-item__view-detail order-item__view-detail--mobile block lg:hidden">
			<a class="button button--primary" href="{{ order.customer_url | split: '.com' | last }}">View Detail</a>
		</div>
	</footer> 
</article>