<script> 
  if(Shopify.Checkout.step == 'shipping_method'){
 $(document).ready(function(){
    emailid =  document.getElementsByClassName("review-block__content")[0].innerText;
    let cookie = getCookie('__exponea_etc__');
    //console.log(emailid)
    console.log(cookie)
    //console.log($('#checkout_email').val());
      $('#checkout_email').attr('status','notupdate');
      //console.log('status',$('#checkout_email')); 
      let customer_id = '{{checkout.customer.id}}';
      let store_name = '{{shop.domain}}';
        jQuery.ajax({
          url: "{{ settings.profile_split_endpoint }}",
          type: 'POST',
          dataType: "json",
          contentType : 'application/json',
          data: JSON.stringify({'cookieid': cookie, 'emailid': emailid}),
          async: false, 
          //headers: {'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*'},
          crossDomain: true,
          success: function(data) {
          //let data_customer_raw = JSON.parse(data);
          //let data_customer = data.body;    
            //console.log(data);
            //if (data_customer.customer.accepts_marketing) {
              //$('#checkout_buyer_accepts_marketing').prop('checked', true);
            //}else{
              //$('#checkout_buyer_accepts_marketing').prop('checked', false);
            //}  
          }
        })
      
    });

    }



    </script>