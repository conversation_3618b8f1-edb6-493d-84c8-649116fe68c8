{% liquid
  assign styles = ''
  for setting in settings
    if setting contains prefix and settings[setting] != blank
      assign property = setting | remove: prefix | remove_first: '_' | replace: '_', '-'
      assign styles = styles | append: property | append: ':' | append: settings[setting] | append: '; '

      assign var = setting | remove: prefix | remove_first: '_' | replace: '_', '-' | prepend:'--'
      assign styles = styles | append: var | append: ':' | append: settings[setting] | append: '; '
    endif
  endfor
  if att
    if styles != blank
      echo styles | prepend:'style="' | append: '"'
    endif
  else
    echo styles
  endif
%}
