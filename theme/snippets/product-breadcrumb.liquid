<div class="product-header__breadcrumbs">
{% liquid
  assign breadcrumb = ''
  assign product_collection_links = product.collections | map: 'url'

  for parent_link in linklists['product-breadcrumbs'].links
    for child_link in parent_link.links
      for grandchild_link in child_link.links
        if product_collection_links contains grandchild_link.url
          assign breadcrumb_parent = parent_link
          assign breadcrumb_child = child_link
          assign breadcrumb_grandchild = grandchild_link
          break
        endif
      endfor

      if breadcrumb_child
        break
      endif
    endfor

    if breadcrumb_parent
      break
    endif
  endfor
%}

{% if breadcrumb_parent.title != blank %}
  <a href="{{ breadcrumb_parent.url }}">{{ breadcrumb_parent.title }}</a> >
  <a href="{{ breadcrumb_child.url }}">{{ breadcrumb_child.title }}</a> >
  <a href="{{ breadcrumb_grandchild.url }}">{{ breadcrumb_grandchild.title }}</a>
{% endif %}
</div>