{% assign _options = options | default: settings.options | newline_to_br | split: '<br />' %}

<details type="context-menu" class="relative dropdown-menu" x-data aria-label="{{ label | default: settings.label | strip_html | downcase }} options">
  
  <summary class="dropdown-menu__trigger flex items-center whitespace-nowrap" role="combobox">
    
    <span class="dropdown-menu__label" tabindex="-1">{{ label | default: settings.label }}</span>
    
    <template x-if="!!!{{ model | default: settings.model | default: 'false'}}">
      <span class="dropdown-menu__value" tabindex="-1">{{ default | default: settings.default | default: 'Please Select'}}</span>
    </template>

    {% for option in _options %}
    <template x-if="{{ model | default: settings.model }} == '{{ option | split: ':' | first | strip }}' ">
      <span class="dropdown-menu__value" aria-label="{{ option | split: ':' | last | strip | replace: ' ', '-'}}">{{ option | split: ':' | last | strip  }}</span>
    </template>
    {% endfor %}

    <span class="dropdown-menu__trigger-icon flex items-center justify-center"  tabindex="-1">
      {% render 'icon' icon:'chevron-down' %}
    </span>

  </summary>

  <ul class="dropdown-menu__menu absolute z-30 top-full w-full bg-white shadow-lg overflow-hidden" role="listbox">
    {% for option in _options %}
    <li class="dropdown-menu__menu-item" role="option">
      <button class="w-full" data-value="{{ option | split: ':' | first | strip }}" onclick="Util.express('{{ onchange | default:settings.onchange }}', this.dataset)" @click="setTimeout(reinitialiseRatingSnippet, 200);" role="button" aria-label="{{ option | split: ':' | last | strip | replace: ' ', '-' }}">
        {{ option | split: ':' | last | strip }}
      </button>
    </li>
    {% endfor %}
  </ul>

</details>
