<script>
  
  (function (u, n, i, v, e, r, s, a, l) { u[r] = {}; u[r].uid = '{{ settings.roark_ambassador_uid }}'; u[r].m = ['identify', 'on', 'ready', 'track', 'getReferrerInfo']; u[r].queue = []; u[r].f = function(t) { return function() { var l = Array.prototype.slice.call(arguments); l.unshift(t); u[r].queue.push(l); return u[r].queue; }; }; for (var t = 0; t < u[r].m.length; t++) { l = u[r].m[t]; u[r][l] = u[r].f(l); } a = n.createElement(v); a.src = e + '/us-' + u[r].uid + '.js'; a.async = s; n.getElementsByTagName(i)[0].appendChild(a); })(window, document, 'head', 'script', 'https://cdn.getambassador.com', 'mbsy', true);
</script>
