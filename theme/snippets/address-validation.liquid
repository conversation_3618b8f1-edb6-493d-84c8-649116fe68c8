<div id="uav-popup">
	<div class="address_verification">
		<svg xmlns="http://www.w3.org/2000/svg" width="108.179" height="25" viewBox="0 0 108.179 25" class="address-logo">
			<g id="olukai_logo_-_black" data-name="olukai logo - black" transform="translate(-121.178 -462.926)">
				<path id="Path_19282" data-name="Path 19282" d="M361.43,470.488l-6.07,7.168h2.048c.975,0,2.954.091,2.954,2.434A7.2,7.2,0,0,1,360,482.1l-1.353,5.477h1.916l-.776,3.1H354.16l2.044-8.15a4.387,4.387,0,0,0,.166-.957c0-.759-.56-.858-2.243-.858H350.5l-2.476,9.965h-5.576l.792-3.1h1.912l4.259-17.092h-1.915l.76-3.1h5.575l-2.623,10.468,8.851-10.468h4.658l-.773,3.1Z" transform="translate(-158.328 -3.192)" fill="#042c4b"/>
				<path id="Path_19283" data-name="Path 19283" d="M305.632,492.851h-5.6l-.773,3.1h1.96L298.71,506.01h-5.257l3.279-13.159h-5.593l-.776,3.1H292.3l-1.991,7.969a9.968,9.968,0,0,0-.381,2.283,2.443,2.443,0,0,0,1.038,2.145c.691.484,1.037.484,2.94.554h9.72v0h0l.775-3.1H302.4Z" transform="translate(-120.749 -21.413)" fill="#042c4b"/>
				<g id="Group_12178" data-name="Group 12178" transform="translate(218.545 464.97)">
					<path id="Path_19284" data-name="Path 19284" d="M467.477,492.847l-.768,3.1h1.932l-2.46,9.845h-1.923l-.776,3.107h7.475l.774-3.107h-1.939l3.226-12.948Z" transform="translate(-463.482 -486.38)" fill="#042c4b"/>
					<g id="Group_12177" data-name="Group 12177" transform="translate(6.835)">
						<path id="Path_19285" data-name="Path 19285" d="M489.483,470.113a2,2,0,0,0-1.972,2.005,1.988,1.988,0,0,0,3.976,0,2.006,2.006,0,0,0-2-2.005" transform="translate(-487.511 -470.113)" fill="#042c4b"/>
					</g>
				</g>
				<path id="Path_19286" data-name="Path 19286" d="M257.835,462.927l-.773,3.1h1.944l-4.569,18.357h-1.948l-.774,3.1h0v0h7.507l.761-3.045h0l.015-.06h-1.933l5.345-21.458Z" transform="translate(-93.407 -0.001)" fill="#042c4b"/>
				<path id="Path_19287" data-name="Path 19287" d="M130.654,467.984c-1.279,1.512-3.717,4.638-4.6,7.185-1.455,4.21.144,7.391,2.579,7.61s4.716-3.025,5.089-7.237a12.142,12.142,0,0,0-.358-4.688s1.51.633,3.267,3.79a8.376,8.376,0,0,1,1.318,5.073s-2.56-3.318-2.782-2.123c-1.178,6.254-5.409,10.9-9.277,10.277-4.034-.648-6.322-5.787-3.389-12.131,1.852-4.008,4.837-6.851,6.707-8.776a10.415,10.415,0,0,0,1.688-3.487s.552-1.177,2.224-.088c1.72,1.119.922,2.018.922,2.018a14.49,14.49,0,0,0-3.391,2.577" transform="translate(0 0)" fill="#042c4b"/>
				<path id="Path_19288" data-name="Path 19288" d="M419.805,505.971h-5.119l.829-3.321h5.121Zm6.124-10.9a2.17,2.17,0,0,0-1.349-2.11,5.026,5.026,0,0,0-1.764-.173h-4.359c-3.736-.068-4.517,2.319-4.867,3.719l-.427,1.715,3.452,0,.6-2.386h5.12l-1,4.013h-4.774c-1.176.069-3.321.173-4.463,2.352a14.343,14.343,0,0,0-1.072,4.186,2.3,2.3,0,0,0,1.972,2.317,11.6,11.6,0,0,0,2.216.155l9.481,0,.777-3.107h-2.012l2.2-8.845a8.09,8.09,0,0,0,.272-1.835" transform="translate(-207.397 -21.369)" fill="#042c4b"/>
				<path id="Path_19289" data-name="Path 19289" d="M200.3,480.733c-.686,2.777-.9,3.646-4.335,3.646h-2.158c-1.56-.032-2.914-.064-2.989-1.355a9.538,9.538,0,0,1,.443-2.258l2.755-11.086c.685-2.777.9-3.651,4.336-3.645h2.17c3.711-.006,3.143,1.211,2.546,3.613Zm7.624-14.711c-.174-2.968-3.84-3.033-5.851-3.1h-3.89c-1.434,0-4.723-.032-6.638,2.225-.923,1.032-1.2,1.752-1.9,4.55L186.586,482a9.509,9.509,0,0,0-.313,2.387c.174,2.968,3.788,3.1,5.791,3.1h3.9c1.6,0,4.774.033,6.689-2.225.924-1.032,1.228-1.82,1.923-4.61l3.044-12.229a9.256,9.256,0,0,0,.305-2.4" transform="translate(-46.576 0)" fill="#042c4b"/>
			</g>
		</svg>

		<h2>Verify Your Shipping Address</h2>

		<p>We weren’t able to verify your shipping address. Please confirm or edit your address. You can also use one of the suggested addresses below.</p>

		<div class="addressList" id="address-container">
			<ul></ul>
			<button type="button" class="product__add-to-bag" data-address-button="original" onclick="doNothing();"><span>KEEP THIS ADDRESS</span></button>
			<button type="button" class="product__add-to-bag" data-address-button="suggested" onclick="updateAddress();" style="display: none"><span>USE THIS ADDRESS</span></button>
		</div>

		<button type="button" data-address-verification-close class="address-verification__close">
			<svg xmlns="http://www.w3.org/2000/svg" width="22.121" height="22.121" viewBox="0 0 22.121 22.121" aria-hidden="true">
				<g id="Group_1" data-name="Group 1" transform="translate(-20.967 -144.821)">
					<path id="Path_1635" data-name="Path 1635" d="M1532.027,145.881l20,20" transform="translate(-1510)" fill="none" stroke="#30231f" stroke-width="3"/>
					<path id="Path_1636" data-name="Path 1636" d="M1552.027,145.881l-20,20" transform="translate(-1510)" fill="none" stroke="#30231f" stroke-width="3"/>
				</g>
			</svg>

			<span class="visually-hidden">Close</span>
		</button>
	</div>
</div>

<div id="uav-popup-loading">
	<div class="add_verify">
		<p><span class="btn--loading"><svg class="icon-svg icon-svg--size-18 btn__spinner icon-svg--spinner-button" aria-hidden="true" focusable="false"> <use xlink:href="#spinner-button"></use> </svg></span> Verifying Address …</p>
	</div>
</div>
<script>
{% if settings.address_validation_timeout != blank %}
	window.addressValidation_timeout = {{settings.address_validation_timeout}};
{% endif %}
let uvaCheckFlag = false;
$(document).ready(function() {
	if(Shopify.Checkout.step == 'contact_information'){

	$('body').on('click', '[data-address-item]', function() {
		$(this).addClass('selected').siblings().removeClass('selected')

		if ($(this).data('address-item') === 'suggested') {
			$('[data-address-button="suggested"]').show()
			$('[data-address-button="original"]').hide()
		} else {
			$('[data-address-button="suggested"]').hide()
			$('[data-address-button="original"]').show()
		}
	})

	$('body').on('click', '[data-address-verification-close]', function() {
		$('body').css("overflow","auto");
		$("#continue_button").removeClass("btn--loading");
		$('#uav-popup').hide();
	})

	$(document).on("click","#continue_button",function( event ) {
		$("#continue_button").addClass("btn--loading");
		let checkAddressFields = true;
		if($.trim($('#checkout_shipping_address_last_name').val()) == '' || $.trim($('#checkout_shipping_address_address1').val()) == '' || $.trim($('#checkout_shipping_address_city').val()) == '' || $.trim($('#checkout_shipping_address_zip').val()) == '' || $.trim($('[id*=checkout_email]').val()) == '' || $.trim($('#checkout_shipping_address_province').val()) == '' || $('#checkout_shipping_address_province').val() == null || $('#checkout_shipping_address_country').val() == null ){
			checkAddressFields = false;
		}

		{% if customer %} 
		
		{% else %}
		let cartId = getCookie('cart');
		var identify = {};		
		var customerEmail = $.trim($('[id*=checkout_email]').val());
		var customerPhone = $.trim(formatPhone($('#checkout_shipping_address_phone').val()));

		if(customerEmail){
			if (cartId) {
				identify['cart_id'] = cartId;
			}	
			
			identify['email_id'] = customerEmail;

			if (customerPhone) {
				identify['phone_id'] = customerPhone;
			}
			// just for testing
			//identify['custom'] = "information....";

      {%- comment -%}
			exponea.identify(
				identify
			);
      {%- endcomment -%}
		}
			 
		
		{% endif %}

		if(!uvaCheckFlag && checkAddressFields) {
			event.preventDefault();

			let maxResponseAddress = 1;
			let firstName = $('#checkout_shipping_address_first_name').val();
			let lastName = $('#checkout_shipping_address_last_name').val();
			let fullName = firstName+' '+lastName;
			fullName = fullName.trim();
			let shippingAddressOne = $('#checkout_shipping_address_address1').val().toUpperCase();
			let shippingAddressTwo = $('#checkout_shipping_address_address2').val().toUpperCase();
			let shippingAddressCity = $('#checkout_shipping_address_city').val().toUpperCase();
			let shippingAddressCountry = $('#checkout_shipping_address_country>[selected=selected]').attr('data-code');
			let shippingAddressProvince = $('#checkout_shipping_address_province option:selected').text();
			let shippingAddressProvinceCode = $('#checkout_shipping_address_province').val();
			let shippingAddressZip = $('#checkout_shipping_address_zip').val().toUpperCase();

			let fullShippingAddress = fullName+'<br>'+shippingAddressOne+'<br>'+shippingAddressCity+', '+shippingAddressProvinceCode+' '+shippingAddressZip;
			if(shippingAddressTwo != '' && shippingAddressTwo != null){
				fullShippingAddress = fullName+'<br>'+shippingAddressOne+'<br>'+shippingAddressTwo+'<br>'+shippingAddressCity+', '+shippingAddressProvinceCode+' '+shippingAddressZip;
			}
			let myKeyVals = {'maxResponseAddress':maxResponseAddress,'fullName':fullName,'shippingAddressOne':shippingAddressOne,'shippingAddressTwo':shippingAddressTwo,'shippingAddressCity':shippingAddressCity,'shippingAddressCountry':shippingAddressCountry,'shippingAddressProvince':shippingAddressProvince,'shippingAddressProvinceCode':shippingAddressProvinceCode,'shippingAddressZip':shippingAddressZip};
			let originalAddressTemplate = '<li class="selected" data-address-item="original"><div class="radio-icon"></div><h4>Original Address<span class="popup-edit-address" onclick="removeSuggestions();">Edit Address</span></h4><p>'+fullShippingAddress+'</p></li>';
			$('#address-container ul').replaceWith('<ul>'+originalAddressTemplate+'</ul>');
			var suggestedAddressTemplate = '';
			{% if settings.enable_logs -%}console.log(window.addressValidation_timeout);{%- endif %}
			
			$.ajax({
				type: "POST",
				dataType: "json",
				url: "https://wixy8sprqg.execute-api.us-west-2.amazonaws.com/default/Fedex_Address_Validation",
				data: JSON.stringify(myKeyVals),
				timeout: window.addressValidation_timeout,
				success: function(resultData) {
					$.each(resultData, function(i, itemObject) {
						let shippingCandidateAddressOne = '';
						let shippingCandidateAddressTwo = '';
						let addressLine = [];
						let postcodePrimaryLow = '';
						let numberOfResults = 10;
						if(itemObject.Candidate === undefined){
							$('body').css("overflow","visible");
							uvaCheckFlag = true;
							$("#continue_button").click();
						} else {
							let suggestedAddressText = '<h4>Suggested Address</h4>';
							$.each(itemObject.Candidate, function(i, candidateObject) {
								numberOfResults = Object.keys(itemObject.Candidate).length;
								if(candidateObject.AddressKeyFormat !== undefined){
									shippingCandidateAddressOne = candidateObject.AddressKeyFormat.PoliticalDivision1;
									shippingCandidateAddressTwo = candidateObject.AddressKeyFormat.PoliticalDivision2;

									if(typeof(candidateObject.AddressKeyFormat.AddressLine) != 'string'){
										addressLine = candidateObject.AddressKeyFormat.AddressLine.join("---");
									} else {
										addressLine = candidateObject.AddressKeyFormat.AddressLine;
									}
									postcodePrimaryLow = candidateObject.AddressKeyFormat.PostcodePrimaryLow
								} else {
									shippingCandidateAddressOne = candidateObject.PoliticalDivision1;
									shippingCandidateAddressTwo = candidateObject.PoliticalDivision2;
									if(typeof(candidateObject.AddressLine) != 'string'){
										addressLine = candidateObject.AddressLine.join("---");
									} else {
										addressLine = candidateObject.AddressLine;
									}
									postcodePrimaryLow = candidateObject.PostcodePrimaryLow
								}

								suggestedAddressTemplate = '<li data-address-item="suggested" data-address="'+shippingCandidateAddressOne+'|'+shippingCandidateAddressTwo+'|'+addressLine+'|'+postcodePrimaryLow+'"><div class="radio-icon"></div>'+suggestedAddressText+'<p>'+fullName+'<br>'+addressLine.replace("---", "<br>")+'<br>'+shippingCandidateAddressTwo+', '+shippingCandidateAddressOne+' '+postcodePrimaryLow+'</p><!--<button type="button" class="product__add-to-bag" onclick="changeAddress(\''+shippingCandidateAddressOne+'\',\''+shippingCandidateAddressTwo+'\',\''+addressLine+'\',\''+postcodePrimaryLow+'\');" ><span>USE THIS ADDRESS</span></button>--></li>';
								$('#address-container ul').append(suggestedAddressTemplate);
								suggestedAddressText = '';
							});
							if(shippingCandidateAddressOne.toLowerCase() == shippingAddressProvinceCode.toLowerCase() && shippingCandidateAddressTwo.toLowerCase() == shippingAddressCity.toLowerCase() && addressLine.replace("---", " ").toLowerCase() == $.trim(shippingAddressOne+' '+shippingAddressTwo).toLowerCase() && postcodePrimaryLow.toLowerCase() == shippingAddressZip.toLowerCase() && numberOfResults == 1){
								$('body').css("overflow","visible");
								uvaCheckFlag = true;
								$("#continue_button").click();
							} else {
								$('body').css("overflow","hidden");
								$("#continue_button").addClass("btn--loading");
								$('#uav-popup').css('display', 'flex');
							}

						}
					});
				},
				error: function(xhr, textStatus, errorThrown) {
					//if (textStatus == 'timeout') {
						{% if settings.enable_logs -%}console.log("Error : Timeout for this call!");{%- endif %}
						$('body').css("overflow","visible");
						uvaCheckFlag = true;
						$("#continue_button").click();
					//}
				}
			});

		} else { $("#continue_button").removeClass("btn--loading"); }
  }});
	}
});

function getCookie(t){var n,e,r=t+"=",i=document.cookie.split(";"),o=i.length;for(n=0;n<o;n+=1){for(e=i[n];" "===e.charAt(0);)e=e.substring(1);if(-1!==e.indexOf(r))return e.substring(r.length,e.length)}return!1}

function formatPhone(phone) {
    if (!phone) {
      return null;
    } 

    phone = phone.split(' ').join('');
    phone = phone.split('(').join('');
    phone = phone.split(')').join('');
    phone = phone.split('-').join('');

    if (phone.length == 10) {
      return '001' + phone;
    } else if (phone.startsWith('+1')) {
      return phone.replace('+1', '001');
    } else if (phone.startsWith('001')) {
      return phone;
    } else if (phone.startsWith('+')) {
      return phone.replace('+', '00');
    } else {
      return null;
    }
  }

function capitalize(s){
	return s;
};

function updateAddress() {
	const $selected = $('[data-address-item].selected')
	const address = $selected.data('address').split('|')
	const addressOne = address[0]
	const addressTwo = address[1]
	let addressLine = address[2]
	const postcodePrimaryLow = address[3]

	uvaCheckFlag = true;
	addressLine = addressLine.split('---');

	$('#checkout_shipping_address_address1').val(capitalize(addressLine[0]));

	if(addressLine[1] != undefined){
		$('#checkout_shipping_address_address2').val(capitalize(addressLine[1]));
	}

	$('#checkout_shipping_address_city').val(capitalize(addressTwo));
	$('#checkout_shipping_address_zip').val(postcodePrimaryLow);
	$('#checkout_shipping_address_province').val(addressOne)
	$('#uav-popup').hide();
	$("#continue_button").removeClass("btn--loading");
	$('body').css("overflow","visible");
	$("#continue_button").click();
}

function doNothing(){
	uvaCheckFlag = true;
	$('#uav-popup').hide();
	$("#continue_button").removeClass("btn--loading");
	$('body').css("overflow","visible");
	$("#continue_button").click();
}

function removeSuggestions(){
	uvaCheckFlag = false;
	$('#uav-popup').hide();
	$("#continue_button").removeClass("btn--loading");
	$('body').css("overflow","visible");
}
</script>
<style>
#uav-popup,
#uav-popup-loading {
	background: rgba(0,0,0,0.7);
	display: none;
	height: 100%;
	position: fixed;
	width: 100%;
	z-index: 999;
}

#uav-popup {
	align-items: center;
	justify-content: center;
}

.address-logo {
	display: block;
	margin-bottom: 35px;
}

.address-verification__close {
	position: absolute;
	right: 40px;
	top: 32px;
}

.address_verification {
	background: #FCF9F3;
	margin: 0 auto;
	max-height: 100%;
	max-width: 415px;
	overflow-y: auto;
	padding: 30px 40px 42px;
	position: relative;
}

@media (max-width: 992px) {
	.address_verification {
		top: 0;
		padding: 36px 20px 24px;
	}
}

.address_verification h2 {
	color: #231F20;
	font-family: "GTA-Medium", Arial, sans-serif;
	font-size: 18px;
	line-height: 22px;
	margin-bottom: 2px;
}

.address_verification p {
	color: #231F20;
	font-family: "GTA-Regular", Arial, sans-serif;
	font-size: 12px;
	line-height: 16px;
}

.address_verification > p {
	margin-bottom: 24px;
}

.addressList .radio-icon {
	border: 1px solid #F5E9D8;
	border-radius: 50%;
	height: 18px;
	left: 15px;
	position: absolute;
	top: 23px;
	width: 18px;
}

.addressList .radio-icon:after {
	background-color: #fff;
	content: "";
	height: 4px;
	left: 50%;
	opacity: 0;
	position: absolute;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 4px;
}

.addressList .selected .radio-icon {
	border-color: #E36662;
	background-color: #E36662;
}

.addressList .selected .radio-icon:after {
	opacity: 1;
}

.addressList ul {
	background-color: #fff;
	border: 1px solid #F5E9D8;
	border-radius: 3px;
}

.addressList ul li {
	border-bottom: 1px solid #F5E9D8;
	cursor: pointer;
	padding: 23px 15px 16px 48px;
	position: relative;
}

.addressList ul li h4 {
	color: #797979;
	font-family: "GTA-Regular", Arial, sans-serif;
	font-size: 11px;
	line-height: 13px;
	margin: 0 0 7px;
}

.addressList .address_innerList p {
	color: #231F20;
	font-family: "GTA-Regular", Arial, sans-serif;
	font-size: 14px;
	line-height: 17px;
	margin: 0 0 16px;
}

.addressList button.product__add-to-bag {
	align-items: center;
	background-color: #E36662;
	border: 1px solid #E36662;
	color: #fff;
	display: flex;
	justify-content: center;
	font-family: "GTA-Bold", Arial, sans-serif;
	font-size: 14px;
	height: 55px;
	letter-spacing: 0.04em;
	line-height: 18px;
	margin: 24px auto 0;
	max-width: 300px;
	padding: 0 25px;
	text-transform: uppercase;
	width: 100%;
}

.addressList button.product__add-to-bag:hover{
	background-color: #B33C0D;
	color:#fff;
}

.addressList ul li:last-child {
	border-bottom: none;
}

.add_verify {
	max-width: 268px;
	background:#a32e0a;
	margin: 0px auto;
	padding: 31px 19px;
	position: relative;
	top: calc(50% - 53px);
}

.add_verify p{
	font-family: "GTA-Regular", Arial, sans-serif;
	font-weight:400;
	font-size: 18px;
	line-height: 21px;
	color: #fff;
	display: flex;
	align-items: center;
}

.add_verify p span{
	margin-right:21px;
}

.add_verify p span .btn__spinner{
	position:static;
	margin:0;
	opacity:1;
	-webkit-transform: translateX(0);
	transform: translateX(0);
	-webkit-transition: -webkit-transform 400ms ease-out;
	transition: -webkit-transform 400ms ease-out;
	transition: transform 400ms ease-out;
	transition: transform 400ms ease-out, -webkit-transform 400ms ease-out;
}

.popup-edit-address {
	color: #E36662;
	cursor: pointer;
	float: right;
	font-family: "GTA-Regular", Arial, sans-serif;
	font-size: 11px;
	line-height: 13px;
	text-decoration: none;
}
</style>
