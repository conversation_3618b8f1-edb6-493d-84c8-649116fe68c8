{% if settings.inclusion  %}

<div 
  x-data="{
      product:$store.products['{{ product.handle }}'],
      variant:$store.products['{{ product.handle }}'].variant,
  }"
  x-init="
    $alpine.listen('Product', () => {
      $data.product = $store.products['{{ product.handle }}']
      $data.variant = $store.products['{{ product.handle }}'].variant
    })
  "
>

  {% if settings.inclusion_js %}
  <template x-if="product && {{ settings.inclusion_js }}">
  {% endif %}  

  <div class="flex product-gate__actions">

    <template x-if="!!product.variant">
      {% capture atc_attributes %}
        @click="(event) => {
          event.preventDefault(); 
          {{ settings.click }}
        }"
      {% endcapture %}
      {%- liquid
        render 'button' tag:'button' attributes:atc_attributes style:'primary' content:settings.enabled_button_text class:'w-full justify-center' 
      -%}
    </template>

    {% if settings.hide %}
    <style>{{ parent }} {{ settings.hide }} { display:none !important; }</style>
    {% endif %}

  </div>

  {% if settings.inclusion_js %}
  </template>
  {% endif %}  

</div>

{% endif %}