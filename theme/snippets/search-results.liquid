<div class="search-results__type search-results__type--{{ type }}">

  <p class="search-results__type-label">{{ type | capitalize }}</p>

  {% case type %}
  
    {% when 'products' %}
    <template x-for="product in $store.search.results.products">
      {% render 'search-item' type:'product' click:click settings:settings %}
    </template>

    {% when 'collections' %}
    <template x-for="collection in $store.search.results.collections">
      {% render 'search-item' type:'collection' settings:settings %}
    </template>

    {% when 'suggestions' %}
    <template x-for="suggestion in $store.search.results.suggestions">
      {% render 'search-item' type:'suggestion' settings:settings %}
    </template>
  
  {% endcase %}

</div>