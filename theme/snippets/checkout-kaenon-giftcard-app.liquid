<script>
	var get_order_id =  "{{ checkout.order_id }}";
	let totalPrice =  {{checkout.total_price | amount_with_apostrophe_separator }}/100;
	let subtotalPrice =  {{checkout.line_items_subtotal_price | amount_with_apostrophe_separator }}/100;
	$(document).ready(function(){
		let get_val = $('.sidebar #checkout_reduction_code').val();
        $(document).on('click','#order-summary button[type=submit]', (e) => {
			get_val = $('.sidebar #checkout_reduction_code').val();
			let due__price = parseInt($('.payment-due__price').attr('data-checkout-payment-due-target'))/100;
			$.ajax({
				url: 'https://y92xhuph52.execute-api.us-west-2.amazonaws.com/giftcard-validation/kaenon-validate-gift-card',
				type: 'post',
				data: JSON.stringify({get_val: get_val,totalPrice:due__price}),
				contentType: "application/json; charset=utf-8",
				async: false,
				success: function(data) {
					if(data == "1"){
						return true;
					}
					else{
						$("#appresponse").html(data);
						$("#mygiftcard_modal").show();
						e.preventDefault(e);
						return false;
					}
				}
			});
		});
	}); 
if(get_order_id==""){
	{% if checkout.gift_cards_amount %}
	$(document).ready(function(){
		var gift_card_array = [];
		{% for gift_card in checkout.applied_gift_cards %}
			gift_card_array.push('{{gift_card.balance | divided_by: 100.0 | round: 2 }}');
		{% endfor %}
		let gift_cards_amount = gift_card_array[0];
		let shipping_price = {{checkout.shipping_price | divided_by: 100.0 | round: 2 }}	
		var tax_price = {{checkout.tax_price | divided_by: 100.0 | round: 2 }}
		if(isNaN(tax_price)) {tax_price = 0;}
		if( gift_cards_amount < totalPrice && gift_cards_amount > 0 ){
			$("button.tag__button").click();
			$("#appresponse").html('The value of items in your cart must be less than the balance of your card.<br/>Please remove items from your cart in order to complete the checkout process.');
			$("#mygiftcard_modal").show();
		}
		{% for tag in checkout.customer.tags %}
			if('{{tag}}' == 'Employee'){
				var payable_total = 0;
				if(gift_cards_amount){					
					var cu_shipping = parseInt($('.input-radio:checked').attr('data-checkout-total-shipping-cents'))/100;
					if(isNaN(cu_shipping)) {cu_shipping = shipping_price}
					payable_total = subtotalPrice + cu_shipping + tax_price - gift_cards_amount;
					if(payable_total > 0){
						setTimeout( function(){ 
							$("a.breadcrumb__link:contains('Payment')").css("pointer-events","none");
						}, 1000 );					
					}
					$(document).on("click","#continue_button",function( event ) {
						jQuery("#continue_button").addClass("btn--loading");
						payable_total = 0;
						let cu_shipping = parseInt($('.input-radio:checked').attr('data-checkout-total-shipping-cents'))/100;
						payable_total = subtotalPrice + cu_shipping + tax_price - gift_cards_amount;
						if(payable_total > 0){
							$("#appresponse").html('The value of items in your cart must be less than the balance of your card.<br/>Please remove items from your cart in order to complete the checkout process.');
							$("#mygiftcard_modal").show();
							$("a.breadcrumb__link:contains('Payment')").css("pointer-events","none");
							jQuery("#continue_button").removeClass("btn--loading");
							return false;
						}else{
							$("a.breadcrumb__link:contains('Payment')").css("pointer-events","all");
							jQuery("#continue_button").removeClass("btn--loading");
							return true;
						}
					});
				}
			}
		{% endfor %}
	});
	{% endif %}
}	
</script>