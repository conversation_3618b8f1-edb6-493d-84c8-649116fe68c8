{% if _settings.enhanced_upsell_item %}
<template x-if="loading">
  <div class="upsell-item upsell-item-skeleton">
    <div class="h-full flex gap-2">
      <div class="bg-gray-200  w-[72px] h-[72px]  mb-2"></div>
      <div class="flex flex-col flex-1">
        <div class="h-4 bg-gray-200 w-3/4 mb-2"></div>
        <div class="h-4 bg-gray-200 w-1/2"></div>
      </div>
    </div>
  </div>
</template>
<template x-if="!loading">
<div class="upsell-item flex flex-col">
  <div class="upsell-item__content flex">
    <a class="upsell-item__media" :href="`{{ routes.root_url }}products/${product.handle}`">
      {% render 'image'
      widths:'200'  
      attributes: ':src="product.featured_image || product.images[0].src || product.images[0]" :alt="product.title"' 
      sizes: '(min-width: 768px) 20vw, 50vw'
      %}
    </a>
    <div class="upsell-item__body flex flex-col">
      
      <header class="upsell-item__header flex">
        
        <div class="upsell-item__titles">
          <p class="upsell-item__title {{ settings.classes_product_item_title }}" x-text="{{ settings.product_item_title_source }}"></p>
          <template x-if="{{ settings.product_item_subtitle_source }}">
            <p class="upsell-item__subtitle {{ settings.classes_product_item_subtitle }}" x-text="{{ settings.product_item_subtitle_source }}"></p>
          </template>
          <p class="upsell-item__type {{ settings.classes_product_item_type }}" 
             x-text="{{ settings.product_item_type_source }} ? {{ settings.product_item_type_source }} : product.product_type">
          </p>
        </div>

        <p class="upsell-item__prices">
          <template x-if="product.compare_at_price">
            <s class="upsell-item__compare-at-price type-item {{ settings.classes_product_item_price }}" x-text="money.format(product.compare_at_price)"></s>
          </template>
          <span class="upsell-item__price type-item {{ settings.classes_product_item_price }}" x-text="money.format(product.price || product.variants[0].price)"></span>
        </p>

        {% if _settings.include_review_summary %}
          {% render 'review-summary' product:false, sku_path:'product.sku' %}
        {% endif %}

      </header>
    </div>
  </div>

  {% if _settings.include_swatches %}
    <div class="upsell-item__swatches-container" x-data="{ 
      arrows: {{_settings.arrows}},
      availableSiblings() {
        return this.product.siblings?.filter(s => s.available) || [];
      },
      visibleCount: function() {
        const breakpoint = this.arrows ? 400 : 375;
        return window.innerWidth <= breakpoint ? 4 : 5;
      },
      remainingCount() {
        return Math.max(0, this.availableSiblings().length - this.visibleCount());
      }
    }">
      <div class="upsell-item__swatches relative no-scrollbar">
        <div class="upsell-item__swatches--visible">

          <template x-for="(sibling, index) in availableSiblings()">
            <button 
              class="upsell-item__swatch w-8 h-8 flex-shrink-0"
              :class="{ 
                'active': sibling.handle == product.handle,
                'hidden': index >= visibleCount()
              }"
              @click.prevent="sibling.sku = sibling.variants?.[0]?.sku; sibling.product_id = sibling.id; product = sibling; console.log(sibling.product_id);"
            >
              {% render 'image'
                widths:'50,100' 
                attributes: ':src="sibling.featuredImage.url" :alt="sibling.featuredImage.altText"' 
                sizes: '50px'
                src: 'sibling.featuredImage.url'
              %}
            </button>
          </template>
          <template x-if="remainingCount() > 0">
            <button 
              class="upsell-item__swatch-more flex items-center justify-center"
              @click="window.location.href = `{{ routes.root_url }}products/${product.handle}`"
              x-text="`+${remainingCount()} more`"
            ></button>
          </template>
        </div>
      </div>
    </div>

  {% endif %}

    <footer class="upsell-item__actions mt-auto" x-data="{
      variant: false, 
      isDisabled: true,
      }"
      x-init="$watch('product.handle', () => {
        const select = $el.querySelector('select');
        if (select && select.selectedIndex > 0) {
          const selectedVariant = product.variants[select.value];
          if (selectedVariant && selectedVariant.available) {
            variant = selectedVariant;
            isDisabled = false;
          } else {
            variant = false;
            isDisabled = true;
          }
        } else {
          variant = false;
          isDisabled = true;
        }
      })"
      @click.stop=""
    >
      <template x-if="product.variants.length < 2">
        <label>
        <select class="hidden" x-init="$data.variant=product.variants[0];console.log('product.variants if ', product.variants, $data.variant.length )" aria-label="select-variant" role="listbox">          
          {% comment %}
            <option selected disabled>{{ 'products.product.variant_selection' | t }}</option>
          {% endcomment %}
          <template x-for="(variant, vi) in product.variants">
            <template x-if="variant">
              <option selected role="option" :value="vi"  x-text="variant.title.split('/').at(-1)" :disabled="!variant.available"></option>
            </template>
          </template>
        </select>
        </label>
      </template>
      <template x-if="product.variants.length > 1">
      <label>
        <select @change="$data.variant=product.variants[Number($el.value)]" aria-label="select-variant" role="listbox">
          
          {% assign variant_selection_t = 'products.product.variant_selection' | t  %}
          {% if variant_selection_t contains "Translation missing" %}
            {% assign variant_selection_t = 'Select a Size'  %}
          {% else %}
            {% assign variant_selection_t = 'products.product.variant_selection' | t  %}
          {% endif %}
            <option selected disabled role="option">{{ variant_selection_t }}</option>
          
          {% comment %}
            <option selected disabled>{{ 'products.product.variant_selection' | t }}</option>
          {% endcomment %}
          <template x-for="(variant, vi) in product.variants">
            <template x-if="variant">
              <option role="option" :value="vi" x-text="variant.title.split('/').at(-1)" :disabled="!variant.available"></option>
            </template>
          </template>
        </select>
        </label>
      </template>
      <button class="button button--primary" 
        x-effect="isDisabled = (!variant || !variant.available ) && product.variants.length > 1" 
        :disabled="isDisabled" 
        @click.stop="{% unless _settings.include_cart_recs %}$el.closest('.upsell-item').classList.add(`sibling-item-${product.id}`);{% endunless %} Cart.add({id:variant.id,product_id:product.id,properties:{_sibling:product.id,_upc:variant.barcode,_source:'{{source_name}}',_tags:product.tags}})" 
      >
        Add {% render 'icon' icon:'plus' %}
      </button>
    </footer>
</div>
</template>

{% else %}

<div class="upsell-item flex">
  <a class="upsell-item__media" :href="`{{ routes.root_url }}products/${product.handle}`">
    {% render 'image'
    widths:'200'  
    attributes: ':src="product.featured_image || product.images[0].src || product.images[0]" :alt="product.title"' 
    sizes: '(min-width: 768px) 20vw, 50vw'
    %}
  </a>
  <div class="upsell-item__body flex flex-col">
    
    <header class="upsell-item__header flex">
      
      <div class="upsell-item__titles">
        <p class="upsell-item__title {{ settings.classes_product_item_title }}" x-text="{{ settings.product_item_title_source }}"></p>
        <template x-if="{{ settings.product_item_subtitle_source }}">
          <p class="upsell-item__subtitle {{ settings.classes_product_item_subtitle }}" x-text="{{ settings.product_item_subtitle_source }}"></p>
        </template>
      </div>

      <p class="upsell-item__prices">
        <template x-if="product.compare_at_price">
          <s class="upsell-item__compare-at-price type-item {{ settings.classes_product_item_price }}" x-text="money.format(product.compare_at_price)"></s>
        </template>
        <span class="upsell-item__price type-item {{ settings.classes_product_item_price }}" x-text="money.format(product.price || product.variants[0].price)"></span>
      </p>
    </header>

    <footer class="upsell-item__actions mt-auto" x-data="{variant:false}" @click.stop="">
      <template x-if="product.variants.length < 2">
        <label>
          <select class="hidden" x-init="$data.variant=product.variants[0];{% if settings.enable_logs -%}console.log('product.variants if ', product.variants, $data.variant.length ){%- endif %}" aria-label="select-variant" role="listbox">          
            {% comment %}
              <option selected disabled>{{ 'products.product.variant_selection' | t }}</option>
            {% endcomment %}
            <template x-for="(variant, vi) in product.variants">
              <template x-if="variant">
                <option selected role="option" :value="vi"  x-text="variant.title.split('/').at(-1)" :disabled="!variant.available"></option>
              </template>
            </template>
          </select>
        </label>
      </template>
      <template x-if="product.variants.length > 1">
      <label>
        <select @change="$data.variant=product.variants[Number($el.value)]" aria-label="select-variant" role="listbox">
          
          {% assign variant_selection_t = 'products.product.variant_selection' | t  %}
          {% if variant_selection_t contains "Translation missing" %}
            {% assign variant_selection_t = 'Select a Size'  %}
          {% else %}
            {% assign variant_selection_t = 'products.product.variant_selection' | t  %}
          {% endif %}
            <option selected disabled role="option">{{ variant_selection_t }}</option>
          
          {% comment %}
            <option selected disabled>{{ 'products.product.variant_selection' | t }}</option>
          {% endcomment %}
          <template x-for="(variant, vi) in product.variants">
            <template x-if="variant">
              <option role="option" :value="vi" x-text="variant.title.split('/').at(-1)" :disabled="!variant.available"></option>
            </template>
          </template>
        </select>
        </label>
      </template>
      <button class="button button--primary" :disabled="(!variant || !variant.available) && product.variants.length > 1" @click.stop="Cart.add({id:variant.id,properties:{_upc:variant.barcode,_source:'{{source_name}}'}})" aria-label="add" role="button">
        Add {% render 'icon' icon:'plus' %}
      </button>
    </footer>
  </div>
</div>
{% endif %}
