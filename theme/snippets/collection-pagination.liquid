<div class="" role="navigation" aria-label="{{ 'general.pagination.label' | t }}" aria-live="polite"neptune-liquid="{topic:Pagination,source:Collection.pagination}">

  {% raw %}
  {% unless total_pages == 1  or total_pages == 0 %}

      <ul class="pagination--page" role="list">

        {% if current_page > 1 %}
        <li>
          <button class="{{active}} w-full pp-control" onclick="Collection.page({{current_page | minus: 1}},1)">
            {% endraw %}
            {% render 'icon' icon:'chevron-left' width:16 height:16%}
            {% raw %}
          </button>
        </li>
        {% endif %}
        
        {% for page in pages %}
        {% assign pageoffset = current_page | minus: page | abs %}
        {% if pageoffset != 0 and pageoffset != 1 and pageoffset != 2 and pageoffset != 3 %}{% continue %}{% endif %}
        {% assign active = false %}
        {% if current_page == page %}
          {% assign active = true %}
        {% endif %}
        <li class="hidden lg:block">
          {% if active == true %}
          <span aria-current="page" aria-label="Page {{ page }}">{{ page }}</span>
          {% else %}
          <button class="{{active}} block w-12 h-12 rounded-full hover:bg-white flex items-center justify-center" onclick="Collection.page({{page}},1)">{{page}}</button>
          {% endif %}
        </li>
          {% assign last_page_shown = page %}
        {% endfor %}
        {% assign active = 'dim' %}
        {% if current_page == total_pages %}
          {% assign active = 'o-50 pointer-none' %}
        {% endif %}
        {% if last_page_shown != total_pages %} 
        <li class="self-end hidden lg:block">
          <span class="block h-full p-4">…</span>
        </li>
        {% endif %}

        {% unless current_page == total_pages %}
        <li>
          <button class="{{active}} pp-control w-full" onclick="Collection.page({{current_page | plus: 1}},1)">
            {% endraw %}
            {% render 'icon' icon:'chevron-right' width:16 height:16 %}
            {% raw %}
          </button>
        </li>
        {% endunless %}
      
      </ul>
  {% endunless %}
  {% endraw %}
  
 </div> 