{%- comment -%}
{% render 'field' name:'rad' id:'' placeholder:'' type:'' class:'bg-pink' attributes:'style="background:pink" %}
{%- endcomment -%}
{%- liquid
  if id == blank
    assign id = name | camelcase
  endif

  if label == blank
    assign label = name 
  endif

  if type == blank
    assign type = 'text'
  endif

  if placeholder != blank
    assign attributes = attributes | append: ' placeholder="' | append: placeholder | append: '"'
  endif

  if error != blank
    assign attributes = attributes | append: ' error-message="' | append: error | append: '"'
  endif

  assign options = options | split: ','
-%}
<div class="{%- liquid
  case style
    when 'floating'
      echo 'floating-label'
    when 'field' 
      echo 'field'
    when blank 
      echo 'field'
    endcase
%} w-full">
      <label for="{{ id }}">{{ label }}</label>
{%- case type -%}
  {%- when 'select' -%}
    <select id="{{ id }}" name="{{ name }}" {{ attributes }}>
    {% for option in options %}
      <option>{{ option }}</option>
    {% endfor %}
      {{ content }}
    </select>
  {%- else -%}
    <input id="{{ id }}" name="{{ name }}" type="{{ type }}" {{ attributes }} >
{%- endcase -%}
</div>
