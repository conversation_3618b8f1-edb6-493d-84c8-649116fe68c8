<div class="account-block order-history">
	<h2 class="account-block__title order-history__title mt-0">{{ title | default: settings.title | default: 'Order History' }}</h2>
	{% assign limit = limit | default: settings.limit | default: 12 %}
	{% paginate customer.orders by limit %}
	{% liquid 
		for order in customer.orders
			render 'order-item' order:order
		endfor
	%}
	{% render 'pagination' paginate:paginate %}
	{% endpaginate %}
	
	{% if customer.orders.size == 0 %}
	
		<div class="order-history-empty">
			<p class="text-sm">{{ 'customer.orders.none' | t }}</p>
			<a class="button button--primary" href="{{ routes.root_url }}">{{ 'customer.orders.none_action' | t }}</a>
		</div>

	{% endif %}
</div>