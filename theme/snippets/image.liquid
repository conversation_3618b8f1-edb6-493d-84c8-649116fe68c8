{% liquid
  assign image = image | default: settings.image
  assign default_widths = '360,720,1080,1200,1440,2400,2800,3600'
  assign widths = widths | default: settings.widths | default: default_widths
  assign widths_array = widths | split: ','

  assign aspect = aspect | default: settings.aspect | default: '4x3'
  assign base_width = widths_array.first | plus: 0
  
  if image.aspect_ratio
    assign aspect_w = image.width
    assign aspect_h = image.height
  else
    assign aspect_parts = aspect | split: 'x'
    assign aspect_w = aspect_parts.first | times: base_width
    assign aspect_h = aspect_parts.last | times: base_width
  endif

  assign loading = loading | default: settings.image_loading | default: 'lazy'
  
  assign is_priority = false
  if attributes contains ':data-priority="isPriority()"' or attributes contains 'fetchpriority="high"'
    assign is_priority = true
  endif
%}

{% liquid
  assign srcset = ''
  for width in widths_array
    assign w = width | plus: 0
    assign url = image | default: settings.image | image_url: width: w
    assign srcset = srcset | append: ',' | append: url | append: ' ' | append: w | append: 'w'
  endfor
%}

<img
  {% if image.src %}
    srcset="{{ srcset | remove_first: ',' }}"
    src="{{ srcset | remove_first: ',' | split: ',' | first }}"
  {% endif %}
  sizes="{{ sizes | default: '100vw' }}"
  class="{{ class }} {% if crop != blank %}image--crop{% endif %} {% render 'class-settings' settings:settings, prefix:'media_class' %} {% unless is_priority %}opacity-0{% endunless %}"
  alt="{{ alt | default: image.alt | escape }}"
  width="{{ aspect_w }}"
  height="{{ aspect_h }}"
  {% if is_priority %}
    onload="this.classList.remove('opacity-0'); this.classList.add('opacity-100');"
  {% else %}
    onload="if (this.classList.contains('opacity-0')) { this.classList.remove('opacity-0'); this.classList.add('opacity-100'); }"
    loading="{{ loading }}"
    decoding="async"
  {% endif %}
  {{ attributes }}
/>
