{% comment %}
    Renders a product thumbnail with a modal-opener

    Accepts:
    - media: {Object} Product Media object
    - position: {String} Position of the media. Used for accessible label.
    - loop: {Boolean} Enable video looping (optional)
    - modal_id: {String} The product modal that will be shown by clicking the thumbnail
    - xr_button: {<PERSON><PERSON><PERSON>} Renders the "View in your space" button (shopify-xr-button) if the media is a 3D Model

    Usage:
    {% render 'product-thumbnail',
      media: media,
      position: forloop.index,
      loop: section.settings.enable_video_looping,
      modal_id: section.id
    %}
{% endcomment %}

<noscript>
  {%- if media.media_type == 'video' or media.media_type == 'external_video' -%}
    <span class="product__media-icon motion-reduce">{% render 'icon-play' %}</span>
    <div class="product__media media" style="padding-top: {{ 1 | divided_by: media.preview_image.aspect_ratio | times: 100 }}%;">
      <img
        srcset="{% if media.preview_image.width >= 288 %}{{ media.preview_image | img_url: '288x' }} 288w,{% endif %}
          {% if media.preview_image.width >= 576 %}{{ media.preview_image | img_url: '576x' }} 576w,{% endif %}
          {% if media.preview_image.width >= 750 %}{{ media.preview_image | img_url: '750x' }} 750w,{% endif %}
          {% if media.preview_image.width >= 1100 %}{{ media.preview_image | img_url: '1100x' }} 1100w,{% endif %}
          {% if media.preview_image.width >= 1500 %}{{ media.preview_image | img_url: '1500x' }} 1500w{% endif %}"
        src="{{ media | img_url: '1500x' }}"
        sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | times: 0.64 | round }}px, (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw - 4rem)"
        loading="lazy"
        width="576"
        height="{{ 576 | divided_by: media.preview_image.aspect_ratio | ceil }}"
        alt="{{ media.preview_image.alt | escape }}"
      >
    </div>
    <a href="{% if media.media_type == 'video' %}{{ media.sources[1].url }}{% else %}{{ media | external_video_url }}{% endif %}" class="product__media-toggle">
      <span class="sr-only">{{ 'products.product.video_exit_message' | t: title: product.title | escape }}</span>
    </a>
  {%- else -%}
    <div class="product__media media" style="padding-top: {{ 1 | divided_by: media.preview_image.aspect_ratio | times: 100 }}%;">
      <img
        srcset="{% if media.preview_image.width >= 288 %}{{ media.preview_image | img_url: '288x' }} 288w,{% endif %}
          {% if media.preview_image.width >= 576 %}{{ media.preview_image | img_url: '576x' }} 576w,{% endif %}
          {% if media.preview_image.width >= 750 %}{{ media.preview_image | img_url: '750x' }} 750w,{% endif %}
          {% if media.preview_image.width >= 1100 %}{{ media.preview_image | img_url: '1100x' }} 1100w,{% endif %}
          {% if media.preview_image.width >= 1500 %}{{ media.preview_image | img_url: '1500x' }} 1500w{% endif %}"
        src="{{ media | img_url: '1500x' }}"
        sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | times: 0.64 | round }}px, (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw - 4rem)"
        loading="lazy"
        width="576"
        height="{{ 576 | divided_by: media.preview_image.aspect_ratio | ceil }}"
        alt="{{ media.preview_image.alt | escape }}"
      >
    </div>
  {%- endif -%}
</noscript>

<modal-opener class="product__modal-opener product__modal-opener--{{ media.media_type }} no-js-hidden" data-modal="#ProductModal-{{ modal_id }}">
  <span class="product__media-icon motion-reduce" aria-hidden="true">
    {%- liquid
      case media.media_type
      when 'video' or 'external_video'
        render 'icon-play'
      when 'model'
        render 'icon-3d-model'
      else
        render 'icon-zoom'
      endcase
    -%}
  </span>

  <div class="product__media media media--transparent" style="padding-top: {{ 1 | divided_by: media.preview_image.aspect_ratio | times: 100 }}%;">
    <img
      srcset="{% if media.preview_image.width >= 288 %}{{ media.preview_image | img_url: '288x' }} 288w,{% endif %}
        {% if media.preview_image.width >= 576 %}{{ media.preview_image | img_url: '576x' }} 576w,{% endif %}
        {% if media.preview_image.width >= 750 %}{{ media.preview_image | img_url: '750x' }} 750w,{% endif %}
        {% if media.preview_image.width >= 1100 %}{{ media.preview_image | img_url: '1100x' }} 1100w,{% endif %}
        {% if media.preview_image.width >= 1500 %}{{ media.preview_image | img_url: '1500x' }} 1500w{% endif %}"
      src="{{ media | img_url: '1500x' }}"
      sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | times: 0.64 | round }}px, (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw - 4rem)"
      loading="lazy"
      width="576"
      height="{{ 576 | divided_by: media.preview_image.aspect_ratio | ceil }}"
      alt="{{ media.preview_image.alt | escape }}"
    >
  </div>
  <button class="product__media-toggle" type="button" aria-haspopup="dialog" aria-label="open-media" data-media-id="{{ media.id }}" >
    <span class="sr-only">
      {%- if position == 'featured' %}
        {{ 'products.product.media.open_featured_media' | t }}
      {%- else -%}
        {{ 'products.product.media.open_media' | t: index: position }}
      {%- endif -%}
    </span>
  </button>
</modal-opener>

{%- if media.media_type != 'image' -%}
  {%- if media.media_type == 'model' -%}
    <product-model class="deferred-media media media--transparent no-js-hidden" style="padding-top: 100%" data-media-id="{{ media.id }}">
  {%- else -%}
    <deferred-media class="deferred-media media no-js-hidden" style="padding-top: {{ 1 | divided_by: media.aspect_ratio | times: 100 }}%" data-media-id="{{ media.id }}">
  {%- endif -%}
  <button id="Deferred-Poster-Modal-{{ media.id }}" class="deferred-media__poster" type="button" aria-label="play">
    <span class="deferred-media__poster-button motion-reduce">
      {%- if media.media_type == 'model' -%}
        <span class="sr-only">{{ 'products.product.media.play_model' | t }}</span>
        {%- render 'icon-3d-model' -%}
      {%- else -%}
        <span class="sr-only">{{ 'products.product.media.play_video' | t }}</span>
        {%- render 'icon-play' -%}
      {%- endif -%}
    </span>
    <img
      srcset="{% if media.preview_image.width >= 288 %}{{ media.preview_image | img_url: '288x' }} 288w,{% endif %}
        {% if media.preview_image.width >= 576 %}{{ media.preview_image | img_url: '576x' }} 576w,{% endif %}
        {% if media.preview_image.width >= 750 %}{{ media.preview_image | img_url: '750x' }} 750w,{% endif %}
        {% if media.preview_image.width >= 1100 %}{{ media.preview_image | img_url: '1100x' }} 1100w,{% endif %}
        {% if media.preview_image.width >= 1500 %}{{ media.preview_image | img_url: '1500x' }} 1500w{% endif %}"
      src="{{ media | img_url: '1500x' }}"
      sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | times: 0.64 | round }}px, (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw - 4rem)"
      loading="lazy"
      width="576"
      height="{{ 576 | divided_by: media.preview_image.aspect_ratio }}"
      alt="{{ media.preview_image.alt | escape }}"
    >
  </button>
  <template>
    {%- liquid
      case media.media_type
      when 'external_video'
        assign video_class = 'js-' | append: media.host
        if media.host == 'youtube'
          echo media | external_video_url: autoplay: true, loop: loop, playlist: media.external_id | external_video_tag: class: video_class, loading: "lazy"
        else
          echo media | external_video_url: autoplay: true, loop: loop | external_video_tag: class: video_class, loading: "lazy"
        endif
      when 'video'
        echo media | media_tag: image_size: "2048x", autoplay: true, loop: loop, controls: true, preload: "none", autoplay: true
      when 'model'
        echo media | media_tag: image_size: "2048x", toggleable: true
      endcase
    -%}
  </template>

  {%- if media.media_type == 'model' -%}
    </product-model>
    {%- if xr_button -%}
      <button
        class="button button--full-width product__xr-button"
        type="button"
        aria-label="{{ 'products.product.xr_button_label' | t }}"
        data-shopify-xr
        data-shopify-model3d-id="{{ media.id }}"
        data-shopify-title="title"
        data-shopify-xr-hidden
        >
        {% render 'icon-3d-model' %}
        {{ 'products.product.xr_button' | t }}
      </button>
    {%- endif -%}
  {%- else -%}
    </deferred-media>
  {%- endif -%}
{%- endif -%}
