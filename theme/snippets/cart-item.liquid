<div class="cart__item-wrapper" x-data>

<template x-if="$store.cart.items.length">
  <form class="">
    <section aria-labelledby="cart-heading" class="cart__items-wrapper">
      
      <h2 id="cart-heading" class="sr-only">{{ 'sections.cart.headings.items' | t }}</h2>

      <ul role="list" class="cart__items">
        <template x-for="(item, index) in $store.cart.items" :key="item.key" hidden>
          <li class="cart__item cart-item">
            <div class="cart-item__image-wrapper">
            <template x-if="item.price > 0 && item.properties._source !== 'GWP'">
            <a :href="item.url" class="cart-item__image-link">
              {% render 'image'
                widths: '100,200,400'
                attributes: '
                  :src="item.featured_image.url"
                  :alt="item.featured_image.alt"
                  :srcset="(() => {
                    const widths = [100, 200, 400];
                    const src = item.featured_image.url;
                    return widths.map(w => `${src}&width=${w} ${w}w`).join(`, `);
                  })()"
                '
                class: 'cart-item__image'
                sizes: '(min-width: 768px) 100px, 80px'
                loading: 'eager'
              %}
            </a>
            </template>
            <template x-if="item.price == 0 || item.properties._source === 'GWP'">
              {% render 'image'
                widths: '100,200,400'
                attributes: '
                  :src="item.featured_image.url"
                  :alt="item.featured_image.alt"
                  :srcset="(() => {
                    const widths = [100, 200, 400];
                    const src = item.featured_image.url;
                    return widths.map(w => `${src}&width=${w} ${w}w`).join(`, `);
                  })()"
                '
                class: 'cart-item__image'
                sizes: '(min-width: 768px) 100px, 80px'
                loading: 'eager'
              %}
            </template>
            </div>

            <div class="cart-item__info">
              <div class="cart-item__info-container">
                <div class="cart-item__info-start">
                  <div class="cart-item__titles">
                    <h3 class="cart-item__title">
                      <template x-if="item.price > 0 && item.properties._source !== 'GWP'">
                        <a :href="item.url" class="cart-item__title-link" x-html="{{ settings.line_item_title_source | escape }}"></a>
                      </template>
                      <template x-if="item.price == 0 || item.properties._source === 'GWP'">
                        <span class="cart-item__title-link" x-html="{{ settings.line_item_title_source | escape }}"></span>
                      </template>
                    </h3>
                    {% if settings.badge_position == 'bottom_title' %}
                    {% assign item_badges = settings.item_badges | newline_to_br | split: '<br />' %}
                    <div class="cart-item__badges">
                      {% for badge in item_badges %}
                        {% assign badge_parts = badge | split: '::' %}
                        <template x-if="{{ badge_parts | first }}">
                          <div 
                            class="cart-item__badge" 
                            x-text="`{{ badge_parts[1] | default: badge_parts[0] }}`"
                            {% if badge_parts[2] != blank or badge_parts[3] != blank %}
                            style="
                              {% if badge_parts[2] != blank %}color: {{ badge_parts[2] }};{% endif %}
                              {% if badge_parts[3] != blank %}background-color: {{ badge_parts[3] }};{% endif %}
                            "
                            {% endif %}
                          ></div>
                        </template>
                      {% endfor %}
                    </div>
                    {% endif %}
                    {% assign show_gift_card_reviews = settings.show_gift_card_reviews %}                       
                    <template x-if="item.properties._source !== 'GWP'"> 
                      <template x-if="{{show_gift_card_reviews}} || item.product_type !== 'Gift Card'" x-data="console.log('item.product_type ',item.product_type)">
                        {% render 'review-summary' product:false, sku_path:'item.sku' %}
                      </template>                                                   
                    </template>
                    {% if settings.show_offer_options %}
                      <template x-if="item.properties._source === 'GWP'"> 
                        {% render 'review-summary' product:false, sku_path:'item.sku' %}
                      </template>
                    {% endif %}

                    {% if settings.show_item_type %}
                      <template x-if="item && item.product_type">
                        <p class="cart-item__line-item" x-text="{{ settings.line_item_type_source }}"></p>
                      </template>
                    {% endif %}

                    <template x-if="item && item.properties._source === 'GWP'">
                      {% if settings.show_offer_options %}
                        <p 
                          class="cart-item__line-item" 
                          x-data="{ 
                          colorOption: item.options_with_values.find(opt => opt.name === 'Color'),
                          sizeOption: item.options_with_values.find(opt => opt.name === 'Size')
                          }"
                          x-text="(colorOption?.value || '') + (colorOption && sizeOption ? ' / ' : '') + (sizeOption?.value || '')"
                        >
                        </p>
                      {% endif %}
                    </template>

                    <template x-if="item.properties._source !== 'GWP'">
                      {%- if settings.show_combined_options -%}
                        <p 
                          class="cart-item__line-item" 
                          x-data="{ 
                          colorOption: item.options_with_values.find(opt => opt.name === 'Color'),
                          sizeOption: item.options_with_values.find(opt => opt.name === 'Size')
                          }"
                          x-text="(colorOption?.value || '') + (colorOption && sizeOption ? ' / ' : '') + (sizeOption?.value || '')"
                        >
                        </p>
                      {% else %}
                        {% assign show_second_option = settings.show_second_option %}
                        <template x-for="(option, index) in item.options_with_values">
                          {%- if settings.hide_second_option -%}
                            <template x-if="!option.name.toLowerCase().includes('size')">                        
                              <p 
                              class="cart-item__line-item" 
                              x-text="`${option.name}: ${option.value}`"
                              >
                              </p>
                            </template>                        
                          {%- else -%}
                            <p 
                            class="cart-item__line-item" 
                            x-show="option.name !== 'Denominations'" 
                            x-text="`${option.name}: ${option.value}`"
                            >
                            </p>
                          {%- endif -%}
                        </template>
                      {% endif %}
                    </template>
                    
                    <template x-for="property in Object.entries(item.properties)">
                      <template x-if="!property[0].startsWith('_') && !property[0].toLowerCase().startsWith('expected ship')">
                        <p class="cart-item__line-item" x-text="`${property[0]}: ${property[1]}`" :class="{'emailField': property[0].includes('email') }"></p>
                      </template>
                    </template>
                  </div>

                  <button 
                    type="button" 
                    class="cart-item__remove"
                    @click="(() => {
                      const siblingId = item.properties._sibling;
                      if (siblingId) {
                        document.querySelectorAll(`.swiper-slide:has(.upsell-item.sibling-item-${siblingId})`).forEach(el => {
                          el.querySelector('.upsell-item').classList.remove(`sibling-item-${siblingId}`);
                          el.classList.add('block');
                        });
                      }
                      Cart.remove(index);
                    })()"
                    aria-label="remove"
                  >
                    {% render 'icon' icon: 'remove' width:16 height:16 strokeWidth:1 %}
                    <span class="sr-only">{{ 'sections.cart.remove_title' | t }}</span>
                  </button>
                </div>

                {% if settings.badge_position == 'bottom_option' %}
                  {% assign item_badges = settings.item_badges | newline_to_br | split: '<br />' %}
                  <div class="cart-item__badges">
                    {% for badge in item_badges %}
                      {% assign badge_parts = badge | split: '::' %}
                      <template x-if="{{ badge_parts | first }}">
                        <div 
                          class="cart-item__badge" 
                          x-text="`{{ badge_parts[1] | default: badge_parts[0] }}`"
                          {% if badge_parts[2] != blank or badge_parts[3] != blank %}
                          style="
                            {% if badge_parts[2] != blank %}color: {{ badge_parts[2] }};{% endif %}
                            {% if badge_parts[3] != blank %}background-color: {{ badge_parts[3] }};{% endif %}
                          "
                          {% endif %}
                        ></div>
                      </template>
                    {% endfor %}
                  </div>
                  {% endif %}

                <div class="cart-item__info-end">
                  <template x-if="!(item.properties._source == 'GWP')">
                  <div class="flex items-center w-1/3 cart-item__quantity field field-plus-minus">
                    <button @click="$event.preventDefault(); Cart.change({
                      'line': index+1,
                      'quantity': item.quantity-1
                    });">{% render 'icon' icon: 'minus' width:16 height:16 strokeWidth:1 %}</button>
                    <input type="text" x-model="item.quantity"></input>
                    <button @click="$event.preventDefault(); Cart.change({
                      'line': index+1,
                      'quantity': item.quantity+1
                    });">{% render 'icon' icon: 'plus' width:16 height:16 strokeWidth:1 %}</button>
                  </div>                                        
                  </template>

                  <template x-if="!(item.price == 0) && !(item.properties._source == 'GWP')">
                    <div class="cart-item__prices">
                      <p class="cart-item__price" x-bind:class="{ 'cart-item__price_with_compare': item.properties._compare_at_price }" x-text="money.format(item.price)"></p>
                      <s x-show="!!item.properties._compare_at_price" class="cart-item__compare-at-price {{ settings.classes_product_item_price }}" x-text="money.format(item.properties._compare_at_price).split('.')[0]"></s>
                    </div>
                  </template>

                  {% if settings.show_offer_price %}
                    <template x-if="item.properties._source == 'GWP'">
                      <div class="cart-item__prices">
                        <p class="cart-item__price" x-bind:class="{ 'cart-item__price_with_compare': item.properties._compare_at_price }" x-text="money.format(item.price)"></p>
                        <s x-show="!!item.properties._compare_at_price" class="cart-item__compare-at-price {{ settings.classes_product_item_price }}" x-text="money.format(item.properties._compare_at_price).split('.')[0]"></s>
                      </div>
                    </template>
                  {% endif %}
                </div>

              </div>

            </div>
          </li>
        </template>
      </ul>
    </section>

    <!-- Empty Cart -->
    <section aria-labelledby="summary-heading" class="mt-10" x-show="$store.cart.item_count == 0">
      <h2 id="summary-heading">{{ 'sections.cart.empty' | t }}</h2>
      <div class="mt-10">
        {% render 'button' content: 'Continue Shopping', style: 'primary', attributes: '@click="$event.preventDefault(); $store.sliderCart.toggle();"', class: 'w-full justify-center' %}
      </div>
    </section>
  </form>
  </template>

  <template x-if="$store.cart.items.length == 0">
    <div class="cart-empty">
      {{ settings.empty_state }}
    </div>
  </template>

</div>
