<style>
  .giftcard_modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 9; /* Sit on top */
    padding-top: 100px; /* Location of the box */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0,0,0); /* Fallback color */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
  }
  
  /* giftcard_modal Content */
  .giftcard_modal-content {
    background-color: #fefefe;
    margin: auto;
    padding: 60px;
    border: 1px solid #888;
    width: 100%;
    max-width: 677px;
    text-align: center;
    position:relative;
  }
  .giftcard_modal-content p#appresponse_continue{
    font-size: 13px;
    letter-spacing: 1.3px;
    background: #000;
    color: #fff;
    width: 250px;
    height: 50px;
    text-align: center;       
    line-height: 50px;   
    margin: 30px auto 0;    
  }
  .giftcard_modal-content p#appresponse_sorry{
    font-family: "din-condensed",'Din Condensed', sans-serif;
    font-size: 30px;
    color: #381300;
    line-height: 1.3em;
  }
  
  /* The giftcard_close Button */
  .giftcard_close {
    float: right;
    font-size: 28px;
    font-weight: bold;
    position: absolute;
    right: 10px;
    top: 10px;
    color: #000;
  }
  
  .giftcard_close:hover,
  .giftcard_close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
  }
  .giftcard_modal-content p#appresponse_continue button{
    display: block;
      width: 100%;
      height: 100%;
  }
  
  @media only screen and (max-width: 992px) {
  .giftcard_modal-content{
    width:75%;
  }
  }
  
  @media only screen and (max-width: 480px) {
  .giftcard_modal-content{
    padding:30px;
  }
  }
   
  </style>
  
  <!-- The giftcard_modal -->
  <div id="mygiftcard_modal" class="giftcard_modal">
  
    <!-- giftcard_modal content -->
    <div class="giftcard_modal-content">
      <span class="giftcard_close">&times;</span>
      <p id="appresponse_sorry">Sorry!</p>
    <p id="appresponse"></p>
    <p id="appresponse_continue">
    <button class="popupContinueButton" name="button" >CONTINUE</button> 
    </p>
    </div>
  
  </div>
  
  <script>
  // Get the giftcard_modal
  var giftcard_modal = document.getElementById("mygiftcard_modal");
  
  // Get the button that opens the giftcard_modal
  //var btn = document.getElementById("giftcard_myBtn");
  
  // Get the <span> element that giftcard_closes the giftcard_modal
  var span = document.getElementsByClassName("giftcard_close")[0];
  var popupContinueButton =  document.getElementsByClassName("popupContinueButton")[0];
  
  // When the user clicks the button, open the giftcard_modal 
  //btn.onclick = function() {
    //giftcard_modal.style.display = "block";
  //}
  
  // When the user clicks on <span> (x), giftcard_close the giftcard_modal
  span.onclick = function() {
    giftcard_modal.style.display = "none";
  }
  
  popupContinueButton.onclick = function() {
    giftcard_modal.style.display = "none";
  }
  
  // When the user clicks anywhere outside of the giftcard_modal, giftcard_close it
  window.onclick = function(event) {
    if (event.target == giftcard_modal) {
      giftcard_modal.style.display = "none";
    }
  }
  </script>