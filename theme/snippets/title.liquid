{% if settings.title_text != blank or title_text != blank or settings.title_liquid != blank or settings.title_attr_x_text != blank %}
{% assign element = title_element | default: settings.title_element | default: 'p' %}
<{{ element }} {% render 'attributes' _settings:settings, prefix:'title_attr' %} {{ title_attributes }} class="{% render 'class-settings' prefix:'title_class' settings:settings %} {{ title_classes }} my-0" style="{% render 'style-settings' prefix:'title_style' settings:settings %}">{{ title_text | default: settings.title_text }}{{  settings.title_liquid }}</{{ element}}>
{% endif %}
