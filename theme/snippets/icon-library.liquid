<div class="flex items-center justify-center">
  <div 
    class="w-full grid grid-cols-4 gap-10" 
    x-data="{icons: []}"
    x-init="icons = Array(...document.querySelectorAll('svg.icon-library symbol')).map(sym => sym.id.replace('icon-def-', ''))"
  >
    <template x-for="icon in icons">
      <div 
        class="flex flex-col items-center py-4 cursor-pointer hover:bg-gray-100" 
        @click="navigator.clipboard.writeText(icon)"
        x-init="
          $el.classList.remove('icon-icon_name')
          $el.classList.add('icon-' + icon)
          $el.querySelector('use').setAttribute('xlink:href', '#icon-def-' + icon)
        "
      >
        {% render 'icon', icon: 'ICON_NAME' width:24 height:24 strokeWidth:0 fill:'currentColor' %}
        <span class="block" x-text="icon">ICON_NAME</span>
      </div>
    </template>
  </div>
</div>
