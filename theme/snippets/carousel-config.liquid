  x-data="{
    swiper: null,
    {% if namespace != blank %}
    section: Alpine.reactive(window.sections.find(s=>s.id=='{{ namespace }}').data),
    {% endif %}
    setActiveSlides: (swiper) => {
      if (!swiper) return false
      const visibleStart = swiper.activeIndex;
      const visibleEnd = visibleStart + swiper.passedParams.breakpoints[swiper.currentBreakpoint].slidesPerView

      swiper.slides.forEach((slide, i) => {
        let visible = false
        if (i >= visibleStart && i < visibleEnd) visible = true

        {% if settings.outer_slides %}
        slide.setAttribute('carousel-visible', visible)
        {% endif%}
        slide.setAttribute('slide-visible', visible)
      })
    },
    config: {
      {% if settings.autoplay == true %}autoplay: {
        'delay': {{ settings.autoplay_slide_duration }}000
      },{% endif %}
      loop: {{ settings.loop | default: false }},
      
      on: {
      
        afterInit: (swiper) => { setTimeout(() => $data.setActiveSlides(swiper), 1000) },
        slideChange: (swiper) => { $data.setActiveSlides(swiper) },
      
        sliderFirstMove: (swiper) => {
          swiper.el.querySelectorAll('img[loading=lazy]').forEach(img=>img.setAttribute('loading','eager'))
        }

      },
      breakpoints: {
        '1': {
          simulateTouch:true,
          cssMode:{% if settings.outer_slides %}false{% else %}true{% endif %},
          {% if settings.center == true %}'centeredSlides':true,{% endif %}
          {% if settings.spacebetween_mobile %}'spaceBetween': {{ settings.spacebetween_mobile | default: 0 }},{% endif %}
          {% if settings.slides_per_view_mobile %}
            'slidesPerView': {{ settings.slides_per_view_mobile | default: 1.5 }},
            'slidesPerGroup': {{ settings.slides_per_group_mobile | default: 1.5 }}
        {% endif %}
        },
        '1025': {
          cssMode:{% if settings.outer_slides %}false{% else %}true{% endif %},
          mousewheel:true,
          {% if settings.center == true %}'centeredSlides':true,{% endif %}
          'spaceBetween': {% if settings.spacebetween %}{{settings.spacebetween}}{% else %}0{% endif %},
          'slidesPerView': {% if settings.slides_per_view %}{{ settings.slides_per_view }}{% else %}1{% endif %},
          'slidesPerGroup': {% if settings.slides_per_view %}{{ settings.slides_per_group }}{% else %}1{% endif %}
        }
      }
    } 
  }"
  x-init="() => {

    {% if namespace != blank %}
    
    window.sections.find(s=>s.id=='{{ namespace }}').data = Alpine.reactive(window.sections.find(s=>s.id=='{{ namespace }}').data)

    {% endif %}
    
    $nextTick(() => {
      {% unless section.settings.split_into_tabs %}
        $data.swiper = Carousels.create($el.querySelector('.swiper'), $data.config)
      {% endunless %}
      setTimeout(function(){
        {% if section.settings.split_into_tabs %}
          $data.swiper = Carousels.create($el.querySelector('.swiper'), $data.config)
        {% endif %}
        // removing mobile slide on desktop and vice versa
        let autoplayEnabled = false
        autoplayEnabled = $el.querySelector('.swiper').swiper?.autoplay?.running
        window.innerWidth >= 1024 ?  $el.querySelector('.swiper').swiper?.removeSlide([...$el.querySelectorAll(`.swiper .swiper-wrapper .swiper-slide .lg\\:hidden`)].map(elm => !isNaN(elm.parentElement.getAttribute('data-swiper-slide-index')) ? parseInt(elm.parentElement.getAttribute('data-swiper-slide-index')) : null)) : $el.querySelector('.swiper').swiper?.removeSlide([...$el.querySelectorAll(`.swiper .swiper-wrapper .swiper-slide .max-lg\\:hidden`)].map(elm => !isNaN(elm.parentElement.getAttribute('data-swiper-slide-index')) ? parseInt(elm.parentElement.getAttribute('data-swiper-slide-index')) : null));
        //going to the first slide to avoid slide jumping bug
        $el.querySelector('.swiper').swiper?.slideTo(0, 0, false);
        if(autoplayEnabled){
          $el.querySelector('.swiper').swiper?.autoplay.start();
        }
      }, 1);
    })

  }"
