<section class="wishlist" x-data>
  <header class="wishlist__header flex justify-between mb-4">
    <div>
      <h2 class="wishlist__title my-0">Wishlist</h2>
        
      <div class="wishlist__item-count flex items-center gap-1" x-show="$store.wishlist.lists[0].items.length > 0" x-cloak>
        {% render 'icon' icon:'heart' class:'active' width:20 height:20 strokeWidth:2 stroke:'#E36662' fill:'#E36662' %}
        <span x-text="$store.wishlist.lists[0].items.length + ' ' + ($store.wishlist.lists[0].items.length > 1 ? 'items' : 'item')"></span>
      </div>
    </div>

    <button class="wishlist__atc wishlist__atc--desktop button button--primary" onclick="Cart.add(wishlist.lists[0].items.filter(i=>i.variant.available&&!i.removed).map(i=>{
        return {
          id:i.variant.id,
          properties:{   
            '_source': 'wishlist',
            '_compare_at_price': i.variant.compare_at_price,
            '_upc': i.variant.barcode
          }
        }
      }));
      wishlist.lists[0].items = wishlist.lists[0].items.filter((i,index)=>!i.variant.available); Wishlist.store()" x-show="$store.wishlist.lists[0].items.length > 0" x-cloak>
      {{ 'wishlist.add_all' | t }}
    </button> 
  </header>

  <div x-show="$store.wishlist.lists[0].items.length == 0" x-cloak>
    <p class="wishlist__message">
      {{ 'wishlist.empty' | t }}
      <span class="wishlist__message--highlight">
        {% capture heart %}
          {% render 'icon' icon:'heart' class:'inline' width:16 height:16 strokeWidth:2 stroke:'#E36662' %}
        {% endcapture %}
        {{ 'wishlist.prompt' | t | replace: '&hearts;', heart }}
      </span>
    </p>
    <a href="/" class="button button--primary">Start Shopping</a>
  </div>

  <div class="wishlist__items">
    <template x-for="(item, index) in $store.wishlist.lists[0].items" hidden>

      <article :class="`wishlist-item ${item.removed?`wishlist-item--removed`:``} pt-5 flex justify-between`">

        <div class="flex gap-4">
          {% render 'image' 
            attributes: ':src="image.format(item.product.featured_image,{width:240})"' 
              class: 'h-32 w-32 object-contain object-center opacity-100'
              sizes: '(min-width: 768px) 20vw, 50vw'
          %}
          <a :href="`/products/${item.product.handle}`">
            <p class="wishlist-item__title" x-text="item.product.title.split(' - ')[0]"></p>
            <p class="wishlist-item__type" x-text="item.product.type"></p>
            <p class="wishlist-item__variant" x-text="item.variant.title"></p>
            <p class="wishlist-item__price" x-text="money.format(item.variant.price)"></p>
          </a>
        </div>

        <template x-if="!item.removed">
          <div class="wishlist-item__buttons flex flex-col justify-between">

            <button class="wishlist-item__remove" @click="wishlist.lists[0].items[index].removed = true; Wishlist.store()">
              {% render 'icon' icon:'x' strokeWidth:2 %}
            </button>

            <template x-if="item.variant.available">
              <button class="wishlist-item__atc" @click="
              Cart.add(item.variant.id); 
              Modal.open('slider-cart'); 
              Wishlist.items.remove(index);">
                {% render 'icon' icon:'bag' strokeWidth:2 %}
                Add to Bag
              </button>
            </template>

            <template x-if="!item.variant.available">
              <p>{{ 'wishlist.item_unavailable' | t }}</p>
            </template>
          </div>
        </template>

        <template x-if="item.removed">
          <div class="wishlist-item__buttons flex flex-col justify-center items-end">
            <p>{{ 'wishlist.removed' | t }}</p>
            <button class="button button-teritary" @click="delete wishlist.lists[0].items[index].removed; Wishlist.store()">
              {{ 'wishlist.undo_remove' | t }}
            </button>
          </template>
        </div>
      </article>

    </template>
  </div>

  <footer class="wishlist__footer">
    <button class="wishlist__atc button button--primary" onclick="Cart.add(wishlist.lists[0].items.filter(i=>i.variant.available&&!i.removed).map(i=>{
        return {
          id:i.variant.id,
          properties:{   
            '_source': 'wishlist',
            '_compare_at_price': i.variant.compare_at_price,
            '_upc': i.variant.barcode
          }
        }
      }));
      wishlist.lists[0].items = wishlist.lists[0].items.filter((i,index)=>!i.variant.available); Wishlist.store()" x-show="$store.wishlist.lists[0].items.length > 0" x-cloak>
      {{ 'wishlist.add_all' | t }}
    </button>
  </footer>
</section>
