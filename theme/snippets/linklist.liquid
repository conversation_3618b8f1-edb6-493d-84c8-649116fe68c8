{% liquid

    assign linklist = linklist | default: linklists[_settings.link_list]

    assign depth = depth | default: 0
    assign depth = depth | plus: 1

    if linklist.links.size > 0
      
      assign class = 'link-list link-list--' | append: linklist.handle
      render 'element' tag:'ul' open:true  _settings:_settings prefix:'linklist' class:class attributes:attributes

      for link in linklist.links
        assign handle = link.title | handle
        assign item_classes = 'items-center link-list__item link-list__item--depth-' | append: depth | append: ' link-list__item--' | append: handle
        for prop in link
          if prop[1] == true
            assign item_classes = item_classes | append: 'link-list__item--' | append: prop[0]
          endif 
        endfor

        render 'element' tag:'li' open:true _settings:_settings prefix:'item' class:item_classes

          if _settings.linklist_class_format contains 'collapsable' and link.links.size > 0
            render 'element' tag:'details' open:true _settings:_settings prefix:'details' class:item_classes
            render 'element' tag:'summary' open:true _settings:_settings prefix:'summary' class:'flex items-center'          
          endif

          assign atts = 'href="' | append:link.url | append: '"'
          render 'element' tag:'a' open:false _settings:_settings prefix:'link' class:'link-list__link' content:link.title attributes:atts link_url:link.url

          if _settings.linklist_class_format contains 'collapsable' and link.links.size > 0
            render 'icon' icon:'arrow-right'
            echo '</summary><div>'
            if _settings.linklist_class_format contains 'panel'
              echo '<button class="link-list__back" role="button" onclick="this.parentNode.parentNode.open=false">'
              render 'icon' icon:'arrow-left'
              echo link.title
              echo '</button>'
            endif
          endif

          render 'linklist' _settings:_settings linklist:link depth:depth

          if _settings.linklist_class_format contains 'collapsable' and link.links.size > 0
            echo '</div></details>'
          endif 

        echo '</li>'

      endfor

      echo '</ul>'

    endif
 
%}
