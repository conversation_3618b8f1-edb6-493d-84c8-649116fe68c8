<article class="account-block account-block--{{ type }} {% render 'class-settings' prefix:'account_block_class' settings:settings %}">

  {% unless type == 'content-item' %}

  	{% if settings.text_item_1_text != blank or text_item_1_text != blank or text_item_1_attributes %}
      
      <!-- text stack container with flex settings (padding, gap, direction) defaults to container settings, section settings -->
      <div class="content-item__text-stack flex {% render 'class-settings' prefix:'text_stack_class' settings:settings %} {{ text_stack_classes }}">
        
        <!-- text items with text content and format settings -->
        
        {% if settings.text_item_1_text != blank or text_item_1_text != blank or text_item_1_attributes %}
        {% assign element = text_item_1_element | default: settings.text_item_1_element | default: 'p' %}
        <{{ element }} class="{% render 'class-settings' prefix:'text_item_1_class' settings:settings %} {{ text_item_1_classes }}" {{ text_item_1_attributes }}>{{text_item_1_text | default: settings.text_item_1_text}}</{{ element}}>
        {% endif %}
        
        {% if settings.text_item_2_text != blank or text_item_2_text != blank or text_item_2_attributes %}
        {% assign element = text_item_2_element | default: settings.text_item_2_element | default: 'p' %}
        <{{ element }} class="{% render 'class-settings' prefix:'text_item_2_class' settings:settings %} {{ text_item_2_classes }}" {{ text_item_2_attributes }}>{{text_item_2_text | default: settings.text_item_2_text}}</{{ element}}>
        {% endif %}
        
        {% if settings.text_item_3_text != blank or text_item_3_text != blank or text_item_3_attributes %}
        {% assign element = text_item_3_element | default: settings.text_item_3_element | default: 'p' %}
        <{{ element }} class="{% render 'class-settings' prefix:'text_item_3_class' settings:settings %} {{ text_item_3_classes }}" {{ text_item_3_attributes }}>{{text_item_3_text | default: settings.text_item_3_text}}</{{ element}}>
        {% endif %}
        
      </div>

    {% endif %}

  {% endunless %}

