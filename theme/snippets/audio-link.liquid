<div 
<div class="enunciation">
  <div>
    {% if enunciation_data.translation != blank %}
    <p class="enunciation__translation">Translation: {{ enunciation_data.translation }}</p>
    {% endif %}
    {% if enunciation_data.audio_url != blank %}
    <button class="enunciation__button" onclick="this.querySelector('audio').play()">
      <span class="enunciation__pronunciation">({{ enunciation_data.pronunciation }})</span>
      <span class="enunciation__icon">
        {% render 'icon' icon: 'speaker' width:18 %}
      </span>
      <audio class="enunciation__audio">
        <source src="{{ enunciation_data.audio_url }}" type="audio/mpeg">
      </audio>
    </button>
    {%- else -%}
      <span class="enunciation__pronunciation">{{ enunciation_data.pronunciation }}</span>
    {% endif %}
  </div>
  {% if enunciation_data.description != blank %}
  <p class="enunciation__description">{{ enunciation_data.description }}</p>
  {%- endif -%}
</div>      