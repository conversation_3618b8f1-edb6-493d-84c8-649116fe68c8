{% liquid 

  if blocks[offset].type == 'overlay'

    echo '<article class="absolute overlay-item '
      render 'class-settings' settings:blocks[offset].settings, prefix:'overlay_class'
      echo '" style="'
      render 'overlay-variables' settings:blocks[offset].settings
    echo '">'
    
    assign next = offset | plus: 1
    assign item = blocks[next]

    case item.type 

      when 'content-item'

        render 'content-item' settings:item.settings

    endcase

    echo '</article>'

  endif

%}
