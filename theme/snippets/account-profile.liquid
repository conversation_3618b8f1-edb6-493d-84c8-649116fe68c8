<section x-data class="account-profiles">

	<template x-if="$store.customer.edit !== false">
		<form class="account-profile-editor" x-data="{
			profile: $store.customer.profiles[$store.customer.edit || 0],
			date: '',
			isValid: true,
			validateAndFormatDate() {
					// If the date is empty, set isValid to true
					if (this.date.trim() === '') {
							this.isValid = true;
							{% if settings.enable_logs %}console.log('Date is empty, considered valid');{% endif %}
							return;
					}

					// Regular expression to match date format with at least two digits for day and month
					const regex = /^(0[1-9]|1[0-2])\/(0[1-9]|[12][0-9]|3[01])\/(19|20)\d{2}$/;


					// Check if the date matches the regex format
					if (!regex.test(this.date)) {
							this.isValid = false;
							{% if settings.enable_logs %}console.log('Invalid format');{% endif %}
							return;
					} 

					// Split the date into components
					const [month, day, year] = this.date.split(/[/.-]/).map(Number);

					// Create a new Date object
					const date = new Date(year, month - 1, day);
					{% if settings.enable_logs %}console.log('date', date){% endif %}
					// Check if the date is valid
					if (date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day) {
							this.isValid = true;
					} else {
							this.isValid = false;
							{% if settings.enable_logs %}console.log('Invalid date');{% endif %}
					}

					// Optionally clear the input or provide feedback
					// if (!this.isValid) {
					//     this.date = ''; // Uncomment if you want to clear the input field
					// }

					{% if settings.enable_logs %}console.log('isValid ', this.isValid);{% endif %}
			}
		}" x-init="$watch('$store.customer.birthday_date', value => { date = value; validateAndFormatDate({ target: { value } }); })">

			{% liquid 
				render 'account-profile-fields' fields:fields placement:'profile'
			%}

			<footer class="flex gap-8">
				<button class="button button--simple button--w-icon" onclick="Customer.profile.close()">
					{% render 'icon' icon:'chevron-left' height:'16' width:'16' strokeWidth:2 %} Cancel
				</button>
				<button :disabled="!isValid" class="button button--primary" onclick="Customer.profile.save(Util.form.values(Util.select('.account-profile-editor')[0]),customer.edit); Customer.profile.close(); return false;">Save Profile</button>
			</footer>

		</form>
	</template>	

	<template x-if="$store.customer.edit === false">
		
		<div>
			<div class="account-profiles__list-container">

			<div class="account-profiles__list">

		  <template x-for="(profile, index) in $store.customer.profiles">
				<div class="account-profile__container">
					<h6 class="account-profile__title" x-text="`${(index == 0 ? 'Primary' : 'Secondary')} Profile`"></h6>
				
					<article class="account-profile border" :class="`account-profile--${(index == 0 ? 'primary' : 'secondary')}`">

						<header class="account-profile__header flex">
							<h4 class="account-profile__name" x-text="`${profile.first_name || ``} ${profile.last_name || ``}`"></h4>
							<div class="account-profile__actions">
								<button type="button" aria-label="Edit" class="button button--link account-profile__edit" @click="Customer.profile.edit(index)">Edit</button>

								<template x-if="index > 0">
									<button type="button" aria-label="Edit" class="button button--link account-profile__edit" @click="Customer.profile.remove(index)">Delete</button>
								</template>
							</div>

						</header>
						
						<template x-if="index==0">
							<address class="account-profile__contact">
								<div class="account-profile__label">Email Address</div> 
								<div class="account-profile__value">{{ customer.email }}</div> 

								<div class="account-profile__label">Phone Number</div>
								{% if customer.phone != blank %}
									<div class="account-profile__value">{{ customer.phone }}</div>
								{% else %}
									{% for field in fields %}
										{% if field.key == 'phone' %}
											<template x-if="!!profile.{{ field.key }}">
												<div class="account-profile__value" x-text="profile.{{ field.key }}"></div>
											</template>
										{% endif %}
									{% endfor %}
								{% endif %}
							</address>
						</template>
						

						<footer class="account-profile__fields flex">

							{% assign summary_fields = fields | where:'profile_summary_inclusion' %}
							{% for field in summary_fields %}

							{% if field.inclusion_js != blank %}
							<template x-if="{{ field.inclusion_js }}">
							{% endif %}

							<div class="account-profile__field account-profile__field--{{ field.title | handle }}">
								<div class="account-profile__field-label">{{ field.title }}</div>
								<template x-if="!!profile.{{ field.key }}">
									<div class="account-profile__field-value" x-text="profile.{{ field.key }}"></div> 
								</template>
							</div> 

							{% if field.inclusion_js != blank %}
							</template>
							{% endif %}
							
							{% endfor %}

						</footer>

					</article>
				</div>
		  </template>

			</div> 

			

				<template x-if="$store.customer.profiles.length <= {{ settings.limit }}">
					<div class="account-profiles__actions">
						<h5 class="account-profile__title">{{ 'customer.profiles.secondary.cta_message' | t | default: 'Do you want another?' }}</h5>
						<button class="button button--light" onclick="Customer.profile.add()"><span class="button__text">Add Profile</span></button>
					</div>
  			</template>

		
			</div>
			

				<form class="account-profile-editor" x-data="{profile:$store.customer.profiles[$store.customer.edit||0]}">
					{% render 'account-profile-fields' fields:fields placement:'account' %}
				</form>

		</div>
	</template>
  
</section>

