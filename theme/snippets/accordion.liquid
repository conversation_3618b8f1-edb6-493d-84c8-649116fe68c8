<details type="accordion" group="{{ section_id }}"
  class="accordion group w-full {% if settings.collapse_padding %}collapse-padding{% endif %} {% render 'class-settings' prefix:'accordion_detail_class' settings:settings %}"
  style="{% render 'style-settings' prefix:'accordion_detail_style' settings:settings %}"
  {% render 'attributes' _settings:settings, prefix:'accordion_detail_attr' %}
>
  <summary class="accordion-title {% render 'class-settings' prefix:'accordion_summary_class' settings:settings %}"
    style="{% render 'style-settings' prefix:'accordion_summary_style' settings:settings %}">
    <span>{{ settings.accordion_title }}</span>
    <i class="flex group-open:hidden accordion-control">
      {% render 'icon', icon: 'plus' width:24 height:24 %}
    </i>
    <i class="hidden group-open:flex accordion-control">
      {% render 'icon', icon: 'minus' width:24 height:24 %}
    </i>
  </summary>
  <div class="x-accordion-panel rte {% render 'class-settings' prefix:'accordion_panel_class' settings:settings %}" 
    style="{% render 'style-settings' prefix:'accordion_panel_style' settings:settings %}">
    {{ settings.accordion_richtext }}
    {{ settings.accordion_liquid }}
{% unless open == true %}
  </div>
</details>
{% endunless %}