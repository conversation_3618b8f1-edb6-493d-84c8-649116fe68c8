<script>
  {%- assign sms_legal_header = settings.kaenon_sms_legal_consent_copy1 -%}
  {%- assign sms_legal_copy = settings.kaenon_sms_legal_consent_copy2 -%}
  
    window.exponeaConsents = [{{ settings.kaenon_exponea_consents }}];
      window.exponeaSmsConsents = [{{ settings.kaenon_sms_exponea_consents }}];
  
  
  var d1 = document.querySelector(".section--shipping-address");
  if (d1) {
    var dc1 = d1.querySelector('.section__content');
    var dc2 = dc1.querySelector('.fieldset');
     var dc3 = dc2.querySelector('.address-fields');
     
    dc3.insertAdjacentHTML('afterend', `<div class="field sms-checkbox"><div class="checkbox-wrapper"><div class="checkbox__input"> <input size="30" type="hidden" name="checkout[sms-checkbox]"> <input name="checkout[sms-checkbox]" type="hidden" value="0"><input class="input-checkbox" data-backup="sms-checkbox" type="checkbox" value="1" name="checkout[sms-checkbox]" id="checkout_sms-checkbox"></div> <label class="checkbox__label" for="checkout_sms-checkbox">
  
        {%- if sms_legal_header != blank -%}
            <b>{{ sms_legal_header }}</b>
        {%- endif -%}
        {%- if sms_legal_copy != blank -%}
            <p>{{ sms_legal_copy }}</p>
        {%- endif -%}
        </label></div></div>`);
  
    // function formatPhoneNumber(value) {
    //       var phoneNumber = value.replace(/[^\d]/g, "")
    //       var phoneNumberLength = phoneNumber.length
  
    //       if (phoneNumberLength < 4) return phoneNumber
  
    //       if (phoneNumberLength < 7) return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3)}`;
  
    //       return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
    //   }
  
    // $('data-phone-formatter').on('keydown, keyup', function() {
    //   $(this).val(formatPhoneNumber($(this).val()))
    // })
  
    var x_phone = document.getElementById("checkout_shipping_address_phone");
    x_phone.setAttribute("maxlength", 14);
    x_phone.addEventListener("focus", myFocusFunction, true);
  
    var sms_checkbox = document.querySelector(".sms-checkbox");
    sms_checkbox.style.display = "none";
  
    function myFocusFunction() {
      sms_checkbox.style.display = "block";
    }
  
    var checkoutPhoneInput = document.getElementById("checkout_shipping_address_phone");
    if (checkoutPhoneInput && checkoutPhoneInput.value) {
      sms_checkbox.style.display = "block";
    }
  
    $('form.edit_checkout').on('submit', function() {
      // SMS Signup Desktop Example
      var checkBox = document.getElementById("checkout_sms-checkbox");
      if (checkBox.checked == true){
        var checkoutPhoneInputNumberValue = document.getElementById("checkout_shipping_address_phone").value;
        var formattedPhoneNumber = formatPhone(checkoutPhoneInputNumberValue);
  
        // Set a SOFT_ID registered as the customers phone number that is MD5 Hashed.
        exponea.identify({
          phone_id: formattedPhoneNumber
        });
        // Update the phone number in the customers profile
        exponea.update({
          phone: formattedPhoneNumber
        });
  
        // Track the double opt in event to trigger doi scenario
        exponea.track('double_opt_in', { 
          action: 'new',
          phone: formattedPhoneNumber,
          source: 'Checkout SMS Signup',
          consent_list: window.exponeaSmsConsents
        });
        
        // Stack Adapt Tracking Pixels
        if (window.stackAdaptPixel) {
          saq('conv','gp7I1Wkr2Z6K2EcNMTWxDg')
          saq('lal','9ZufSeT1JVqr15CzgAtvRB')
        }
  
        // Function to format the number to 001 format instead of +1
        function formatPhone(phone) {
          phone = phone.split(" ").join("");
          phone = phone.split("(").join("");
          phone = phone.split(")").join("");
          phone = phone.split("-").join("");
          if (phone.length == 10) {
            return "001" + phone;
          } else if (phone.startsWith("+1")) {
            return phone.replace("+1", "001");
          } else if (phone.startsWith("001")) {
            return phone;
          } else if (phone.startsWith("+")) {
            return phone.replace("+", "00");
          } else return null;
        }
      }
    });
  }
  </script>