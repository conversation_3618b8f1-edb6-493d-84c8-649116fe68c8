{% if settings.brand_name contains 'kaenon' %}
  <link rel="stylesheet" href="https://use.typekit.net/upf4czc.css">
{% endif %}


{% style %}

  {% if settings.brand_name contains 'olukai' %}
    @font-face {
      font-family: 'Olukai-Bold';
      src: url("{{ 'Olukai_Set_v1.0-Bold.woff2' | asset_url }}") format('woff2'), url("{{ 'Olukai_Set_v1.0-Bold.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    {% assign olukai_regular_2 = 'Olukai_Set_v1.0-Regular.woff2' | asset_url %}
    {% assign olukai_regular = 'Olukai_Set_v1.0-Regular.woff' | asset_url %}
    {% if olukai_regular != blank or olukai_regular_2 != blank %}
    @font-face {
      font-family: 'Olukai-Regular';
      src: url("{{ 'Olukai_Set_v1.0-Regular.woff2' | asset_url }}") format('woff2'), url("{{ 'Olukai_Set_v1.0-Regular.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }
    {% endif %}

    @font-face {
      font-family: 'GTA-Regular';
      src: url("{{ 'GT-America-Standard-Regular.woff2' | asset_url }}") format('woff2'), url("{{ 'GT-America-Standard-Regular.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'GTA-Medium';
      src: url("{{ 'GT-America-Standard-Medium.woff2' | asset_url }}") format('woff2'), url("{{ 'GT-America-Standard-Medium.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'GTA-Bold';
      src: url("{{ 'GT-America-Standard-Bold.woff2' | asset_url }}") format('woff2'), url("{{ 'GT-America-Standard-Bold.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'GTA-Compressed-Bold';
      src: url("{{ 'GT-America-Compressed-Bold.woff2' | asset_url }}") format('woff2'), url("{{ 'GT-America-Compressed-Bold.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'GTA-Condensed-Bold';
      src: url("{{ 'GT-America-Condensed-Bold.woff2' | asset_url }}") format('woff2'), url("{{ 'GT-America-Condensed-Bold.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'Moret-Regular';
      src: url("{{ 'moret-1.woff2' | asset_url }}") format('woff2'), url("{{ 'moret-1.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: 400;
    }

    @font-face {
      font-family: 'Moret-Medium';
      src: url("{{ 'moret-2.woff2' | asset_url }}") format('woff2'), url("{{ 'moret-2.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: 700;
    }

    @font-face {
      font-family: 'Moret-Bold';
      src: url("{{ 'moret-3.woff2' | asset_url }}") format('woff2'), url("{{ 'moret-3.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: 800;
    }

    :root {
      --font-body-family: 'GTA-Regular', sans-serif;
      --font-heading-family: 'GTA-Medium', sans-serif;
      --font-subheading-family: 'GTA-Regular', sans-serif;

      --font-regular: 'GTA-Regular', sans-serif;
      --font-medium: 'GTA-Medium', sans-serif;
      --font-bold: 'GTA-Medium', sans-serif;
      --font-olukai-bold: 'Olukai-Bold', sans-serif;
      --font-olukai-regular: 'Olukai-Regular', sans-serif;
    }

  {% elsif settings.brand_name contains 'roark' %}

    @font-face {
      font-family: 'Adobe Caslon Pro Bold';
      src: url("{{ 'adobe-caslon-pro-bold.woff2' | asset_url }}") format('woff2'), url("{{ 'adobe-caslon-pro-bold.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'Adobe Caslon Pro Regular';
      src: url("{{ 'adobe-caslon-pro-regular.woff2' | asset_url }}") format('woff2'), url("{{ 'adobe-caslon-pro-regular.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'Roboto Bold';
      src: url("{{ 'roboto-bold.woff2' | asset_url }}") format('woff2'), url("{{ 'roboto-bold.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'Roboto Medium';
      src: url("{{ 'roboto-medium.woff2' | asset_url }}") format('woff2'), url("{{ 'roboto-medium.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'Roboto Regular';
      src: url("{{ 'roboto-regular.woff2' | asset_url }}") format('woff2'), url("{{ 'roboto-regular.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'Roboto Condensed Bold';
      src: url("{{ 'robotocondensed-bold.woff2' | asset_url }}") format('woff2'), url("{{ 'robotocondensed-bold.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'Roboto Condensed Regular';
      src: url("{{ 'robotocondensed-regular.woff2' | asset_url }}") format('woff2'), url("{{ 'robotocondensed-regular.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'Roboto Condensed Light';
      src: url("{{ 'robotocondensed-light.woff2' | asset_url }}") format('woff2'), url("{{ 'robotocondensed-light.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'DIN Condensed Bold';
      src: url("{{ 'DINCondensed-Bold.woff2' | asset_url }}") format('woffs'), url("{{ 'DINCondensed-Bold.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: "DIN Next Medium";
      src: url("{{ 'din_next_n5' | font_url }}") format("woff2"), url("{{ 'din_next_n5' | font_url: 'woff' }}") format('woff');
    }

    :root {
      --font-body-family: 'Roboto Regular', sans-serif;
      --font-heading-family: 'Adobe Caslon Pro Bold', serif;
      --font-subheading-family: 'DIN Condensed Bold', sans-serif;

      --font-regular: 'Roboto Regular', sans-serif;
      --font-medium: 'Roboto Medium', sans-serif;
      --font-bold: 'Roboto Bold', sans-serif;

      --font-heading-regular: 'Adobe Caslon Pro Regular', sans-serif;
      --font-heading-bold: 'Adobe Caslon Pro Bold', serif;
    }

  {% elsif settings.brand_name contains 'melin' %}

    @font-face {
      font-family: 'Avenir-Black';
      src: url("{{ 'Avenir-Black.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'Avenir-Book';
      src: url("{{ 'Avenir-Book.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: 400;
    }

    @font-face {
      font-family: 'Avenir-Heavy';
      src: url("{{ 'Avenir-Heavy.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }


    @font-face {
      font-family: 'Avenir-Light';
      src: url("{{ 'Avenir-Light.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: 400;
    }

    @font-face {
      font-family: 'Avenir-Medium';
      src: url("{{ 'Avenir-Medium.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: 400;
    }

    :root {
      --font-body-family: 'Avenir-Book', sans-serif;
      --font-heading-family: 'Avenir-Black', serif;
      --font-subheading-family: 'Avenir-Medium', sans-serif;

      --font-regular: 'Avenir-Book', sans-serif;
      --font-medium: 'Avenir-Medium', sans-serif;
      --font-bold: 'Avenir-Black', sans-serif;
    }

  {% elsif settings.brand_name contains 'kaenon' %}

    @font-face {
      font-family: 'AGaramondPro-Bold';
      src: url("{{ 'AGaramondPro-Bold.woff2' | asset_url }}") format('woff2'), url("{{ 'AGaramondPro-Bold.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'interstate-regularcondensed';
      src: url("{{ 'interstate-regularcondensed-webfont.woff2' | asset_url }}") format('woff2'), url("{{ 'interstate-regularcondensed-webfont.woff' | asset_url }}") format('woff');
      font-style: normal;
      font-weight: normal;
    }

    :root {
      --font-body-family: 'interstate', sans-serif;
      --font-heading-family: 'AGaramondPro-Bold', serif;
      --font-subheading-family: 'interstate', sans-serif;

      --font-regular: 'interstate', sans-serif;
      --font-medium: 'interstate', sans-serif;
      --font-bold: 'interstate', sans-serif;
      --font-condensed: 'interstate-regularcondensed', sans-serif;
    }

  {% elsif settings.brand_name contains 'amble' %}

    @font-face {
      font-family: 'Helvetica-Heue-Bold';
      src: url("{{ 'helvetica-neue-bold.ttf' | asset_url }}") format('ttf');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'HamburgHand-Bold';
      src: url("{{ 'HamburgHand-Bold.otf' | asset_url }}") format('otf');
      font-style: normal;
      font-weight: normal;
    }

    @font-face {
      font-family: 'CarnabyStreet-Clean';
      src: url("{{ 'CarnabyStreet-Clean.otf' | asset_url }}") format('otf');
      font-style: normal;
      font-weight: normal;
    }

    :root {
      --font-body-family: 'Helvetica-Heue-Bold', sans-serif;
      --font-heading-family: 'CarnabyStreet-Clean', sans-serif;
      --font-subheading-family: 'HamburgHand-Bold', sans-serif;

      --font-regular: 'Helvetica-Heue-Bold', sans-serif;
      --font-medium: 'HamburgHand-Bold', sans-serif;
      --font-bold: 'CarnabyStreet-Clean', sans-serif;
 
    }


  {% else %}

    :root  {
      --font-body-family: Helvetica, sans-serif;
      --font-heading-family: Helvetica, sans-serif;
      --font-subheading-family: Helvetica, sans-serif;
    }

  {% endif %}
{% endstyle %}

<!--
pointer-events-auto
lg:pointer-events-auto
lg:flex-col-reverse{}
lg:self-end{}
-->
