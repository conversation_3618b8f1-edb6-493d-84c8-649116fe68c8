{% liquid 

  if settings.product_item_variant_selector
    assign variants = true
    assign form = true
  endif 
	
	if settings.products or products 
		for product in settings.products limit: settings.limit
			render 'product-item' item:item index:index  attributes:attributes product:product settings:settings _settings:_settings class:"self-stretch" variants:variants form:form  location:'grid'
		endfor
	endif
	
	if settings.product != blank
		render 'product-item' item:item index:index  attributes:attributes product:settings.product settings:settings _settings:_settings class:"self-stretch" variants:variants form:form location:'grid'
	endif

	if settings.collection != blank 
		assign collection = collections[settings.collection]
	endif

	if collection
		paginate collection.products by settings.limit
			for product in collection.products
				render 'product-item' item:item index:index  attributes:attributes product:product settings:settings _settings:_settings class:"self-stretch" variants:variants form:form location:'grid'
			endfor
		endpaginate
	endif
%}

