{% liquid

case position

	when 'left'
		assign classes = 'top-0 left-0 lg:w-1/3 overflow-auto h-[100vh] md:h-[80vh]'

	when 'right'
		assign classes = 'top-0 right-0 left-auto lg:w-1/3 overflow-auto h-[100vh] md:h-[80vh] transform translate-x-full open:translate-x-0 transition delay-75'

	when 'center'
		assign classes = 'left-1/2 top-1/2 max-w-screen-lg w-full overflow-auto h-[100vh] md:h-[80vh] -translate-x-1/2 -translate-y-1/2'

	when 'top'
		assign classes = 'top-0 right-0 left-0 w-screen overflow-auto'

	when 'bottom'
		assign classes = 'bottom-0 right-0 left-0 w-screen overflow-auto'

	when 'full'
		assign classes = 'inset-0 w-screen h-full overflow-auto'

	when 'mini'
		assign classes = 'left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[480px] max-w-full max-h-screen overflow-scroll'

	when 'mini-geo'
		assign classes = 'left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-[360px] max-h-screen overflow-hidden'

	when 'top-center'
		assign classes = 'top-0 left-1/2 max-w-max h-[100vh] transform -translate-x-1/2 w-screen overflow-auto'
	
endcase
%}

<dialog
	data-modal="{{ key | handle }}"
	class="modal modal--{{ key | handle }} modal--{{ position | handle }} fixed m-0 bg-white z-50 animate transition-transform ease-in-out duration-300 focus:outline-none focus:shadow-none {{ classes }} p-0 {% if close_button_position == 'over_images' %} bg-white {% else %}no-scrollbar bg-transparent{% endif %}"
	tabindex="0">
	<div class="modal__content {% if close_button_position == 'beside_images' %}modal--beside-popup relative my-[0px] lg:my-[0px] px-4 lg:px-0 mx-auto shadow-xl bg-transparent max-w-[800px] flex justify-between{% endif %}">
	  	{{ content }}
	  	{% if close_button_position == 'beside_images' %}
		<div class="flex justify-end sticky top-0 h-[48px] w-[48px] fixed  modal__close ml-[-48px] md:ml-0 mt-2.5" style="">
			<button class="block ml-auto p-lg product-essentials__modal-close text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-full text-sm p-1.5 ml-auto inline-flex items-center h-8 outline-1" onclick="Modal.close()">
				<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
					<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
				</svg>
			</button>
		</div>
		{% endif %}
	</div>
	{% if close_button_position == 'over_images' %}
	<div class="absolute top-0 right-0 modal__close">
	  <button class="block ml-auto p-lg" onclick="Modal.close()">
		{% render 'icon' icon: 'x' %}
	  </button>
	</div>
	{% endif %}
  </dialog>
