{%- capture payments_content -%}
  <div x-data>
    <template x-if="$store.payments && $store.payments.config[`image_${$store.payments.active}`] && !$store.payments.config[`link_${$store.payments.active}`]">
      <img class="mx-auto w-full" :src="$store.payments.config[`image_${$store.payments.active}`]">
    </template>
    <template x-if="$store.payments && $store.payments.config[`image_${$store.payments.active}`] && $store.payments.config[`link_${$store.payments.active}`]">
      <a :href="$store.payments.config[`link_${$store.payments.active}`]">
        <img class="mx-auto w-full" :src="$store.payments.config[`image_${$store.payments.active}`]">
      </a>
    </template>
  </div>
{%- endcapture -%}
{%- liquid 
  render 'modal' topic:'payment'  position:'center' content:payments_content
-%}
