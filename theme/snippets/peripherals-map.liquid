{% if settings.peripherals_map != blank %}
<script type="text/javascript">
  window.addEventListener('Customer:peripheral', e=>{
  
    const pkey = Object.entries(e.detail)[0][0];
    
    if(!peripherals_map[pkey]) return;

    const peripheral = customer.peripherals[pkey].data;

    if(peripheral){
    Object.entries(peripherals_map[pkey]).forEach(entry=>{

      {%- if settings.enable_logs %}
      console.log(
        entry[1],
        entry[0],
        peripheral,
        Util.dot(entry[0], peripheral)
      );
      {%- endif %}

      let val = Util.dot(entry[0], peripheral);
      if (val === null) return;

      if (typeof val=='string' && val.includes(', ')) val = val.split(', ');

      Util.nest(
        entry[1],
        customer,
        val
      );
    })
    }
  })
  window.peripherals_map = {{ settings.peripherals_map }};
</script>
{% endif %}
