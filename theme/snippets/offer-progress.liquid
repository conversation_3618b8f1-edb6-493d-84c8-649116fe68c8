<div class="offer" :class="{ 'offer--combinable': isCombinable }" x-data="{ 
  isCombinable: {{ settings.combinable }},
  getCombinableOffers() {
    return $store.offers
      .filter(o => o.combinable)
      .sort((a, b) => a.progress_denominator - b.progress_denominator);
  },
  getCurrentTierIndex() {
    return this.getCombinableOffers()
      .findLastIndex(offer => parseFloat(offer.progress) >= 100);
  },
  getProgress() {
    const offers = this.getCombinableOffers();
    if (!offers.length) return '0%';
    
    const lastCompletedIndex = this.getCurrentTierIndex();
    if (lastCompletedIndex === -1) return '0%';
    if (lastCompletedIndex === offers.length - 1) return '100%';
    
    const segmentWidth = 100 / (offers.length - 1);
    const progressPoint = (lastCompletedIndex * segmentWidth) + (segmentWidth * 0.66);
    
    return `${progressPoint}%`;
  },
  getMessage() {
    const offers = this.getCombinableOffers();
    if (!offers.length) return '';
    
    const currentTier = offers.findIndex(o => parseFloat(o.progress) < 100);
    
    if (currentTier === -1) return offers[offers.length - 1].success;
    if (currentTier > 0) return offers[currentTier].message;
    return offers[0].message;
  },
  getThresholdStyle(offer) {
    const isComplete = parseFloat(offer.progress) >= 100;
    return {
      backgroundColor: isComplete 
        ? '{{ settings.offer_progress_bar_style_background_color }}' 
        : '{{ settings.offer_progress_style_background_color }}',
      color: isComplete ? '{{ settings.offer_threshold_passed_style_color }}' : null
    };
  }
  }" {% if settings.combinable %}
    {% if settings.combinable_empty_cart %}
      x-show="$store.cart.items.length || {{ settings.combinable_empty_cart | json }}"
    {% else %}
      x-show="$store.cart.items.length"
    {% endif %}
  {% endif %} >

  <div class="{% render 'class-settings' settings:settings, prefix:'offer_class'%}" style="{% render 'style-settings' settings:settings, prefix:'offer_style'%}">
   <p class="offer__message m-0 leading-snug {% render 'class-settings' settings:settings, prefix:'offer_message_class'%}" 
      x-html="isCombinable ? getMessage() : $store.offers.find(o=>o.key=='{{ settings.key }}').message"></p>

   <div class="flex w-full rounded-full progress relative" style="
     {% render 'style-settings' settings:settings, prefix:'offer_progress_style'%}
     ">
     <span class="progress--fill transition-all rounded-full pb-xs"
        :class="{ 
         'progress--last-tier': getCurrentTierIndex() === (getCombinableOffers().length - 1)
        }"
        :style="`
          width:${isCombinable ? getProgress() : $store.offers.find(o=>o.key=='{{ settings.key }}').progress};
          --progress-fill-color: {{ settings.offer_progress_bar_style_background_color }};
          {% render 'style-settings' settings:settings, prefix:'offer_progress_bar_style'%}
        `">
      </span>

      <div class="progress__threshold-markers">
        <template x-for="(offer, index) in isCombinable ? getCombinableOffers() : [$store.offers.find(o=>o.key=='{{ settings.key }}')]">
          <template x-if="offer.threshold_icon || offer.threshold_label">
            <div class="progress__threshold-marker">
              <template x-if="offer.threshold_icon">
                <div class="progress__threshold-marker-icon" 
                  :style="getThresholdStyle(offer, 'icon')" x-html="offer.threshold_icon">
                </div>
              </template>
              <template x-if="offer.threshold_label">
                <span class="progress__threshold-marker-label" 
                      x-text="offer.threshold_label"
                      style="color: {{ settings.offer_threshold_label_style_color }}"></span>
              </template>
            </div>
          </template>
        </template>
      </div>
    </div>
  </div>
</div>
