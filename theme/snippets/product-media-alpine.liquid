<template x-if="{{ media }}.media_type == 'image'">
  <div>
    {%- capture media_attributes -%}
      :src="image.format({{ media }}.preview_image.src, { width: 750 })" 
      :alt="{{ media }}.alt"
      :data-media-id="{{ media }}.id"
      fetchpriority="high"
      draggable="false"
      onclick="
        {%- if settings.media_grid_modal_zoom == 'zoom_hover_over_click' -%}
          this.classList.toggle('zoom')
        {%- elsif settings.media_grid_modal_zoom == 'modal_over_click' -%}
          this.closest('.product-essentials__media').parentElement.querySelector('.product-essential-modal').style.display = 'block';
          let currentElementIndex = [...this.closest('.product-essentials__media').querySelectorAll('article[data-media-type=\'image\']')].indexOf(this.parentElement.parentElement);
          this.closest('.product-essentials__media').parentElement.querySelector('.product-essential-modal').querySelectorAll('img')[currentElementIndex].scrollIntoView({ block: 'center' });
          document.querySelector('body').classList.add('overflow-hidden');
        {%- endif -%}
      "
      {%- if settings.media_grid_modal_zoom == 'zoom_hover_over_click' -%}
        onmouseleave="this.classList.remove('zoom')"
        onmousemove="this.style.setProperty('--zoom-x', 
          Math.round(
          (
            (event.clientX - this.parentNode.getBoundingClientRect().left) / 
            this.parentNode.clientWidth
          )*100)
          +'%'
        );
        this.style.setProperty('--zoom-y', 
          Math.round(
          (
            (event.clientY - this.parentNode.getBoundingClientRect().top) / 
            this.parentNode.clientHeight
          )*100)
          +'%'
        );"
      {%- endif -%}
      {% comment %}
      :srcset="[550, 1100, 1190, 1680, 2048, 4096].map(width => {
        if (width > {{ media }}.width) return false
        return image.format({{ media }}.src, { width: width }) + ' ' + width + 'w'
      }).filter(v => !!v).join(', ')"
      sizes="(min-width: 1024px) 33vw, 100vw"
      {% endcomment %}
    {%- endcapture -%}
    {%- capture media_src -%}image.format({{ media }}.preview_image.src, { width: 750 }){%- endcapture -%}
    {% render 'image'
      class: 'w-full h-full object-center object-contain zoomable product-media-object'
      alt: media.alt
      src: media_src
      attributes: media_attributes
      loading: 'eager'
      widths: '200,400,600,800,1000,1200'
    %}
    {% render 'image'
      class: 'absolute'
      loading: 'lazy'
      attributes: '
        style="top: 20px; left: 20px; width: 60px;"
        fetchpriority="low"
        decode="async"
        x-show="index == 0 && $store.products[window.document.location.pathname.split(`/`).at(-1)].badge_image" 
        :src="$store.products[window.document.location.pathname.split(`/`).at(-1)].badge_image"
      '
    %}
  </div>
</template>

<template x-if="{{ media }}.media_type == 'external_video' && {{ media }}.host == 'youtube'">
  <div
    x-data="asyncVideo({
    loadingMethod: '{{ settings.loading_method }}',
    loadingDelay: {{ settings.loading_delay | times: 1000 }}
    })"
    x-init="initVideo()"
  >
    <iframe 
      frameborder="0" 
      class="w-full h-full"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" 
      allowfullscreen="allowfullscreen" 
      :src="`https://www.youtube.com/embed/${ {{ media }}.external_id }?color=white&amp;controls=1&amp;autoplay=0&amp;enablejsapi=1&amp;modestbranding=1&amp;origin=https%3A%2F%2Fpolinas-potent-potions.myshopify.com&amp;playsinline=1&amp;rel=0`" 
      :title="product.title"
    ></iframe>
  </div>
</template>

<template x-if="{{ media }}.media_type == 'external_video' && {{ media }}.host == 'vimeo'">
  <div
    x-data="asyncVideo({
    loadingMethod: '{{ settings.loading_method }}',
    loadingDelay: {{ settings.loading_delay | times: 1000 }}
    })"
    x-init="initVideo()"
  >
    <iframe 
      frameborder="0" 
      class="w-full h-full"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" 
      allowfullscreen="allowfullscreen" 
      :src="`https://player.vimeo.com/video/${ {{ media }}.external_id }?byline=0&amp;controls=1&amp;autoplay=0&amp;loop=1&amp;muted=1&amp;playsinline=1&amp;title=0`" 
      :title="product.title"
    ></iframe>
  </div>
</template>

<template x-if="{{ media }}.media_type == 'video'">
  <div
    x-data="asyncVideo({
      loadingMethod: '{{ settings.loading_method }}',
      loadingDelay: {{ settings.loading_delay | times: 1000 }}
    })"
    x-init="initVideo()"
  >
  <video 
    class="{{ settings.media_object_classes }} w-full" 
    :alt="{{ media }}.alt" {{ settings.media_item_attr | replace: '"',"'" }} 
    x-data="{
      attributesArr: `{{ settings.media_item_attr | replace: '"',"'"}}`.split(' ')
    }"
    x-init="
      {%- if settings.additional_video_attributes -%}
        window.addEventListener('Products:optionSelected', e => {
          setTimeout(() => {
            let videos = document.querySelectorAll('.product-essentials__media-item video');
            if (media.alt.includes('|muted') || attributesArr.includes('muted')) {
              $el.muted = true;
              videos.forEach(video => {
                let altText = video.getAttribute('alt');
                if (media.alt.includes('desktop') && altText?.includes('desktop')) {
                  video.setAttribute('muted', true);
                } else if (media.alt.includes('mobile') && altText?.includes('mobile')) {
                  video.setAttribute('muted', true);
                }
              });
            }else{
              $el.muted = false;
            }
            if (media.alt.includes('|autoplay') || attributesArr.includes('autoplay')) {
              $el.autoplay = true;
              videos.forEach(video => {
                let altText = video.getAttribute('alt');
                if (media.alt.includes('desktop') && altText?.includes('desktop')) {
                  video.setAttribute('autoplay', true);
                } else if (media.alt.includes('mobile') && altText?.includes('mobile')) {
                  video.setAttribute('autoplay', true);
                }
              });
            }else{
              $el.autoplay = false;
            }
            if (media.alt.includes('|loop') || attributesArr.includes('loop')) {
              $el.loop = true;
              videos.forEach(video => {
                let altText = video.getAttribute('alt');
                if (media.alt.includes('desktop') && altText?.includes('desktop')) {
                  video.setAttribute('loop', true);
                } else if (media.alt.includes('mobile') && altText?.includes('mobile')) {
                  video.setAttribute('loop', true);
                }
              });
            }else{
              $el.loop = false;
            }
            if (media.alt.includes('|controls') || attributesArr.includes('controls')) {
              $el.controls = true;
              videos.forEach(video => {
                let altText = video.getAttribute('alt');
                if (media.alt.includes('desktop') && altText?.includes('desktop')) {
                  video.setAttribute('controls', true);
                } else if (media.alt.includes('mobile') && altText?.includes('mobile')) {
                  video.setAttribute('controls', true);
                }
              });
            }else{
              $el.controls = false;
            }
            $el.load()
          }, 100);
        });
      {%- else -%}
        window.addEventListener('Products:optionSelected',()=>setTimeout(()=>$el.load(),100))
      {%- endif -%}
    "
  >
     
    <template x-if="shouldLoad">
      <template x-for="source in {{ media }}.sources">
        <source :type="source.mime_type" :src="source.url">
      </template>
    </template>
  </video>
  </div>
</template>
