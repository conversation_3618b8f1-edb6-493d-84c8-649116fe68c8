{% if background == blank %}
  {% assign background = 'bg-gray-warm'%}
{% endif %}
{% capture bg %}{{background}}{% endcapture %}
<article
  class="product-item__wrapper flex flex-col font-body relative text-center {{classes}} {% if product.available == false %}opacity-50{% endif %}"
  data-product-item
  data-part-number="{{ product.title }}"
  tabindex="0"
  data-batch-index="{{loader.pendingBatchIndex}}" 
  data-item-index="{{forloop.index0}}" 
  onclick="window.history.replaceState({batch:{{loader.pendingBatchIndex}},item:{{forloop.index0}} },document.title)"
  >

  {% if product.hover_image_alt contains 'https' or forloop.index == -1 %}
  <div class="productitem h-full flex flex-col relative" data-product-item-content data-hover-video>
  {% else %}
  <div class="relative flex flex-col h-full productitem" data-product-item-content>
  {% endif %}
  <div 
     class="flex flex-col h-full overflow-hidden product-item__link" hover>
      <div class="relative flex-grow rounded-sm">
        
        <div class="relative {{bg}} product-item__image-bg group">
          <a
            id="product_link_{{ product.id }}"
            {{'h'}}ref="{% if handle != blank %}/collections/{{ handle }}{% endif %}/products/{{ product.handle }}"
          >
            <figure class="relative m-0 product-item__image-wrapper bg-white opacity-90 aspect-h-1 aspect-w-1" data-product-item-image>
              {% assign feat_img_classes = "product-item__image--main absolute mix-blend-multiply object-contain inset-0 " %}
              {% assign hoverImage = product.images[1] %}
              {% if hoverImage == blank %} 
              {% assign feat_img_classes = feat_img_classes | append: " transition-transform bg-white transform group-hover:scale-105" %}
              {% endif %}
              
              {% render 'image' image: product.featured_image, image_class: feat_img_classes %}

              {% if hoverImage != blank %}
                {% assign hover_alt = product.images[1].alt %}
                <div 
                  class="product-image__hover {{ bg }} product-item__image-bg flex absolute opacity-0 group-hover:opacity-100  {% if hover_alt contains 'https' %} {{bg}} {% else %} inset-0 p-4 {% endif %}" 
                  {% if hover_alt contains 'https' %}onmouseenter="if(this.innerHTML.includes('<!--')){this.innerHTML = this.innerHTML.replace('<!--','').replace('-->','')}"{% endif %}
                >
                  
                  {% if hover_alt contains 'https' %}
                  <!--<video class="inset-0 {{ bg }} product_item--image-bg product-video" preload="true" src="{{ hover_alt }}" muted mute autoplay loop="" style="" poster=""><source src="{{ hover_alt }}"></video>-->
                  {% else %} 
                  <div class="{{ bg }} absolute inset-0">
                    {% render 'image' image: hoverImage, image_class: "object-contain w-full h-full mix-blend-darken" %}
                  </div>
                  {% endif %}
                </div>
              {% endif %}

            </figure>
          </a>

        </div>
      

        <div class="absolute flex opacity-100 lg:top-2 top-1 group-hover:opacity-100 lg:right-2 right-1 lg:opacity-0">
          {%- comment -%}
          <button class="p-3 rounded-full hover:bg-white" onclick="_n.qs('[quickshop]').innerHTML='';Neptune.periscope.view(_n.qs('[quickshop]'),'/products/{{product.handle}}?view=quickshop'); modal.open('quickshop'); return false;">{% render 'icon' icon:'eye' width:20 height:20 %}</button>
          
          <button class="p-3 rounded-full hover:bg-white">{% render 'icon' icon:'heart' width:20 height:20 %}</button>
          {%- endcomment -%}
        </div>

        <div class="product-item__info flex flex-col px-4 pt-1 pb-4 text-left">
          <div class="absolute top-0 left-0 flex flex-col items-start mt-3 ml-3 space-y-1 text-sm brand-semibold mb-0-5 product_badge">
            {% for badge in badges %}
              
              {% if product.tags contains badge.tag and badge.tag != 'more-colors' %}
                <span class="badge--sm" style="background-color:{{ badge.badge_bg_color }}; color:{{ badge.badge_text_color }};">{{ badge.collection_badge }}</span>
              {% endif %}  
            {% endfor %}

            {% if product.available == false %}
            <span class="badge--sm">{{ 'products.product.sold_out' | t }}</span>
            {% endif %}

          </div>
          <div class="mb-2 product-item__meta">
            {% assign productTitle = product.title | remove: product.type | split: ' | ' %}
            <h3 class="my-1 leading-snug product-title product-item__title">
              {{ productTitle[1] }}
            </h3>
              
            <div class="leading-snug product-item__price flex space-x-2">{% assign discount_amount = product.price | divided_by: product.compare_price | times: -100 | plus: 100 | round %}  
            {% if product.compare_price > product.price %}
                <span class="text-red-600 block">{{ product.price | money | remove: " " }}</span>
                <s class="block">{{ product.compare_price | money | remove: " " }}</s>
              {% else %}
                {% if product.price_min == product.price_max %}
                  {{ product.price_min | money | remove: " " }}
                {% else %}
                  from {{ product.price | money | remove: " " }}
                {% endif %}
              {% endif %}
            </div>
          </div>

          <div class="product-item__footer -mx-4">

            <div class="product-item__siblings overflow-x-hidden">
              <p class="mb-0 mt-4 text-xs">Color: {{ productTitle[2] }}</p>
              <div class="swiper-wrapper items-center space-x-5">
                {% for swatch in (1..5) %}
                <div class="swiper-slide flex h-20 w-20">
                  <div class="relative h-20 w-20 overflow-hidden bg-gray-100 swatch{% unless forloop.first %} opacity-50{% endunless %}">
                  </div>
                </div>
                {% endfor %}
                <span class="swiper-slide text-xs whitespace-nowrap">+ 4 more Colors</span> 
              </div>
            </div>
          </div>

          <!-- {% if product.swatches.size %}
            <div class="flex space-x-3">
            {% for swatch in product.swatches %}
              {% if forloop.index == 5 %}
                <div class="flex w-9 h-4">

                <span class="text-xs -mt-px">+ {{ product.swatches.size | minus: forloop.index0 }}</span> 
                </div>
              {% break %}
              {% endif %}
              <div class="swatch overflow-hidden w-9 h-4 relative" neptune-engage="{on: 'mouseenter', targets: [
                {selector:'_parent .swatch .border-2' classes:remove:active},
                {selector:'_self .border-2' classes:add:active},
                {selector:'#img_{{ product.id }}' attributes:[
                  {att:src set:{{ swatch.featured_image }}},
                  {att:srcset set:{{ swatch.featured_image }}}
                ]},
                {selector:'#product_link_{{ product.id }}' attributes:[{att:href set:{{ swatch.product_url }}}]}
              ]}">
                <img class="object-cover max-w-none" src="{{ swatch.swatch_image }}" />
                <div class="absolute border-2 border-white inset-0.5 hidden active:block{% if swatch.current %} active{% endif %}"></div> 
              </div>
            {% endfor %}
            </div>
          {% endif %}
          {% for badge in badges %}
            {% if product.tags contains badge.tag and badge.tag == 'more-colors' %}
              <span class="block mb-0 text-sm leading-normal brand-semibold" style="background-color:{{ badge.badge_bg_color }}; color:{{ badge.badge_text_color }};">{{ badge.collection_badge }}</span>
            {% endif %}  
          {% endfor %}
        </div> -->
      </div>   
    </div> 

  </div>
</article>

