{% if settings.block_scripts != blank %}
{% assign exceptions = settings.script_block_exceptions | newline_to_br | split:'<br />' %}
<script>

  const Blocker = {
    log:[],
    exceptions:[
      {% for exception in exceptions %}
      {% assign x = exception | split: ':' %}
      { href:{{ x[0] | json }}.replace("\n",''), allow:{{ x[1] | split: ',' | json }}}{% unless forloop.last %},{% endunless %}
      {% endfor %}
    ]
  }
  window.Blocker = Blocker

  document.head._insertBefore = document.head.insertBefore
  document.head.insertBefore = function(s, x) {

    if(!s.src || Blocker.exceptions.filter(x=>document.location.pathname==x.href||(x.href.includes('*')&&document.location.pathname.includes(x.href.replace('*','')))).map(x=>x.allow).flat().find(a=>s.src.includes(a))){
      return document.head._insertBefore(s, x)
    }
    Blocker.log.push(s.src)
  }

  {% if settings.block_scripts == 'before' %}
  window.addEventListener('DOMContentLoaded', ()=>{
    document.head.insertBefore = document.head._insertBefore
  })
  {% endif %}

</script>
{% endif %}