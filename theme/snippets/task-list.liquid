<ul class="task-list" x-data>
  {% assign tasks = tasks | default: settings.tasks | newline_to_br | split: '<br />' %}
  {% for _task in tasks %}
  {% assign task = _task | split: '|' %}
  <li :class="`task flex {% if task.size > 1 %}${ !!{{task | last}} ? `complete`: ``}{% endif %}`">
    <span class="check">{% render 'icon' icon:'check' %}</span>
    <span>{{ task | first }}</span>
  </li>
  {% endfor %}
</ul>