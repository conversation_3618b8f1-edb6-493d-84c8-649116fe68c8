{%- liquid 
  if product.title != blank
    assign product = 'product'
  endif
  assign product = product | default: 'product'
  assign location = location | default: 'grid'
  assign text_value = 'badge.' | append: location
-%}
<div class="product-badges product-badges--{{ location }}" x-data="{badge: false}" x-init="() => {
    function checkTags() {
      $data.badge = false
      for (i in productBadges) {
        const badge = productBadges[i]
        if (!{{ product }}.tags?.includes(badge.tag)) continue
        $data.badge = badge
        break;
      }
    }
    if (!productBadges) return false
    checkTags()
    $watch('{{ product }}.tags', () => {
      checkTags()
    })
    window.addEventListener('Products:siblingChange', checkTags)
  }">
  <template x-if="badge">
    {%- render 'product-badge' text:text_value text_color:'badge.color' background_color:'badge.bg_color' -%}
  </template>
</div>
