{% assign ns = 'redirecttovip-message' %}
<div id="{{ ns }}" class="{{ ns }} lity-hide">
    <div class="{{ ns }}__modal">
        <button class="lity-close {{ ns }}__close" type="button" aria-label="Close (Press escape to close)" data-lity-close="">X</button>
        <h1 class="{{ ns }}__heading">{{ settings.roark_vip_modal_heading }}</h1>
        <p class="{{ ns }}__message">{{ settings.roark_vip_modal_paragraph }}</p>
        <a aria-label="Continue" href="#" style="background-color:{{ settings.roark_vip_modal_button_color }};color:{{ settings.roark_vip_modal_text_color }};" class="{{ ns }}__button">Continue</a>
    </div>
</div>