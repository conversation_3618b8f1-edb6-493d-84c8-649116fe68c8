<div class="text-center">
	<p class="cart__empty-text">{{ 'sections.cart.empty' | t }}</p>
	{% if return_url != blank or template contains 'cart' %}
	<a href="{{ routes.all_products_collection_url }}" class="block button button--primary">
		{{ 'general.continue_shopping' | t}}
	</a>
	{% else %}	
	<button class="button button--primary" neptune-engage="[{
				'targets':[{
					'selector':'html',
					'attributes':[{
						'att':'data-active-modal',
						'set':'_remove'
					}]
				},
				{ 
					'selector':'[data-return-focus]',
					'attributes':[{
						'att':'data-return-focus',
						'set':'_remove'
					}],
					'focus':true
				}]
				}]">
		{{ 'general.continue_shopping' | t}}
	</button>
	{% endif %}
</div>