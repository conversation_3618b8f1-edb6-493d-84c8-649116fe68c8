<script type="text/javascript">
  let productOptionNameMapJsonString = '{{ settings.product_option_name_map | strip_html | strip_newlines }}';

  let productOptionNameMap = null;
  if (productOptionNameMapJsonString) {
    let productOptionNameMapJson = JSON.parse(productOptionNameMapJsonString);
    productOptionNameMap = productOptionNameMapJson ? productOptionNameMapJson.product_option_name_map : null;
  }
  window.plpBackInStockEnable = {{ settings.plp_back_in_stock }}
</script>
<dialog 
  data-modal="quickadd" 
  class="modal modal--quickadd" 
  tabindex="0"
  x-data
>

  <template x-if="!!$store.quickadd">

  
  <div>

    <button class="absolute top-0 right-0 p-4 quickadd__close" onclick="QuickAdd.close()">
      {% render 'icon' icon:'x' strokeWidth:2 %}
    </button>

    <article>

      <template x-if="!$store.quickadd.success && !!$store.quickadd.config && !!$store.quickadd.config.title">
        <h2 class="quickadd__title" x-text="$store.quickadd.config.title"></h2>
      </template>

      <template x-if="$store.quickadd.success && !!$store.quickadd.config.success && !!$store.quickadd.config.success.title">
        <h2 class="quickadd__title" x-text="$store.quickadd.config.success.title"></h2>
      </template>

      <template x-if="$store.quickadd.success && !! $store.quickadd.config.success && !!$store.quickadd.config.success.button">
        {% render 'button' style:'primary' class:'w-full' link:'#' attributes:':href="$store.quickadd.config.success.button.link" x-text="$store.quickadd.config.success.button.text"' %}
      </template>
      
      <template x-if="!$store.quickadd.success && !!$store.quickadd.product">

        <div class="quickadd__container" x-data="{plpBackInStockEnable:{{ settings.plp_back_in_stock }}}">


          {% if settings.quickadd_media %}

          <div class="product-essentials__media product-essentials__media--carousel" x-data="{swiper: null }" x-init="swiper = Carousels.create($refs.container);">
            <div class="swiper-container swiper" swiper x-ref="container">
              <script type="swiper/config">
                { 
                  {% if settings.quickadd_carousel_pagination == 'progressbar' %}
                  renderProgressbar: function (progressbarFillClass) {
                    {% if settings.enable_logs -%}console.log('progressbarFillClass',progressbarFillClass){%- endif %}
                    return '<span class="' + progressbarFillClass + '"></span>';
                  },
                  {% endif %}
                  breakpoints:{
                    1:{
                      slidesPerView: {{ settings.quickadd_slides_per_view | default: 1 }}    
                    },
                    1025:{
                      slidesPerView: {{ settings.quickadd_slides_per_view_desktop | default: 2.25 }}    
                    }
                  },
                  {% if settings.quickadd_carousel_pagination != blank %}
                  pagination: {
                    el: '.swiper-pagination',
                    type: '{{ settings.quickadd_carousel_pagination }}',
                    clickable: true
                  }
                  {% endif %}
                }
              </script>
              <div class="swiper-wrapper">
                <template x-for="(media, index) in $store.quickadd.product.media">
                  <template x-if="{{ settings.enable_disable_videos_quick_add }} || media.media_type != 'video'">
                    <article class="product-essentials__media-item swiper-slide" style="{% render 'style-settings' settings:settings prefix:'media_item_style' %}">
                      {% render 'product-media-alpine' media: 'media' %}
                    </article>
                  </template>
                </template>
                <article class="swiper-slide swiper-slide--description quickadd__description h-full flex items-center justify-center p-lg">
                  <div>
                    <h4 x-text="$store.quickadd.{{ settings.quickadd_title_source }}" class=""></h4>
                    {% if settings.quickadd_subtitle_source %}
                    <h5 x-text="$store.quickadd.{{ settings.quickadd_subtitle_source }}" class=""></h5>
                    {% endif %}
                    <hr/>
                    <div x-html="$store.quickadd.product.description" class="measure text-wrap-balance"></div>
                  </div>
                </article>
                
              </div>
            </div>
            <div class="swiper-pagination swiper-pagination--{{ settings.quickadd_carousel_pagination }} absolute mb-2 inset-x-0"></div>
          </div>
          {% endif %}

          {% if settings.quickadd_siblings %}
          <div class="quickadd__product-siblings flex overflow-y-scroll">
            <template x-for="(sibling, index) in $store.quickadd.product.swatches">
              <a @click="QuickAdd.open(sibling.handle)" class="quickadd__product-sibling w-1/6" :class="{ selected: $store.quickadd.product && $store.quickadd.product.handle == sibling.handle }">
                <img :src="sibling.featured_image">
              </a>
            </template>
          </div>
          {% endif %}

          <div class="flex items-start justify-start quickadd__product-info">

              <div class="w-24 h-24 quick-add__img-container bg-tertiary">
                {% render 'image' 
                attributes: ':src="$store.quickadd.product.featured_image || $store.quickadd.product.images[0].src || $store.quickadd.product.images[0]" :alt="$store.quickadd.product.title" style="mix-blend-mode:multiply;"' 
                  class: 'h-24 w-24 object-contain object-center opacity-100'
                  sizes: '(min-width: 768px) 20vw, 50vw'
                %}
              </div>

            <div class="flex flex-col justify-between product-meta quickadd__product-meta">
              
              {% if settings.quickadd_title_source != blank %}
              <h3 class="m-0 quickadd__product-title product-meta__title type-item" x-text="$store.quickadd.{{ settings.quickadd_title_source }}"></h3>
              {% endif %}
              
              {% if settings.quickadd_type_source != blank %}
              <h4 class="m-0 product-meta__type type-subline" x-text="$store.quickadd.{{ settings.quickadd_type_source }}"></h4> 
              {% endif %}
              
              {% if settings.quickadd_reviews %}
                {% render 'review-summary' sku_path:'$store.quickadd.product.variants[0].sku' %}
              {% endif %}
              
              <div class="product-meta__footer">

                {% if settings.quickadd_selected_color %}
                  <span class="product-meta__color" x-text="$store.quickadd.product.variants[0].option1"></span>
                {% endif %}
                
                <div class="flex items-center gap-2">
                  <p class="product-item__prices">
                    <span class="product-item__price type-item {{ settings.classes_product_item_price }}" x-bind:class="{ 'product-item__price_with_compare': $store.quickadd.product.compare_at_price }" x-text="money.format($store.quickadd.product.price).split('.')[0]"></span>

                    <template x-if="!!$store.quickadd.product.compare_at_price">
                      <s class="product-item__compare-at-price type-item {{ settings.classes_product_item_price }}" x-text="money.format($store.quickadd.product.compare_at_price).split('.')[0]"></s>
                    </template>
                  </p>
                </div>
              </div>
            </div>
          </div>


          <template x-if="!!$store.quickadd.product">
            <div class="quickadd__product-options" 
               {% if settings.preselect_variant %}x-init="() => {
                  const availableVariant = $store.quickadd?.product.variants.find(v => v.available == true)
                  if (!availableVariant) return false

                  QuickAdd.select(availableVariant.id)
                  availableVariant.options.forEach((op,i) => {
                    $data.selectedOptions['option' + (i+1)] = op
                  })
               }"{% endif %}
              x-data="{ 
                productOptionNameMap: productOptionNameMap,
                groupedOptionsEntries: Object.entries($store.quickadd.groupedOptions), 
                optionNames: $store.quickadd.product.options_with_values.map(o => o.name),
                selectedOptions: {},
                variants: $store.quickadd.product.variants,
                
                optionAvailable: function(optionKey, value) {
                  return QuickAdd.variantAvailable(this.variants, optionKey, value, this.selectedOptions);
                },


                updateSelection: function(optionKey, value) {
                  this.selectedOptions[optionKey] = value
                  this.selectOption()
                },
                selectOption: function() {
                  const selectedCount = Object.keys(this.selectedOptions).filter(key => this.selectedOptions[key] != null).length
                  const allOptionsCount = this.groupedOptionsEntries.length - 1 // account for option1
          
                  if (selectedCount >= allOptionsCount) {
                    QuickAdd.selectVariant(this.variants, this.selectedOptions)
                  }
                },
                schemaRename: function(originalName) {
                  let mappedName = originalName
                  if (productOptionNameMap) {
                    const mapping = productOptionNameMap.find(map => {
                      if (map.original === originalName) {
                        // check for 'show' property
                        if (map.show === false) {
                          return false
                        }
                        // if tags array is not empty, check if product has any of these tags
                        if (Array.isArray(map.tags) && map.tags.length > 0) {
                          return map.tags.some(tag => $store.quickadd.product.tags.includes(tag))
                        }
                        return true
                      }
                      return false
                    })
                    if (mapping && mapping.show) {
                      mappedName = mapping.rename
                    }
                  }
                  return mappedName
                }
              }">
              <!-- Iterate over groupedOptionsEntries -->
              <template x-for="([optionKey, optionValues], index) in groupedOptionsEntries" :key="optionKey">
                <!-- Show the option group if it's not 'option1' -->
                <div x-show="optionKey !== 'option1'">
                  <!-- Always show the h3 element for each option group -->
                  <h3 class="quickadd__product-option-title" x-show="groupedOptionsEntries.length > 2" x-text="schemaRename(optionNames[index])"></h3>
                  <div class="flex-wrap mb-4 field__buttons field__buttons--tight no-scrollbar">
                    <!-- Iterate over option values for current optionName -->
                    <template x-for="value in optionValues" :key="value">
                      <button 
                        class="py-2 rounded field__button quick-add__field-button"
                        :class="{ 
                          'selected': selectedOptions[optionKey] === value && $store.quickadd.product,
                          'unavailable': !optionAvailable(optionKey, value)
                        }"
                        :disabled="!optionAvailable(optionKey, value) && !(plpBackInStockEnable && !$store.quickadd.product.tags.includes('bis-hidden'))"
                        @click="updateSelection(optionKey, value)"
                      >
                        <span class="rounded field__button-text" x-text="value"></span>
                      </button>
                    </template>
                  </div>
                </div>
              </template>

            </div>
          </template>
          
          
          

        </div>

      </template>      

    </article>

    {% if settings.quickadd_cta %}
    <footer class="flex items-center quickadd__footer rounded-buttons">
      <button onclick="QuickAdd.close()" class="quickadd__footer-close button button--primary">
        {% render 'icon' icon:'x' %}
      </button>
      <a class="button button--simple flex-shrink-1" :href="`/products/${$store.quickadd.product.handle}`">
        <span>Details</span>
        {% render 'icon' icon:'arrow-up-right' %}
      </a>
      <template x-if="$store.quickadd.variant">
        <template x-if="plpBackInStockEnable && !$store.quickadd.variant.available && !$store.quickadd.product.tags.includes('labor_day_gwp') && !$store.quickadd.product.tags.includes('GWP') && !$store.quickadd.product.tags.includes('bis-hidden')">
          <button class="plp-notify-me-when-available button button--dark flex-grow" onclick="event.preventDefault(); openBackInStockModal();" role="button" aria-label="notify-me-when-available" >
            Notify Me When Available
          </button>
        </template>
      </template>
      <template x-if="$store.quickadd.variant">
        <template x-if="!plpBackInStockEnable || $store.quickadd.variant.available || $store.quickadd.product.tags.includes('labor_day_gwp') || $store.quickadd.product.tags.includes('GWP')">
          <button class="button button--dark flex-grow" onclick="QuickAdd.buy()" x-if="!plpBackInStockEnable || $store.quickadd.variant.available || $store.quickadd.product.tags.includes('labor_day_gwp') || $store.quickadd.product.tags.includes('GWP')">
            Add to Cart
          </button>
        </template>
      </template>
      <template x-if="!$store.quickadd.variant || (!$store.quickadd.variant.available  && $store.quickadd.product.tags.includes('bis-hidden'))">
        <button class="button button--dark button--disabled flex-grow" disabled>
          Select Options
        </button>
      </template>
    </footer>
    {% endif %}


  </div>

  </template>

</dialog>
