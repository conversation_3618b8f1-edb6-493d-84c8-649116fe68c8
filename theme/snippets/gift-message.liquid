<form 
  class="gift-message {{ class }}"
  x-data="{
    include_message: false,
    display_form: false,
    from: '',
    to: '',
    message: '', 
    saveCartAttributes() {
      Cart.attributes({
        gift_order: $data.include_message,
        from: $data.from,
        recipient_email_address: $data.to,
        gift_message: $data.message
      })
    },
    storeData() {
      sessionStorage.setItem('gift_message', JSON.stringify({ 
        include_message: $data.include_message, 
        from: $data.from,
        email: $data.to, 
        message: $data.message 
      }))
      $data.saveCartAttributes()
    },
    recallData() {
      let data = sessionStorage.getItem('gift_message')

      if (data == null) return false
      data = JSON.parse(data)
      Object.entries(data).forEach((entry) => {
        
        if (entry[0] == 'email') return $data.to = entry[1]

        $data[entry[0]] = entry[1]
      })
      $data.saveCartAttributes()
    }
  }"
  x-init="() => {
    recallData()
    storeData()

    $watch('$data.include_message', storeData)
    $watch('$data.from', storeData)
    $watch('$data.to', storeData)
    $watch('$data.message', storeData)
  }"
>


  <div class="flex justify-between gift-trigger">
    <div class="field">
      <label class="field__checkbox">
        <input 
          id="GiftMessage" 
          name="Gift Message" 
          type="checkbox" 
          x-model="include_message"
          @change="() => {
            if ($el.checked) {
              $data.display_form = true;
              window.setTimeout(()=>{
                window.document.querySelector(`[name='Gift Message']`).scrollIntoView({behavior:'smooth'});  
              },100);
              return;
            }
            $data.display_form = false
          }"
         >
         <span class="flex items-center gap-1">
           {%- render 'icon' icon:'gift' width: 16 -%}
           Gift Message
         </span>
      </label>
    </div>
    <template x-if="include_message">
      <button @click="$event.preventDefault(); display_form = !display_form; if(display_form){window.setTimeout(()=>{
                window.document.querySelector(`[name='Gift Message']`).scrollIntoView({behavior:'smooth'});  
              },100);}" class="flex item-center gap-1">
        Edit 
        <template x-if="!display_form">
          {%- render 'icon' icon:'chevron-down' width: 16 -%}
        </template>
        <template x-if="display_form">
          {%- render 'icon' icon:'chevron-up' width: 16 -%}
        </template>
      </button>
    </template>
  </div>

  <div x-show="display_form" class="gift-form">
    {% render 'field' label:'From'  name:'From' style: 'field--floating-label' placeholder:'From' attributes: 'x-model="from" required' %}
    {% render 'field' label:'Recipient Email' name:'Recipient Email' type: 'email' style: 'field--floating-label' placeholder:'Email' attributes: 'x-model="to" required' %}
    {% render 'field' name:'Gift Message' label: 'Gift Message' type: 'textarea'  style:"field--floating-label" attributes: 'x-model="message" required' %}
  </div>
</form>
