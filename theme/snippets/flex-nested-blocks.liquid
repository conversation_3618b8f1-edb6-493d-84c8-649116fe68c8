{% liquid 

	assign frame_depth = 0

	assign _from = false	

	for block in blocks offset:offset

		assign blockIndex = forloop.index | plus: offset

		if from != blank
			if block.type == from
				assign _from = true
			endif
			unless _from
				continue
			endunless
		endif 

		if stop == block.type or until contains block.type or block.type contains until
			break
		endif

		assign _offset = offset | plus: forloop.index

		if block.type == 'break'
			if frame_depth == 0
				break
			endif
			assign frame_depth = frame_depth | minus: 1
		endif

		if frame_depth == 0
			case block.type

				when 'frame'
					render 'flex-frame' settings:block.settings blocks:blocks offset:_offset, attributes:block.shopify_attributes, block:block

				when 'content-item'
					render 'content-item' settings:block.settings blocks:section.blocks offset:_offset, attributes:block.shopify_attributes block:block

				when 'menu-item'
					render 'menu-item' settings:block.settings section_settings:section.settings, attributes:block.shopify_attributes

				when 'nav-item'
					render 'nav-item' settings:block.settings, attributes:block.shopify_attributes

				when 'button'
					render 'button' settings:block.settings, attributes:block.shopify_attributes

				when 'title'
					render 'title' settings:block.settings, attributes:block.shopify_attributes
				
				when 'text'
					render 'text' settings:block.settings, attributes:block.shopify_attributes
				
				when 'rich-text'
					render 'text' settings:block.settings, attributes:block.shopify_attributes, text_element:'div'
				
				when 'dynamic-text'
					render 'dynamic-text' settings:block.settings, attributes:block.shopify_attributes

				when 'image'
					render 'media' settings:block.settings, attributes:block.shopify_attributes

				when 'video'
					render 'element' tag:'video' prefix:'video' _settings:block.settings, attributes:block.shopify_attributes

				when 'form'
					render 'form' settings:block.settings blocks:blocks offset:_offset, attributes:block.shopify_attributes open:true 

				when 'form-end'
					render 'form' close:true settings:block.settings

				when 'form-errors'
					render 'form-errors' settings:block.settings

				when 'form-success'
					render 'form-success' settings:block.settings

				when 'text-field'
					render 'field' settings:block.settings blocks:blocks offset:_offset, attributes:block.shopify_attributes block_id:block.id

				when 'checkbox'
					render 'field' type:'checkbox' settings:block.settings blocks:blocks offset:_offset, attributes:block.shopify_attributes block_id:block.id

				when 'multiselect'
					render 'field' settings:block.settings blocks:blocks offset:_offset, attributes:block.shopify_attributes block_id:block.id

				when 'mini-form'
					render 'mini-form' settings:block.settings, attributes:block.shopify_attributes

				when 'dropdown-menu'
					render 'dropdown-menu' settings:block.settings, attributes:block.shopify_attributes block_id:block.id

				when 'toggle-switch'
					render 'toggle-switch' settings:block.settings, attributes:block.shopify_attributes block_id:block.id

				when 'collection-filters'
					render 'collection-filters' settings:block.settings, attributes:block.shopify_attributes block_id:block.id

				when 'applied-filters'
					render 'collection-applied-filters' settings:block.settings, attributes:block.shopify_attributes block_id:block.id
					
				when 'accordion'
					render 'accordion' settings:block.settings, attributes:block.shopify_attributes, block_id:block.id, section_id:section.id

				when 'rich_accordion'
					render 'accordion' open:true, settings:block.settings, attributes:block.shopify_attributes, block_id:block.id, section_id:section.id

        when 'rich_accordion_close'
          echo '</div></details>'
					
				when 'hotspot'
					render 'hotspot' settings:block.settings, attributes:block.shopify_attributes, block_id:block.id, section_id:section.id

				when 'line_items'
					render 'cart-item' settings:block.settings, attributes:block.shopify_attributes, block_id:block.id

				when 'cart-header'
          render 'cart-header' settings:block.settings, attributes:block.shopify_attributes, block_id:block.id, index:forloop.index, blocks:section.blocks

				when 'cart-footer'
					echo '<template x-if="$store.cart.items.length">'
						render 'cart-footer' settings:block.settings, attributes:block.shopify_attributes, block_id:block.id, class: 'slider-cart__buttons'
					echo '</template>'

				when 'order_summary'
					echo '<template x-if="$store.cart.items.length">'
						render 'cart-summary' settings:block.settings, attributes:block.shopify_attributes, block_id:block.id
					echo '</template>'

				when 'order_summary_simple'
					echo '<template x-if="$store.cart.items.length">'
						render 'cart-summary-simple' settings:block.settings
					echo '</template>'

				when 'gift_message'
					echo '<template x-if="$store.cart.items.length">'
						render 'gift-message' settings:block.settings, attributes:block.shopify_attributes, block_id:block.id class: 'slider-cart__gift-message px-4'
					echo '</template>'

				when 'product_recs'
					render 'cart-recs' _settings:block.settings, attributes:block.shopify_attributes, block_id:block.id
	
				when 'offer_progress'
					render 'offer-progress' settings:block.settings, attributes:block.shopify_attributes, block_id:block.id
			
				when 'offer_progress_summary'
					render 'offer-progress-summary' settings:block.settings, attributes:block.shopify_attributes, block_id:block.id

				when 'product-header'
					render 'product-title-lockup' settings:block.settings

				when 'product_actions'
					render 'product-actions' settings:block.settings

				when 'product_actions_close'
					echo '</div></div>'

				when 'features'
					render 'features' settings:block.settings

				when 'task-list'
					render 'task-list' settings:block.settings

				when 'product-media-grid'
					render 'product-gallery-grid' settings:block.settings, attributes:block.shopify_attributes block_id:block.id

				when 'product-media-carousel'
					render 'product-gallery-carousel' settings:block.settings, attributes:block.shopify_attributes block_id:block.id

				when 'carousel'
					render 'carousel' index:index settings:block.settings attributes:block.shopify_attributes id:block.id open:true

				when 'carousel-end'
					render 'carousel' index:index settings:block.settings attributes:block.shopify_attributes open:false close:true

        when 'bundle-component'
					render 'product-form' settings:block.settings
					render 'product-title-lockup' settings:block.settings
					render 'variant-selector' settings:block.settings, product_color_option_type:settings.product_color_option_type, ___settings:settings, product_color_swatch_page:settings.product_color_swatch_page, product_default_swatch_color_1:settings.product_default_swatch_color_1, variants_displayed_on_desktop:settings.variants_displayed_on_desktop
          if block.settings.categories != blank or block.settings.categories_dynamic != blank
            render 'fit-guide' settings:block.settings block:block
          endif
					render 'divider' settings:block.settings, attributes:block.shopify_attributes block_id:block.id
          echo '</form>'

				when 'bundle' 
					render 'product-bundle' settings:block.settings blocks:section.blocks

				when 'payment_widget' 
					if product
						render 'payment-widget' settings:block.settings total:product.price
					else
						echo '<div class="slider-cart__payment-widget">'
						render 'payment-widget' settings:block.settings total:'$store.cart.total_price'
						echo '</div>'
					endif

				when 'gate' 
					render 'product-gate' settings:block.settings

				when 'variant-selector'
					render 'variant-selector' settings:block.settings hide_single_size_variant:settings.hide_single_size_variant type:block.type variant_selector_logic: 'variant_selector_logic' product_color_option_type:settings.product_color_option_type ___settings:settings product_color_swatch_page:settings.product_color_swatch_page product_default_swatch_color_1:settings.product_default_swatch_color_1 variants_displayed_on_desktop:settings.variants_displayed_on_desktop

				when 'variant-selector-table'
					render 'variant-selector' settings:block.settings hide_single_size_variant:settings.hide_single_size_variant type:block.type ___settings:settings

        when 'product-item'
          render 'product-items' item:item index:index settings:block.settings _settings:settings attributes:block.shopify_attributes

				when 'product_form'
					render 'product-form' settings:block.settings

        when 'upsell'
          render 'microcomponent-upsell' settings:block.settings _settings:settings

				when 'promo'
					render 'product-promo' settings:block.settings
			
				when 'wishlist-toggle'
					render 'product-form-wishlist-toggle' settings:block.settings

				when 'breadcrumbs'
					render 'product-breadcrumb' product:product

				when 'enunciation'
					render 'product-enunciation' settings:block.settings

        when 'fit_guide',
          render 'fit-guide' settings:block.settings block:block

				when 'gift_card_form', 
					render 'gift-card-recipient-form', product: product, form: form, settings:block.settings section: section 

				when 'toggle-pdp',
					render 'toggle-pdp' settings:block.settings block:block

        when 'linklist',
          render 'linklist' _settings:block.settings block:block

				when 'collection_carousel',
					render 'collection-carousel' settings:block.settings, _settings:settings

				when 'announcements',
					render 'announcements' settings:block.settings, _settings:settings blocks:section.blocks index:blockIndex

				when 'nav-tools',
					render 'header-tools' settings:block.settings, _settings:settings blocks:section.blocks index:blockIndex

				when 'divider' or 'spacer'
					render 'element' tag:'div' class:block.type prefix:block.type index:index _settings:block.settings attributes:block.shopify_attributes 
				when 'collection_grid',
					render 'collection-grid' settings:block.settings, _settings:settings, id:block.id

				when 'liquid'
					echo block.settings.liquid | default: block.settings.custom_liquid

				when 'custom_liquid'
					echo block.settings.liquid | default: block.settings.custom_liquid

				when 'search_words_suggest' 
					render 'search-suggestion' settings:block.settings, _settings:settings

			endcase

		endif

		if block.type == 'frame'
			assign frame_depth = frame_depth | plus: 1
		endif

		if block.type == 'render-till'
			break
		endif

	endfor 

%}
