
<div class="account__nav group {{ classes }}">
  <ul id="AccountMenu" class="account__nav--list list m-0 p-0 lg:max-h-max overflow-hidden">
    <li id="accountInformation" class="py-3 active:underline hover:underline" :class="selected === $el.id ? 'active' : ''">
      <a 
        {% unless template contains 'account' %}href="/account"{% endunless %}
        class="cursor-pointer"
        @click="active = 'accountInformation'; selected = $el.parentElement.id; return false;"
        >
        {% if settings.icon_display_icon %}
          <span class="w-8 flex justify-center pr-2">{% render 'icon' icon:'account' strokeWidth:1 %}</span>
        {% endif %}
        <span>Account Details</span>
      </a> 
    </li>
    <li id="orderHistory" class="py-3 active:underline hover:underline" :class="selected === $el.id ? 'active' : ''">
      <a 
        class="cursor-pointer"
        @click="active = 'orderHistory'; selected = $el.parentElement.id; return false;"
        >
        {% if settings.icon_display_icon %}
          <span class="w-8 flex justify-center pr-2">{% render 'icon' icon:'custom-bag' strokeWidth:2 %} </span>
        {% endif %}
        <span>Orders</span>
      </a>
    </li>
    <li id="returns" class="py-3 active:underline hover:underline" :class="selected === $el.id ? 'active' : ''">
      <a class="cursor-pointer" href="{{ settings.returns_url }}"
        @click="active = 'returns'; selected = $el.parentElement.id;">
        {% if settings.icon_display_icon %}
          <span class="w-8 flex justify-center pr-2">{% render 'icon' icon:'package' strokeWidth:1 %} </span>
        {% endif %}
        <span>Returns</span>
      </a>
    </li>
    <li id="rewards" class="py-3 active:underline hover:underline" :class="selected === $el.id ? 'active' : ''">
      <a class="cursor-pointer" href="{{ settings.rewards_url }}"
        @click="active = 'rewards'; selected = $el.parentElement.id;">
        {% if settings.icon_display_icon %}
          <span class="w-8 flex justify-center pr-2">{% render 'icon' icon:'award' strokeWidth:1 %} </span>
        {% endif %}
        <span>Rewards</span>
      </a>
    </li>
    <li id="wishlist" class="py-3 active:underline hover:underline" :class="selected === $el.id ? 'active' : ''">
      <a class="cursor-pointer" {% unless template contains 'account' %}{% if settings.wishlist_url != blank %}href="{{ settings.wishlist_url }}"{% endif %}{% endunless %}
        @click="active = 'wishlist'; selected = $el.parentElement.id">
        {% if settings.icon_display_icon %}
          <span class="w-8 flex justify-center pr-2">{% render 'icon' icon:'heart' strokeWidth:1 %} </span>
        {% endif %}
        <span>Wishlist</span>
      </a>
    </li>
    <li id="deliveryAddresses"  class="py-3 active:underline hover:underline" :class="selected === $el.id ? 'active' : ''">
      <a class="cursor-pointer" href="/account/addresses"
        @click="active = 'Delivery Addresses'; selected = $el.parentElement.id; return false;">
        {% if settings.icon_display_icon %}
          <span class="w-8 flex justify-center pr-2">{% render 'icon' icon:'truck' strokeWidth:1 %} </span>
        {% endif %}
        <span>Delivery Addresses</span>
      </a>
    </li>
    <li id="logout" class="py-3 active:underline hover:underline" :class="selected === $el.id ? 'active' : ''">
      <a href="/account/logout" class="cursor-pointer">
        {% if settings.icon_display_icon %}
          <span class="w-8 flex justify-center pr-2">{% render 'icon' icon:'logout' strokeWidth:1 %} </span>
        {% endif %}
        <span>Logout</span>
      </a>
    </li>
  </ul>
</div>
