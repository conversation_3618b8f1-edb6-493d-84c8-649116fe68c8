<div class="account-block address-book">
	<header class="flex">
		<h2 class="account-block__title address-book__title mt-0">{{ title | default: settings.title | default: 'Addresses' }}</h2>
		<span class="ml-auto">
			<button class="button button--light button--w-icon" onclick="document.querySelector('.address-item__new').classList.toggle('active')">
				{% render 'icon' icon:'plus' %}
				<span class="button__text">
					{{ 'customer.addresses.add_new' | t }}
				</span>
			</button>
		</span>
	</header>


	<div class="grid grid-cols-1 lg:grid-cols-2 gap-sm mt-md address-items">
	{% assign limit = limit | default: settings.limit | default: 12 %}
	{% paginate customer.addresses by limit %}
	
	{% liquid 
		for address in customer.addresses
			render 'address-item' address:address
		endfor
	%}

	{% render 'address-item' %}

	</div>
	{% render 'pagination' paginate:paginate %}
	{% endpaginate %}
	
	{% if customer.addresses.size == 0 %}
	
		<div class="address-book-empty">
			<p class="text-sm">{{ 'customer.addresses.none' | t }}</p>
		</div>

	{% endif %}

	</div>

</div>

<style>
	.address-item__new:not(.active),
	.address-items:has(.address-item.active) .address-item:not(.active),
	.address-book:has(.address-item.active) > header,
	.address-item:not(.active) .address-item__edit
	{
		display:none!important;
	}
	.address-items:has(.address-item.active) 
	{
		grid-template-columns: repeat(1, minmax(0, 1fr));
	}
</style>
<script type="text/javascript">
	provinces = function(el){
		try {
			var provinces = JSON.parse(el.options[el.selectedIndex].dataset.provinces)
			var $provinces = el.closest('form').querySelector('[name="address[province]"]')
			$provinces.innerHTML = provinces.map(p=>`<option value="${p[0]}">${p[1]}</option>`).join(``);
			$provinces.value = $provinces.getAttribute('value')
		} catch(err){}
		
	}
	window.addEventListener('change', e=> {
		if (e.target.name != 'address[country]') return;
		provinces(e.target)
	})
	window.addEventListener('DOMContentLoaded', e=>{
		document.querySelectorAll('[name="address[country]"]').forEach(el=>provinces(el))
	})
</script>