<div 
  class="micro-upsell__buttons" 
  x-data="{
    selections:[],
    productsFound: false,
    initializeProductItems() {
      const wrapper = $el.closest('*:has(.micro-upsell__buttons):has(.product-item)');

      if (!wrapper || $data.productsFound) return false

      const productItems = wrapper.querySelectorAll('.product-item'),
          separator = $el.querySelector('[data-separator]') && $el.querySelector('[data-separator]').content,
          itemWrapper = $el.querySelector('[data-item-wrapper]').content;

      productItems.forEach(function(item, i) {
        const wrapper = itemWrapper.cloneNode(true).firstElementChild;
        const form = item.nextElementSibling

        productItems[i].parentNode.replaceChild(wrapper, productItems[i]);

        wrapper.appendChild(item)

        if (form.tagName == 'FOOTER') wrapper.appendChild(form)
      })
      
      const upsellItems = wrapper.querySelectorAll('.micro-upsell__item')

      if (!!separator) upsellItems.forEach(function(item, i) {
        if (i == upsellItems.length - 1) return false

        const separatorEl = separator.cloneNode(true).firstElementChild

        item.insertAdjacentElement('afterend', separatorEl)
      })

      $nextTick(() => {
        upsellItems.forEach((item) => {
          item.querySelector('input[type=checkbox]').addEventListener('change', () => $data.setSelections())
          item.querySelector('footer select').addEventListener('change', () => $data.setSelections())
        })
      })

      $data.productsFound = true
    },
    setSelections() {
      const wrapper = $el.closest('*:has(.micro-upsell__buttons):has(.product-item)');
      $data.selections = Util.array(wrapper.querySelectorAll('.micro-upsell__item:has(input:checked)')).map(item => {
        const select = item.querySelector('footer')
        return Alpine.evaluate(select,'$data')
      }) 
    }
  }"
  x-init="() => {
    $data.initializeProductItems()
    $data.setSelections()
  }"
>
  {% if settings.separator_content != blank %}
    <template data-separator>
      <span class="micro-upsell__separator {% render 'class-settings' settings:settings prefix:'separator_class' %}" style="{% render 'style-settings' settings:settings prefix:'separator_style' %}">
        {{ settings.separator_content }}
      </span>
    </template>
  {% endif %}

  <template data-item-wrapper>
    <div 
      class="micro-upsell__item self-stretch relative cursor-pointer {% render 'class-settings' prefix:'product_item_wrapper' settings:settings %}">
      <div class="absolute top-0 right-0 p-1.5 z-10 leading-[0] origin-top-right scale-150 lg:scale-125">
        <input type="checkbox" {% if settings.selected %}checked{% endif %} onchange="
          if (this.checked) this.closest('.micro-upsell__item').querySelector(`select`).focus();
        ">
      </div>
    </div>
  </template>

  <template x-if="selections.length == 0">
    <button class="micro-upsell__button--disabled button button--disabled" disabled>Select Products</button>
  </template>

  <template x-if="selections.length && selections.find(s => !s.variant)">
    <button class="micro-upsell__button--disabled button button--disabled" disabled>Select Sizes</button>
  </template>

  <template x-if="selections.length && !selections.find(s => !s.variant)">
    <button class="micro-upsell__button--active button button--primary" @click="Cart.add($data.selections.map(s => ({id:s.variant.id,properties:{_upc:s.variant.barcode,_source:`{{source_name | default: 'upsellblock'}}`}})))" x-text="`Add ${selections.length} to Cart`"></button>
  </template>
</div>
