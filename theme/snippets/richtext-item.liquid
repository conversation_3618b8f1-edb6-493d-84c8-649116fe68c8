<article 
  class="{% render 'class-settings' prefix:'item_class' settings:settings %}"
>
  {% if settings.title_text != blank or title_text != blank or title_attributes %}
    {% assign element = settings.title_element | default: settings.title_element | default: 'h2' %}
    <{{ element }} class="{% render 'class-settings' prefix:'title_class' settings:settings %} !mt-0" {{ title_attributes }}>{{ title_text | default: settings.title_text }}</{{ element}}>
  {% endif %}
  {% if settings.featured_content_text != blank or featured_content_text != blank or featured_content_attributes %}
    <div class="rte {% render 'class-settings' prefix:'featured_content_class' settings:settings %}" {{ featured_content_attributes }}>{{ featured_content_text | default: settings.featured_content_text }}</div>
  {% endif %}
</article>