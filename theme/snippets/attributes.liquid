{% liquid
  assign atts = ''
  for setting in _settings
    if setting contains prefix and _settings[setting] != blank
      assign att = setting | remove: prefix | remove_first: '_' | replace: '_', '-'
      assign value = _settings[setting]
      if att contains '-path'
        assign att = att | remove: '-path'
        assign value = item[value] | default: value
      endif
      # add alt text here
      if value.alt != blank
        assign atts = atts | append: 'alt="' | append: value.alt | append: '" '
      endif
      if value.src != blank
        assign value = value | image_url
      endif
      assign atts = atts | append: att | replace:'QQ', ':' | append: '="' | append: value | append: '" '
    endif
  endfor
  echo atts
%}
