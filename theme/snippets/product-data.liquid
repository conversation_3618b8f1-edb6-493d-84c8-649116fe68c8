{% if script_tag %}
  {% comment %}
  <script>
    {% if settings.enable_logs -%}console.log("metafields",{{section.settings.metafields | split: ',' | json }}){%- endif %}
    {% assign fields = section.settings.metafields | split: ',' %}
    {% for field in fields %}
      {% assign ns = field | split: '.' | first | strip %}
      {% assign key = field | split: '.' | last | strip %}
      {% assign _field = field | strip | json %}
      
      {% assign val = product.metafields[ns][key] | strip | json %}
      {% if val %}
        {{ _field | append: ':' | append: val | append: ',' }} 
      {% else %}
        {{ _field | append: ':' | append: "" }}
      {% endif %}
    {% endfor %}
  </script>
  {% endcomment %}
  <script product-data>
    window.products = window.products || {};
    window.products['{{ product.handle }}'] =
{% endif %}
    {
      "id":{% if product.id %}{{ product.id }}{% else %}""{% endif %},
      "handle":{{ product.handle | json }},
      "title":{{ product.title | json }},
      "available":{{ product.available | json }},
      {%- if include_description %}
        "description":{{ product.description | json }},
      {%- endif %}
      "color":{{ product.variants[0].option1 | json }},
      "tags":{{ product.tags | json }},
      "price":{% if product.price %}{{ product.price}}{% else %}null{% endif %},
      "price_min":{% if product.price_min %}{{ product.price_min}}{% else %}null{% endif %},
      "price_max":{% if product.price_max %}{{ product.price_max}}{% else %}null{% endif %},
      "compare_at_price":{{ product.compare_at_price | json }},
      {%- if include_media or include contains 'media' -%}
        "media":{{ product.media | json }},
        "images":[{% for image in product.images %}
          {
          "src":{{ image.src | json }},
          "alt":{{ image.alt | json }},
          "variants": {{ image.variants | map: 'id' | json }},
          "position": {{ image.position }}
          }{% unless forloop.last -%},{%- endunless -%}
        {% endfor %}],
      {%- endif %}
      {%- if include_variants or include contains 'variants' -%}
        {%- if include_inventory or include contains 'inventory' or include contains 'shipment_date' -%}
          "variants": [
          {%- liquid
          for variant in product.variants
            assign variant_data = variant | json
    
            if include_inventory or include contains 'inventory'
              assign inv_string = variant.inventory_quantity | prepend: '"inventory_quantity":' | append: ',"product":"' | append: product.handle | append:'","inventory_management"'
              assign variant_data = variant_data | replace: '"inventory_management"', inv_string
            endif
    
            if include contains 'shipment_date' and variant.metafields.custom.shipment_date
              assign shipment_date = ', "shipment_date": "' | append: variant.metafields.custom.shipment_date | append: '"}'
              assign variant_data = variant_data | replace: '}', shipment_date
            endif
    
            echo variant_data
            unless forloop.last
              echo ','
            endunless
          endfor 
        -%}
          ],
        {%- else -%}
          "variants": {{ product.variants | json }},
        {%- endif -%}
      {%- endif -%}
      "newmedia":[],
      "options":[],
      "_options": {{ product.options | json }},
      "options_by_name": {{ product.options_by_name | json }},
      "options_with_values": {{ product.options_with_values | json }},
    
      {% if preselect_variant -%}
        "selected_options": {{ product.selected_or_first_available_variant.options | json }},
        "preselect_variant":true,
      {%- else -%}
        "selected_options":[],
        "preselect_variant":false,
      {%- endif %}
    
      {% if preselect_option != blank %}
        "preselect_option":{{ preselect_option | json }},
      {% endif %}
    
      "variant_selection": {{ variant_selection | default: 'progressive' | json }},
      "sku": {{ product.selected_or_first_available_variant.sku | json }},
      "variant_title": {{ product.selected_or_first_available_variant.title | json }},
      "product_type": {{ product.type | json }},
      "brand": {{ product.vendor | json }},
      "swatch_image":"{% if product.metafields.product.swatch_image %}{{ product.metafields.product.swatch_image | img_url: '200x' }}{%  else %}{% endif %}",
      "swatch_color_1":"{{ product.metafields.product.swatch_color_1 }}",
      "swatch_color_2":"{{ product.metafields.product.swatch_color_2 }}",
      "swatch_color_3":"{{ product.metafields.product.swatch_color_3 }}",
      "featured_image": {{ product.featured_image.src | json }},
      "featured_image_aspect_ratio": {{ product.images[0].aspect_ratio | json }},
      "featured_image_width": {{ product.images[0].width | json }},
      "featured_image_height": {{ product.images[0].height | json }},
      "featured_image_id": {{ product.featured_image.id | json }},
      "featured_image_alt": {{ product.featured_image.alt | json }},
      "hover_image": {{ product.images[1].src | json }},
      "hover_image_alt": {{ product.images[1].alt | json }},
      "product_video": "{{ product.metafields.product.product_gallery_video }}",
      {% for hover_image in product.images -%}
        {% if hover_image.alt contains 'lifestyle' -%}
          "hover_image": {{ hover_image.src | json }},
          "hover_image_alt": {{ hover_image.alt | json }},
          {% break %}
        {%- endif %}
      {%- endfor %}
      {% if history_state -%}
        "history_state":true,
      {%- endif %}
      "_metafields":{{ metafields | split: ',' | json }},
      "metafields":{
        {% liquid 
          assign fields = section.settings.metafields | split: ','
          for field in fields
          assign ns = field | split: '.' | first | strip
          assign key = field | split: '.' | last | strip
          assign _field = field | strip | json
          assign val = product.metafields[ns][key] | strip | json 
          if val
          echo _field | append: ':' | append: val 
          unless forloop.last
          echo ','
          endunless
          else
          echo _field | append: ':' | append: " " 
          unless forloop.last
          echo ','
          endunless
          endif
          endfor
        %}
      },
      product_features: '{{product.metafields.product.style.value.features_list | metafield_tag}}',
      "url":{{ product.url | json }},
      "badge_image":"{% if product.metafields.custom.product_badge_image.value -%}{{ product.metafields.custom.product_badge_image.value | image_url: width: 200 }}{%- endif %}",
      "color_groups": [
        {% if product.metafields.custom.color_groups %}
          {% assign color_groups = product.metafields.custom.color_groups.value %}
          {% for group in color_groups %}
            {
              "name": {{ group.name | json }},
              "family": {{ group.family | json }},
              "handle": {{ group.name | handleize | json }},
              "display_order": {{ group.display_order | json }},
              "products": [
                {% for product in group.color_products.value %}
                  {{ product.id | json }}{% unless forloop.last %},{% endunless %}
                {% endfor %}
              ]
            }{% unless forloop.last %},{% endunless %}
          {% endfor %}
        {% endif %}
      ]
    }
{% if script_tag %};
  </script>
{% endif %}
