{% if settings.inclusion_js != blank %}
<template x-if="{{ settings.inclusion_js }}">
{% endif %}
<div class="announcement swiper-slide w-full">
  {% if settings.link != blank %}
  <a href="{{ settings.link }}" aria-label="Announcement {{ settings.link | split: '/' | last | replace: '-', ' ' }}">
  {% endif %}
  <span class="inline-flex items-center gap-4 {% if settings.text_desktop != blank %}lg:hidden{% endif %}">
    {% render 'inline-icon-text' source: settings.text %}

    {% if settings.image_size != blank %}
      {% assign dimensions = settings.image_size | split: 'x' %}
      {% assign width = dimensions[0] %}
      {% assign height = dimensions[1] %}
    {% endif %}
  
    {% if settings.image != blank %}
      <img src="{{ settings.image | img_url: 'master' }}" alt="" style="{% if settings.image_size != blank %}width:{{ width }}px; height:{{ height }}px;{% endif %}">
    {% endif %}
    
    {% if settings.svg != blank %}
      <div style="{% if settings.image_size != blank %}width:{{ width }}px; height:{{ height }}px;{% endif %}">
        {{ settings.svg | safe }}
      </div>
    {% endif %}

  </span>
  {% if settings.text_desktop != blank %}
    <span class="hidden lg:inline-flex lg:items-center lg:gap-4">
      {% render 'inline-icon-text' source: settings.text_desktop %}
    </span>
  {% endif %}

  {{ settings.liquid }}
  {% if settings.link != blank %}
  </a>
  {% endif %}
</div>
{% if settings.inclusion_js != blank %}
</template>
{% endif %}
