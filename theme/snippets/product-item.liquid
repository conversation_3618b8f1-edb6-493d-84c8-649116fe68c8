{%- liquid 
  
  if settings.product_item_settings
    assign settings = settings
  else 
    assign settings = _settings
  endif

  if variants 
    assign include_variants = variants
  else
    assign include_variants = false
  endif

-%} 
{% assign theme_swatch_option_location = false %}
{% assign section_swatch_option_location = false %}
{% if _settings.product_color_option_type == 'image' or _settings.product_color_swatch_page == 'only_pdp' %}
	{% if settings.product_color_option_type == 'admin_setting' %}
		{% assign theme_swatch_option_location = true %}
	{% endif %}
{% endif %}
{% if settings.product_color_option_type == 'image' %}
	{% assign section_swatch_option_location = true %}
{% endif %}

{% assign theme_swatch_option_type = false %}
{% assign section_swatch_option_type = false %}
{% if _settings.product_color_option_type == 'swatch' or _settings.product_color_option_type == 'swatch_image' %}
	{% if settings.product_color_option_type == 'admin_setting' %}
		{% assign theme_swatch_option_type = true %}
	{% endif %}
{% endif %}
{% if settings.product_color_option_type == 'swatch' or settings.product_color_option_type == 'swatch_image' %}
	{% assign section_swatch_option_type = true %}
{% endif %}

{% assign theme_option_type_swatch_image = false %}
{% if settings.product_color_option_type == 'admin_setting' and _settings.product_color_option_type == 'swatch_image' %}
	{% assign theme_option_type_swatch_image = true %}
{% endif %}
<a 
  {% if product -%}
    x-data="{ 
      product: {}
    }"
    x-init="product = JSON.parse($el.querySelector('[product-data]').innerHTML)"
  {%- endif %}
  {%- if skeleton -%}
    x-data="{
      product: { handle: '', variants: [] },
      minPrice: null,
      maxPrice: null
    }"
  {%- endif -%}
  prefetch 
  :class="{ 'no-model-image': !(product.hover_image || (product.images && product.images[1] && product.images[1].src)) }"
  class="product-item group {% unless settings.product_item_show_swatches == true %}group/image {% endunless %}{{ settings.classes_product_item }} {{ class }} {% render 'class-settings' prefix:'product_item_class' settings:settings %}"
  :href="`{{ routes.root_url }}products/${product && product.handle}${ (product.variants && !!product.variants.find(v=>v.available)) ? `?variant=${product.variants.find(v=>v.available).id}`:``}`" 
  {{ attributes }}
>

  {% if product %}<template product-data>{ {%- render 'product-item-data' product: product, include_variants:include_variants include_inventory:include_variants -%} }</template>{% endif %}
  <div 
    x-data="{
      swapImages() {
        if(typeof collection !== `undefined` && product.images[1]){
          [product.images[0], product.images[1]] = [product.images[1], product.images[0]];
        }
      }
    }"
    x-init="
      if (typeof collection !== 'undefined') {
        $watch('collection.view', value => {
          $nextTick(() => {
            swapImages();
          });
        })
      }
    "
  class="product-item__images {% if settings.product_item_show_swatches == true %}group/image{% endif %} aspect-ratio aspect-h-1 aspect-w-1 {{ settings.classes_product_item_image_wrapper }} relative">

    <template x-if="product && (product.featured_image || (product.images && product.images[0] && product.images[0].src))">
      <div>
        <template x-if="product">
          <div>
            {%- if section.settings.default_image_type == 'model_image' -%}
              {% render 'image'
                widths: '200,400,600'
                attributes: '
                  x-data="{
                    isPriority() {
                      const el = this.$el.closest(`[data-index]`);
                      const index = el ? parseInt(el.dataset.index) : 0;
                      const priority = window.innerWidth >= 1024 ? index < 3 : index < 2;
                      return priority
                    }
                  }"
                  :src="product.hover_image || (product.images && product.images[1] && product.images[1].src) || product.featured_image || product.images[0].src"
                  :alt="product.title"
                  :data-priority="$data.isPriority()"
                  :fetchpriority="$data.isPriority() ? `high` : `low`"
                  :loading="$data.isPriority() ? `eager` : `lazy`"
                  :decoding="$data.isPriority() ? `sync` : `async`"
                  :class="`product-item__image-main${product.hover_image || (product.images && product.images[1]) ? ` has-hover` : ``}`"
                  :srcset="(() => {
                    const widths = [200, 400, 600];
                    const src = product.hover_image || (product.images && product.images[1] && product.images[1].src) || product.featured_image || product.images[0].src;
                    return widths.map(w => `${src}&width=${w} ${w}w`).join(`, `);
                  })()"
                '
                class: 'product-item__image h-full w-full object-contain object-center absolute inset-0'
                sizes: '(min-width: 1536px) 384px, (min-width: 1024px) 338px, (min-width: 768px) 384px, 200px'
              %}
              {% else %}
              {% render 'image'
                widths: '200,400,600'
                attributes: '
                  x-data="{
                    isPriority() {
                      const el = this.$el.closest(`[data-index]`);
                      const index = el ? parseInt(el.dataset.index) : 0;
                      const priority = window.innerWidth >= 1024 ? index < 3 : index < 2;
                      return priority
                    }
                  }"
                  :src="product.featured_image || product.images[0].src"
                  :alt="product.title"
                  :data-priority="$data.isPriority()"
                  :fetchpriority="$data.isPriority() ? `high` : `low`"
                  :loading="$data.isPriority() ? `eager` : `lazy`"
                  :decoding="$data.isPriority() ? `sync` : `async`"
                  :class="`product-item__image-main${product.hover_image || (product.images && product.images[1]) ? ` has-hover` : ``}`"
                  :srcset="(() => {
                    const widths = [200, 400, 600];
                    const src = product.featured_image || product.images[0].src;
                    return widths.map(w => `${src}&width=${w} ${w}w`).join(`, `);
                  })()"
                '
                class: 'product-item__image h-full w-full object-contain object-center absolute inset-0'
                sizes: '(min-width: 1536px) 384px, (min-width: 1024px) 338px, (min-width: 768px) 384px, 200px'
              %}
            {%- endif -%}
            <template x-if="product.hover_image || (product.images && product.images[1] && product.images[1].src)">
              {%- if section.settings.default_image_type == 'model_image' -%}
                {% render 'image'
                  widths: '200,400,600'
                  attributes: '
                    :src="product.featured_image || product.images[0].src"
                    :alt="product.title"
                    :srcset="(() => {
                      const widths = [200, 400, 600];
                      const src = product.featured_image || product.images[0].src;
                      return widths.map(w => `${src}&width=${w} ${w}w`).join(`, `);
                    })()"
                  '
                  class: 'product-item__hover-image h-full w-full object-contain object-center opacity-0 absolute inset-0 transition-opacity'
                  sizes: '(min-width: 1536px) 384px, (min-width: 1024px) 338px, (min-width: 768px) 384px, 200px'
                loading: 'lazy'
              %}
              {% else %}
                {% render 'image'
                  widths: '200,400,600'
                  attributes: '
                    :src="product.hover_image || (product.images && product.images[1] && product.images[1].src)"
                    :alt="product.title"
                    :srcset="(() => {
                      const widths = [200, 400, 600];
                      const src = product.hover_image || (product.images && product.images[1] && product.images[1].src);
                      return widths.map(w => `${src}&width=${w} ${w}w`).join(`, `);
                    })()"
                  '
                  class: 'product-item__hover-image h-full w-full object-contain object-center opacity-0 absolute inset-0 transition-opacity'
                  sizes: '(min-width: 1536px) 384px, (min-width: 1024px) 338px, (min-width: 768px) 384px, 200px'
                loading: 'lazy'
                %}
            {%- endif -%}
            </template>
          </div>
        </template>
      </div>
    </template>
    
    <template x-if="product && ((product.images && product.images[0] && product.images[0].src) && (!product.hover_image && !product.images[1]) && (product.featured_image != product.images[0]))">
      {% render 'image' 
        widths:'200,400,600' 
        attributes: '
          :src="product.images[0].src" 
          :alt="product.title"
          fetchpriority="low"
          decode="async"
          :srcset="(() => {
            const widths = [200, 400, 600];
            const src = product.images[0].src;
            return widths.map(w => `${src}&width=${w} ${w}w`).join(`, `);
          })()"
        ' 
        class: 'product-item__hover-image h-full w-full object-contain object-center opacity-0 absolute inset-0 transition-opacity'
        sizes: '(min-width: 1024px) 25vw, 50vw'
        loading: 'lazy'
      %}
    </template>

    {% unless quickadd == false %}
    {%- liquid   
      if section.settings.single_variant_direct_addtocart 
      assign quick_add_click_attributes = '@click="e => {e.preventDefault(); (product.variants && product.variants.length === 1) ? Cart.add(product.variants[0].id) : QuickAdd.open(product, null, e);}"'
      else 
        assign quick_add_click_attributes = '@click="e => {e.preventDefault(); QuickAdd.open(product, null, e);}"'
      endif
      assign trigger_classes = 'product-item__quick-add w-full'
      if settings.quickadd_trigger_full
        assign trigger_classes = trigger_classes | append: ' product-item__quick-add--full'
      endif
    -%} 

    {%- if settings.product_item_quick_add_position_desktop == 'image' or settings.product_item_quick_add_position_mobile == 'image' -%}
      {%- if settings.product_item_quick_add_position_desktop != 'image' -%}
        {% assign trigger_classes = trigger_classes | append: ' lg:hover:!hidden' %}
      {%- elsif settings.product_item_quick_add_position_mobile != 'image' -%}
        {% assign trigger_classes = trigger_classes | append: ' !hidden lg:group-hover:!flex' %}
      {%- endif -%}

      {%- render 'button' 
        style: 'secondary' 
        tag:'button' 
        content: 'Quick Add' 
        leading_icon: 'plus' 
        trailing_icon: 'quick-add' 
        class: trigger_classes
        attributes: quick_add_click_attributes
      -%}
    {%- endif -%}

    {% endunless %}
    {% render 'product-badges' location:location %}
  </div>

  {% if settings.product_item_show_swatches %}
  <template x-if="product.siblings">
    <div class="product-item__swatches flex gap-2 overflow-x-scroll relative no-scrollbar snap-x snap-mandatory" 
    data-width="{{ settings.product_item_swatches_view | default: 3.2 }}"
    data-width-lg="{{ settings.product_item_swatches_view_desktop | default: 5 }}"
    {% if settings.product_item_swatch_interaction == 'click' %}
    @click.prevent="product = selectedSibling;"
    {% endif %}
     x-data="{
       wrapper: $el,
       selectedSibling:product.siblings[0],
       swatchWidth: '33%',
       swatchGap: 8,
       scrollable: false,
       currentScroll: 0,
       centerSwatch(el) {
        if ($data.wrapper.scrollWidth <= $data.wrapper.clientWidth) return false

        const wrapperRect = $data.wrapper.getBoundingClientRect()
        const elRect = el.getBoundingClientRect()
        const centerPoint = wrapperRect.x + (wrapperRect.width / 2)

        $data.wrapper.scrollTo({
          left: $data.wrapper.scrollLeft + (elRect.x - centerPoint + (elRect.width / 2)),
          behavior: 'smooth'
        })
        },
        sizeSwatches() {
          let perView = parseFloat($el.dataset.width)
          if (window.innerWidth >= 1024) perView = parseFloat($el.dataset.widthLg)
          let newWidth = ($el.clientWidth - ($data.swatchGap * (Math.floor(perView) - 1))) / perView
          if (newWidth < 0) return false
          $data.swatchWidth = newWidth
          $data.scrollable = $data.product.siblings.filter(s => !!s.available).length > perView
          productHeight = `${$refs.productItem.getBoundingClientRect().height}px`
        },
        scroll(direction = 'forward') {
          let amount = 2 * ($data.swatchWidth + $data.swatchGap)
          $data.wrapper.scrollTo({ 
            left: direction == 'forward' ? $data.wrapper.scrollLeft + amount : $data.wrapper.scrollLeft - amount, 
            behavior:'smooth' 
          })
        }
     }"
     x-init="() => {
      $data.sizeSwatches()
     }"
     @resize.window="$data.sizeSwatches"
     @scroll="$data.currentScroll = $data.wrapper.scrollLeft"
   >
    <button 
      class="product-item__swatch-button product-item__swatch-button--prev sticky self-center"
      @click.prevent="$data.scroll('backward')"
      x-show="scrollable && $data.currentScroll > 10 && window.innerWidth > 1023"
      aria-label="Previous"
    >
      {% render 'icon' icon: 'chevron-left' %}
      <span class="sr-only">Previous</span>
    </button>
    <template x-for="(sibling, siblingIndex) in product.siblings">
      {% if settings.product_item_show_oos_siblings == false %}<template x-if="sibling.available">{% endif %}
        <button 
          :data-index="siblingIndex"
          :style="`width: ${$data.swatchWidth}px;height: ${$data.swatchWidth}px;`"
          :aria-label="`select-${sibling.title.replace(/[^a-zA-Z ]/g,'')}`"
          class="product-item__swatch flex flex-shrink-0 snap-start items-center justify-center {% if settings.product_color_option_type == 'admin_setting' and  _settings.product_color_option_type == 'swatch_image' or _settings.product_color_option_type == 'swatch' and _settings.product_color_swatch_page != 'only_pdp'  %}rounded-full{% endif %} {% if settings.product_color_option_type != 'admin_setting' and settings.product_color_option_type == 'swatch_image' or settings.product_color_option_type == 'swatch' %}rounded-full{% endif %}"
          :class="{ 'active': sibling.handle == product.handle }"
          {% if settings.product_item_swatch_interaction == 'hover' %}
          @mouseenter="product = sibling;"
          @click="window.innerWidth < 1024 ? ($event.preventDefault(), product = sibling, centerSwatch($el)) : product = sibling;" 
          {% else %}
          @mouseenter="selectedSibling = sibling;"
          @click.prevent="product = sibling; centerSwatch($el);"
          {% endif %}
        >
          {% if theme_swatch_option_location or section_swatch_option_location %}
            {% render 'image'
              widths: '48,96,192'
              attributes: '
                :src="sibling.featuredImage.url" 
                :alt="sibling.featuredImage.altText"
                :data-priority="isPriority()"
                :data-sibling-index="siblingIndex"
                fetchpriority="low"
                loading="lazy"
                x-bind:fetchpriority="isPriority() && siblingIndex < 6 ? `high` : `low`"
                x-bind:loading="isPriority() && siblingIndex < 6 ? `eager` : `lazy`"
                decoding="async"
                x-data="{
                  isPriority() {
                    const el = this.$el.closest(`[data-index]`);
                    const index = el ? parseInt(el.dataset.index) : 0;
                    return window.innerWidth >= 1024 ? index < 3 : index < 2
                  }
                }"
                :srcset="() => {
                  const widths = [`48`,`96`,`192`]
                  return widths.map(w => `${sibling.featuredImage.url}&width=${w} ${w}w`).join(`, `)
                }"
              '
              class: 'w-full h-full object-cover object-center'
              sizes: '(min-width: 1536px) 96px, (min-width: 1024px) 48px, 64px'
              src: 'sibling.featuredImage.url'
            %}
          {% elsif theme_swatch_option_type or section_swatch_option_type %}
            <template x-if="{% if theme_option_type_swatch_image or settings.product_color_option_type == 'swatch_image' %}!((sibling.metafields.find(m => m?.key === 'swatch_image') && sibling.metafields.find(m => m?.key === 'swatch_image')?.reference.image.url != '')){% else %}true{% endif %}">
              <div 
                class="color_swatch"
                x-data="{
                  gradient_color_1:false,
                  gradient_color_2:false,
                  gradient_color_3:false,
                  hasSwatchColors() {
                    sibling.metafields?.forEach((elm) =>{
                      if (elm && elm.key) {
                        if(elm.key == 'swatch_color_1'){
                          this.gradient_color_1 = elm.value.trim()
                        } else if(elm.key == 'swatch_color_2'){
                          this.gradient_color_2 = elm.value.trim()
                        } else if(elm.key == 'swatch_color_3'){
                          this.gradient_color_3 = elm.value.trim()
                        }
                      }
                    })
                    return this.gradient_color_1 !== false || this.gradient_color_2 !== false || this.gradient_color_3 !== false;
                  },
                  backgroundStyle() {
                    if (this.gradient_color_1 && this.gradient_color_2 && this.gradient_color_3) {
                      return `linear-gradient(to right, ${this.gradient_color_1} 33%, ${this.gradient_color_2} 33%, ${this.gradient_color_2} 66%, ${this.gradient_color_3} 66%)`;
                    } else if (this.gradient_color_1 && this.gradient_color_2) {
                      return `linear-gradient(135deg, ${this.gradient_color_1} 50%, ${this.gradient_color_2} 50%)`;
                    } else if (this.gradient_color_1) {
                      return this.gradient_color_1;
                    } else {
                      return '';
                    }
                  }
                }"
  
                x-init=""
                :style="{ background: hasSwatchColors() ? backgroundStyle() : '{{ _settings.product_default_swatch_color_1 }}',
                  width:`${$data.swatchWidth-2}px`,
                  height:`${$data.swatchWidth-2}px` }"
                style="margin: 0px auto;border-radius: 50%;position: relative;border: 1px solid rgb(211, 212, 213);"
              >
              </div>
            </template>
          {% if theme_option_type_swatch_image or settings.product_color_option_type == 'swatch_image' %}
            <template x-if="(sibling.metafields.find(m => m?.key === 'swatch_image') && sibling.metafields.find(m => m?.key === 'swatch_image')?.reference.image.url != '')">
              {% render 'image'
                widths: '48,96,192'
                attributes: '
                  :src="sibling.metafields.find(m => m?.key === `swatch_image`)?.reference.image.url"
                  alt="product-swatch-image"
                  loading="lazy"
                  :srcset="() => {
                    const widths = [`48`,`96`,`192`]
                    return widths.map(w => `${sibling.metafields.find(m => m?.key === `swatch_image`)?.reference.image.url}&width=${w} ${w}w`).join(`, `)
                  }"
                  style="margin: 0px auto;border-radius: 50%;position: relative;border: 1px solid rgb(211, 212, 213);"
                '
                class: 'color_swatch'
                sizes: '(min-width: 1536px) 96px, (min-width: 1024px) 48px, 64px'
              %}
            </template>
            <style>
              .product-form__option:not(.product-form__option--color) .color_swatch {
                display: none;
              }
            </style>
            {% endif %}
          {% endif %}
        </button>
      {% if settings.product_item_show_oos_siblings == false %}</template>{% endif %}
    </template>
    <button 
      class="product-item__swatch-button product-item__swatch-button--next sticky self-center"
      @click.prevent="$data.scroll()"
      x-show="scrollable && ($data.currentScroll + $data.wrapper.clientWidth) < ($data.wrapper.scrollWidth - 10) && window.innerWidth > 1023"
      aria-label="Next"
    >
      {% render 'icon' icon: 'chevron-right' %}
      <span class="sr-only">Next</span>
    </button>
    </div>
  </template>

  <template x-if="product && typeof product.siblings == 'undefined'">
    <div 
      x-data="{
        width: ($el.closest('.product-item').offsetWidth / parseFloat(window.innerWidth > 1024 ? $el.dataset.widthLg : $el.dataset.widthLg )) + 'px'
      }"
      data-width="{{ settings.product_item_swatches_view | default: 3.2 }}"
      data-width-lg="{{ settings.product_item_swatches_view_desktop | default: 5 }}"
      :style="{ height: $data.width }"
    >
    </div>
  </template>
  {% endif %}

  <div class="product-item__meta {{ settings.product_item_info_layout }}">

    <!-- <pre>{{ settings | json }}</pre> --> 
    {%- assign skeleton_text = '<span class="opacity-0" aria-hidden="true">Skeleton Text</span>' -%}
    {%- assign initialized_product = "product.handle != ''" -%}
    {%- assign uninitialized_product = "product.handle == ''" -%}

    {% if settings.product_item_show_title %}
      <div class="product-item__title-price-wrap">
        <template x-if="{{ initialized_product }}">    
          <h3 class="product-item__title type-item m-0 {{ settings.classes_product_item_title }}" x-text="{{ settings.product_item_title_source }}">{{ skeleton_text }}</h3>
        </template>
        <h3 class="product-item__title type-item m-0 {{ settings.classes_product_item_title }}" x-show="{{ uninitialized_product }}">{{ skeleton_text }}</h3>
        {% if section.settings.product_item_show_price_right == 'right' %}
        {% if settings.product_item_show_price %}        
          <div class="product-item__prices product-item__prices--right m-0">          
             
              <div>
                <template x-if="product.type === 'Gift Card' && typeof(product.variants) == 'undefined' && typeof(minPrice) != 'undefined'">    
                    <span class="product-item__price type-item {{ settings.classes_product_item_price }}" x-text="money.format(minPrice) + ' - ' + money.format(maxPrice)"></span>
                </template>
                <template x-if="product.type === 'Gift Card' && typeof(product.variants) == 'undefined'">    
                    <span class="product-item__price type-item {{ settings.classes_product_item_price }}" x-text="money.format(product.price_min) + ' - ' + money.format(product.price_max)"></span>
                </template>
                <template x-if="product.type !== 'Gift Card' && (product.price || (product.variants[0] && product.variants[0].price))">    
                  <span class="product-item__price type-item {{ settings.classes_product_item_price }}" x-bind:class="{ 'product-item__price_with_compare': product.compare_at_price }" x-text="money.format(product.price || product.variants[0].price)"></span>
                </template>   
                <template x-if="{{ initialized_product }} && product.compare_at_price">
                  <s class="product-item__compare-at-price type-item {{ settings.classes_product_item_price }}" x-text="money.format(product.compare_at_price)"></s>
                </template> 
                <span class="product-item__price type-item {{ settings.classes_product_item_price }}" x-show="{{ uninitialized_product }}">{{ skeleton_text }}</span>
              </div>      
          </div>    
        {% endif %}
        {% endif %}
      </div>
    {% endif %}
    
    {% if settings.product_item_show_subtitle %}
    <template x-if="{{ initialized_product }} && {{ settings.product_item_subtitle_source }}">
      <h4 class="product-item__subtitle type-item  m-0 {{ settings.classes_product_item_subtitle }}" x-text="{{ settings.product_item_subtitle_source }}"></h4>
    </template>
    <h4 class="product-item__subtitle type-item  m-0 {{ settings.classes_product_item_subtitle }}" x-show="{{ uninitialized_product }}">{{ skeleton_text }}</h4>
    {% endif %}

    {% if settings.product_item_show_type %}
      {% if template.name == 'product' %}
        <template x-if="{{ initialized_product }} && {% if settings.product_item_type_source %}{{ settings.product_item_type_source }}{% else %}false{% endif %}">
          <h4 class="product-item__type type-item m-0 {{ settings.classes_product_item_type }}" x-data="{ subtitle: '' }" x-text="subtitle" x-init="setTimeout(() => { subtitle = product.subtitle }, 200)">
          </h4>
        </template>
      {% else %}
        <template x-if="{{ initialized_product }} && {% if settings.product_item_type_source %}{{ settings.product_item_type_source }}{% else %}false{% endif %}">
          <h4 class="product-item__type type-item m-0 {{ settings.classes_product_item_type }}" x-text="{{ settings.product_item_type_source }}"></h4>
        </template>
      {% endif %}
    {% endif %}

    {% comment %} <template x-if="product.variants && product.variants.length > 1">
      <h4 class="product-item__style type-item m-0 {{ settings.classes_product_item_subtitle }}" x-text="() => {
        const colors = [...new Set(product.variants.map(v => v.option1))].length

        if (colors == 1) return colors + ' color'
        return colors + ' colors'

       }"></h4>
    </template> {% endcomment %}
    
    {% if section.settings.product_item_show_price_right == 'bottom' or section.settings.product_item_show_price_right == null %}
      {% if settings.product_item_show_price %}
        <div class="product-item__prices m-0">
       
          <div x-data="{             
              minPrice: null,
              maxPrice: null,
              calculatePrices: function() {
                this.minPrice = Math.min(...this.product.variants?.filter(v => v.available).map(v => v.price));
                this.maxPrice = Math.max(...this.product.variants?.filter(v => v.available).map(v => v.price));
              } 
            }" x-init="if ({{ initialized_product }} && product.variants) calculatePrices()">
            <template x-if="product.type === 'Gift Card' && typeof(product.variants) != 'undefined'">
              <span class="product-item__price type-item {{ settings.classes_product_item_price }}" x-text="money.format(minPrice) + ' - ' + money.format(maxPrice)"></span>
            </template>
            <template x-if="product.type === 'Gift Card' && typeof(product.variants) == 'undefined'">
              <span class="product-item__price type-item {{ settings.classes_product_item_price }}" x-text="money.format(product.price_min) + ' - ' + money.format(product.price_max)"></span>
            </template>

            <template x-if="{{ initialized_product }} && product.type !== 'Gift Card' && (product.price || (product.variants[0] && product.variants[0].price))">        
              <span class="product-item__price type-item {{ settings.classes_product_item_price }}" x-bind:class="{ 'product-item__price_with_compare': product.compare_at_price }" x-text="money.format(product.price || product.variants[0].price)"></span>
            </template>  
            
            <template x-if="{{ initialized_product }} && product.compare_at_price">
              <s class="product-item__compare-at-price type-item {{ settings.classes_product_item_price }}" x-text="money.format(product.compare_at_price)"></s>
            </template>
            <span class="product-item__price type-item {{ settings.classes_product_item_price }}" x-show="{{ uninitialized_product }}">{{ skeleton_text }}</span>
          </div>          
        </div>
      {% endif %}
  {% endif %}

    {%- if settings.product_item_additional_liquid -%}
      <div class="product-item__additional-liquid">{{ settings.product_item_additional_liquid }}</div>
    {%- endif -%} 
    
    {% if settings.product_item_show_reviews and skeleton != true %}
      {% render 'review-summary' product:product, sku_path:'product.variants[0].sku'%}
    {%- endif -%} 
    
    {% unless quickadd == false %}
      {%- if settings.product_item_quick_add_position_desktop == 'info' or settings.product_item_quick_add_position_mobile == 'info' -%}
        {%- if settings.product_item_quick_add_position_desktop != 'info' -%}
          {% assign trigger_classes = trigger_classes | append: ' lg:!hidden' %}
        {%- elsif settings.product_item_quick_add_position_mobile != 'info' -%}
          {% assign trigger_classes = trigger_classes | append: ' !hidden lg:!flex' %}
        {%- endif -%}

        {%- render 'button' 
          style: 'secondary' 
          tag:'button' 
          content: 'Quick Add' 
          leading_icon: 'plus' 
          trailing_icon: 'quick-add' 
          class: trigger_classes
          attributes: quick_add_click_attributes
        -%}
      {% endif %}
    {% endunless %}
  </div>
</a>

{% if form %}
  <footer class="product-item__actions" 
    x-data="{variant:false, product:false}" 
    @click.stop="" 
    x-init="() => {
      $data.product = Alpine.evaluate($el.previousElementSibling, '$data.product');
      if ($data.product.variants.length == 1) $data.variant = $data.product.variants[0];
    }"
  >
    <template x-if="product.variants.length > 1">
      <div class="field">
        <select @change="$data.variant=$data.product.variants[Number($el.value)]">
          <option selected disabled>{{ 'products.product.variant_selection' | t }}</option>
          <template x-for="(variant, vi) in $data.product.variants">
            <template x-if="variant.available">
              <option :value="vi" x-text="variant.title.split('/').at(-1)" :disabled="!variant.available"></option>
            </template>
          </template>
        </select>
      </div>
    </template>
    <button class="button button--primary" :disabled="!variant || !variant.available" @click.stop="Cart.add({id:variant.id,properties:{_upc:variant.barcode,_source:'{{source_name}}'}})" aria-label="add-to-cart" role="button">
      Add {% render 'icon' icon:'plus' %}
    </button>
  </footer>
{% endif %}
