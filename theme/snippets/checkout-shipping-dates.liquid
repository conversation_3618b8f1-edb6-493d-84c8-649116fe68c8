<script>
    let shippingMapConfigJson = '{{ settings.shipping_map | strip_html | strip_newlines }}';
    jQuery(document).on(`page:load page:change`, function() {
        /* check presale and show getEstimatedShippingDates*/
        let length = 0;
        let presale_length = 0;
        {% for line_item in checkout.line_items %}
            var line_item_sku = '{{line_item.sku}}';
            if(line_item_sku.indexOf('PRESALE') != -1){presale_length++;}
            length = {{ forloop.length }};
        {% endfor %}
        /* check presale and show getEstimatedShippingDates*/
        if(length === presale_length){
            console.log('length === presale_length');
        }else{
            console.log('length != presale_length');
            setLoadingDots();
            let counter = 0;
            let i = setInterval(function(){
                if( $('[data-shipping-method-label-title]').length > 0 && checkShippingSettingJson(shippingMapConfigJson)){
                    clearInterval(i);			
                    getEstimatedShippingDates();
                }
                counter++;
                if(counter == 30) {
                    clearInterval(i);
                }
            }, 1000);
    }
    });
    function setLoadingDots(){
        $('.section--shipping-method .content-box').append('<span id="loading_modal_overlay" class="hide_me"></span>');
    
        $( "[data-shipping-method-label-title]" ).append(`<span class="loading_dots_msg"><span class="content-box__emphasis loading_dots">{{ settings.shipping_text_loading_message }}<span class='ldot'>.</span><span class='ldot'>.</span><span class='ldot'>.</span></span></span>`);
    
        let checkwidth = parseInt(jQuery('.radio__label__accessory').outerWidth()) + parseInt(jQuery('.loading_dots_msg').outerWidth());
        let nameWidth = jQuery('.radio__label').outerWidth() - checkwidth;
        nameWidth = nameWidth - 1;
        jQuery('[data-shipping-method-label-title]').css('width',nameWidth+"px");
    }
                        
    let days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    let holidays = [];
    let holidays_dayof_year = [];
    let holidayArrayLength = holidays.length;
    for (let i = 0; i < holidayArrayLength; i++) {
        holidays_dayof_year.push(dayFromDate(holidays[i]));
    }
    
    let all_blackout_dates = null;
    {% if settings.blackout_dates %}
        all_blackout_dates = '{{ settings.blackout_dates }}';
    {% endif %}
    
    let shippingConfigJson = '{{ settings.shipping_map | strip_html | strip_newlines }}';
    if(checkShippingSettingJson(shippingConfigJson)){
        shippingConfigJson = JSON.parse(shippingConfigJson);
        // console.log('bugfix/shipping-json-weekends - settings.shipping_map', shippingConfigJson)
    }
    let carrierUsedArray = [];
    let allShippingMethodsArray = [];
    let allShippingMethodsConfigArray = [];
    
    let daysName = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    let MonthName = ["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sept","Oct","Nov","Dec"];
    var standardDate = false;
    
    var selectedState;
    selectedState = "{{ checkout.shipping_address.province_code }}";
    var expeditedHideExclusionStates;
    expeditedHideExclusionStates = [{{ settings.exclude_states }}];
    
    function getEstimatedShippingDates() {
        $( ".radio__label__primary" ).each(function( index ) {
            allShippingMethodsArray.push($( this ).attr('data-shipping-method-label-title'));
        });
        $.each(shippingConfigJson.shipping_map, function(i, shipping_map_obj) {
            $.each(shipping_map_obj.shipping_methods, function(i, shipping_methods_obj) {
                if(allShippingMethodsArray.indexOf(shipping_methods_obj.shopify_title) != -1){
                    carrierUsedArray.push(shipping_map_obj.carrier);
                    return false;
                }
            });
        });
    
        $.each(shippingConfigJson.shipping_map, function(i, shipping_map_obj) {
            $.each(shipping_map_obj.shipping_methods, function(i, shipping_methods_obj) {
                allShippingMethodsConfigArray[shipping_methods_obj.shopify_title] = [shipping_methods_obj.carrier_code, shipping_methods_obj.delay, shipping_methods_obj.delay_end, shipping_methods_obj.date_show, shipping_methods_obj.date_hide ,shipping_map_obj.carrier];
            });
        });
    
        let todaydate = new Date();
        let todayday = todaydate.getDay();
        let get_Hours = todaydate.getHours();
        let todayDayOfYear = dayFromDate(todaydate);
    
        let cutOffTime = {{ settings.cutoff_time | minus: 1 }};
        let am_pm = 0;
        if(get_Hours > {{ settings.cutoff_time | minus: 1 }} &&  holidays_dayof_year.indexOf(parseInt(todayDayOfYear)) == -1 ){
            am_pm = 1;
        } else if(holidays_dayof_year.indexOf(parseInt(todayDayOfYear))> -1) {
            am_pm = 1;
        }
                    
        {% assign cart_weight = 0 %}
        {% assign cart_quantity = 0 %}
        {% for line_item in checkout.line_items %}
            {% assign cart_quantity = cart_quantity | plus: line_item.quantity %}
            {% assign current_item_weight = line_item.grams | times: line_item.quantity %}
            {% assign cart_weight = cart_weight | plus: current_item_weight %}
        {% endfor %}
                    
        let arrivalDate = calculateEstimated(am_pm);
        // console.log('bugfix/shipping-json-weekends - pre 1st checkTodayDate - arrivalDate:', arrivalDate)
        arrivalDate = checkTodayDate(arrivalDate, all_blackout_dates);
        arrivalDate = new Date(arrivalDate);
        // console.log('bugfix/shipping-json-weekends - 1st checkTodayDate - arrivalDate:', arrivalDate)
    
        let arrivalMonth = arrivalDate.getMonth()+1;
        if(arrivalMonth<10){ arrivalMonth = '0'+arrivalMonth }
        let arrivalDay = arrivalDate.getDate();
        if(arrivalDay<10){ arrivalDay = '0'+arrivalDay }
        arrivalDate = arrivalDate.getFullYear()+''+arrivalMonth+''+arrivalDay
        let arrivalDate_without_ampm = calculateEstimated(am_pm);
        let orderAddressObject = {{ checkout.shipping_address | json }};
        let orderPriceObject = {{ checkout.total_price | json }};
        let cart_weight = {{ cart_weight }};
        let cart_quantity = {{ cart_quantity }};
        let global_delay = {{ settings.estimated_shipping_delay }};
        let radio__label__accessory__width = {{ settings.estimated_text_space }};
    
        let myKeyVals = { 'arrivalDate' : arrivalDate,'arrivalDate_without_ampm':arrivalDate_without_ampm,'orderAddressObject':orderAddressObject,'orderPriceObject':orderPriceObject,'cart_weight':cart_weight,'cart_quantity':cart_quantity };
    
        $('.hide_me').addClass('loading_modal_overlay');
            
        $.each(carrierUsedArray, function(i, carrier_used_obj) {
            let datePartialMessage = 'Est. Arrival: ';
            let api_url = null;
            if (carrier_used_obj == 'ups'){
                api_url = 'https://grazitti.olukai.com/olukai-ups/SimpleXMLTimeInTransitClient-new.php';
            } else if(carrier_used_obj == 'fedex'){
                  api_url = 'https://grazitti.olukai.com/fedex-shipping/fedex-shipping.php';
            } else if(carrier_used_obj == 'other'){
                api_url = '';
                
                $.each(allShippingMethodsArray, function(i, our_shipping_method) {
                    if(typeof allShippingMethodsConfigArray[our_shipping_method] !== 'undefined' && carrier_used_obj == allShippingMethodsConfigArray[our_shipping_method][5]){
                        let configs_array = allShippingMethodsConfigArray[our_shipping_method];
    
                        if(configs_array[4] != null){
                            let date_hide = new Date(configs_array[4]);
                            date_hide = date_hide.getTime();
                            let current_datetime = new Date();
                            current_datetime = current_datetime.getTime();
                            
                            if(current_datetime > date_hide){
                                $('[data-shipping-method-label-title="'+our_shipping_method+'"]').parent().parent().parent().hide();
                                $('[data-shipping-method-label-title="'+our_shipping_method+'"]').parent().parent().parent().parent().css('border-top','none');
                            } else {
                                if(configs_array[3] != null){
                                    let date_show = configs_array[3];
                                    let deliveryDate = new Date(configs_array[3]);
                                    console.log('bugfix/shipping-json-weekends - pre 2nd checkTodayDate - deliveryDate:', deliveryDate)
                                    deliveryDate = checkTodayDate(deliveryDate, all_blackout_dates, date_show);
                                    console.log('bugfix/shipping-json-weekends - 2nd checkTodayDate - deliveryDate:', deliveryDate)
                                    deliveryDate = new Date(deliveryDate);
                                    deliveryDate = daysName[deliveryDate.getDay()]+', '+MonthName[deliveryDate.getMonth()]+' '+deliveryDate.getDate();
                                    $('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.loading_dots_msg').replaceWith(`<span class="arrival_date"><span class="content-box__emphasis">${datePartialMessage} ${ deliveryDate }</span></span>`);
                                } else {
                                    let total_delay = 0;
                                    let delay_end_date = null;
                                    total_delay = total_delay + global_delay;
                                    if(configs_array[1] != null){
                                        total_delay = total_delay + configs_array[1];
                                    }
                                    let deliveryDate = new Date();
                                    console.log('bugfix/shipping-json-weekends - pre 3rd checkTodayDate - deliveryDate:', deliveryDate)
                                    deliveryDate = checkTodayDate(deliveryDate, all_blackout_dates);
                                    console.log('bugfix/shipping-json-weekends - 3rd checkTodayDate - deliveryDate:', deliveryDate)
                                    deliveryDate = new Date(deliveryDate);
                                    if(deliveryDate.getHours() > cutOffTime){
                                        deliveryDate.setDate(deliveryDate.getDate()+1);
                                    }
                                    if(total_delay != 0){
                                        deliveryDate = new Date(deliveryDate);
                                        deliveryDate = addWorkingDay(deliveryDate, all_blackout_dates, total_delay);
                                    }
                                    
                                    delay_end_date = deliveryDate;
                                    deliveryDate = new Date(deliveryDate);
                                    deliveryDate = daysName[deliveryDate.getDay()]+', '+MonthName[deliveryDate.getMonth()]+' '+deliveryDate.getDate();
                                    
                                    if(configs_array[2] != null && configs_array[2] != 0){
                                        delay_end_date = new Date(delay_end_date);
                                        delay_end_date = addWorkingDay(delay_end_date, all_blackout_dates, configs_array[2]);
                                        delay_end_date = new Date(delay_end_date);
                                        delay_end_date = daysName[delay_end_date.getDay()]+', '+MonthName[delay_end_date.getMonth()]+' '+delay_end_date.getDate();
                                        deliveryDate = deliveryDate+' - '+delay_end_date;
                                    }
                                    $('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.loading_dots_msg').replaceWith(`<span class="arrival_date"><span class="content-box__emphasis">${datePartialMessage} ${ deliveryDate }</span></span>`);
                                }
                            }
                        } else if(configs_array[3] != null){
                            let deliveryDate = new Date(configs_array[3]);
                            console.log('bugfix/shipping-json-weekends - pre 4th checkTodayDate - deliveryDate:', deliveryDate)
                            deliveryDate = checkTodayDate(deliveryDate);
                            console.log('bugfix/shipping-json-weekends - 4th checkTodayDate - deliveryDate:', deliveryDate)
                            deliveryDate = daysName[deliveryDate.getDay()]+', '+MonthName[deliveryDate.getMonth()]+' '+deliveryDate.getDate();
                            $('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.loading_dots_msg').replaceWith(`<span class="arrival_date"><span class="content-box__emphasis">${datePartialMessage} ${ deliveryDate }</span></span>`);
                        } else {
                            let total_delay = 0;
                            let delay_end_date = null;
                            total_delay = total_delay + global_delay;
                            if(configs_array[1] != null){
                                total_delay = total_delay + configs_array[1];
                            }
                            let deliveryDate = new Date();
                            console.log('bugfix/shipping-json-weekends - pre 5th checkTodayDate - deliveryDate:', deliveryDate)
                            deliveryDate = checkTodayDate(deliveryDate, all_blackout_dates);
                            console.log('bugfix/shipping-json-weekends - 5th checkTodayDate - deliveryDate:', deliveryDate)
                            deliveryDate = new Date(deliveryDate);
                            if(deliveryDate.getHours() > cutOffTime){
                                deliveryDate.setDate(deliveryDate.getDate()+1);
                            }
                            if(total_delay != 0){
                                deliveryDate = new Date(deliveryDate);
                                deliveryDate = addWorkingDay(deliveryDate, all_blackout_dates, total_delay);
                            }
                            
                            delay_end_date = deliveryDate;
                            deliveryDate = new Date(deliveryDate);
                            deliveryDate = daysName[deliveryDate.getDay()]+', '+MonthName[deliveryDate.getMonth()]+' '+deliveryDate.getDate();
                            
                            if(configs_array[2] != null && configs_array[2] != 0){
                                delay_end_date = new Date(delay_end_date);
                                delay_end_date = addWorkingDay(delay_end_date, all_blackout_dates, configs_array[2]);
                                delay_end_date = new Date(delay_end_date);
                                delay_end_date = daysName[delay_end_date.getDay()]+', '+MonthName[delay_end_date.getMonth()]+' '+delay_end_date.getDate();
                                deliveryDate = deliveryDate+' - '+delay_end_date;
                            }
                            $('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.loading_dots_msg').replaceWith(`<span class="arrival_date"><span class="content-box__emphasis">${datePartialMessage} ${ deliveryDate }</span></span>`);
                        }
                                
                        jQuery('[data-shipping-method-label-title="'+our_shipping_method+'"]').children(".section--shipping-method .radio__label__accessory").css('width',radio__label__accessory__width+"px");
                        let checkwidth = parseInt(jQuery('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.radio__label__accessory').outerWidth()) + parseInt(jQuery('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.arrival_date').outerWidth());
                        let nameWidth = jQuery('[data-shipping-method-label-title="'+our_shipping_method+'"]').parent('.radio__label').outerWidth() - checkwidth;
                        nameWidth = nameWidth - 3;
                        jQuery('[data-shipping-method-label-title="'+our_shipping_method+'"]').css('width',nameWidth+"px");
                    }
                });
            }
          
            $.ajax({
                type: "POST",
                url: api_url,
                data: myKeyVals,
                success: function(resultData) {
                    if(resultData != '') {
                        successResponseJsonObject = JSON.parse(resultData);
                        $.each(allShippingMethodsArray, function(i, our_shipping_method) {
                            if(typeof allShippingMethodsConfigArray[our_shipping_method] !== 'undefined' && carrier_used_obj == allShippingMethodsConfigArray[our_shipping_method][5]){
                                let configs_array = allShippingMethodsConfigArray[our_shipping_method];
                                if(configs_array[4] != null){
                                    let date_hide = new Date(configs_array[4]);
                                    date_hide = date_hide.getTime();
                                    let current_datetime = new Date();
                                    current_datetime = current_datetime.getTime();
                                    if(current_datetime > date_hide){
                                        $('[data-shipping-method-label-title="'+our_shipping_method+'"]').parent().parent().parent().hide();
                                        $('[data-shipping-method-label-title="'+our_shipping_method+'"]').parent().parent().parent().parent().css('border-top','none');
                                    } else {
                                        if(configs_array[3] != null){
                                            let deliveryDate = new Date(configs_array[2]);
                                            deliveryDate = daysName[deliveryDate.getDay()]+', '+MonthName[deliveryDate.getMonth()]+' '+deliveryDate.getDate();
                                            $('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.loading_dots_msg').replaceWith(`<span class="arrival_date"><span class="content-box__emphasis">${datePartialMessage} ${ deliveryDate }</span></span>`);
                                        } else {
                                            let total_delay = 0;
                                            let delay_end_date = null;
                                            total_delay = total_delay + global_delay;
                                            if(configs_array[1] != null){
                                                total_delay = total_delay + configs_array[1];
                                            }
                                            if(typeof configs_array[0] != 'undefined'){
                                                let deliveryDate = successResponseJsonObject[configs_array[0].toUpperCase()];
                                                if(total_delay != 0){
                                                    deliveryDate = new Date(deliveryDate);
                                                    deliveryDate = addWorkingDay(deliveryDate, all_blackout_dates, total_delay);
                                                }
                                                console.log('bugfix/shipping-json-weekends - pre 6th checkTodayDate - deliveryDate:', deliveryDate)
                                                deliveryDate = checkTodayDate(deliveryDate,all_blackout_dates);
                                                console.log('bugfix/shipping-json-weekends - 6th checkTodayDate - deliveryDate:', deliveryDate)
                                                delay_end_date = deliveryDate;
                                                deliveryDate = new Date(deliveryDate);
                                                deliveryDate = daysName[deliveryDate.getDay()]+', '+MonthName[deliveryDate.getMonth()]+' '+deliveryDate.getDate();
                                                if(configs_array[2] != null){
                                                    delay_end_date = new Date(delay_end_date);
                                                    delay_end_date = addWorkingDay(delay_end_date, all_blackout_dates, configs_array[2]);
                                                    delay_end_date = new Date(delay_end_date);
                                                    delay_end_date = daysName[delay_end_date.getDay()]+', '+MonthName[delay_end_date.getMonth()]+' '+delay_end_date.getDate();
                                                    deliveryDate = deliveryDate+' - '+delay_end_date;
                                                }
                                                $('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.loading_dots_msg').replaceWith(`<span class="arrival_date"><span class="content-box__emphasis">${datePartialMessage} ${ deliveryDate }</span></span>`);
                                            }
                                        }
                                    }
                                } else if(configs_array[3] != null){
                                    let deliveryDate = new Date(configs_array[3]);
                                    deliveryDate = daysName[deliveryDate.getDay()]+', '+MonthName[deliveryDate.getMonth()]+' '+deliveryDate.getDate();
                                    $('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.loading_dots_msg').replaceWith(`<span class="arrival_date"><span class="content-box__emphasis">${datePartialMessage} ${ deliveryDate }</span></span>`);
                                } else if(configs_array[1] != null || configs_array[2] != null){
                                    let total_delay = 0;
                                    let delay_end_date = null;
                                    total_delay = total_delay + global_delay;
                                    if(configs_array[1] != null){
                                        total_delay = total_delay + configs_array[1];
                                    }
                                  
                                    if(typeof configs_array[0] != 'undefined'){
                                      console.log(our_shipping_method)
                                      if(typeof successResponseJsonObject[configs_array[0].toUpperCase()] != 'undefined'){
                                          let deliveryDate = successResponseJsonObject[configs_array[0].toUpperCase()];
                                          if(total_delay != 0){
                                              deliveryDate = new Date(deliveryDate);
                                              deliveryDate = addWorkingDay(deliveryDate, all_blackout_dates, total_delay);
                                          }
                                          console.log('bugfix/shipping-json-weekends - pre 7th checkTodayDate - deliveryDate:', deliveryDate)
                                          deliveryDate = checkTodayDate(deliveryDate,all_blackout_dates);
                                          console.log('bugfix/shipping-json-weekends - 7th checkTodayDate - deliveryDate:', deliveryDate)
                                          delay_end_date = deliveryDate;
                                          deliveryDate = new Date(deliveryDate);
                                          deliveryDate = daysName[deliveryDate.getDay()]+', '+MonthName[deliveryDate.getMonth()]+' '+deliveryDate.getDate();
                                          if(configs_array[2] != null){
                                              delay_end_date = new Date(delay_end_date);
                                              delay_end_date = addWorkingDay(delay_end_date, all_blackout_dates, configs_array[2]);
                                              delay_end_date = new Date(delay_end_date);
                                              delay_end_date = daysName[delay_end_date.getDay()]+', '+MonthName[delay_end_date.getMonth()]+' '+delay_end_date.getDate();
                                              deliveryDate = deliveryDate+' - '+delay_end_date;
                                          }									
                                          $('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.loading_dots_msg').replaceWith(`<span class="arrival_date"><span class="content-box__emphasis">${datePartialMessage} ${ deliveryDate }</span></span>`);
                                        if(our_shipping_method == 'Standard'){
                                            standardDate = successResponseJsonObject[configs_array[0].toUpperCase()];
                                        }
                                        if(successResponseJsonObject["FEDEX_GROUND"] && typeof standardDate != false){
                                            standardDate = successResponseJsonObject["FEDEX_GROUND"];
                                        }
                                        if(our_shipping_method == 'Expedited' && typeof standardDate != false && !expeditedHideExclusionStates.includes(selectedState)){
                                            standardDate = new Date(standardDate);
                                              standardDate = standardDate.getTime();
                                              let expeditesDate = new Date(successResponseJsonObject[configs_array[0].toUpperCase()]);
                                              expeditesDate = expeditesDate.getTime();                                                                        
                                              if(standardDate < expeditesDate){ 
                                                  $('[data-shipping-method-label-title="'+our_shipping_method+'"]').parent().parent().parent().remove();
                                              }
                                        }
                                      } else { 
                                          $('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.loading_dots_msg').replaceWith('');
                                          }
                                    } 
                                } else {
                                    if(typeof configs_array[0] != 'undefined'){
                                        let deliveryDate = successResponseJsonObject[configs_array[0].toUpperCase()];
                                        if(global_delay > 0){
                                            deliveryDate = addWorkingDay(deliveryDate, all_blackout_dates, global_delay);
                                        } else {
                                            console.log('bugfix/shipping-json-weekends - pre 8th checkTodayDate - deliveryDate:', deliveryDate)
                                            deliveryDate = checkTodayDate(deliveryDate, all_blackout_dates);
                                            console.log('bugfix/shipping-json-weekends - 8th checkTodayDate - deliveryDate:', deliveryDate)
                                        }
    
                                        deliveryDate = new Date(deliveryDate);
                                        deliveryDate = daysName[deliveryDate.getDay()]+', '+MonthName[deliveryDate.getMonth()]+' '+deliveryDate.getDate();
                                        $('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.loading_dots_msg').replaceWith(`<span class="arrival_date"><span class="content-box__emphasis">${datePartialMessage} ${ deliveryDate }</span></span>`);
                                    }
                                }
                                
                                jQuery('[data-shipping-method-label-title="'+our_shipping_method+'"]').children(".section--shipping-method .radio__label__accessory").css('width',radio__label__accessory__width+"px");
                                let checkwidth = parseInt(jQuery('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.radio__label__accessory').outerWidth()) + parseInt(jQuery('[data-shipping-method-label-title="'+our_shipping_method+'"]').children('.arrival_date').outerWidth());
                                let nameWidth = jQuery('[data-shipping-method-label-title="'+our_shipping_method+'"]').parent('.radio__label').outerWidth() - checkwidth;
                                nameWidth = nameWidth - 2;
                                jQuery('[data-shipping-method-label-title="'+our_shipping_method+'"]').css('width',nameWidth+"px");
                            }
                        });
    
                    }
                    $('#loading_modal_overlay').removeClass('loading_modal_overlay');
                },
                error: function(XMLHttpRequest, textStatus, errorThrown) {
                    $('#loading_modal_overlay').removeClass('loading_modal_overlay');
                },
                done: function(){
                    $('#loading_modal_overlay').removeClass('loading_modal_overlay');
                },
                dataType: "text"
            });
        });
    
    }
    
    function calculateEstimated(shippingTime) { 
        let today = new Date();
        let deliveryDate = today;
        console.log('bugfix/shipping-json-weekends - Initial deliveryDate:', deliveryDate)
        let total_days = shippingTime;
        if(shippingTime != 0){
          for (let days = 1; days <= total_days; days++) {
            deliveryDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);
            let form_del_date = dayFromDate(deliveryDate);
            if (deliveryDate.getDay() === 0 || deliveryDate.getDay() === 6 || holidays_dayof_year.indexOf(form_del_date) > -1 ) {
              total_days++;
            }
          }
        } else {
            for (let days = 1; days <= total_days+1; days++) { 
                yesterday_date = new Date();
                yesterday_date.setDate(yesterday_date.getDate()-1);
                deliveryDate = new Date(yesterday_date.getTime() + days * 24 * 60 * 60 * 1000);
                let form_del_date = dayFromDate(deliveryDate);
                if (deliveryDate.getDay() === 0 || deliveryDate.getDay() === 6 || holidays_dayof_year.indexOf(form_del_date) > -1 ) {
                    total_days++;
                }
            }
        }
        let deliveryMonth = deliveryDate.getMonth()+1;
        if(deliveryMonth<10){ deliveryMonth = '0'+deliveryMonth }
        deliveryDay = deliveryDate.getDate();
        if(deliveryDay<10){ deliveryDay = '0'+deliveryDay }
        // console.log('bugfix/shipping-json-weekends - Calculated deliveryDay:', deliveryDay)
        return deliveryDate.getFullYear()+'/'+deliveryMonth+'/'+deliveryDay;
    }
        
    function dayFromDate(dates){
        let now = new Date(dates);
        let start = new Date(now.getFullYear(), 0, 0);
        let diff = (now - start) + ((start.getTimezoneOffset() - now.getTimezoneOffset()) * 60 * 1000);
        let oneDay = 1000 * 60 * 60 * 24;
        let day = Math.floor(diff / oneDay);
        return day;
    }
    
            
    function setCalculateApproxEstimatedTime(){
        $("span.ups_second_day_air").replaceWith(`<span class="ups_second_day_air arrival_date"><span class="content-box__emphasis">Est. Arrival: ${calculateApproxEstimatedTime(am_pm+2)}</span></span>`);
        $("span.ups_next_day_air").replaceWith(`<span class="ups_next_day_air arrival_date"><span class="content-box__emphasis">Est. Arrival: ${calculateApproxEstimatedTime(am_pm+1)}</span></span>`);
    }
        
    function calculateApproxEstimatedTime(shippingTime) {
        let today = new Date();
        let deliveryDate = today;
        let total_days = shippingTime;
        for (let days = 1; days <= total_days; days++) {
            deliveryDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);
            let form_del_date = dayFromDate(deliveryDate);
            if (deliveryDate.getDay() === 0 || deliveryDate.getDay() === 6 || holidays_dayof_year.indexOf(form_del_date) > -1 ) {
                total_days++;
            }
        }
        deliveryDate = deliveryDate.toDateString().slice(0, -5);
        deliveryDate = deliveryDate.split(' ');
        return deliveryDate[0]+', '+deliveryDate[1]+' '+deliveryDate[2];
    }
    
    function addWorkingDay(date, all_blackout_dates, daysToIncreament){
        daysToIncreament = daysToIncreament - 1;
        let tempDate = new Date(date);
        tempDate.setDate(tempDate.getDate() + 1);
        let daysName = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
        let MonthName = ["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sept","Oct","Nov","Dec"];
        let theMonth = tempDate.getMonth()+1;
        let theDay = tempDate.getDay();
        tempDate = theMonth+'/'+tempDate.getDate()+'/'+tempDate.getFullYear().toString().substr(-2);
        if(all_blackout_dates !== null){
            let isHoliday = parseInt(in_array( tempDate, all_blackout_dates));
            if(isHoliday !== -1 || theDay > 5 || theDay < 1 ){
                tempDate = new Date(tempDate);
                let nextDay = tempDate;
                nextMonth = nextDay.getMonth()+1;
                nextDay = nextMonth+'/'+nextDay.getDate()+'/'+nextDay.getFullYear();
                if( theDay > 0 && theDay < 6 && daysToIncreament < 1 && isHoliday == -1){
                    console.log('bugfix/shipping-json-weekends - New date after adding working days 1st:', nextDay)
                    return nextDay;
                } else if(theDay > 5 || theDay < 1 || isHoliday !== -1){
                    daysToIncreament = daysToIncreament + 1;
                }
                return addWorkingDay(nextDay, all_blackout_dates, daysToIncreament);
            } else {
                let nextDay = new Date(tempDate);
                nextMonth = nextDay.getMonth()+1;
                nextDay = nextMonth+'/'+nextDay.getDate()+'/'+nextDay.getFullYear();
                if( theDay > 0 && theDay < 6 && daysToIncreament < 1 ){
                    return nextDay;
                } else if(theDay > 5 || theDay < 1){
                    daysToIncreament = daysToIncreament + 1;
                }
                console.log('bugfix/shipping-json-weekends - New date after adding working days 2nd:', nextDay)
                return addWorkingDay(nextDay, all_blackout_dates, daysToIncreament);
            }
        } else {
            let nextDay = new Date(tempDate);
            if( theDay > 5 || theDay < 1 ){
                daysToIncreament = daysToIncreament + 1;
                console.log('bugfix/shipping-json-weekends - New date after adding working days 3rd:', nextDay)
                return addWorkingDay(nextDay, all_blackout_dates, daysToIncreament);
            }
            nextDay = daysName[nextDay.getDay()]+', '+MonthName[nextDay.getMonth()]+' '+nextDay.getDate();
            if( theDay > 0 && theDay < 6 && daysToIncreament < 1 ){
                console.log('bugfix/shipping-json-weekends - New date after adding working days 4th:', nextDay)
                return nextDay;
            }
        }
    }
        
    function in_array(needle, haystack){
        haystack = haystack.split(",");
        let found = 0;
        for (let i=0, len=haystack.length;i<len;i++) {
            if (haystack[i] == needle) return i;
            found++;
        }
        return -1;
    }	
    function checkTodayDate(date, all_blackout_dates, date_show) {
        // console.log('bugfix/shipping-json-weekends - checkTodayDate(): Starting with date_show:', date_show);
        if (date_show) date_show = date_show.replace(/^(\d{4})\/(\d{2})\/(\d{2})$/, (match, year, month, day) => `${month}/${day}/${year.slice(-2)}`)
        
        let tempDate = new Date(date);
        
    
        let daysName = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
        let MonthName = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        
        let theMonth = tempDate.getMonth() + 1;
        let theDay = tempDate.getDay();
        console.log('bugfix/shipping-json-weekends - checkTodayDate(): theDay', theDay);
    
        tempDate = theMonth + '/' + tempDate.getDate() + '/' + tempDate.getFullYear().toString().substr(-2);
        // console.log('bugfix/shipping-json-weekends - checkTodayDate(): Reformatted tempDate', tempDate);
        // console.log('bugfix/shipping-json-weekends - checkTodayDate(): Reformatted date_show', date_show);
        // prioritize a set date_show, ifdate matches date_show and return directly
        if (date_show && tempDate === date_show) {
            let nextDay = new Date(tempDate)
            // nextMonth = nextDay.getMonth() + 1
            // nextDay = nextMonth + '/' + nextDay.getDate() + '/' + nextDay.getFullYear()
            // console.log('bugfix/shipping-json-weekends - checkTodayDate(): Date matches date_show. Returning date', nextDay)
            // return nextDay
            let dayName = daysName[nextDay.getDay()]
            let formattedDate = `${dayName}, ${MonthName[nextDay.getMonth()]} ${nextDay.getDate()}, ${nextDay.getFullYear()}`
            // console.log('bugfix/shipping-json-weekends - checkTodayDate(): Date matches date_show. Returning date with correct day name', formattedDate)
            return formattedDate
        }
    
        if (all_blackout_dates) {
            let isHoliday = parseInt(in_array(tempDate, all_blackout_dates));
            // console.log('bugfix/shipping-json-weekends - checkTodayDate(): isHoliday', isHoliday);
    
            if (isHoliday !== -1 || theDay === 0 || theDay === 6) {
                // console.log('bugfix/shipping-json-weekends - checkTodayDate(): Date is holiday or weekend, incrementing date');
    
                tempDate = new Date(tempDate);
                let nextDay = new Date(tempDate.getTime() + (24 * 60 * 60 * 1000));
                // console.log('bugfix/shipping-json-weekends - checkTodayDate(): Next day after increment', nextDay);
    
                nextMonth = nextDay.getMonth() + 1;
                nextDay = nextMonth + '/' + nextDay.getDate() + '/' + nextDay.getFullYear();
                // console.log('bugfix/shipping-json-weekends - pre 9th checkTodayDate - deliveryDate:', nextDay)
                return checkTodayDate(nextDay, all_blackout_dates, date_show);
            } else {
                // console.log('bugfix/shipping-json-weekends - checkTodayDate(): Date is not a holiday or weekend');
    
                let nextDay = new Date(tempDate);
                nextMonth = nextDay.getMonth() + 1;
                nextDay = nextMonth + '/' + nextDay.getDate() + '/' + nextDay.getFullYear();
                // console.log('bugfix/shipping-json-weekends - checkTodayDate(): Returning date', nextDay);
    
                return nextDay;
            }
        } else {
            let nextDay = new Date(tempDate);
            nextDay = daysName[nextDay.getDay()] + ', ' + MonthName[nextDay.getMonth()] + ' ' + nextDay.getDate();
            // console.log('bugfix/shipping-json-weekends - checkTodayDate(): Returning date without blackout dates', nextDay);
    
            return nextDay;
        }
    }
    function checkShippingSettingJson(json_value) { 
        if (typeof json_value !== "string") { 
            return false;
        } 
        try { 
            JSON.parse(json_value); 
            return true; 
        } catch (error) { 
            return false; 
        } 
    } 
    
    </script>
    
    <style>
    .display-table .radio__label__primary{
        width: auto;
        flex: auto;
    }
    .section--shipping-method .radio__label {
        display: flex !important;
        justify-content: flex-start;
        flex-wrap:wrap;
        align-items:center;
    }
    .section--shipping-method .display-table .radio__label__primary{
        display: inherit;
    }
    .section--shipping-method .arrival_date{
        font-family: interstate,sans-serif,"Century Gothic",sans-serif;
        color: #717171;
        font-size: 13px;
        
    }
    
    .section--shipping-method .arrival_date .content-box__emphasis {
        font-family: "GTA-Regular";   
    }
    
    .section--shipping-method .arrival_date .content-box__emphasis{
        color: #717171;
    }
    .section--shipping-method .loading_dots_msg{
        font-family: interstate,sans-serif,"Century Gothic",sans-serif;
        color: #717171;
        display: block;
        font-size: 13px;
    }
    .radio__label__primary{ width:228px; }
    .section--shipping-method .loading_dots_msg .content-box__emphasis{
        color: #717171;
    }
    .section--shipping-method .shipping-dates .radio__label__accessory{
        display:inline-block;
        margin-left:auto;
      padding-left:0; 
    }
    .section--shipping-method .shipping__notice{
        margin-bottom:13px;
    }
    .section--shipping-method .small-text{
        display:none;
    } 
    .display-table .radio__label__primary {
        width: auto;
        flex: auto;
        line-height: 17px;
        letter-spacing: -0.07px;
    }
    .section--shipping-method .arrival_date .content-box__emphasis {
        color: #717171;
        line-height: 15px;
        letter-spacing: -0.07px;
        margin-top: 4px;
        display: inline-block;
    }
    .section--shipping-method .radio__label__accessory .content-box__emphasis {
        font-family: "GTA-Regular";
        font-size: 15px;
        line-height: 18px;
    }
    .checkout__content .content-box .content-box__row {
        padding: 16px 24px;
    }
    .section--shipping-method .radio__input {
        padding-right: 18px;
    }
    .section--shipping-method .radio__label__accessory .content-box__emphasis {
        font-family: "GTA-Regular";
        font-size: 15px;
        line-height: 18px;
        color: #000000;  /* #381300 */
        padding-top: 1px;
        display: inline-block;
    }
    .section--shipping-method .arrival_date {
        font-family: interstate,sans-serif,"Century Gothic",sans-serif;
        color: #717171;
        font-size: 13px;
        display: block;
    }
    @media screen and (max-width:470px){
        .section--shipping-method .content-box__row{ padding-bottom: 33px; }
        .section--shipping-method label.radio__label{ 
            position: relative; 
        }
        .section--shipping-method .radio__label__primary{
            width: 45% !important;
        }
        .section--shipping-method .arrival_date{
            position: absolute;
            left: 0;
            bottom: -22px;
            padding: 0;
        }
        .section--shipping-method .loading_dots_msg{
            display: block;
            padding: 0;
        }
        .section--shipping-method .radio__input{ width:18.6px; }
        .section--shipping-method .checkout__content .content-box .content-box__row{
            padding-left: 10px;
            padding-right: 10px;
        }
    }
    
    @media screen and (max-width:375px){
        .section--shipping-method .radio__label__primary{
            width: 43% !important;
        }
    }
    
    @media (max-width : 470px){
        .section--shipping-method .radio__input {
            width: auto;
            position: static;
            padding-right: 18px;
            height: auto;
            margin: 0;
        }
        .section--shipping-method label.radio__label {
            position: relative;
            padding-left: 0;
        }
        .section--shipping-method .arrival_date {
            display: block;
            padding: 0;
        }
        .section--shipping-method .radio-wrapper {
            display: flex !important;
            align-items: center;
        }
        .section--shipping-method .radio__input {
            width: auto;
        }
        .section--shipping-method label.radio__label {
            position: relative;
        }
    }
    
    .section--shipping-method .content-box__emphasis{ font-weight:400; }
    @media screen and (min-width:471px){
        .section--shipping-method .radio-wrapper {
            display: flex !important;
            align-items:center;
        }
        .section--shipping-method .radio__label__accessory{
              /*width: {{ settings.estimated_text_space }}px;*/
              width: auto;
        }
    }
      
      /* loading dots */
    .ldot{ font-size:22px; line-height: 10px; }
    @keyframes blink {
        0% {
            opacity: .2;
        }
        20% {
            opacity: 1;
        }
        100% {
            opacity: .2;
        }
    }
    .loading_dots_msg .content-box__emphasis span {
        animation-name: blink;
        animation-duration: 1.4s;
        animation-iteration-count: infinite;
        animation-fill-mode: both;
    }
    .loading_dots_msg .content-box__emphasis span:nth-child(2) {
        animation-delay: .2s;
    }
    .loading_dots_msg .content-box__emphasis span:nth-child(3) {
        animation-delay: .4s;
    }
    </style>