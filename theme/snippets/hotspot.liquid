{%- if settings.hotspot_product.available -%}
  <div
    class="absolute overlay-item"
    style="{%- render 'overlay-variables' settings:settings -%} z-index:39;"
    x-data="{ tooltipVisible: false, product: null }"
    x-init="$data.product = JSON.parse($el.querySelector('[product-data]').innerHTML)">
    <template product-data>{ {%- render 'product-item-data' product: settings.hotspot_product -%} }</template>
    <div class="relative hotspot">
      <button class="hotspot__button" aria-label="View Product" @click="() =>  { 
                                                                                         $data.tooltipVisible = !$data.tooltipVisible 
                                                                                       
                                                                                     }">
        <span class="sr-only">View Product</span>
      </button>
      <div
        class="hotspot__tooltip"
        hotspot-tooltip="{{ settings.hotspot_tooltip_direction }}"
        x-cloak
        x-show="tooltipVisible"
        @click.outside="$data.tooltipVisible = false">
        <div class="hotspot__tooltip-inner">
          <a href="{{ settings.hotspot_product.url }}">
            {{ settings.hotspot_product | image_url: width: 200 | image_tag: class: 'md:w-24 md:h-24 w-[50px] h-auto object-contain tooltip__image' }}</a>
          <div class="mr-3">
            <a href="{{ settings.hotspot_product.url }}" class="tooltip__title">{{ settings.hotspot_product.title | split: ' - ' | first }}</a>
            {% if settings.hotspot_product.title contains ' - ' %}
              <p class="tooltip__price">{{ settings.hotspot_product.title | split: ' - ' | last }}</p>
            {% endif %}
            <p class="tooltip__price">{{ settings.hotspot_product.price | money }}</p>

          </div>
        </div>
      </div>
    </div>
  </div>
{%- endif -%}
