{% if title != blank and content != blank %}
<li class="accordion group">
  <button 
    id="AccordionTitle-{{forloop.index}}" 
    class="accordion-title {{ fontSize }} {{ fontSizeMobile }}" 
    aria-expanded="false" 
    aria-disabled="false" 
    aria-controls="AccordionSection-{{forloop.index}}" 
    neptune-engage='targets:[
      { 
        selector:_parent,
        classes:toggle:active,
        siblings:classes:remove:active
      },
      {
        selector:"_grandparent button",
        attributes:[{att:"aria-disabled" set:"false"} {att:"aria-expanded" set:"false"}]
      },
      { 
        selector:"_grandparent .active button",
        attributes:[{att:"aria-disabled" set:"true"} {att:"aria-expanded" set:"true"}]
      },
      {
        selector:"_grandparent .accordion-panel",
        attributes:{att:"aria-hidden" set:"true"}
      },
      { 
        selector:"_grandparent .active .accordion-panel",
        attributes:{att:"aria-hidden" set:"false"}
      }
    ]'
  >
    <span>{{ title }}</span>

    <span class="flex group-active:hidden accordion-control">
      {% render 'icon', icon: 'chevron-down' width:24 height:24 %}
    </span>
    <span class="hidden group-active:flex accordion-control">
      {% render 'icon', icon: 'chevron-up' width:24 height:24 %}
    </span>


  </button>
  <div 
    id="AccordionSection-{{ forloop.index }}" 
    aria-hidden="true" 
    aria-labelledby="AccordionTitle-{{ forloop.index }}" 
    role="region" 
    class="accordion-panel active-show">
    <div class="py-4 px-6 {{ fontSize }} {{ fontSizeMobile }} border-t">
      {{ content }}
      <input class="sr-only" type="text">
    </div>
  </div>
</li>
{% endif %}