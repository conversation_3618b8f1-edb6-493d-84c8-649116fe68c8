{% comment %}
    Renders an article card for a given blog with settings to either show the image or not.

    Accepts:
    - blog: {Object} Blog object
    - article: {Object} Article object
    - show_image: {String} The setting either show the article image or not. If it's not included it will show the image by default
    - show_date: {String} The setting either show the article date or not. If it's not included it will not show the image by default
    - show_author: {String} The setting either show the article author or not. If it's not included it will not show the author by default

    Usage:
    {% render 'article-card' blog: blog, article: article, show_image: section.settings.show_image %}
{% endcomment %}

<article class="article-card self-start flex flex-full items-start {% if article.image == blank or show_image == false %} article-card--no-image{% endif %} {{ card_classes }}" aria-labelledby="Article-{{ article.id }}">
  <a href="{{ article.url }}" class="article-content w-full h-full flex {{ layout | default: 'lg:flex-row' }} flex-col no-underline transition-none {{ container_classes }}">
    {%- if show_image == true and article.image -%}
      <div class="{{ img_width | default: 'lg:w-1/4' }} w-full lg:flex-shrink-0">
        <div class="overflow-hidden block relative aspect-w-6 aspect-h-4">
          <img
            src="{{ article.image.src | img_url: '533x' }}"
            sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
            alt="{{ article.image.src.alt | escape }}"
            width="{{ article.image.width }}"
            height="{{ article.image.height }}"
            loading="lazy"
            class="transition-none object-cover"
          >
        </div>
      </div>
    {%- endif -%}

    <div class="flex flex-col lg:flex-grow {{ header_classes | default:'lg:px-10 pt-6 lg:pt-0 px-6'}}">
      
      <header class="article-card__header leading-none mb-0.5">

        {% if author != false %}
          {% render 'author' name:article.author format:'mini' %}
        {% endif %}

        <h2 class="title--primary title--article" id="Article-{{ article.id }}">
          {{ article.title | escape }}
        </h2>

        {% if excerpt != false %}
        <p class="max-w-prose mt-0 mb-4 clamp-3 text-sm">
          {%- if article.excerpt.size > 0 -%}
            {{ article.excerpt | strip_html | truncatewords: 20 }}
          {%- else -%}
            {{ article.content | strip_html | truncatewords: 20 }}
          {%- endif -%}
        </p>
        {% endif %}
        
        {% if show_meta != false %}
        <div class="flex flex-wrap text-xs items-center">
            

          {% if date != false %}
            <time pubdate class="block mr-4">{{- article.published_at | time_tag: format: 'month_year' -}}</time>
          {% endif %}

          {% if length != false %}
            <span class="mr-4">{{ article.content | strip_html | size | divided_by: 1200 }} min read</span>
          {% endif %}
              
          {% if comments != false %}
            <span class="text-xs flex items-center mr-4">
              {% render 'icon' icon:'message-square' width:16 height:16 class:'mr-1'%}
              {{ article.comments_count }}
            </span>
          {% endif %}

        </div>
        {% endif %}

        {% if tags != false %}
          <div class="flex flex-wrap text-xs items-center">
            <span class="flex">
              {% for tag in article.tags limit: 4 %}
                {% unless tag contains 'Author' or tag contains 'author' %}
                <span class="pr-2 py-1 bg-gray-100 mr-2 text-xs">#{{ tag | handle }}</span>
                {% endunless %}
              {% endfor %}
            </span>  
          </div>
        {% endif %}
        
      </header>
      
    </div>
  </a>
</article>
