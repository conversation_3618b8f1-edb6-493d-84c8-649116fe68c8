
{% liquid 
  if settings.inclusion_liquid == ''
    assign hide = true
  endif
  assign settings_title = settings.title | handle
  assign block_id = block.id

  assign combined_id = settings_title | append: "-" | append: block_id

  if settings_title == 'frame'  
    assign idname = combined_id
  else
    assign idname = settings_title
  endif
  
%}
{%- if settings.inclusion_js != blank -%}
  <template x-data x-if="{{ settings.inclusion_js }}">
{% elsif settings.teleport != blank %}
  <template x-data x-teleport="{{ settings.teleport }}">
{%- endif -%}
{%- unless hide == true -%}
  <div name="" {{ attributes }} id="{{ idname }}" class="block-{{ block.id }} flex-frame flex {{ classes }} {% render 'class-settings' prefix:'article_class' settings:settings %}" style="{% render 'style-settings' prefix:'article_style' settings:settings %}">
    {% comment %}<!-- {{ settings | json }} -->{% endcomment %}
    {% render 'flex-nested-blocks' blocks:blocks offset:offset %} 
</div>
{%- endunless -%}
{%- if settings.inclusion_js != blank or settings.teleport != blank -%}
  </template>
{%- endif -%}
