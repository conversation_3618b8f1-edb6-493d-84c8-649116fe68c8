<div 
  class="offer-summary" 
  x-data="{ 
    getCompletedOffers() {
      const completedOffers = $store.offers
        .filter(o => o.combinable && parseFloat(o.progress) >= 100)
        .sort((a, b) => a.progress_denominator - b.progress_denominator);

      return completedOffers.filter((offer, index) => {
        const similarOffers = completedOffers.filter(o => 
          o !== offer && 
          (o.key.includes('{{ settings.offer_combine_key }}') && offer.key.includes('{{ settings.offer_combine_key }}'))
        );

        if (similarOffers.length === 0) {
          return true;
        }

        const isHighestDenominator = !similarOffers.some(o => 
          o.progress_denominator > offer.progress_denominator
        );

        return isHighestDenominator;
      });
    }
  }"
  x-show="$store.cart.items.length && getCompletedOffers().length > 0"
>
  <template x-for="offer in getCompletedOffers()" :key="offer.key">
    <div class="offer-summary__item">
      <div class="offer-summary__header">
        <div class="offer-summary__icon" :style="{ backgroundColor: '{{ settings.threshold_icon_style_background_color }}', color: '{{ settings.threshold_icon_style_color }}' }">
          <svg viewBox="0 0 7 7" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1.5 4L3.12528 5.99976L5.5 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <span class="offer-summary__title" x-text="offer.title"></span>
      </div>
      <span class="offer-summary__value" :style="{ color: '{{ settings.threshold_label_style_color }}' }">FREE</span>
    </div>
  </template>
</div>

