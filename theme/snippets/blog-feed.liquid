{% liquid 

  assign page_size = 20
  assign layout = 'vertical'
  assign params = content_for_header | split: '?view=feed\u0026' | last | split: '"' | first | split: '\u0026'

%}
{%- for param in params -%}
{%- liquid 
  assign key = param | split: '=' | first | url_decode
  assign value = param | split: '=' | last | replace: '%20', ' '
  if key == 'layout'
    assign layout = value
  endif
  if key == 'page_size'
    assign page_size = value | plus: 0 
  endif
-%}
{%- endfor -%}
{%- liquid 
  paginate blog.articles by page_size
    for article in blog.articles
      render 'article-item' article:article blog:blog settings:section.settings layout:layout
    endfor
  endpaginate
-%}

