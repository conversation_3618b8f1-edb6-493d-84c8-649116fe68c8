{% assign product = settings.title_product | default: product %}

<div class="product-header">
  <div class="product-header__titles {% if inline %}flex items-center gap-lg{% endif %}">

    {% if section.settings.title_product != blank %}
    <a href="/products/{{ section.settings.title_product }}">
    {% endif %}

    {% unless title == false %}
      <{{ title_element | default: 'h2' }} class="product-header__title type-headline my-0">
        {{ product.title | split: ' - ' | first }}
      </{{ title_element | default: 'h2' }}>
    {% endunless %}

    {% if section.settings.title_product != blank %}
    </a>
    {% endif %}

    {% unless subtitle == false %}
      <{{ subtitle_element | default: 'p' }} class="product-header__subtitle type-subline type--sm my-0">
        {{ subtitle | default: product.metafields.product.style.value.subtitle | default: product.metafields.style.description | default: product.type }}
      </{{ subtitle_element | default: 'p' }}>
    {% endunless %}

      {% if settings.button_1_text != blank or button_1_text != blank %}
        <button style = " margin-left: auto; " {% if settings.button_1_onclick != blank %}onclick="{{ settings.button_1_onclick }};" {% else %}onclick="window.location='{{ button_1_link | default:settings.button_1_link }}'" {% endif %} class="button-desktop button collection-title-shop-all {{ settings.button_1_class_style }} {{ settings.button_1_class_size }} ">
          <span>
            {{ button_1_text | default: settings.button_1_text }}
          </span>
        </button>
      {% endif %}

  </div>
    
  {% unless price == false %}
  <p class="product-header__prices">
    {% if product.compare_at_price > product.price %}
      <s class="product-header__compare-at-price type-item">
        {{ product.compare_at_price | money | split: '.' | first }}
      </s>
    {% endif %}
    <span class="product-header__price type-item">
      {{ product.price | money | split: '.' | first }}
    </span>
  </p>

  {% endunless %}

{% for block in section.blocks %}
  {% if block.settings.enable_review_stars %}
      {% unless reviews == false %}
        {% if block.settings.show_reviews_for_mobile and block.settings.show_reviews_for_desktop %}
          <div class="product-header__reviews">
            {% render 'review-summary' product:product %}
          </div>
        {% elsif block.settings.show_reviews_for_mobile %}
          <div class="product-header__reviews md:hidden">
            {% render 'review-summary' product:product %}
          </div>
        {% elsif block.settings.show_reviews_for_desktop %}
          <div class="product-header__reviews hidden md:block">
            {% render 'review-summary' product:product %}
          </div>
        {% else %}
          <div class="product-header__reviews hidden">
            {% render 'review-summary' product:product %}
          </div>
        {% endif %}
      {% endunless %}
  {% else %}
    {% unless reviews == false %}
      <div class="product-header__reviews">
        {% render 'review-summary' product:product %}
      </div>
    {% endunless %}
    {% break %}
  {% endif %}
{% endfor %}

</div>