{% if settings.search %}
  <button x-data class="nav-tool nav-tools__search flex {{ settings.button_classes }}" @click="
  
  document.querySelector('.menus').classList.add('hidden');
  document.querySelector('.menus').classList.add('lg:hidden');

  if($store.search.form=='SearchModalInput'){
    $store.search.form='';
    document.querySelector('.navbar').classList.remove('active');
    Modal.close();
  } else {
    $store.search.form='SearchModalInput'; 
    Modal.open();
    if(!!document.querySelector('.navbar')){
      document.querySelector('.navbar').classList.add('active');
    }
    setTimeout(function(){
      window.Search.search_redirects();
    }, 200);
  }">
    {% unless settings.display == 'text' %}{% render 'icon' icon:'search' width:settings.icon_size %}{% endunless %}
    {% unless settings.display == 'icons' %}{{ 'sections.header.tools.search' | t }}{% endunless %}
  </button>
{% endif %}
{% if settings.wishlist %}
  <a x-data href="{{ routes.account_url }}?view=wishlist" class="nav-tool nav-tools__wishlist flex {{ settings.button_classes }}">
    {% unless settings.display == 'text' %}{% render 'icon' icon:'heart' width:settings.icon_size %}{% endunless %}
    {% unless settings.display == 'icons' %}{{ 'sections.header.tools.account' | t }}{% endunless %}
    <template x-if="$store.wishlist">
      <span class="nav-tool__count nav-tools__count" x-show="$store.wishlist.lists[0].items.length > 0" x-text="$store.wishlist.lists[0].items.length" :data-count="$store.wishlist.lists[0].items.length"></span>
    </template>
  </a>
{% endif %}
{% if settings.account %}
  <a class="nav-tool nav-tools__account flex items-center {{ settings.button_classes }}"
     href="{{ routes.account_url }}"
     x-data="{ customer: $store.customer, greeting: '{{ settings.greeting | escape }}' }"
     x-init="
        $watch('$store.customer.account.first_name', (name) => {
          greeting = name 
                     ? 'Aloha, ' + name + '!' 
                     : 'Sign In';
        })
        window.addEventListener('Wishlist:add', () => {
         greeting = $store.customer.account.first_name
                    ? 'Aloha, ' + $store.customer.account.first_name + '!' 
                    : 'Sign In';
       })">

    {% if settings.greeting != blank %}
      <span class="relative px-4 text-base nav-tools__account-greeting type-nav-link" x-html="greeting + '&nbsp;'" x-ref="greeting"></span>
    {% endif %}

    {% unless settings.display == 'text' %}{% render 'icon' icon:'account' width:settings.icon_size %}{% endunless %}
    {% unless settings.display == 'icons' %}{{ 'sections.header.tools.account' | t }}{% endunless %}
  </a>
{% endif %}
{% if settings.geo %}
  <button class="nav-tool nav-tools__geo flex {{ settings.button_classes }}" onclick="Modal.toggle('geolocation')">
    {% unless settings.display == 'text' %}{% render 'icon' icon:'globe' width:settings.icon_size %}{% endunless %}
    {% unless settings.display == 'icons' %}{{ 'sections.header.tools.account' | t }}{% endunless %}
  </button>
{% endif %}
{% if settings.cart %}
  <button x-data class="nav-tool nav-tools__cart flex {{ settings.button_classes }}" @click="Modal.toggle('slider-cart')">
     {% unless settings.display == 'text' %}{% render 'icon' icon:'cart' width:settings.icon_size %}{% endunless %}
    {% unless settings.display == 'icons' %}{{ 'sections.header.tools.cart' | t }}{% endunless %}
    <template x-if="$store.cart">
      <span class="nav-tool__count nav-tools__cart-count" x-show="$store.cart.item_count > 0" x-text="$store.cart.item_count" :data-count="$store.cart.item_count"></span>
    </template>
  </button>
{% endif %}
