<script>
	
	(function(){

		window.addEventListener('Collection:filter', e=>{
			if (Object.entries(collection.filters.applied).length) {
				document.body.classList.add('template-collection--filtered')
			} else {
				document.body.classList.remove('template-collection--filtered')
			}
		})

		if (document.location.search.includes('filter')) {
			document.body.classList.add('template-collection--filtered')
		}
	})();
	
</script>
<style>
	
	body:not(.template-collection--filtered) .collection__product-grid {
		display:none;
	}
	body.template-collection--filtered .collection__carousel-rows {
		display:none;
	}
	
</style>