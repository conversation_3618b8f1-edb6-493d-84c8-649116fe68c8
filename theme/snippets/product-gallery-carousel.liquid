{%  if template == 'product.bundle-v2' %} 
  <script>
    function checkinggg(){
      let radioButtons = document.querySelectorAll('input[type="radio"].sr-only');
      let selectedValues = [];
      radioButtons.forEach(radio => {
        if (radio.checked && radio.name == 'Color') {
            selectedValues.push(radio.value);
        }
      });      
      window.selectedValues = selectedValues
    }
    window.addEventListener('DOMContentLoaded', ()=>{window.isBundleProduct = true;window.isBundleProducthandle = Object.keys(window.products)[0];})
    window.addEventListener('Products:optionSelected', e => {checkinggg()})
  </script> 
  
  <div
  class="product-essentials__media product-essentials__media--carousel {% render 'class-settings' settings:settings, prefix: 'media_carousel_class' %}"
  x-data="{swiper: null, contains_video: false, video_slide: 0, 
  initCarousel() {
    this.swiper = Carousels.create(this.$refs.container);
  },
  setPlayBtn() {
    const pathName = window.document.location.pathname.split('/').at(-1);
    const mediaItems = this.$store.products[pathName].newmedia;
    this.contains_video = mediaItems.some(media => media.media_type === 'video');
    this.video_slide = mediaItems.findIndex(media => media.media_type === 'video');
}}" x-init="initCarousel();setPlayBtn();" x-effect="setPlayBtn();">
  <div class="swiper-container swiper" swiper x-ref="container">
    <script type="swiper/config">
      { 
        pagination: {
          el: '.swiper-pagination',
          type: 'bullets',
          clickable: true
        }
      }
    </script>
    <div class="swiper-wrapper">
      <template x-for="(media, index) in $store.products[window.document.location.pathname.split('/').at(-1)].newmedia">
        <article class="product-essentials__media-item swiper-slide" :data-media-type="media.media_type" 
          style="{% render 'style-settings' settings:settings prefix:'media_item_style' %}">
          {% render 'product-media-alpine' media: 'media' %}
          <template x-if="index == 0">
            {% render 'product-badges' product:"$store.products[window.document.location.pathname.split('/').at(-1)]" location:'detail' %}
          </template>
        </article>
      </template>
    </div>

    {% if settings.show_play_button %}
    <div x-cloak x-show="contains_video">
      <button class="product-essentials__media-play absolute flex"
        @click="$refs.container.swiper.slideTo(video_slide, 0)">
        <span>Play</span>
        {% render 'icon', icon: 'play', class: 'w-3 h-3' %}
      </button>
    </div>
    {% endif %}

    <div class="swiper-pagination absolute mb-2 inset-x-0"></div>
  </div>
</div>

{% else %}

<div
  class="product-essentials__media product-essentials__media--carousel {% render 'class-settings' settings:settings, prefix: 'media_carousel_class' %}"
  x-data="{swiper: null, contains_video: false, video_slide: 0, 
  initCarousel() {
    this.swiper = Carousels.create(this.$refs.container);
  },
  setPlayBtn() {
    const pathName = window.document.location.pathname.split('/').at(-1);
    const mediaItems = this.$store.products[pathName].media;
    this.contains_video = mediaItems.some(media => media.media_type === 'video');
    this.video_slide = mediaItems.findIndex(media => media.media_type === 'video');
}}" x-init="initCarousel();setPlayBtn();" x-effect="setPlayBtn();">
  <div class="swiper-container swiper" swiper x-ref="container">
    <script type="swiper/config">
      { 
        {% if settings.media_carousel_pagination == 'progressbar' %}
        renderProgressbar: function (progressbarFillClass) {
          {% if settings.enable_logs %}console.log('progressbarFillClass',progressbarFillClass){% endif %}
          return '<span class="' + progressbarFillClass + '"></span>';
        },
        slidesPerView: {{ settings.media_items_per_view | default: 1 }},
        {% endif %}
        {% if settings.media_carousel_pagination != blank %}
        pagination: {
          el: '.swiper-pagination',
          type: '{{ settings.media_carousel_pagination }}',
          clickable: true
        }
        {% endif %}
      }
    </script>
    <div class="swiper-wrapper">
      <template x-for="(media, index) in $store.products[window.document.location.pathname.split('/').at(-1)].media">
        <article {% if settings.carousel_hide_model_image and settings.carousel_hide_alt_txt != blank %}x-show="(media.alt !== null && !('{{settings.carousel_hide_alt_txt}}'.split(',').some(value => media.alt.includes(value)))) || media.alt === null"{% endif %} class="product-essentials__media-item swiper-slide" :data-media-type="media.media_type" style="{% render 'style-settings' settings:settings prefix:'media_item_style' %}">
          {% render 'product-media-alpine' media: 'media' settings: settings %}
          <template x-if="index == 0">
            {% render 'product-badges' product:"$store.products[window.document.location.pathname.split('/').at(-1)]" location:'detail' %}
          </template>
        </article>
      </template>
    </div>

    {% if settings.show_play_button %}
    <div x-cloak x-show="contains_video">
      <button class="product-essentials__media-play absolute flex"
        @click="$refs.container.swiper.slideTo(video_slide, 0)">
        <span>Play</span>
        {% render 'icon', icon: 'play', class: 'w-3 h-3' %}
      </button>
    </div>
    {% endif %}

    <div class="swiper-pagination swiper-pagination--{{ settings.media_carousel_pagination }} absolute mb-2 inset-x-0"></div>
  </div>
</div>

{%endif%}

{%- if settings.media_grid_modal_zoom == 'modal_over_click' -%}
  {% render 'product-essentials-modal' hide_model_image_check:settings.carousel_hide_model_image, image_hide_alt_txt:settings.carousel_hide_alt_txt %}
{%- endif -%}
