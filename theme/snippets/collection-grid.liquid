{% unless settings.collection.products.size == 0 %}
<div id='{{ settings.id | replace: " ", "_" | replace: "'", "" }}' class="collection-grid">
	<div class="flex items-center">	
		{% if settings.header_link != blank %}
			<a href="{{ settings.header_link }}" class="w-full inline-block">
		{% endif %}
			<div class="collection-grid__header w-auto {% render 'class-settings' prefix:'header_class' settings:settings %}">
				{% if settings.title_text != blank %}
					<{{ settings.title_element }} class="{% render 'class-settings' prefix:'title_class' settings:settings %}" style="{% render 'style-settings' settings:settings prefix:'title_style' %}">{{settings.title_text}}</{{ settings.title_element }}>
				{% endif %}
				{% if settings.subtitle_text != blank %}
					<{{ settings.subtitle_element }} class="{% render 'class-settings' prefix:'subtitle_class' settings:settings %}" style="{% render 'style-settings' settings:settings prefix:'subtitle_style' %}">{{settings.subtitle_text}}</{{ settings.subtitle_element }}>
				{% endif %}
			</div>
		{% if settings.header_link != blank %}
			</a>
		{% endif %}
		{% if settings.filter_button %}
			<div class="flex flex-1 justify-end">	
				{% render 'button' content: 'Filters', style: 'dark', onclick:"Modal.open('filters');", class: 'lg:!hidden w-auto justify-center' %}
			</div>
		{% endif %}
	</div>
	<div class="w-full h-full relative" aria-live="polite">
    <div class="h-full items-stretch grid {% render 'class-settings' prefix:'collection_grid_class' settings:settings %}" 
			{% if settings.product_item_show_swatches %}
				x-init="$nextTick(async () => {
					await Util.hooks.process('Products:enhance', Util.array($el.querySelectorAll('.product-item')).map(item => item._x_dataStack?.[0]?.product));
					productHeight = `${$refs.productItem.getBoundingClientRect().height}px`;
				})"
			{% endif %} 
				x-data="{ productHeight: '0px' }"
		>
			{% for product in settings.collection.products limit:settings.limit %}
        <div class="relative" style="order: {{ forloop.index }}" x-ref="productItem" :data-index="{{  forloop.index }}">
          {% render 'product-item' product:product, settings:settings, _settings:_settings location:'grid' %}
				</div>
			{% endfor %}
			{% if settings.content_position != blank or settings.content_position_desktop != blank %}
				{% liquid

					if settings.content_position != blank and settings.content_position != '0'
            assign order_class_mobile = settings.content_position | plus: 0
            assign order_class_mobile = order_class_mobile | minus: 1 | prepend: 'order-' | replace: 'order-0', 'order-first'
					else
						assign order_class_mobile = 'hidden'
					endif

          if settings.content_position_desktop != blank and settings.content_position_desktop != '0'
            assign order_class_desktop = settings.content_position_desktop | plus: 0
            assign order_class_desktop = order_class_desktop | minus: 1 | prepend: ' lg:order-' | replace: 'lg:order-0', 'lg:order-first'
					else
						assign order_class_desktop = order_class_desktop | append: ' lg:hidden'
					endif

          assign order_classes = order_class_mobile | append: order_class_desktop
					assign content_width = settings.content_class_gridcols | append: ' ' | append: settings.content_class_gridcols_desktop 
					assign content_item_classes = order_classes | append: ' ' | append: content_width
					assign attributes = ':data-index="(window.innerWidth >= 1024) ? ' | append: settings.content_position_desktop | append: ' : ' | append: settings.content_position | append: '"'

					render 'content-item' settings:settings item_classes:content_item_classes collection_grid:true attributes:attributes
				%}
			{% endif %}
		</div>
	</div>
</div>
{% endunless %}
