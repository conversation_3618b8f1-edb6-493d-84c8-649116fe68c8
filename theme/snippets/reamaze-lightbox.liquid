<script type="text/javascript" async defer src="https://cdn.reamaze.com/assets/reamaze.js"></script>
<script type="text/javascript">
    var _support = _support || { 'ui': {}, 'user': {} };
    _support['account'] = '{{settings.reamaze_account}}';
    _support['ui']['contactMode'] = '{{settings.reamaze_contactMode}}';
    _support['ui']['enableKb'] = '{{settings.reamaze_enableKb}}';
    _support['ui']['styles'] = {
        widgetColor: '{{settings.reamaze_widget_color | color_to_rgb}}',
        gradient: '{{settings.reamaze_gradient}}',
    };
    _support['ui']['shoutboxFacesMode'] = '{{settings.reamaze_shoutboxFacesMode}}';
    _support['ui']['faces'] = ['{{settings.reamaze_faces_image | img_url}}'];
    _support['ui']['shoutboxHeaderLogo'] = '{{settings.reamaze_shoutboxHeaderLogo}}';
    _support['apps'] = {
        faq: {"enabled":'{{settings.reamaze_faq}}'},
        recentConversations: {},
        orders: {
            "enabled":'{{settings.reamaze_orders}}',
            "enable_notes":'{{settings.reamaze_orders_notes}}'
        }
    };
    _support['user'] = {
        shop: '{{ shop.permanent_domain }}',
        id: '{{ customer.id }}',
        authkey: '{{ customer.id | append:':' | append:customer.email | append:':' | append:shop.permanent_domain | append:':' | append:settings.reamaze_sso_key | sha1 }}',
        email: '{{ customer.email }}',
        name: '{{ customer.name }}',
        data: {
            '({{ shop.permanent_domain }}) Order count': '{{ customer.orders_count }}',
            '({{ shop.permanent_domain }}) Total spent': '{{ customer.total_spent | money }}',
            '({{ shop.permanent_domain }}) Recent order': '{% if customer.last_order.name %}{{ customer.last_order.name }} - {{ customer.last_order.created_at | date: '%B %d, %Y %I:%M%p' }}{% endif %}'
        }
    };
</script>

