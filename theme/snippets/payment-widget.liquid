{% liquid 
  if settings.image_1 != blank 
    assign buttons = true
  endif
%}

  {% if settings.inclusion_js != blank %}
  <template x-if="{{ settings.inclusion_js }}">
  {% endif %}

  <div>

    <p class="items-center text-xs ml-0 mb-0 payments-banner">  
      {% capture payment %}
      <span x-text="money.format({{ total | default: default_payment }} / {{ settings.payment_count }})"></span>
      {% endcapture %}
      {% 
        liquid
        assign text = settings.text | replace: '[ payment ]', payment | replace: '[ count ]', settings.payment_count
      %}
      {% render 'inline-icon-text' source:text icon_classes:'inline-block' buttons:buttons %}
    </p>
    <script>
      window.paymentWidget = {
        config:{{ settings | json }},

      }
      document.querySelectorAll('.payments-banner button').forEach((button,i)=>{
        button.addEventListener('click', e=>{
          paymentWidget.active = i+1
          Alpine.store('payments', paymentWidget)
          Modal.open('payment')
        })
      })
    </script>

  </div>

  {% if settings.inclusion_js %}
  </template>
  {% endif %}
