<dialog 
  data-modal="login" 
  class="modal modal--account modal--center"
  tabindex="0"
  x-data
>
  <button class="absolute p-4 right-0 top-0" onclick="Modal.close()">
    {% render 'icon' icon:'x' strokeWidth:2 %}
  </button>

  <template x-if="!!$store.customerRequest">
    <header>
      <template x-if="!!$store.customerRequest.login.title">
        <h2 class="account-modal__title" x-text="$store.customerRequest.login.title"></h2>
      </template>

      <template x-if="!!$store.customerRequest.login.prompt">
        <p class="account-modal__subtitle" x-text="$store.customerRequest.login.prompt"></p>
      </template>
    </header>
  </template>

  <main>
    <form action="/account/login" data-action="/account/login" onsubmit="event.preventDefault(); Customer.login(Util.form.values(this)).then(c=>Modal.close())">
      <label>{% render 'field' name: 'email' type: 'email' placeholder: 'Email' %}</label>
      <label>{% render 'field' name: 'password' type: 'password' placeholder: 'Password' %}</label>
      <label><input type="submit" value="Login" class="button button--primary button--large account-modal__button"></label>
    </form>
    <a class="account-modal__link" onclick="Modal.open('register')">{{ 'customer.register.title' | t }}</a>
  </main>

</dialog>

<dialog 
  data-modal="register" 
  class="modal modal--account modal--center"
  tabindex="0"
  x-data
>
  <button class="absolute p-4 right-0 top-0" onclick="Modal.close()" role="button" aria-label="close-popup">
    {% render 'icon' icon:'x' strokeWidth:2 %}
  </button>

  <template x-if="!!$store.customerRequest">
    <header>

      <template x-if="!!$store.customerRequest.register.title">
        <h2 class="account-modal__title" x-text="$store.customerRequest.register.title"></h2>
      </template>

      <template x-if="!!$store.customerRequest.register.prompt">
        <p class="account-modal__subtitle" x-text="$store.customerRequest.register.prompt"></p>
      </template>
    </header>
  </template>

  <main>
    <form action="/account" data-action="/account" onsubmit="event.preventDefault(); Customer.register(Util.form.values(this)).then(c=>Modal.close())">
      <label>{% render 'field' name: 'email' type: 'email' placeholder: 'Email' %}</label>
      <label>{% render 'field' name: 'password' type: 'password' placeholder: 'Password' %}</label>
      <label><input type="submit" value="Create An Account" class="button button--primary button--large account-modal__button" role="button" aria-label="create-an-account"></label>
    </form>
    <a class="account-modal__link" onclick="Modal.open('login')" role="link" aria-label="{{ 'customer.login.title' | t | downcase | replace: ' ', '-' }}">{{ 'customer.login.title' | t }}</a>
  </main>

</dialog>