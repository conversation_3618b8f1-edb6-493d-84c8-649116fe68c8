<div class="mega__link_list w-full lg:mb-0 mb-4 bg-white {{block.settings.width}} {{block.settings.desktop_width}} {{ block.settings.padding_vertical }} {{ block.settings.padding_horizontal }} {{ block.settings.padding_vertical_desktop }} {{ block.settings.padding_horizontal_desktop }} {% unless block.settings.linklist_border == 'border-none' %}{{ block.settings.linklist_border }} border-light {% endunless %}">
  {% if block.settings.linklist_background_color != blank or block.settings.linklist_color != blank %}
    {% style %}
      .dropdown-{{ dropdown.id }} .mega__link_list, .nav__off_canvas ul {
        background-color: {{ block.settings.linklist_background_color }};
        color: {{ block.settings.linklist_color }};
      }
      .dropdown-{{ dropdown.id }} .mega__link_list a {
        color: {{ block.settings.linklist_color }};
      }
    {% endstyle %}
  {% endif %}
  <div class="{% if block.settings.show_desktop == true and block.settings.show_mobile == true %} block {% elsif block.settings.show_desktop == true and block.settings.show_mobile == false %} lg:block hidden {% elsif block.settings.show_desktop == false and block.settings.show_mobile == true %} lg:hidden block {% endif %}">
    <div class="nav__list-columns nav-type-{{block.type|handle}} flex-grow mx-auto">
      <div class="nav__list-column-wrap flex flex-col {{block.settings.text_align}}">

        {% assign linkList = block.settings.linklist_menu %}
        
        <ul class="list-none m-0 p-0 flex flex-col{% if block.settings.nested_links %} lg:flex-row lg:flex-wrap{% endif %}">
          {% for link in linklists[linkList].links %}
            <li class="flex items-center justify-between flex-col items-start nav__item pt-4 lg:py-0 lg:px-2 {% if block.settings.nested_links %} lg:flex-1{% endif %}">
              <a class="lg:mt-2 lg:first:mt-0 text-xs w-full font-semibold" href="{{ link.url }}">
                {{ link.title }}
              </a>
              {%- if link.links != blank -%}
                <ul class="list-none m-0 p-0 w-full">
                  {%- for child_link in link.links -%}
                    <li class="flex items-center justify-between nav__item px-4 py-2 lg:px-0 lg:pb-0">
                      <a class="text-xs font-semibold lg:font-normal" href="{{ child_link.url }}" {% if child_link.active %}aria-current="page"{% endif %}>
                        {{ child_link.title }}
                      </a>
                    </li>
                  {%- endfor -%}
                </ul>
              {%- endif -%}
            </li>
          {% endfor %}
        </ul>

      </div>
    </div>
  </div>
</div>