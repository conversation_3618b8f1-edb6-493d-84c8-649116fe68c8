<div 
  class="product-actions"
  {% comment %} Special handling for products reactivity{% endcomment %}
  x-cloak 
  x-data="{
    product:$store.products['{{ product.handle }}'],
    variant:$store.products['{{ product.handle }}'].variant,
  }"
  x-init="
    $alpine.listen('Product', () => {
      $data.product = $store.products['{{ product.handle }}']
      $data.variant = $store.products['{{ product.handle }}'].variant
    })
  "> 

  <div class="flex product-form__actions">
