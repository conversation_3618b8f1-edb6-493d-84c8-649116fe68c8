{%  if template == 'product.bundle-v2' %} 
  <div class="product-essentials__media product-essentials__media--grid grid grid-cols-2 {% render 'class-settings' settings:settings, prefix:'media_grid_class' %}" x-data>
      <template x-for="(media, index) in $store.products[Object.keys(window.products)[0]].newmedia">
        <article class="product-essentials__media-item overflow-hidden" :data-media-type="media.media_type" style="{% render 'style-settings' settings:settings prefix:'media_item_style' %}">
          {% render 'product-media-alpine' media: 'media' %}
          <template x-if="index == 0">
            {% render 'product-badges' product:"$store.products[window.document.location.pathname.split('/').at(-1)]" location:'detail' %}
          </template>
        </article>
      </template> 
      {% style %}
        {% if settings.full_width_index != blank %}
          article.product-essentials__media-item:nth-of-type({{settings.full_width_index}}){
            grid-column: span 2 / span 2;
          }
        {% endif %}
        {% if settings.full_width_words != blank %}
          article.product-essentials__media-item:has(img[alt*="{{ settings.full_width_words }}"]) {
            grid-column: span 2 / span 2;   
          }
        {% endif %}
        @media (min-width:1024px) {
          .product-essentials__media-item img.zoomable {cursor:zoom-in;}
          .product-essentials__media-item img.zoomable.zoom {
            cursor:zoom-out;
            transform: scale(2);
            transform-origin: var(--zoom-x) var(--zoom-y);
          }
        }
      {% endstyle %}
    </div>
{% else %}
  <div class="product-essentials__media product-essentials__media--grid grid grid-cols-2 {% render 'class-settings' settings:settings, prefix:'media_grid_class' %}" x-data>
  <template x-for="(media, index) in $store.products[window.document.location.pathname.split('/').at(-1)].media">
    <article {% if settings.grid_hide_model_image and settings.grid_hide_alt_txt != blank %}x-show="(media.alt !== null && !('{{settings.grid_hide_alt_txt}}'.split(',').some(value => media.alt.includes(value)))) || media.alt === null"{% endif %} class="product-essentials__media-item overflow-hidden" :data-media-type="media.media_type" style="{% render 'style-settings' settings:settings prefix:'media_item_style' %}">
      {% render 'product-media-alpine' media: 'media' settings: settings %}
      <template x-if="index == 0">
        {% render 'product-badges' product:"$store.products[window.document.location.pathname.split('/').at(-1)]" location:'detail' %}
      </template>
    </article>
  </template> 
  
    <template x-if="($store.products[window.document.location.pathname.split('/').at(-1)].media.length % 2) !== 0">
      <style>
        {% if settings.full_width_index != blank %}
          article.product-essentials__media-item:nth-of-type({{ settings.full_width_index }}) {
            grid-column: span 2 / span 2;
          }
        {% endif %}
      </style>
    </template>
    <template x-if="($store.products[window.document.location.pathname.split('/').at(-1)].media.length % 2) === 0">
      <style>
        {% if settings.full_width_index != blank and settings.even_number_image_width == false %}
          article.product-essentials__media-item:nth-of-type({{ settings.full_width_index }}) {
            grid-column: span 2 / span 2;
          }
        {% endif %}
      </style>
    </template>  

    <style>
    {% if settings.full_width_words != blank %}
      article.product-essentials__media-item:has(:is(img,video)[alt*="{{ settings.full_width_words }}"]) {
        grid-column: span 2 / span 2;   
      }
    {% endif %}
  </style>
  <style>
    @media(min-width:1024px){
    .product-essentials__media-item img.zoomable {cursor:{%- if settings.media_grid_modal_zoom == 'modal_over_click' -%}url(https://cdn.shopify.com/s/files/1/0015/9229/5523/files/zoom.png?381615),auto{%- elsif settings.media_grid_modal_zoom == 'zoom_hover_over_click' -%}zoom-in{%- endif -%};}
    .product-essentials__media-item img.zoomable.zoom {
      cursor:zoom-out;
      transform: scale(2);
      transform-origin: var(--zoom-x) var(--zoom-y);
    }
    }
  </style>
</div>
{% endif %}

{%- if settings.media_grid_modal_zoom == 'modal_over_click' -%}
  {% render 'product-essentials-modal' hide_model_image_check:settings.grid_hide_model_image, image_hide_alt_txt:settings.grid_hide_alt_txt %}
{%- endif -%}