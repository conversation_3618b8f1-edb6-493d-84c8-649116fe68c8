{% assign announcements = blocks %}
{% assign offset = index  %}

<div class="announcements swiper w-full" aria-live="polite" x-init="$nextTick(()=>{Carousels.create($el)})">
  <script type="swiper/config">
    { 
      autoplay: {
        delay:{{ interval | default: settings.interval | default: 5 | times: 1000 }}
      },
      loop:1
    }
  </script>
  <div class="swiper-wrapper" x-data>
    {% liquid 
      assign has_js = false
      for announcement in announcements offset:offset
        if announcement.settings.inclusion_js != blank
          assign has_js = true
        endif
        if announcement.type != 'announcement' 
          break 
        endif
        unless announcement.settings.inclusion == blank
          render 'announcement' settings:announcement.settings
        endunless
      endfor
    %}
  </div>
</div>