<script>
  if (Shopify.Checkout.step == 'contact_information') {
    document.addEventListener('page:load', function() {
      {%- assign sms_legal_header = settings.melin_sms_legal_consent_copy1 -%}
      {%- assign sms_legal_copy = settings.melin_sms_legal_consent_copy2 -%}
      var d1 = document.querySelector(".section--shipping-address");

      if (d1) {
        var dc1 = d1.querySelector('.section__content');
        var dc2 = dc1.querySelector('.fieldset');
        dc2.insertAdjacentHTML('beforeend', `<div class="field sms-checkbox"><div class="checkbox-wrapper"><div class="checkbox__input"> <input size="30" type="hidden" name="checkout[sms-checkbox]"> <input name="checkout[sms-checkbox]" type="hidden" value="0"><input class="input-checkbox" data-backup="sms-checkbox" type="checkbox" value="1" name="checkout[sms-checkbox]" id="checkout_sms-checkbox"></div> <label class="checkbox__label" for="checkout_sms-checkbox">
          {%- if sms_legal_header != blank -%}
              <b>{{ sms_legal_header }}</b>
          {%- endif -%}
          {%- if sms_legal_copy != blank -%}
              <p>{{ sms_legal_copy }}</p>
          {%- endif -%}</label></div></div>
        `);

        var x_phone = document.getElementById("checkout_shipping_address_phone");
        if (x_phone) {
          x_phone.setAttribute("maxlength", 14);
          x_phone.addEventListener("focus", myFocusFunction, true);
        }
        var sms_checkbox = document.querySelector(".sms-checkbox");

        function myFocusFunction() {
          sms_checkbox.style.display = "block";
        }

        var myInput = document.getElementById("checkout_shipping_address_phone");

        if (myInput && myInput.value) {
          sms_checkbox.style.display = "block";
        }

        $('[data-step="contact_information"] form').on('submit', (e) => {
          var checkBox = document.getElementById("checkout_sms-checkbox");
          if (checkBox.checked == true) {
            var formattedNumber = document.getElementById("checkout_shipping_address_phone").value;
            var formatNumber = formatPhone(formattedNumber);

            exponea.identify({ phone_id: formatNumber });
            exponea.update({ phone: formatNumber });
            exponea.track('double_opt_in', {
              action: 'new',
              phone: formatNumber,
              source: 'API Checkout SMS Signup',
              source_id: '257484',
              consent_list: []
            });

            // Function to format the number to 001 format instead of +1
            function formatPhone(phone) {
              phone = phone.split(' ').join('');
              phone = phone.split('(').join('');
              phone = phone.split(')').join('');
              phone = phone.split('-').join('');
              if (phone.length === 10) {
                return '+1' + phone;
              } else 
              if (phone.startsWith('001')) {
                return phone.replace('001', '+1');
              } else
              if  (phone.startsWith('+1')) {
                return phone;
              } else
              if (phone.startsWith('1')) {
                return phone.replace('1', '+1');
              } else return phone;
            }
          }
        });
      }
    });
  }
</script>