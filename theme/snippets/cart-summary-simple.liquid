<div class="slider-cart__summary text-black flex py-4" x-data>
  <h2 class="slider-cart__summary-text"> 

    <template x-if="$store.cart.item_count == 0">
      <span>{{ 'sections.cart.empty' | t }}</span>
    </template>
    <template x-if="$store.cart.item_count > 0">
      <span>
        <span class="slider-cart__summary-count" x-text="$store.cart.item_count + ' Items'"></span>
        <span>|</span>
        <span class="slider-cart__summary-price" x-text="money.format($store.cart.total_price).split('.')[0]"></span>
      </span>
    </template>
  </h2>
</div>
