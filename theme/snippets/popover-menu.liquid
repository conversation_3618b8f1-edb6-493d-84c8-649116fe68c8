<div class="popover-menu" x-data type="popover">
  <summary class="popover-menu__trigger">
    <span class="popover-menu__label">
      
    </span>
    <span 
    class="popover-menu__value" {% if settings.source != blank or source != blank %}x-text="{{ source | default: settings.source }}"{% endif %}
    >
      {{ value | default: settings.value }}
    </span>
  </summary>
  <div class="popover-menu__popover absolute top-full">
    <ul class="popover-menu__menu">
      {% assign values = values | default: settings.values | newline_to_br | split: '<br/>' %}
      {% for value in values %}
      <li class="popover-menu__menu-item">
        {{ value }}
      </li>
      {% endfor %}
    </ul>
  </div>
</details>


<div class="field">
  <label class="field__inline-select">
    <span>{{ label | default: settings.label }}</span>
    <select id="" class="" name="Favorite Color">
      <option>Green</option>
      <option> Red</option>
      <option> Blue</option>
    </select>
  </label>
</div>