<div x-data="{optionsVisible:false}">

	{% assign subscription_options = settings.subscription_options | newline_to_br | split: '<br />' %}
		
	{%- liquid 
		capture all
		for option in subscription_options
			echo option | split: ':' | first | strip | prepend: "'" | append: "',"
		endfor
		echo settings.subscription_key | prepend: "'" | append: "'"
		endcapture
		assign subscribe = all | prepend: 'Customer.subscriptions.subscribe([' | append: ']); return false;'

		comment
		if settings.identity_key != blank
			assign identify = "Customer.identify('" | append: settings.identity_key | append: "'," | append: settings.field_name | append: ");"
			assign subscribe = subscribe | prepend: identify
		endif
		endcomment
	-%}

	{% if settings.title != blank or settings.subtext != blank %}
		<header class="w-full">
			{% if settings.title != blank %}<h4 class="account-subscription__title my-0">{{ settings.title }}</h4>{% endif %}
			{% if settings.subtext != blank %}<template x-if="!optionsVisible"><div class="account-subscription__subtext">{{ settings.subtext }}</div></template>{% endif %}
			{% if settings.info_text != blank %}<template x-if="optionsVisible"><div class="account-subscription__subtext">{{ settings.info_text }}</div></template>{% endif %}
		</header>
	{% endif %}

	<template x-if="!$store.customer.subscriptions['{{ settings.subscription_key }}']">

		{% render 'mini-form' settings:settings on_submit:subscribe %}

	</template>

	<template x-if="$store.customer.subscriptions['{{ settings.subscription_key }}']">

		<article>
			<label>
			<input class="account-subscription__input" x-mask="{{ settings.input_pattern }}" :value="$store.customer.identity['{{ settings.identity_key }}'].replace(/^\+[0-9]/, '');" disabled>
			</label>
			{% comment %} <input class="account-subscription__input" x-mask="{{ settings.input_pattern }}" :value="$store.customer.identity['{{ settings.identity_key }}'].replace(/^\+[0-9]/, '');" disabled /> {% endcomment %}

			<details class="group">
				<summary @click="optionsVisible = !optionsVisible" class="group-open:hidden button button--light">{{ 'customer.account.manage_subscription' | t }}
				</summary> 
				<article>
					<form action="">
						<fieldset class="field">
							{% for option in subscription_options %}
								{% assign sub = option | split: ':' %}
									<label class="account-subscription__option field__checkbox">
										<input id="{{ sub[0] | strip }}" name="{{ sub[0]| strip  }}" type="checkbox" onclick="Customer.subscriptions.toggle('{{ sub[0] | strip }}')" x-model="$store.customer.subscriptions['{{ sub[0] | strip }}']">
										<span>
											<h5 class="account-subscription__option-label my-0">{{ sub[1] }}</h5>
											<p class="account-subscription__option-desc my-0">{{ sub[2] }}</p>
										</span>
									</label>
							{% endfor %}
						</fieldset>

						<fieldset class="account-subscription__button-set flex gap-3">
							<button @click="optionsVisible = false" class="button button--primary" onclick="this.closest('details').open=false; return false;">
								{{ 'customer.account.save_subscription' | t }}
							</button>
							<button class="button button--light" onclick="Customer.subscriptions.unsubscribe([{{ all | replace: ' ',''}}]); return false;">
								{{ 'customer.account.unsubscribe_all' | t }} 
							</button>
						</fieldset>

						<div class="account-subscription__cancel">
							<button @click="optionsVisible = false" class="button button--simple button--w-icon" onclick="this.closest('details').open=false; return false;">
								{% render 'icon' icon:'chevron-left' height:'16' width:'16' strokeWidth:2 %} 
								{{ 'customer.account.subscription_edit_cancel' | t }}
							</button>
						</div>
					</form>
				</article>
			</details>

		</article>

	</template>

</div>



