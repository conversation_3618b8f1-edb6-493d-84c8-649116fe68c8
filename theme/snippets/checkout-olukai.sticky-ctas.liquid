<button class="step__footer__continue-btn sticky__continue-btn btn" onclick="sessionStorage.setItem('CheckoutButton', 'Sticky_Button'); document.getElementById('continue_button').click();">
	<span class="continue-btn--contact">Continue to Shipping</span>
	<span class="continue-btn--shipping">Continue to Payment</span>
	<span class="continue-btn--payment">Pay Now</span>
</button>

<style>
	@media (max-width: 999px) {
		.checkout__content .step__footer__continue-btn.sticky__continue-btn {
			width: 100%;
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 3;	
			border-radius: 0;
		}
		.continue-btn--contact, .continue-btn--shipping, .continue-btn--payment {
			display: none;
		}
		.checkout-step--contact_information .continue-btn--contact {
			display: block;
		}
		.checkout-step--shipping_method .continue-btn--shipping {
			display: block;
		}
		.checkout-step--payment_method .continue-btn--payment {
			display: block;
		}
		.step__footer, .checkout__footer {
			z-index: 4;
			position: relative;
		}
		.step__footer > *, .checkout__footer > * {
			position: relative;
			z-index: 4;
		}
		.step__footer:before, .checkout__footer:before {
			position: absolute;
	    background-color: #fcf9f3;
	    width: 100vw;
	    height: calc(100% + 50px);
	    left: 50%;
	    top: -20px;
	    content: '';
	    transform: translateX(-50%);
			z-index: 3;
		}
		.checkout__footer:before {
			top: 0;
		}
	}
</style>

<script>
	(function(){ document.documentElement.classList.add(`checkout-step--${ Shopify.Checkout.step }`)})()

	document.addEventListener('DOMContentLoaded', function() {
		var formError = document.querySelector('.field--error')

		if (!formError) return false

		formError.scrollIntoViewIfNeeded()
	})

	document.addEventListener("scroll", function() {
		const continueBtn = document.querySelector(".sticky__continue-btn");

		if (window.innerWidth >= 1000) return false;

		if (window.innerHeight + window.scrollY >= document.querySelector('.content.checkout__content').offsetHeight) {
			continueBtn.style.display = "none";
		} else {
			continueBtn.style.display = "block";
		}
	});
</script>