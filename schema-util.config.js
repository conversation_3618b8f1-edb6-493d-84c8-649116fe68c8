const ConditionalDisplay = require('./components/conditional-display/schema')

module.exports = {
  "sectionPath": "./theme/sections",
  

    
    "Spacing": [
      {
        "value": "{{prop}}-0",
        "label": "None"  
      },
	  {
        "value": "{{prop}}-3xs",
        "label": "3XS"  
      },
      {
        "value": "{{prop}}-2xs",
        "label": "2XS"  
      },
      {
        "value": "{{prop}}-xs",
        "label": "XS"  
      },
      {
        "value": "{{prop}}-sm",
        "label": "SM"  
      },
      {
        "value": "{{prop}}-md",
        "label": "MD"  
      },
      {
        "value": "{{prop}}-lg",
        "label": "LG"  
      },
      {
        "value": "{{prop}}-xl",
        "label": "XL"  
      },
      {
        "value": "{{prop}}-2xl",
        "label": "2XL"  
      },
      {
        "value": "{{prop}}-3xl",
        "label": "3XL"  
      }
    ],

  "SpacingDesktop": [
    {
      "value": "lg:{{prop}}-0",
      "label": "None"  
    },
	{
      "value": "lg:{{prop}}-3xs",
      "label": "3XS"  
    },
    {
      "value": "lg:{{prop}}-2xs",
      "label": "2XS"  
    },
    {
      "value": "lg:{{prop}}-xs",
      "label": "XS"  
    },
    {
      "value": "lg:{{prop}}-sm",
      "label": "SM"  
    },
    {
      "value": "lg:{{prop}}-md",
      "label": "MD"  
    },
    {
      "value": "lg:{{prop}}-lg",
      "label": "LG"  
    },
    {
      "value": "lg:{{prop}}-xl",
      "label": "XL"  
    },
    {
      "value": "lg:{{prop}}-2xl",
      "label": "2XL"
    },
    {
      "value": "lg:{{prop}}-3xl",
      "label": "3XL"
    },
    {
      "value": "lg:{{prop}}-4xl",
      "label": "4XL"
    },
    {
      "value": "lg:{{prop}}-5xl",
      "label": "5XL"
    },
    {
      "value": "lg:{{prop}}-6xl",
      "label": "6XL"
    },
    {
      "value": "lg:{{prop}}-7xl",
      "label": "7XL"
    },
    {
      "value": "lg:{{prop}}-8xl",
      "label": "8XL"
    }
  ],

  "SizePercent": [
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}{{ prop }}-auto",
      "label": "Auto"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}{{ prop }}-0",
      "label": "0%"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}{{ prop }}-[10%]",
      "label": "10%"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}{{ prop }}-1/5",
      "label": "20%"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}{{ prop }}-3/10",
      "label": "30%"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}{{ prop }}-2/5",
      "label": "40%"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}{{ prop }}-1/2",
      "label": "50%"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}{{ prop }}-3/5",
      "label": "60%"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}{{ prop }}-7/10",
      "label": "70%"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}{{ prop }}-4/5",
      "label": "80%"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}{{ prop }}-9/10",
      "label": "90%"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}{{ prop }}-full",
      "label": "100%"
    }
  ],

  "Grid": [
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}grid-cols-1",
      "label": "1 Column"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}grid-cols-2",
      "label": "2 Columns"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}grid-cols-3",
      "label": "3 Columns"
    },
    {
      "value": "{% if variant %}{{ variant }}:{% endif %}grid-cols-4",
      "label": "4 Columns"
    }
  ],

  "TextElement": [
    {
      "value": "h1",
      "label": "{{ label_prefix }} Heading 1"
    },
    {
      "value": "h2",
      "label": "{{ label_prefix }} Heading 2"
    },
    {
      "value": "h3",
      "label": "{{ label_prefix }} Heading 3"
    },
    {
      "value": "h4",
      "label": "{{ label_prefix }} Heading 4"
    },
    {
      "value": "h5",
      "label": "{{ label_prefix }} Heading 5"
    },
    {
      "value": "p",
      "label": "{{ label_prefix }} Paragraph"
    },
    {
      "value": "div",
      "label": "{{ label_prefix }} Div"
    }
  ],


  "TypeStyle": [
    {
      "value": "",
      "label": "{{ label_prefix }} Auto"
    },
    {
      "value": "type-body",
      "label": "{{ label_prefix }} Body"
    },
    {
      "value": "type-hero",
      "label": "{{ label_prefix }} Hero"
    },
    {
      "value": "type-eyebrow",
      "label": "{{ label_prefix }} Eyebrow"
    },
    {
      "value": "type-headline",
      "label": "{{ label_prefix }} Headline"
    },
    {
      "value": "type-subline",
      "label": "{{ label_prefix }} Subline"
    },
    {
      "value": "type-micro",
      "label": "{{ label_prefix }} Micro"
    },
    {
      "value": "type-item",
      "label": "{{ label_prefix }} Item Title"
    },
    {
      "value": "type-section",
      "label": "{{ label_prefix }} Section Title"
    }
  ],


  "TypeSize": [
    {
      "value": "",
      "label": "{{ label_prefix }} Default"
    },
    {
      "value": "type--sm",
      "label": "{{ label_prefix }} Smaller"
    },
    {
      "value": "type--lg",
      "label": "{{ label_prefix }} Larger"
    }
  ],


  "ButtonStyle": [
    {
      "value": "button--primary",
      "label": "{{ label_prefix }} Primary"
    },
    {
      "value": "button--secondary",
      "label": "{{ label_prefix }} Secondary"
    },
    {
      "value": "button--tertiary",
      "label": "{{ label_prefix }} Tertiary"
    },
    {
      "value": "button--light",
      "label": "{{ label_prefix }} Light"
    },
    {
      "value": "button--dark",
      "label": "{{ label_prefix }} Dark"
    },
    {
      "value": "button--pop",
      "label": "{{ label_prefix }} Pop"
    },
    {
      "value": "button--highlight",
      "label": "{{ label_prefix }} Highlight"
    },
    {
      "value": "button--action",
      "label": "{{ label_prefix }} Action"
    },
    {
      "value": "button--simple",
      "label": "{{ label_prefix }} Simple"
    },
    {  
      "value": "button--emphasis",
      "label": "{{ label_prefix }} Emphasis"
    },
    {
      "value": "button--light-text-link",
      "label": "{{ label_prefix }} Light Text Link"
    }, 
    {
      "value": "button--link",
      "label": "{{ label_prefix }} Text Link"
    },
    {
      "value": "button--micro-link",
      "label": "{{ label_prefix }} Micro Text Link"
    },
    {
      "value": "button--icon",
      "label": "{{ label_prefix }} Icon"
    },
    {
      "value": "button--primary-hover",
      "label": "{{ label_prefix }} Primary Hover"
    },
    {
      "value": "button--secondary-hover",
      "label": "{{ label_prefix }} Secondary Hover"
    },
    {
      "value": "button--tertiary-hover",
      "label": "{{ label_prefix }} Tertiary Hover"
    }
  ],

  "Break": [
    {
      "type": "paragraph",
      "content": "▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀\n{{ label }}\n▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀▄▀"
    }
  ],

  "Position": [
    {
      "type": "header",
      "content": "Position Settings"
    },
    {
      "type": "range",
      "id": "pos_x",
      "label": "Position X",
      "min": 0,
      "max": 100,
      "unit": "%",
      "default": 0
    },
    {
      "type": "range",
      "id": "pos_y",
      "label": "Position Y",
      "min": 0,
      "max": 100,
      "unit": "%",
      "default": 0
    },
    {
      "type": "select",
      "id": "content_origin",
      "label": "Content Origin",
      "options": [
        {
          "label": "Top Left",
          "value": "translate(0,0)"
        },
        {
          "label": "Top Center",
          "value": "translate(-50%,0)"
        },
        {
          "label": "Top Right",
          "value": "translate(-100%,0)"
        },
        {
          "label": "Middle Left",
          "value": "translate(0,-50%)"
        },
        {
          "label": "Middle Center",
          "value": "translate(-50%,-50%)"
        },
        {
          "label": "Middle Right",
          "value": "translate(-100%,-50%)"
        },
        {
          "label": "Bottom Left",
          "value": "translate(0,-100%)"
        },
        {
          "label": "Bottom Center",
          "value": "translate(-50%,-100%)"
        },
        {
          "label": "Bottom Right",
          "value": "translate(-100%,-100%)"
        }
      ]
    },
    {
      "type": "range",
      "id": "pos_x_mobile",
      "label": "Position X (mobile)",
      "min": 0,
      "max": 100,
      "unit": "%",
      "default": 0
    },
    {
      "type": "range",
      "id": "pos_y_mobile",
      "label": "Position Y (mobile)",
      "min": 0,
      "max": 100,
      "unit": "%",
      "default": 0
    },
    {
      "type": "select",
      "id": "content_origin_mobile",
      "label": "Content Origin (mobile)",
      "options": [
        {
          "label": "Top Left",
          "value": "translate(0,0)"
        },
        {
          "label": "Top Center",
          "value": "translate(-50%,0)"
        },
        {
          "label": "Top Right",
          "value": "translate(-100%,0)"
        },
        {
          "label": "Middle Left",
          "value": "translate(0,-50%)"
        },
        {
          "label": "Middle Center",
          "value": "translate(-50%,-50%)"
        },
        {
          "label": "Middle Right",
          "value": "translate(-100%,-50%)"
        },
        {
          "label": "Bottom Left",
          "value": "translate(0,-100%)"
        },
        {
          "label": "Bottom Center",
          "value": "translate(-50%,-100%)"
        },
        {
          "label": "Bottom Right",
          "value": "translate(-100%,-100%)"
        }
      ]
    }
  ],

  "Aspect": [
    {
      "type":"select",
      "id":"{{ id_prefix }}_aspect",
      "label": "{{ label_prefix }} Aspect Ratio",
      "options":[
        {
          "value":"{{ value_prefix }}aspect-auto",
          "label":"Auto" 
        },
        {
          "value":"{{ value_prefix }}aspect-[2/1]",
          "label":"2\:1" 
        },
        {
          "value":"{{ value_prefix }}aspect-[16/9]",
          "label":"16\:9" 
        },
        {
          "value":"{{ value_prefix }}aspect-[4/3]",
          "label":"4\:3" 
        },
        {
          "value":"{{ value_prefix }}aspect-[1/1]",
          "label":"1\:1" 
        },
        {
          "value": "{{ value_prefix }}aspect-[3/1]",
          "label": "3\:1"
        },
        {
          "value":"{{ value_prefix }}aspect-[3/4]",
          "label":"3\:4" 
        },
        {
          "value": "{{ value_prefix }}aspect-[8/30]",
          "label": "8\:30"
        },
        {
          "value":"{{ value_prefix }}aspect-[9/16]",
          "label":"9\:16" 
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_aspect_desktop",
      "label": "{{ label_prefix }} Aspect Ratio Desktop",
      "options":[
        {
          "value":"{{ value_prefix }}lg:aspect-auto",
          "label":"Auto" 
        },
        {
          "value":"{{ value_prefix }}lg:aspect-[2/1]",
          "label":"2\:1" 
        },
        {
          "value":"{{ value_prefix }}lg:aspect-[16/9]",
          "label":"16\:9" 
        },
        {
          "value":"{{ value_prefix }}lg:aspect-[4/3]",
          "label":"4\:3" 
        },
        {
          "value":"{{ value_prefix }}lg:aspect-[1/1]",
          "label":"1\:1" 
        },
        {
          "value": "{{ value_prefix }}lg:aspect-[3/1]",
          "label": "3\:1"
        },
        {
          "value":"{{ value_prefix }}lg:aspect-[3/4]",
          "label":"3\:4" 
        },
        {
          "value": "{{ value_prefix }}lg:aspect-[8/30]",
          "label": "8\:30"
        },
        {
          "value":"{{ value_prefix }}lg:aspect-[9/16]",
          "label":"9\:16" 
        }
      ]
    }
  ],

  "ArticleItem": [
    {
      "type": "article",
      "id": "article",
      "label": "Blog Article"
    },
    {
      "type": "color",
      "id": "item_style_background_color",
      "label": "Item Background Color"
    }
  ],

  "MenuItem": [
    {
      "type":"image_picker",
      "label":"Image",
      "id":"image"
    },
    {
      "type": "link_list",
      "id": "link_list",
      "label": "Navigation Menu"
    },
    {
      "type": "number",
      "id": "columns",
      "label": "Desktop Columns for Submenus"
    },
    {
      "type": "select",
      "id": "menu_item_interaction",
      "label": "Menu Item Interaction",
      "default": "grouped",
      "options": [
        {
          "label":"Grouped",
          "value":"grouped"
        },
        {
          "label":"Independant",
          "value":"independant"
        }
      ]
    },
    {
      "type": "paragraph",
      "content": "@include FlexLayout, id_prefix:item_class, label_prefix:Item, default_direction:flex-col"
    },
    {
      "type": "header",
      "content": "Menu Settings",
      "info": "Settings for the first level of the link list"
    },
    {
      "type": "paragraph",
      "content": "@include FlexLayout, id_prefix:menu_class, label_prefix:Menu, default_direction:flex-col"
    }
  ],

  "ContentItem": [
    {
			"type": "paragraph",
			"content": "@include SectionDisplay, label_prefix:Display, id_prefix:item_class"
		},
    {
      "type": "paragraph",
      "content": "@include FlexLayout, id_prefix:item_class, label_prefix:Item, default_direction:flex-col"
    },
    {
      "type": "color_background",
      "id": "item_style_background",
      "label": "Item Background Color"
    },
    {
      "type": "color_background",
      "id": "content_style_background",
      "label": "Content Background Color"
    },
    {
      "type": "header",
      "content": "Content Interactivity"
    },
    {
      "type": "url",
      "id": "link",
      "label": "Link"
    },
    {
      "type": "liquid",
      "id": "liquid_link",
      "label": "Link (Liquid)",
      "info": "Replaces the basic Link setting."
    },
    {
      "type": "paragraph",
      "content": "@include Break, label:🅜🅔🅓🅘🅐"
    },
    {
      "type": "header",
      "content": "Media"
    },
    {
      "type":"image_picker",
      "label":"Image",
      "id":"image"
    },
    {
      "type":"image_picker",
      "label":"Image (Desktop)",
      "id":"image_desktop"
    },
    {
      "type":"select",
      "label":"Image Position",
      "id":"image_class_position",
      "options": [
        {
          "label":"Inline",
          "value":""
        },
        {
          "label":"Background Fill",
          "value":"absolute inset-0 h-full w-full object-cover"
        }
      ]
    },
    {
      "type": "select",
      "label": "Loading",
      "id": "image_loading",
      "options": [
        {
          "label": "Lazy",
          "value": "lazy"
        },
        {
          "label": "Eager",
          "value": "eager"
        }
      ],
      "default": "eager"
    },
    {
      "type": "paragraph",
      "content": "@include BlockWidths, id_prefix:media_class, label_prefix:Media "
    },
    {
      "type":"text",
      "label":"Video",
      "id":"video"
    },
    {
      "type":"text",
      "label":"Video For Mobile",
      "id":"videomobile"
    },
    {
      "type":"select",
      "label":"Video Position",
      "id":"video_class_position",
      "options": [
        {
          "label":"Inline",
          "value":""
        },
        {
          "label":"Background Fill",
          "value":"absolute inset-0 h-full"
        }
      ]
    },
    {
      "type":"select",
      "label":"Video Position For Mobile",
      "id":"videomobile_class_position",
      "options": [
        {
          "label":"Inline",
          "value":""
        },
        {
          "label":"Background Fill",
          "value":"absolute inset-0 h-full"
        }
      ]
    },
    {
      "type":"image_picker",
      "label":"Video Poster For Desktop",
      "id":"posterDesktop"
    },
    {
      "type":"image_picker",
      "label":"Video Poster for Mobile",
      "id":"posterMobile"
    },
    {
      "type": "checkbox",
      "label": "Autoplay",
      "id": "video_attr_autoplay",
      "default": true
    },
    {
      "type": "checkbox",
      "label": "Muted",
      "id": "video_attr_muted",
      "default": true
    },
    {
      "type": "checkbox",
      "label": "Loop",
      "id": "video_attr_loop",
      "default": true
    },
    {
      "type": "checkbox",
      "label": "Controls",
      "id": "video_attr_controls",
      "default": false
    },
    {
      "type": "checkbox",
      "label": "Playsinline",
      "id": "video_attr_playsinline",
      "default": true
    },
    {
      "type": "header",
      "content": "Video Defer Settings"
    },
    {
			"type": "select",
			"id": "loading_method",
			"label": "Video Loading Method",
			"options": [
				{
					"value": "immediate",
					"label": "Immediate"
				},
				{
					"value": "scroll",
					"label": "On Scroll"
				},
				{
					"value": "hover",
					"label": "On Hover"
				},
				{
					"value": "time",
					"label": "After Delay"
				}
			],
			"default": "scroll"
		},
		{
			"type": "range",
			"id": "loading_delay",
			"label": "Loading Delay (seconds)",
			"min": 0,
			"max": 10,
			"step": 1,
			"default": 2,
			"info": "Only applies when loading method is After Delay"
		},
    {
      "type": "paragraph",
      "content": "@include Break, label:🅒🅞🅝🅣🅔🅝🅣 🅛🅐🅨🅞🅤🅣"
    },

    {
      "type": "paragraph",
      "content": "@include BlockWidths, id_prefix:content_class, label_prefix:Content "
    },
    {
      "type": "paragraph",
      "content": "@include FlexLayout, id_prefix:content_class, label_prefix:Content"
    }, 
    {
      "type": "paragraph",
      "content": "@include Aspect, id_prefix:content_class, label_prefix:Content "
    },
    {
      "type": "paragraph",
      "content": "@include Break, label:🅣🅔🅧🅣 🅢🅣🅐🅒🅚"
    },
    {
      "type": "header",
      "content": "Text Stack"
    },
    {
      "type": "paragraph",
      "content": "@include BlockWidths, id_prefix:text_stack_class, label_prefix:Text Stack "
    },
    {
      "type": "paragraph",
      "content": "@include FlexLayout, id_prefix:text_stack_class, label_prefix:Text"
    },
    {
      "type":"image_picker",
      "label":"Title Image",
      "id":"title_image"
    },
    {
      "type":"liquid",
      "id": "svg",
      "label": "SVG"
    },
    {
      "type": "paragraph",
      "content": "@include BlockWidths, id_prefix:title_image_class, label_prefix:Title Image / SVG "
    },
    {
      "type":"radio",
      "label":"Text Justification",
      "id":"text_stack_class",
      "default":"text-center",
      "options": [
        {
          "label":"←",
          "value":"text-left"
        },
        {
          "label":"↔",
          "value":"text-center"
        },
        {
          "label":"→",
          "value":"text-right"
        }
      ]
    },
    {
      "type": "color",
      "id": "content_style_color",
      "label": "Text Color"
    },
    {
      "type": "paragraph",
      "content": "@include Text, id_prefix:text_item_1, label_prefix:Text 1 "
    },
    {
      "type": "paragraph",
      "content": "@include Text, id_prefix:text_item_2, label_prefix:Text 2 "
    },
    {
      "type": "paragraph",
      "content": "@include Text, id_prefix:text_item_3, label_prefix:Text 3 "
    },
    {
      "type": "header",
      "content": "Custom Liquid"
    },
    {
      "type":"liquid",
      "id": "liquid",
      "label": "Custom Liquid/HTML"
    },
    {
      "type": "paragraph",
      "content": "@include Break, label:🅑🅤🅣🅣🅞🅝 🅢🅔🅣"
    },
    {
      "type": "header",
      "content": "Button Settings"
    },
    {
      "type": "paragraph",
      "content": "@include FlexLayout id_prefix:buttons_, label_prefix:Buttons"
    },
    {
      "type": "paragraph",
      "content": "@include Button id_prefix:button_1, label_prefix:Button 1 "
    },
    {
      "type": "paragraph",
      "content": "@include Button id_prefix:button_2, label_prefix:Button 2 "
    },
    {
      "type": "paragraph",
      "content": "@include Button id_prefix:button_3, label_prefix:Button 3 "
    }
  ],


  "Hotspot": [
    {
      "type": "paragraph",
      "content": "@include Position id_prefix:overlay_class_"
    },
    {
      "type": "select",
      "id": "hotspot_tooltip_direction",
      "label": "Tooltip Direction",
      "options": [
        {
          "value": "top",
          "label": "Top"
        },
        {
          "value": "bottom",
          "label": "Bottom"
        },
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ]
    },
    {
      "type": "product",
      "id": "hotspot_product",
      "label": "Product"
    }
  ],

  "OverlayItem": [
    {
      "type": "paragraph",
      "content": "@include Position id_prefix:overlay_class_"
    },
    {
      "type": "select",
      "id": "overlay_class_width",
      "label": "Width",
      "options": [
        {
          "value": "@include SizePercent prop:w",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "overlay_class_height",
      "label": "Height",
      "options": [
        {
          "value": "@include SizePercent prop:h",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "overlay_class_width_desktop",
      "label": "Width (Desktop)",
      "options": [
        {
          "value": "@include SizePercent prop:w, variant:lg",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "overlay_class_height_desktop",
      "label": "Height (Desktop)",
      "options": [
        {
          "value": "@include SizePercent prop:h, variant:lg",
          "label": "Inclusion"
        }
      ]
    }
  ],

  "Button": [
    {
      "type": "header",
      "content": "{{ label_prefix }} Settings"
    },
    {
      "type": "text",
      "id": "{{ id_prefix }}_text",
      "label": "{{ label_prefix }} Text"
    },
    {
      "type": "text",
      "id": "{{ id_prefix }}_leading_icon",
      "label": "Leading Icon"
    },
    {
      "type": "text",
      "id": "{{ id_prefix }}_trailing_icon",
      "label": "Trailing Icon"
    },
    {
      "type": "url",
      "id": "{{ id_prefix }}_link",
      "label": "{{ label_prefix }} Link"
    },
    {
      "type": "liquid",
      "id": "{{ id_prefix }}_liquid_link",
      "label": "{{ label_prefix }} Link (Liquid)",
      "info": "Replaces the basic Link setting."
    },
    {
      "type": "liquid",
      "id": "{{ id_prefix }}_onclick",
      "label": "{{ label_prefix }} On Click"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_class_style",
      "label": "{{ label_prefix }} Style",
      "options": [
        {
          "value": "@include ButtonStyle",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_class_size",
      "label": "{{ label_prefix }} Size",
      "options": [
        {
          "value": "",
          "label": "Standard"
        },
        {
          "value": "{{ value_prefix }}button--large",
          "label": "Large"
        }
      ]
    },
  ],

  "SectionDisplay": [
    {
      "type": "header",
      "content": "{{ label_prefix }} Settings"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_visibility",
      "label": "{{ label_prefix }} Visibility",
      "options": [
        {
          "value": "",
          "label": "Mobile & Desktop"
        },
        {
          "value": "lg:hidden",
          "label": "Mobile"
        },
        {
          "value": "max-lg:hidden",
          "label": "Desktop"
        }
      ]
    }
  ],

  "Text": [
    {
      "type": "header",
      "content": "{{ label_prefix }} Settings"
    },
    {
      "type": "{{ type | default:'text' }}",
      "id": "{{ id_prefix }}_text",
      "label": "{{ label_prefix }} Text"
    },
    {
      "type": "liquid",
      "id": "{{ id_prefix }}_liquid",
      "label": "{{ label_prefix }} Text (Liquid)"
    },
    {
      "type": "liquid",
      "id": "{{ id_prefix }}_attr_x_text",
      "label": "{{ label_prefix }} Dynamic Text (Alpine)"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_element",
      "label": "{{ label_prefix }} Element",
      "default": "{{ default_element | default: 'p' }}",
      "options": [
        {
          "value": "@include TextElement",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_class_type_style",
      "label": "{{ label_prefix }} Type Style",
      "options": [
        {
          "value": "@include TypeStyle",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_class_type_size",
      "label": "{{ label_prefix }} Type Size",
      "options": [
        {
          "value": "@include TypeSize",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"color",
      "id":"{{ id_prefix }}_style_color",
      "label": "{{ label_prefix }} Color"
    },
  ],

  "RichText": [
    {
      "type": "header",
      "content": "{{ label_prefix }} Settings"
    },
    {
      "type": "richtext",
      "id": "{{ id_prefix }}_text",
      "label": "{{ label_prefix }} Text"
    },
    {
      "type": "liquid",
      "id": "{{ id_prefix }}_liquid",
      "label": "{{ label_prefix }} Text (Liquid)"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_class_type_style",
      "label": "{{ label_prefix }} Type Style",
      "options": [
        {
          "value": "@include TypeStyle",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_class_type_size",
      "label": "{{ label_prefix }} Type Size",
      "options": [
        {
          "value": "@include TypeSize",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"color",
      "id":"{{ id_prefix }}_style_color",
      "label": "{{ label_prefix }} Color"
    },
  ],

  "Accordion": [
    {
      "type": "checkbox",
      "id": "accordion_detail_attr_open",
      "label": "Open by Default",
      "info": "When grouped, only enable this for one of the accordions.",
      "default": false,
    },
    {
      "type": "header",
      "content": "Accordion Summary Settings"
    },
    {
      "type": "text",
      "id": "accordion_title",
      "label": "Accordion Title"
    },
    {
      "type": "select",
      "id": "accordion_summary_class_vertical_padding",
      "label": "{{ label_prefix }} Vertical Padding",
      "options": [
        {
          "value": "@include Spacing prop:py",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "accordion_summary_class_horizontal_padding",
      "label": "{{ label_prefix }} Horizontal Padding",
      "options": [
        {
          "value": "@include Spacing prop:px",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "accordion_summary_class_vertical_padding_desktop",
      "label": "{{ label_prefix }} Vertical Padding Desktop",
      "options": [
        {
          "value": "@include SpacingDesktop prop:py",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "accordion_summary_class_horizontal_padding_desktop",
      "label": "{{ label_prefix }} Horizontal Padding Desktop",
      "options": [
        {
          "value": "@include SpacingDesktop prop:px",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "header",
      "content": "Accordion Panel Settings"
    },
    {
      "type": "richtext",
      "id": "accordion_richtext",
      "label": "Accordion Rich Text"
    },
    {
      "type": "liquid",
      "id": "accordion_liquid",
      "label": "Accordion Liquid"
    },
    {
      "type": "select",
      "id": "accordion_panel_class_vertical_padding",
      "label": "{{ label_prefix }} Vertical Padding",
      "options": [
        {
          "value": "@include Spacing prop:py",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "accordion_panel_class_horizontal_padding",
      "label": "{{ label_prefix }} Horizontal Padding",
      "options": [
        {
          "value": "@include Spacing prop:px",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "accordion_panel_class_vertical_padding_desktop",
      "label": "{{ label_prefix }} Vertical Padding Desktop",
      "options": [
        {
          "value": "@include SpacingDesktop prop:py",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "accordion_panel_class_horizontal_padding_desktop",
      "label": "{{ label_prefix }} Horizontal Padding Desktop",
      "options": [
        {
          "value": "@include SpacingDesktop prop:px",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "header",
      "content": "Additional Settings"
    },
    {
      "type": "checkbox",
      "id": "collapse_padding",
      "label": "Collapse Padding",
      "info": "Collapse vertical padding when accordion is open"
    },
    {
      "type": "checkbox",
      "id": "accordion_detail_attr_data_animate",
      "label": "Enable animation",
      "default": false
    }
  ],

  "Heading": [
    {
      "type": "text",
      "id": "{{ id_prefix }}_text",
      "label": "{{ label_prefix }} Text"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_element",
      "label": "{{ label_prefix }} Element",
      "default": "{{ default_element | default: 'p' }}",
      "options": [
        {
          "value": "@include TextElement",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_class_type_style",
      "label": "{{ label_prefix }} Type Style",
      "options": [
        {
          "value": "@include TypeStyle",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_class_type_size",
      "label": "{{ label_prefix }} Type Size",
      "options": [
        {
          "value": "@include TypeSize",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "color",
      "id": "{{ id_prefix }}_style_color",
      "label": "{{ label_prefix }} Text Color"
    },
    {
      "type": "url",
      "id": "{{ id_prefix }}_link",
      "label": "{{ label_prefix }} Link"
    },
  ],

  "Container": [
    {
      "type": "header",
      "content": "Container Settings"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_container",
      "label": "{{ label_prefix }} Container",
      "options": [
        {
          "value": "w-full",
          "label": "Full Screen Width"
        },
        {
          "value": "container",
          "label": "Container Width"
        },
        {
          "value": "container container--wide",
          "label": "Wide Container"
        },
        {
          "value": "container container--narrow",
          "label": "Narrow Container"
        },
        {
          "value": "container container--tight",
          "label": "Very Narrow Container"
        },
        {
          "value": "container--product",
          "label": "Product Container Width"
        },
        {
          "value": "container--wide",
          "label": "Wide Container Width"
        }
      ]
    },
    {
      "type": "select",
      "id": "{{ id_prefix }}_vertical_padding",
      "label": "{{ label_prefix }} Vertical Padding",
      "options": [
        {
          "value": "@include Spacing prop:py",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "{{ id_prefix }}_horizontal_padding",
      "label": "{{ label_prefix }} Horizontal Padding",
      "options": [
        {
          "value": "@include Spacing prop:px",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "{{ id_prefix }}_vertical_padding_desktop",
      "label": "{{ label_prefix }} Vertical Padding Desktop",
      "options": [
        {
          "value": "@include SpacingDesktop prop:py",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "{{ id_prefix }}_horizontal_padding_desktop",
      "label": "{{ label_prefix }} Horizontal Padding Desktop",
      "options": [
        {
          "value": "@include SpacingDesktop prop:px",
          "label": "Inclusion"
        }
      ]
    },
  ],

  "BackgroundStyles": [
    {
      "type": "color",
      "id": "{{ id_prefix | default: 'wrapper_' }}style_background_color",
      "label": "{{ label_prefix }} Background Color"
    },
    {
      "type": "color_background",
      "id": "{{ id_prefix | default: 'wrapper_' }}style_background",
      "label": "{{ label_prefix }} Background Gradient"
    },
    {
      "type":"image_picker",
      "label":"{{ label_prefix }} Background Image Mobile",
      "id":"{{ id_prefix | default: 'wrapper_' }}bg_image_mob"
    },
    {
      "type":"image_picker",
      "label":"{{ label_prefix }} Background Image",
      "id":"{{ id_prefix | default: 'wrapper_' }}bg_image"
    },
  ],

  "SectionWrapper": [
    {
      "type": "header",
      "content": "{{ label_prefix }} Settings"
    },
    { 
      "type": "paragraph",
      "content": "@include BackgroundStyles"
    },
    {
      "type": "select",
      "id": "wrapper_class_vertical_padding",
      "label": "{{ label_prefix }} Vertical Padding",
      "options": [
        {
          "value": "@include Spacing prop:py",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "wrapper_class_horizontal_padding",
      "label": "{{ label_prefix }} Horizontal Padding",
      "options": [
        {
          "value": "@include Spacing prop:px",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "wrapper_class_vertical_padding_desktop",
      "label": "{{ label_prefix }} Vertical Padding Desktop",
      "options": [
        {
          "value": "@include SpacingDesktop prop:py",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type": "select",
      "id": "wrapper_class_horizontal_padding_desktop",
      "label": "{{ label_prefix }} Horizontal Padding Desktop",
      "options": [
        {
          "value": "@include SpacingDesktop prop:px",
          "label": "Inclusion"
        }
      ]
    },
  ],


  "FlexLayout" : [
    {
      "type": "header",
      "content": "{{ label_prefix }} Layout"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_display",
      "label": "{{ label_prefix }} Display",
      "default":"{{rst}}flex",
      "options": [
        {
          "value":"{{rst}}flex",
          "label":"Flex"
        },
        {
          "value":"{{rst}}grid {{rst}}grid-cols-1",
          "label":"Grid 1 Column"
        },
        {
          "value":"{{rst}}grid {{rst}}grid-cols-2",
          "label":"Grid 2 Column"
        },
        {
          "value":"{{rst}}grid {{rst}}grid-cols-3",
          "label":"Grid 3 Column"
        },
        {
          "value":"{{rst}}grid {{rst}}grid-cols-4",
          "label":"Grid 4 Column"
        },
        {
          "value":"{{rst}}grid {{rst}}grid-cols-5",
          "label":"Grid 5 Column"
        },
        {
          "value":"{{rst}}grid {{rst}}grid-cols-6",
          "label":"Grid 6 Column"
        },
        {
          "value":"{{rst}}hidden",
          "label":"Hidden"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_display_desktop",
      "label": "{{ label_prefix }} Desktop Display",
      "default":"lg:flex",
      "options": [
        {
          "value":"lg:flex",
          "label":"Flex"
        },
        {
          "value":"lg:grid lg:grid-cols-1",
          "label":"Grid 1 Column"
        },
        {
          "value":"lg:grid lg:grid-cols-2",
          "label":"Grid 2 Column"
        },
        {
          "value":"lg:grid lg:grid-cols-3",
          "label":"Grid 3 Column"
        },
        {
          "value":"lg:grid lg:grid-cols-4",
          "label":"Grid 4 Column"
        },
        {
          "value":"lg:grid lg:grid-cols-5",
          "label":"Grid 5 Column"
        },
        {
          "value":"lg:grid lg:grid-cols-6",
          "label":"Grid 6 Column"
        },
        {
          "value":"lg:hidden",
          "label":"Hidden"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_direction",
      "label": "{{ label_prefix }} Direction",
      "default":"{{rst}}{{ default_direction | default: 'flex-row' }}",
      "options": [
        {
          "value":"{{rst}}flex-row",
          "label":"→"
        },
        {
          "value":"{{rst}}flex-row-reverse",
          "label":"←"
        },
        {
          "value":"{{rst}}flex-col",
          "label":"↓"
        },
        {
          "value":"{{rst}}flex-col-reverse",
          "label":"↑"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_layout",
      "label": "{{ label_prefix }} Layout",
      "options": [
        {
          "value":"{{rst}}layout-top",
          "label":"Top (Full Width)"
        },
        {
          "value":"{{rst}}layout-left {{rst}}layout-top",
          "label":"Top Left"
        },
        {
          "value":"{{rst}}layout-center {{rst}}layout-top",
          "label":"Top Center"
        },
        {
          "value":"{{rst}}layout-spaced {{rst}}w-full {{rst}}layout-top",
          "label":"Top Spaced"
        },
        {
          "value":"{{rst}}layout-right {{rst}}layout-top",
          "label":"Top Right"
        },
        {
          "value":"{{rst}}layout-middle",
          "label":"Middle (Full Width)"
        },
        {
          "value":"{{rst}}layout-left {{rst}}layout-middle",
          "label":"Middle Left"
        },
        {
          "value":"{{rst}}layout-center {{rst}}layout-middle",
          "label":"Middle Center"
        },
        {
          "value":"{{rst}}layout-spaced {{rst}}w-full {{rst}}layout-middle",
          "label":"Middle Spaced"
        },
        {
          "value":"{{rst}}layout-right {{rst}}layout-middle",
          "label":"Middle Right"
        },
        {
          "value":"{{rst}}layout-left {{rst}}layout-bottom",
          "label":"Bottom Left"
        },
        {
          "value":"{{rst}}layout-center {{rst}}layout-bottom",
          "label":"Bottom Center"
        },
        {
          "value":"{{rst}}layout-spaced {{rst}}w-full {{rst}}layout-bottom",
          "label":"Bottom Spaced"
        },
        {
          "value":"{{rst}}layout-right {{rst}}layout-bottom",
          "label":"Bottom Right"
        },
        {
          "value":"{{rst}}layout-bottom",
          "label":"Bottom (Full Width)"
        },
        {
          "value":"{{rst}}layout-left",
          "label":"Left (Full Height)"
        },
        {
          "value":"{{rst}}layout-right",
          "label":"Right (Full Height)"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_layout_spacing",
      "label": "{{ label_prefix }} Layout Spacing",
      "options": [
        {
          "value": "{{rst}}layout-space-packed",
          "label": "Packed"
        },
        {
          "value": "{{rst}}layout-space-between",
          "label": "Space Bewteen"
        },
        {
          "value": "{{rst}}layout-space-around",
          "label": "Space Around"
        },
        {
          "value": "{{rst}}layout-space-evenly",
          "label": "Spaced Evenly"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_vertical_padding",
      "label": "{{ label_prefix }} Vertical Padding",
      "options": [
        {
          "value": "@include Spacing prop:py",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_horizontal_padding",
      "label": "{{ label_prefix }} Horizontal Padding",
      "options": [
        {
          "value": "@include Spacing prop:px",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_gap",
      "label": "{{ label_prefix }} Spacing Gap",
      "options": [
        {
          "value": "@include Spacing prop:gap",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_vertical_padding_desktop",
      "label": "{{ label_prefix }} Vertical Padding Desktop",
      "options": [
        {
          "value": "@include SpacingDesktop prop:py",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_horizontal_padding_desktop",
      "label": "{{ label_prefix }} Horizontal Padding Desktop",
      "options": [
        {
          "value": "@include SpacingDesktop prop:px",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_gap_desktop",
      "label": "{{ label_prefix }} Spacing Gap Desktop",
      "options": [
        {
          "value": "@include SpacingDesktop prop:gap",
          "label": "Inclusion"
        }
      ]
    }
  ],

  "FlexWrap":[
    {
      "type":"select",
      "id":"{{ id_prefix }}_wrap",
      "label": "{{ label_prefix }} Item Wrap",
      "options": [
        {
          "value": "",
          "label": "Never"
        },
        {
          "value": "flex-wrap",
          "label": "Always"
        },
        {
          "value": "max-lg:flex-wrap",
          "label": "Small Screens Only"
        },
        {
          "value": "lg:flex-wrap",
          "label": "Large Screens Only"
        }
      ]
    }
  ],

  "BlockWidths" : [
    {
      "type": "header",
      "content": "{{ label_prefix }} Width Settings"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_width",
      "label": "{{ label_prefix }} Width",
      "options": [
        {
          "value": "container",
          "label": "Container"
        },
        {
          "value": "w-full",
          "label": "100%"
        },
        {
          "value": "w-1/3",
          "label": "33%"
        },
        {
          "value": "w-2/5",
          "label": "40%"
        },
        {
          "value": "w-[45%]",
          "label": "45%"
        },
        {
          "value": "w-1/2",
          "label": "50%"
        },
        {
          "value": "w-2/3",
          "label": "66%"
        },
        {
          "value": "w-auto",
          "label": "Auto"
        },
        {
          "value": "col-span-1",
          "label": "1 Grid Column"
        },
        {
          "value": "col-span-2",
          "label": "2 Grid Columns"
        },
        {
          "value": "col-span-3",
          "label": "3 Grid Columns"
        },
        {
          "value": "col-span-4",
          "label": "4 Grid Columns"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_width_desktop",
      "label": "{{ label_prefix }} Desktop Width",
      "options": [
        {
          "value": "lg:container",
          "label": "Container"
        },
        {
          "value": "lg:w-full",
          "label": "100%"
        },
        {
          "value": "lg:w-[10%]",
          "label": "10%"
        },
        {
          "value": "lg:w-1/5",
          "label": "20%"
        },
        {
          "value": "lg:w-1/4",
          "label": "25%"
        },
        {
          "value": "lg:w-1/3",
          "label": "33%"
        },
        {
          "value": "lg:w-2/5",
          "label": "40%"
        },
        {
          "value": "lg:w-1/2",
          "label": "50%"
        },
        {
          "value": "lg:w-3/5",
          "label": "60%"
        },
        {
          "value": "lg:w-2/3",
          "label": "66%"
        },
        {
          "value": "lg:w-3/4",
          "label": "75%"
        },
        {
          "value": "lg:w-4/5",
          "label": "80%"
        },
        {
          "value": "lg:w-9/10",
          "label": "90%"
        },
        {
          "value": "lg:w-auto",
          "label": "Auto"
        },
        {
          "value": "lg:col-span-1",
          "label": "1 Grid Column"
        },
        {
          "value": "lg:col-span-2",
          "label": "2 Grid Columns"
        },
        {
          "value": "lg:col-span-3",
          "label": "3 Grid Columns"
        },
        {
          "value": "lg:col-span-4",
          "label": "4 Grid Columns"
        },
        {
          "value": "lg:col-span-5",
          "label": "5 Grid Columns"
        },
        {
          "value": "lg:col-span-6",
          "label": "6 Grid Columns"
        },
        {
          "value": "container",
          "label": "Standard Container"
        },
        {
          "value": "container container--narrow",
          "label": "Narrow Container"
        },
        {
          "value": "container container--wide",
          "label": "Wide Container"
        },
        {
          "value": "container--product",
          "label": "Product Container"
        }
      ]
    }
  ],



  "Overflow" : [
    {
      "type": "header",
      "content": "Overflow Settings",
    },
		{
			"type": "select",
			"id": "{{ id_prefix }}_class_overflow",
			"label": "Horizontal Overflow",
			"default": "",
			"options": [
				{
					"value": "",
					"label": "Contain"
				},
				{
					"value": "overflow-hidden",
					"label": "Hidden"
				},
				{
					"value": "scroll-normal",
					"label": "Smooth Scroll"
				},
				{
					"value": "scroll-snap-start",
					"label": "Snap Start"
				},
				{
					"value": "scroll-snap-center",
					"label": "Snap Center"
				}
			]
		}
  ],

  "BlockGrid" : [
    {
      "type": "header",
      "content": "{{ label_prefix }} Grid Settings"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_grid",
      "label": "{{ label_prefix }} Columns",
      "options": [
        {
          "label": "1 column",
          "value": "grid-cols-1"
        },
        {
          "label": "2 columns",
          "value": "grid-cols-2"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_width_desktop",
      "label": "{{ label_prefix }} Columns (Desktop)",
      "options": [
        {
          "label": "1 column",
          "value": "lg:grid-cols-1"
        },
        {
          "label": "2 columns",
          "value": "lg:grid-cols-2"
        },
        {
          "label": "3 columns",
          "value": "lg:grid-cols-3"
        },
        {
          "label": "4 columns",
          "value": "lg:grid-cols-4"
        },
        {
          "label": "5 columns",
          "value": "lg:grid-cols-5"
        },
        {
          "label": "6 columns",
          "value": "lg:grid-cols-6"
        }
      ]
    }
  ],

  "Carousel": [
    {
      "type": "header",
      "content": "{{ label_prefix }} Carousel Settings"
    }, 
    {
      "type":"number",
      "id":"items_per_view",
      "label": "Items per View"
    },
    {
      "type":"number",
      "id":"items_per_view_desktop",
      "label": "Desktop Items per View"
    },
    {
      "type":"number",
      "id":"items_spacing",
      "label": "Item Spacing"
    },
    {
      "type":"number",
      "id":"items_spacing_desktop",
      "label": "Desktop Item Spacing"
    }
  ],

  "TabSettings": [
    {
      "type": "header",
      "content": "Convert Into Tabs"
    },
    {
      "type": "checkbox",
      "id": "split_into_tabs",
      "label": "Split Carousel Into Tabs",
      "default": false,
      "info": "Can only add blocks from Shopify Collection, Bloomreach Recs and Bloomreach Recs"
    },
    {
      "type": "select",
      "id": "tab_interaction",
      "label": "Tab Interaction",
      "default": "hover",
      "options": [
        {
          "value": "hover",
          "label": "Hover"
        },
        {
          "value": "click",
          "label": "Click"
        }
      ]
    },
    {
      "type": "color",
      "id": "tabs_button_text",
      "label": "Carousel Tab Button Text",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "tabs_active_button_text",
      "label": "Carousel Tab Active Button Text",
      "default": "#000000"
    },
    {
      "type": "color_background",
      "id": "tabs_active_button_background",
      "label": "Carousel Tab Active Button Background",
      "default": "linear-gradient(#ffffff, #000000)"
    }
  ],

  "GridContainer": [
    {
      "type": "header",
      "content": "{{ label_prefix }} Settings"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_class_column",
      "label": "Grid Mobile Columns",
      "options": [
        {
          "value": "@include Grid",
          "label": "Inclusion"
        }
      ],
      "default": "grid-cols-1"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_class_column_desktop",
      "label": "Grid Desktop Columns",
      "options": [
        {
          "value": "@include Grid variant:lg",
          "label": "Inclusion"
        }
      ],
      "default": "lg:grid-cols-1"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_class_gap",
      "label": "Grid Mobile Gap",
      "options": [
        {
          "value": "@include Spacing prop:gap",
          "label": "Inclusion"
        }
      ]
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_class_gap_desktop",
      "label": "Grid Desktop Gap",
      "options": [
        {
          "value": "@include SpacingDesktop prop:gap",
          "label": "Inclusion"
        }
      ]
    }
  ],

  "ProductItem": [
    {
      "type": "header",
      "content": "Product Item Settings"
    },
    {
      "id":"product_item_settings",
      "label": "Override Global Product Item Settings",
      "type":"checkbox"
    },
    {
      "type": "liquid",
      "id": "product_item_title_source",
      "label": "Product Item Title Source",
      "default": "product.title.split('-')[0]"
    },
    {
      "type": "liquid",
      "id": "product_item_subtitle_source",
      "label": "Product Item Subtitle Source",
      "default": "product.title.split('-')[1]"
    },
    {
      "type": "liquid",
      "id": "product_item_type_source",
      "label": "Product Item Type Source",
      "default": "product.type"
    },
    {
      "type": "header",
      "content": "Classes"
    },
    {
      "type": "text",
      "id": "classes_product_item",
      "label": "Product Item"
    },
    {
      "type": "text",
      "id": "classes_product_item_image_wrapper",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "classes_product_item_title",
      "label": "Product Item Title"
    },
    {
      "type": "text",
      "id": "classes_product_item_subtitle",
      "label": "Product Item Subtitle"
    },
    {
      "type": "liquid",
      "id": "product_item_additional_liquid",
      "label": "Additional Liquid"
    },
    {
      "type": "checkbox",
      "id": "product_item_show_title",
      "label": "Show Title",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "product_item_show_subtitle",
      "label": "Show Subtitle",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "product_item_show_type",
      "label": "Show Product Type",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "product_item_show_price",
      "label": "Show Price",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "product_item_show_reviews",
      "label": "Show Review Stars",
      "default": true
    },
    {
      "type": "select",
      "id": "product_item_info_layout",
      "label": "Info Layout",
      "options": [
        {
          "value":"",
          "label":"Column"
        },
        {
          "value":"flex flex-row justify-between",
          "label":"Spaced Row" 
        }
      ]
    },
    { 
      "type": "select",
      "id": "product_item_quick_add_position_mobile",
      "label": "Quick Add Button position (Mobile)",
      "default": "image",
      "options": [
        {
					"label": "Image Container",
					"value": "image"
				},
				{
					"label": "Info Container",
					"value": "info"
				}
      ]
    },
    { 
      "type": "select",
      "id": "product_item_quick_add_position_desktop",
      "label": "Quick Add Button position (Desktop)",
      "default": "image",
      "options": [
        {
					"label": "Image Container",
					"value": "image"
				},
				{
					"label": "Info Container",
					"value": "info"
				}
      ]
    },
    { 
      "type": "select",
      "id": "product_item_show_button",
      "label": "Show/Hide Button",
      "default": "hide",
      "options": [
        {
					"label": "Show",
					"value": "show"
				},
				{
					"label": "Hide",
					"value": "hide"
				}
      ]
    },
    {
      "type": "checkbox",
      "id": "product_item_variant_selector",
      "label": "Include Variant Form",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "product_item_show_swatches",
      "label": "Show Swatches"
    },
    {
      "type": "checkbox",
      "id": "product_item_show_oos_siblings",
      "label": "Show Out of Stock Siblings",
      "default": false
    },
    {
      "type": "number",
      "id": "product_item_swatches_view",
      "label": "Swatches Per View (Mobile)",
      "default": 3
    },
    {
      "type": "number",
      "id": "product_item_swatches_view_desktop",
      "label": "Swatches Per View (Desktop)",
      "default": 5
    },
    {
      "type": "select",
      "id": "product_item_swatch_interaction",
      "label": "User swatch interaction to update product item on desktop",
      "default": "click",
      "options": [
        {
          "label": "Hover",
          "value": "hover"
        },
        {
          "label": "Click",
          "value": "click"
        }
      ]
    },
    {
      "type": "select",
      "id": "product_color_option_type",
      "label": "Product Color Option Type",
      "default": "admin_setting",
      "options": [
        {
          "label": "Theme Setting",
          "value": "admin_setting"
        },
        {
          "label": "Image",
          "value": "image"
        },
        {
          "label": "Swatch Color",
          "value": "swatch"
        },
        {
          "label": "Swatch Image",
          "value": "swatch_image"
        }
      ]
    }
  ],

  "GridColumns": [
    {
      "type": "header",
      "content": "{{ label_prefix }} Grid Column Settings"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_gridcols",
      "label": "{{ label_prefix }} Grid Columns",
      "options": [
        {
          "value": "col-span-1",
          "label": "1 Column"
        },
        {
          "value": "col-span-2",
          "label": "2 Columns"
        },
        {
          "value": "col-span-3",
          "label": "3 Columns"
        }
      ],
      "default": "col-span-1"
    },
    {
      "type":"select",
      "id":"{{ id_prefix }}_gridcols_desktop",
      "label": "{{ label_prefix }} Grid Columns Desktop",
      "options": [
        {
          "value": "lg:col-span-1",
          "label": "1 Column"
        },
        {
          "value": "lg:col-span-2",
          "label": "2 Columns"
        },
        {
          "value": "lg:col-span-3",
          "label": "3 Columns"
        },
        {
          "value": "lg:col-span-4",
          "label": "4 Columns"
        }
      ],
      "default": "lg:col-span-1"
    }
  ],

  "GridPosition": [
    {
      "type":"number",
      "id":"grid_position_desktop",
      "label": "Desktop Grid Position"
    },
    {
      "type":"number",
      "id":"grid_position_mobile",
      "label": "Mobile Grid Position"
    }
  ],

  "TextStack": [
    {
      "type": "header",
      "content": "Text Stack"
    },
    {
      "type": "paragraph",
      "content": "@include BlockWidths, id_prefix:text_stack_class, label_prefix:Text Stack "
    },
    {
      "type": "paragraph",
      "content": "@include FlexLayout, id_prefix:text_stack_class, label_prefix:Text"
    },
    {
      "type":"radio",
      "label":"Text Justification",
      "id":"text_stack_class",
      "default":"text-center",
      "options": [
        {
          "label":"←",
          "value":"text-left"
        },
        {
          "label":"↔",
          "value":"text-center"
        },
        {
          "label":"→",
          "value":"text-right"
        }
      ]
    },
    {
      "type": "color",
      "id": "content_style_color",
      "label": "Text Color"
    },
    {
      "type": "paragraph",
      "content": "@include Text, id_prefix:text_item_1, label_prefix:Text 1 "
    },
    {
      "type": "paragraph",
      "content": "@include Text, id_prefix:text_item_2, label_prefix:Text 2 "
    },
    {
      "type": "paragraph",
      "content": "@include Text, id_prefix:text_item_3, label_prefix:Text 3 "
    }
  ],
  
  "LogicInclusion": [
    {
      "type": "liquid",
      "label": "Liquid Logic Inclusion",
      "id": "inclusion_liquid",
      "info": "Insert any liquid logic that returns a value to display the section",
      "default": "true"
    },
    {
      "type": "textarea",
      "label": "Javascript Logic Inclusion",
      "id": "inclusion_js",
      "info": "Insert any javascript logic that evaluates to true to display the section"
    }
  ],
  ...require('./components/carousels/schema.js'),
  ...require('./components/product-essentials/schema.js'),
  ...ConditionalDisplay
}
