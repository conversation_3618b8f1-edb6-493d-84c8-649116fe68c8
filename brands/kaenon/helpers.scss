/* LAYOUT */

.p-reset p {
    margin:0;
    font-size:inherit;
}
.underline-links a {
    text-decoration:underline;
}

/* Challenge
--------------------------------------------- */

.shopify-challenge__container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.shopify-challenge__button {
  background: var(--os-primary);
}


/* Helpers
--------------------------------------------- */

.circle-divider::after {
  content: '\2022';
  margin: 0 1.3rem 0 1.5rem;
}

.circle-divider:last-of-type::after {
  display: none;
}

@media (min-width: 1024px) {
	.dropdown--wrap {
		@apply lg:absolute lg:transform lg:-translate-x-1/2 lg:left-1/2 lg:top-full lg:w-52 w-full lg:pt-2.5 box-border;
	}
	.dropdown--content {
		@apply bg-white lg:rounded lg:shadow-2xl w-full;
	}
  .dropdown--nav-item:before, .nav_item__caret{
    content: "";
    display: block;
    position: absolute;
    width: 0px;
    height: 0px;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid rgb(255, 255, 255);
    bottom: -18px;
    left: 50%;
    transform: rotate(90deg) translateX(-50%);
    opacity: 0;
    z-index: 30;
  }
  details[open] .dropdown--nav-item:before { 
  	opacity: 1;
  }
  .nav__item, #mainMenu-account {
    &:hover {
      .nav_item__caret {
        opacity: 1;
      }
    }
  }
}


.fade-in {
  opacity: 0;
}

.fade-in-rise {
  opacity: 0;
  transform: translateY(40px);
  &.active {
    opacity: 1;
    transform: translateY(0);
  }
}

.in-view {
  
  &.in-view\:fade-in{
    opacity:1;
  }

  &.in-view\:fade-in-rise {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate {
  transition: all 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);

  &.animate-slow {
    transition-duration: 1.2s
  }
  &.animate-fast {
    transition-duration: 0.3s
  }

    &.push-hover {
    transform: scale(1.05);
    :hover > & {
      transform: scale(1.0);
    }
  }
  &.pull-hover {
    transform: scale(1.0);
    :hover > & {
      transform: scale(1.05);
    }
  }
}

.accordion--section .accordion:after {
  display: none;
}


/* clears the ‘X’ from Internet Explorer */
input[type=search]::-ms-clear { display: none; width : 0; height: 0; }
input[type=search]::-ms-reveal { display: none; width : 0; height: 0; }
/* clears the ‘X’ from Chrome */
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration { display: none; }

.logo {
  @media screen and (max-width: 767px){
    max-height: 90%; 
  }
}

#epb_container, [class*="htusb"] {
  display:none !important;
}

[data-modal="quickshop"] .product-gallery-slider .swiper-slide {
  pointer-events: none;
}

.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
.no-scrollbar::-webkit-scrollbar {
    display: none;
}
[data-modal="quickshop"] .product-badge {
  display: none;
}

.animation-pulse, .animate-pulse {
  box-shadow: none !important;
}

#shopify-section-mega-menu {display: none !important;}

.basis-0 {
  flex-basis: 0px;
}

.no-bg {
  figure {
      margin: 0;
  }
  .product-info {
    padding-bottom: 0;
  }
  .productitem--extra button {
    background-color: #fff;
  }
}

details,
details summary {
  background-image:none;
  -webkit-appearance:none;
}

details summary::-webkit-details-marker {
  display:none;
}

.container {
  &--product {
    @apply mx-auto max-w-[1220px] #{!important};
  }
  &--narrow {
    @apply max-w-7xl mx-auto px-4 lg:px-8 #{!important};
  }
  &--tight {
     @apply max-w-5xl mx-auto px-4 lg:px-8 #{!important};
  }
  &--wide {
     @apply max-w-screen-2xl w-full mx-auto px-4 sm:px-6 lg:px-8;
  }
}

.page-width {
  @apply max-w-7xl mx-auto py-8 px-4 lg:px-8;

  &--narrow {
    @apply max-w-5xl mx-auto px-4 lg:px-8;
  }
}

*:has(> table) {
  overflow-x:scroll;
}
