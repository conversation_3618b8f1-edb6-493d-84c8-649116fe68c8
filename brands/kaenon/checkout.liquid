<!DOCTYPE html>
  <html lang="{{ locale }}" dir="{{ direction }}" class="{{ checkout_html_classes }}">
    <head>
    
    {%- comment -%} Exponea Initilization Script {%- endcomment -%}
    {% render 'checkout-exponea' %}
    {%- comment -%} Track Exponea Checkout Events {%- endcomment -%}
    <script>
      window.onload = function() {
        if (Shopify && Shopify.Checkout && Shopify.Checkout.step) {
          exponea.track('checkout', {
            referrer: document.referrer,
            location: window.location.href,
            path: window.location.pathname,
            step: Shopify.Checkout.step
          });
        }
      };
    </script>
   
  
      <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <meta name="viewport" content="width=device-width, initial-scale=1.0, height=device-height, minimum-scale=1.0, user-scalable=0">
      <meta name="referrer" content="origin">
  
      <title>{{ page_title }}</title>
  
      {{ content_for_header }}
  
      {{ checkout_stylesheets }}
      {{ checkout_scripts }}
  
      {% if settings.favicon_light != blank %}
      <!-- Favicon Light Mode -->
      <link rel="icon" type="image/png" href="{{ settings.favicon_light | img_url: '64x64' }}" sizes="192x192">
      <link rel="shortcut icon" href="{{ settings.favicon_light | img_url: '64x64' }}o" type="image/x-icon">
      <link rel="appletouchicon" href="{{ settings.apple_touch_light | img_url: '180x180' }}">
      <meta name="msapplicationsquare70x70logo" content="{{ settings.ms_square_sm_light | img_url: '70x70' }}">
      <meta name="msapplicationsquare150x150logo" content="{{ settings.ms_square_md_light | img_url: '150x150' }}">
      <meta name="msapplicationsquare310x310logo" content="{{ settings.ms_square_lg_light | img_url: '310x310' }}">
      <meta name="msapplicationwide310x150logo" content="{{ settings.ms_rec_sm_light | img_url: '310x150' }}">
      <meta name="msapplicationTileColor" content="{{ settings.ms_fav_tile_color_light }}">
      <script>
      var darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      handleDarkmode(darkModeMediaQuery);
      function handleDarkmode(e){
      var darkModeOn = e.matches; // true if dark mode is enabled
      var favicon = document.querySelector('link[rel="shortcut icon"]'); 
      var largeFavicon = document.querySelector('link[rel="icon"]'); 
      var appletouchicon = document.querySelector('link[rel="appletouchicon"]'); 
      var square70x70logo = document.querySelector('meta[name="msapplicationsquare70x70logo"]'); 
      var square150x150logo = document.querySelector('meta[name="msapplicationsquare150x150logo"]'); 
      var square310x310logo = document.querySelector('meta[name="msapplicationsquare310x310logo"]'); 
      var wide310x150logo = document.querySelector('meta[name="msapplicationwide310x150logo"]'); 
      var TileColor = document.querySelector('meta[name="msapplicationTileColor"]'); 
      if(!favicon || !largeFavicon){
        return; 
      }
      // replace icons with dark/light themes as appropriate
      if(darkModeOn){
        favicon.href = '{{ settings.favicon_dark | img_url: '64x64' }}';
        largeFavicon.href = '{{ settings.favicon_dark | img_url: '64x64' }}';
        appletouchicon = '{{ settings.apple_touch_dark | img_url: '180x180' }}';
        square70x70logo = '{{ settings.ms_square_sm_dark | img_url: '70x70' }}';
        square150x150logo = '{{ settings.ms_square_md_dark | img_url: '150x150' }}';
        square310x310logo = '{{ settings.ms_square_lg_dark | img_url: '310x310' }}';
        wide310x150logo = '{{ settings.ms_rec_sm_dark | img_url: '310x150' }}';
        TileColor.content = '{{ settings.ms_fav_tile_color_dark }}';
      }else{
        favicon.href = '{{ settings.favicon_light | img_url: '64x64' }}';
        largeFavicon.href = '{{ settings.favicon_light | img_url: '64x64' }}';
        appletouchicon = '{{ settings.apple_touch_light | img_url: '180x180' }}';
        square70x70logo = '{{ settings.ms_square_sm_light | img_url: '70x70' }}';
        square150x150logo = '{{ settings.ms_square_md_light | img_url: '150x150' }}';
        square310x310logo = '{{ settings.ms_square_lg_light | img_url: '310x310' }}';
        wide310x150logo = '{{ settings.ms_rec_sm_light | img_url: '310x150' }}';
        TileColor.content = '{{ settings.ms_fav_tile_color_light }}';
      }
      }
      darkModeMediaQuery.addListener(handleDarkmode);
      </script>
      {% endif %}
      <style>
       p.shipping_address_notice {	padding: 0px 7px 10px}
        .cn_modal {height: 118px;background: #F6EBD8 0% 0% no-repeat padding-box;border: 1px solid #D9D9D9;opacity: 1;display: flex;}
        .cn_superscript {text-align: left;letter-spacing: 0px;color: #000000;opacity: 1;}
        .cn_heading {text-align: left;letter-spacing: 0px;    color: #000000;text-transform: uppercase;opacity: 1;}
        .cn_description {text-align: left;letter-spacing: -0.07px;color: #717171;opacity: 1;}
        #cn_modal_button {width: 109px;height: 55px;opacity: 1;}
        .cn_icon {padding: 46px 15px;}
        .cn_message {padding: 20px 30px;}
        .cn_btn {padding: 30px;}
        .cn_loader {
          border: 2px solid #fff;
          border-top: 0px solid #fff;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          -webkit-animation: spin 2s linear infinite;
          animation: spin 0.5s linear infinite;
          display: inline-block;
        }
  
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
  
        .cn_modal{
          background: #F6EBD8;
          border: 1px solid #D9D9D9;
          border-bottom:none;
          height:auto;
          display:block;
          padding:24px;
          overflow:hidden;
          border-radius: 5px 5px 0 0px;
        }
        .cn_modal .cn_description{
            font-family: interstate,sans-serif,"Century Gothic",sans-serif;
            font-size: 13px;
            line-height: 15px;
            letter-spacing: -0.07px;
            color: #717171;
        }
        .cn_modal .cn_icon {
            padding:27px 20px 0 0;
            float:left;
            width: 14px;
            height: auto;
        }
        .cn_modal .cn_message{
          float:left;
          padding: 0;
        }
        .cn_modal .cn_btn{
          float: right;
            padding: 6px 0 0;
        }
  
        .cn_modal .cn_superscript{
            padding: 0 0 6px;
            font-family: interstate,sans-serif,"Century Gothic",sans-serif;
            font-size: 13px;
            line-height: 15px;
        }
        .cn_modal .cn_heading{
          padding:0 0 6px;
          font-family: interstate,sans-serif,"Century Gothic",sans-serif;
          font-size: 24px;
          line-height: 28px;
        }
        .cn_modal #cn_modal_button {
            font-family: interstate,sans-serif,"Century Gothic",sans-serif;
            font-size: 15px;
            line-height: 18px;
            letter-spacing: 1.13px;
            text-transform: uppercase;
        }
        .cn_modal .cnHeading_mobile.cn_heading {
            display: none;
        }
  
        @media (max-width:480px){
          .cn_modal .cnHeading_mobile.cn_heading {
              display: block;
            text-align:center;
          }
          .cn_modal .cn_heading,
          .cn_modal .cn_icon{
            display:none;
          }
          .cn_modal .cn_message {
              float: none;
              text-align: center;
          }
          .cn_modal .cn_superscript,
          .cn_modal .cn_description{
              text-align: center;
          }
          .cn_modal .cnHeading_mobile.cn_heading img{
            display: inline-block;
              padding-right: 8px;
              width: 14px;
              height: auto;
          }
          .cn_modal .cn_btn {
              float: none;
              padding: 16px 0 0;
              display: block;
              text-align: center;
          }
          .cn_modal #cn_modal_button{
            width:100%;
            height:40px;
          }
        }
      </style>
    <input type="hidden" name="exponea_consent" id="exponea_consent" value="{{ settings.kaenon_exponea_consents | replace: " ", "" | replace: "	", "" | strip_newlines }}" />
    {% render 'checkout-kaenon-analyzify-gtm-checkout' %}
  </head>
    <body>
      <!-- Google Tag Manager (noscript) -->
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ settings.kaenon_gtm_container_id }}"
  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
  <!-- End Google Tag Manager (noscript) -->
      {{ skip_to_content_link }}
  
      <div class="banner" data-header>
        <div class="wrap">
          {{ content_for_logo }}
        </div>
      </div>
  
      {{ order_summary_toggle }}
  
      <div class="content" data-content>
        <div class="wrap">
          <div class="main" role="main">
            <div class="main__header">
              {{ content_for_logo }}
              {{ breadcrumb }}
              {{ alternative_payment_methods }}
            </div>
            <div class="main__content">
              {{ content_for_layout }}
            </div>
            <div class="main__footer">
              {{ content_for_footer }}
            </div>
          </div>
          <div class="sidebar" role="complementary">
            <div class="sidebar__header">
              {{ content_for_logo }}
            </div>
            <div class="sidebar__content">
              {{ content_for_order_summary }}
            </div>
          </div>
        </div>
      </div>
  
      {{ tracking_code }}
      {% render 'checkout-plus_show_discount' plus_show_discount:settings.kaenon_plus_show_discount_enable %}
  
  
  
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
  {% if settings.kaenon-pobox-blocker_enable %}
  {% render 'checkout-kaenon-pobox' %}
  {% endif %}
  
  {% if settings.kaenon_address_validation_enable %}
  {% render 'checkout-kaenon-address-validation' %}
  {% endif %}
  
  {% if settings.kaenon_site_mode == 'usa' %}
  <script>
    if(Shopify.Checkout.step == 'contact_information'){ 
      document.querySelector("#order-summary .order-summary__section.order-summary__section--discount").style.display = 'none'; 
      document.querySelector(".order-summary__section.order-summary__section--total-lines").style.borderTop = '0';
    }
  </script>
    {% render 'checkout-kaenon-hide-messages' %}
  {% endif %}
    </body>
  </html>
  
  <script>
    //console.log("AFTERPAY: in afterpay-checkout.liquid");  
    var afterpay_available = false;
    var checkout_total = {{checkout.total_price}};
    if (checkout_total >= 3500 && checkout_total <= 100000) {
      {%- if settings.afterpay_enabled -%}
        afterpay_available = true;
      {%- endif -%}    
    }
  
    var paymentMethodSection = document.querySelector('.section--payment-method');
  
    //event listener for content rendered
    document.addEventListener('page:load', function() {
      if (paymentMethodSection) {
       // console.log('AFTERPAY: on paymentMethodSection');
        checkAfterpayRange(afterpay_available);
      } else {
      //  console.log('AFTERPAY: Not on paymentMethodSection');
      }
    });
  
    function checkAfterpayRange(afterpay_available) {
      var images = paymentMethodSection.getElementsByTagName('img');
  
      for (i = 0, len = images.length; i < len; i++) {
        //check image exist
        if (images[i]) {
          var altContent = images[i].alt;
  
          //check if the image is Afterpay Image
          if (
            altContent.length &&
            (altContent.toLowerCase() == 'afterpay' ||
              altContent.toLowerCase() == 'afterpay north america')
          ) {
           // console.log("AFTERPAY: found AU or North America Gateway");
            if (afterpay_available) {
              //replace the image so its not blurry on hi-res (retina screens)
              images[i].parentElement.innerHTML =
                '<p style="display: inline;">Installments by </p><img class="afterpay-logo" src="https://static.afterpay.com/integration/product-page/logo-afterpay-colour.png" srcset="https://static.afterpay.com/integration/product-page/logo-afterpay-colour.png 1x, https://static.afterpay.com/integration/product-page/<EMAIL> 2x, https://static.afterpay.com/integration/product-page/<EMAIL> 3x" width="100" height="21" alt="Afterpay" style="vertical-align: middle; width: 100px;">';
            } else {
              //Disable afterpay radio button and place afterpay messaging
              images[i].parentElement.parentElement.parentElement.innerHTML =
                '<p class="afterpay-paragraph"><span class="afterpay-text1">Installments by </span><img class="afterpay-logo" src="https://static.afterpay.com/integration/product-page/logo-afterpay-colour.png" srcset="https://static.afterpay.com/integration/product-page/logo-afterpay-colour.png 1x, https://static.afterpay.com/integration/product-page/<EMAIL> 2x, https://static.afterpay.com/integration/product-page/<EMAIL> 3x" width="100" height="21" alt="Afterpay" style="vertical-align: middle; width: 100px;"><span class="afterpay-text2"> available between </span><strong class="afterpay-instalments">$35 - $1000</strong></p>';
  
              var afterpayParentRow =
                images[i].parentElement.parentElement.parentElement;
              var afterpayInputs = afterpayParentRow.getElementsByTagName('input');
  
              // If Afterpay is the initial selection
              if (afterpayInputs[0].checked) {
                //set the selected method to the first row
                var inputs = paymentMethodSection[0].getElementsByTagName('input');
  
                for (j = 0, len = inputs.length; j < len; j++) {
                  if (
                    inputs[j].type == 'radio' &&
                    inputs[j].parentElement.parentElement !== afterpayParentRow
                  ) {
                    inputs[j].checked = 'checked';
                    return;
                  }
                }
              }
            }
          }
        }
      }
    }
  </script>
  
  {{ 'exponea.js' | asset_url | script_tag }}
  <script>
    $(document).on(`page:load page:change`, function() {          
        if (Shopify.Checkout.step == "shipping_method") { 
          if ($('.shipping__notice')[0]) {} else {
            jQuery('<p class="shipping__notice">{{ settings.kaenon_shipping_text_message | strip_html }}</p>').insertBefore('[data-shipping-methods]');
          }
        }
      if ($('.shipping_address_notice')[0]) {} else {
          if (Shopify.Checkout.step == "contact_information") {
          jQuery('<p class="shipping_address_notice">{{ settings.kaenon_shipping_address_notice }}</p>').insertBefore('[data-address-fields]');
          }
      }	  
      });
    </script>
    
    {% render 'checkout_kaenon_newsletter' %}  
    
  
  
  {% if settings.kaenon_site_mode == 'vip' %}
  {% render 'checkout-kaenon-giftcard-app' %}
  {% endif %}
  
  {% if settings.kaenon_estimated_shipping_enabled %}
    {% render 'checkout-kaenon-shipping-dates' %}
  {% endif %}
  
  {% if settings.kaenon_site_mode == 'vip' %}
  {% render "checkout-kaenon-gift_card_message" %}   
  {% endif %}
  
  {% if settings.kaenon_enable_consent_checkout %}

    {% render 'checkout-kaenon-sms-checkout' %}

  {% endif %}