@import '../shared/globals.scss';



/* Color Guidelines
  ==========================================================================
  primary: Highest level user attention.
  secondary: Second highest level user attention.
  tertiary: Third highest level user attention.
  light: Most prominent light background color. Must be able to overlay on top of dark.
  dark: Most prominent dark background color. Must be able to overlay on top of light.
  pop: Usage examples are badges.
  highlight: Think about this as using a highlighter pen.
  body: Most common text color.
  header: Most common text color for headers.
*/

:root {

  --color-primary: #000;
  --color-secondary: #f05524;
  --color-tertiary: #f4f4f5;
  --color-light: #fff;
  --color-dark: #585858;
  --color-pop: #f05524;
  --color-highlight: #f05524;
  --color-body: #000;

  /*

  --swiper-theme-color: #000;

  --header-height:76px;
  --preheader-height: 34px;
  --header-height-half:38px;
  --unscrolled-header-height: 100px;
  --scrolled-header-height: 76px;

  @media screen and (max-width: 1024px) {
    --header-height:100px;
    --preheader-height: 32px;
    --header-height-half: 34px;
    --unscrolled-header-height: 100px;
    --scrolled-header-height: 68px;
  }

  --header-height:48px;

  */

  --font-body-weight: 400;
  --font-body-style: normal;
  --font-heading-weight: 700;
  --font-heading-style: normal;
  --font-subheading-weight: 600;
  --font-subheading-style: normal;
}


.top-main {
  top:var(--header-offset)
}
@media screen and (min-width: 1025px) {
  .lg\:top-main {
    top:var(--header-offset);
  }
}

 

   /* Type Styles
========================================================================== 
 Guidelines
==========================================================================
  **Type Styles**

  primary: Primary Headline or Title type.
  secondary: Commonly used as Subtitle type, compliments Primary.
  tertiary: Third highest level user attention, smaller but stylistically similar to primary.

  **Type Layouts / Spacing**

  page: Most common text color.
  section: Most common text style for headers.
  article: Most common text style for headers.

*/

.type {

  &-hero {
    font-size: 5.61rem;
    line-height: 1;
    @apply font-heading;
    
    &.type--sm {
      font-size: 4.209rem;
    }
    &.type--lg {
      font-size: 7.478rem;
    }
  }
  &-headline {
    font-size: 34px;
    line-height: 1;
    @apply font-heading;

    &.type--sm {
      font-size: 24px;
    }
    &.type--lg {
      font-size: 55px;
    }
  }
  &-subline {
    
    font-size: 16px;    
    @apply font-subheading tracking-[8px] uppercase;
 
    &.type--sm {
      font-size: 12px;
    }
    &.type--lg {
      font-size: 20px;
    }
  }
  &-item {
    font-size: 14px;
    letter-spacing: normal;
    @apply font-subheading;

    &.type--sm {
      font-size: 12px;
    }
    &.type--lg {
      font-size: 18px;
    }
  }
  
  &-section {
    font-size: 24px;
    @apply font-subheading;

    &.type--sm {
      font-size: 18px;
    }
    &.type--lg {
      font-size: 26px;
    }
  }
  &-eyebrow {
    font-size: 18px;
    @apply font-subheading;

    &.type--sm {
      font-size: 16px;
    }
    &.type--lg {
      font-size: 22px;
    }
  }
  &-body {
    font-size: 15px;
    letter-spacing: -0.12px;
    @apply font-body;

    &.type--sm {
      font-size: 14px;
    }
    &.type--lg {
      font-size: 18px;
    }
  }
  &-nav-link {
    @apply font-subheading text-[15px] capitalize;
  }
  &-micro {
    font-size: 11px;
    line-height: normal;
    border-width: 0;
    padding:0;
    margin:0;
    height: auto;
    @apply font-body;
  }
}

p {
  // font-family: var(--font-body-family);
  // font-weight: var(--font-body-weight);
  // font-weight: var(--font-body-style);
  // letter-spacing: -0.12px;
  
  strong {
    font-family: var(--font-bold);
  }
}

.rte a {
  @apply underline text-pop;
}

h1, .h1 {
  @apply headings text-2xl;
}

h2, .h2 { 
  @apply headings text-xl; 
}

h3, .h3 { 
  @apply headings text-lg;
}

h4, .h4 { 
  @apply headings text-base; 
}

h5, .h5 {  
  @apply headings text-base;  
}

h6, .h6 {  
  @apply headings text-base;  
}


/* Buttons and Controls
========================================================================== */

.button, .btn {
  @apply whitespace-nowrap items-center border-2 font-bold tracking-[2.6px] inline-flex text-[13px] h-[39px] justify-center opacity-100 overflow-hidden py-0 px-[18px] relative uppercase cursor-pointer;
  font-family: var(--font-bold);
  &--primary { @apply  bg-primary border-primary text-white;}
  &--secondary { @apply border-secondary bg-secondary text-white; }
  &--tertiary { 
    @apply border-primary bg-transparent text-primary; 
    &:before {
      @apply bg-primary;
    }
  }
  &--light { 
    @apply border-white  text-black;
    &:before {
      @apply bg-white;
    }
  }
  &--dark { 
    @apply border-primary text-white; 
    &:before {
      @apply bg-primary;
    }
    &:hover {
      @apply text-primary;
    }
  }
  &--pop { 
    @apply border-pop text-white; 
    &:before {
      @apply bg-pop;
    }
    &:hover {
      @apply text-black;
    }
  }
  &--light, &--dark, &--pop {
    position: relative;
    z-index: 1;
    transition: color .45s cubic-bezier(.785,.135,.15,.86),border .45s cubic-bezier(.785,.135,.15,.86);
    -webkit-transition: color .45s cubic-bezier(.785,.135,.15,.86),border .45s cubic-bezier(.785,.135,.15,.86);

    &:before {
      transition: transform .45s cubic-bezier(.785,.135,.15,.86),-webkit-transform .45s cubic-bezier(.785,.135,.15,.86);
      -webkit-transition: transform .45s cubic-bezier(.785,.135,.15,.86),-webkit-transform .45s cubic-bezier(.785,.135,.15,.86);
      position: absolute;
      content: "";
      display: block;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      -webkit-transform: scale(1,1);
      transform: scale(1);
      -webkit-transform-origin: left center;
      transform-origin: left center;
    }
    
    &:hover, a:hover & {
      &:before {
        @apply text-primary;
        transform-origin: right center;
        -webkit-transform: scale(0,1);
        transform: scaleX(0);
      }
    }
  }
  &--light {
    &:before {
      @apply text-white;
      z-index: -1;
    }
    &:hover, a:hover & {
      @apply text-white;
      &:before {
        background-color: #fff;
        transform-origin: right center;
        -webkit-transform: scale(0,1);
        transform: scaleX(0);
      }
    }
  }
  &--link { 
    @apply relative font-body tracking-normal capitalize bg-transparent text-[13px] h-[30px] leading-normal text-primary p-0 border-0;
    transition: color .2s ease-in-out,opacity .2s ease-in-out;

    &:after {
      content: "";
      position: absolute;
      width: 100%;
      height: 1px;
      left: 0;
      bottom: 0px;
      background: currentColor;
      -webkit-transform: scale(1,1);
      transform: scale(1);
      -webkit-transform-origin: left center;
      transform-origin: left center;
      -webkit-transition: -webkit-transform .2s ease-in-out;
      transition: -webkit-transform .2s ease-in-out;
      transition: transform .2s ease-in-out;
      transition: transform .2s ease-in-out,-webkit-transform .2s ease-in-out;
    }
    &:hover { 
      &:after {
        -webkit-transform: scale(0,1);
        transform: scaleX(0);
      }
    }
  }
  &--w-icon { 
    @apply gap-2;

  }
  &--simple {
		@apply text-secondary gap-1 border-transparent px-0;
  }
  &--disabled {
    @apply bg-dark border-[#eee] text-white cursor-not-allowed opacity-20;
  }
  &--action { 
    @apply flex justify-center items-center border-black border bg-transparent rounded-full text-[13px] tracking-normal capitalize font-normal h-[35px];
    font-family: var(--font-regular);
    &:before {
      @apply bg-secondary;
    }
    .icon {
      @apply text-black mr-0 w-4 h-3;
    }
  }
  &--large {
    height:56px; padding:0 50px; border-width:2px;
  }
  > span {
    @apply relative z-10;
  }

  &--micro-link {
    font-size: 13px;
    @apply font-body capitalize underline font-normal tracking-normal border-0 p-0 m-0 h-auto;
  }

  &--primary-hover { 
    @apply bg-secondary border-secondary text-white;
    &:hover , a:hover & {
      @apply opacity-70;
    }
  }

  &--secondary-hover { 
    @apply bg-primary border-primary text-white;
    &:hover , a:hover & {
      @apply opacity-70;
    }
  }

  &--tertiary-hover { 
    @apply bg-tertiary border-tertiary text-black;
    &:hover , a:hover & {
      @apply opacity-70;
    }
  }
}

.wishlist-toggle {
	@apply pl-3;

	.icon {
		@apply stroke-primary;

		&.active {
			@apply fill-primary;
		}
	}
}


/* Forms
========================================================================== */

.field {
  @apply font-body;

  label {
    @apply text-[13px] text-body mb-1;
  }
  &__input,
  &__textarea,
  &__select {
    @apply border-[#d9d9d9] text-sm rounded-none;
   
  }

  &__select {
    @apply h-12 py-0 text-sm appearance-none;

    background-position: right 12px center;
    background-image: url(https://olukai.com/cdn/shop/t/405/assets/select-icon.svg?v=43184182532122065261626389908);
    background-repeat: no-repeat;
    cursor: pointer;
  }

  &__toggle {
    @apply bg-tertiary;

    label {
      @apply inline-flex items-center mb-0 whitespace-nowrap;

      input {
        &:not(:checked) {
          & ~ .toggle__label {
            @apply cursor-pointer text-[12px] px-3 py-1 border bg-transparent border-transparent rounded-full text-[#999];
          }
        }

        &:checked {
          & ~ .toggle__label {
            @apply text-[12px] px-3 py-1 border bg-white border-primary text-body rounded-full;
          }
        }
      }
    }
  }

  &__image {
    @apply border rounded p-[15px] relative flex flex-col items-center justify-end border-tertiary;
    font-weight: 700;
    line-height: 18px;
    letter-spacing: 1.5px;
    input {
      @apply absolute inset-0 opacity-0;
    }
    img {
      width: 155px;
      max-width: 100%;
    }
    label {
      @apply flex items-center flex-col justify-end text-center uppercase text-[15px] gap-2.5;
    }
    &--horizontal {
      @apply justify-start items-start;
      label {
        @apply flex-row;
        img {
          width: 72px;
        }
      }
    }
    &:hover, &:focus {
      @apply border-primary;
      -webkit-box-shadow: 0 5px 6px rgba(0,0,0,.1);
      box-shadow: 0 5px 6px #0000001a;
    }
    &:has(input:checked) {
      @apply border-2 border-primary;
      background: #f0f8f8;
      -webkit-box-shadow: 0 5px 6px rgba(0,0,0,.1);
      box-shadow: 0 5px 6px #0000001a;
    }
  }
}

.field {
  &--floating-label {
    label {
      @apply text-[11px] text-[#939393];
    } 
  }
  input {
    &[type="checkbox"],
    &[type="radio"] {
      @apply h-[18px] w-[18px];

      & + span {
        @apply capitalize font-body text-[13px] border-tertiary;
      }
    }  

    &[type="radio"] {
      &:checked {
        @apply border-0 shadow-[inset_0_0_0_5px] shadow-black;
      }
    }
  }

  &__checkbox {

    span { 
      text-transform: capitalize;
    }
    
    input[type="checkbox"] {
      @apply relative rounded-none;

      &:checked {
        @apply border bg-white shadow-none;
    
        &:after {
          content: "";
          display: block;
          width: 5px;
          height: 8px;
          border: solid black;
          border-width: 0 1.5px 1.5px 0;
          position: absolute;
          transform: rotate(45deg);
          top: 50%;
          left: 50%;
          translate: -50% -65%;
          background: transparent;
        }
      }
    }
  }
  
  &__color {
    &-swatch {
      @apply outline-offset-[1px] outline-[2px];
    }
    
    &:hover &-swatch {
      @apply outline-black;
    }

    span {
      font-family: var(--font-regular);
      font-weight: 400;
      @apply text-dark text-[12px];
    }

    &:has(input:checked),
    &:hover {
      span {
        @apply text-black;
      }
    }
  }

  &__chip {
    span {
      font-family: var(--font-regular);
      font-weight: 400;
      font-size: 12px;
      text-transform: capitalize;
    }

    &-swatch {
      @apply outline-offset-0 outline-[4px];
    }
    
    &:hover &-swatch {
      @apply outline-primary;
    }

    @apply bg-tertiary text-black rounded-[3px] px-5 py-2.5 font-heading flex-row items-center;

    &:has(input:checked) {
      @apply bg-secondary text-white;
    }

    &:has(button) {
      @apply pr-3;
    }

    button {
      @apply pl-2.5;
    }
  }

  label.field__chip {
    @apply mb-2;
  }

  &__buttons {
    @apply gap-[9px];
  }

  &__button {
    @apply max-w-[92px] h-16 flex-none border border-tertiary p-0;

    input[type="checkbox"]:checked,
    input[type="radio"]:checked {
      & + span {
        @apply border-black;
      }
    } 

    &-text {
      @apply rounded-none uppercase font-body text-[13px] border [input:checked_~_&]:border-black [input:checked_~_&]:bg-black [input:checked_~_&]:text-white;
    }

    &:hover &-text {
      @apply border-black border-2 rounded-none;
    }
    
    &.unavailable {
      @apply text-body;
    
      &:after {
        content: '';
        background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1' preserveAspectRatio='none' viewBox='0 0 100 100'><line x1='0' y1='100' x2='100' y2='0' stroke='%23e4e4e4' stroke-width='2' vector-effect='non-scaling-stroke'/></svg>");
        background-repeat: no-repeat;
        background-size: cover;
        @apply w-full h-full absolute top-0 left-0 bg-[#f4f4f5]/75;
      }
    }
  
    &-text {
      @apply hidden;
    }
  
    &:focus,
    &:has(input:checked) {
      @apply border-dark border;
    }
  
    img {
      @apply object-contain aspect-[4/3];
    }
  }

  &-plus-minus {
    @apply flex flex-row items-center border-[#d9d9d9] border text-[#939393];
    input {
      @apply w-9 p-1 text-center bg-transparent;
    }
    button {
      @apply w-9 h-10 flex items-center justify-center bg-transparent;
    }
    svg { 
      stroke-width: 2px;
    }
  }
}

progress {
  @apply bg-white rounded-lg h-2.5 w-full;

  &::-webkit-progress-bar {
    @apply h-2.5 rounded-lg bg-[#f8f8f9] w-full;
  }

  &::-webkit-progress-value {
    @apply rounded-lg bg-gradient-to-r from-[#f5c456] to-[#f05524];
  }

  &::-moz-progress-bar {
    @apply rounded-lg bg-gradient-to-r from-[#f5c456] to-[#f05524];
  }
}

.progress-bar {
  &__label {
    @apply font-body text-body text-[13px] font-normal tracking-wider uppercase;
  }
}

.dropdown-menu {
	@apply border border-solid rounded-sm text-current border-[#e4e4e4] text-sm;
	
	&__trigger {
		@apply rounded-[2px] h-8 text-sm appearance-none py-0 pl-3;

		.dropdown-menu__label {
			@apply text-xs text-[#797979];
		}

		.dropdown-menu__value {
			@apply text-xs pl-2;
		
		}
		
		&-icon {
			@apply ml-3 pl-3 pr-3 border-l text-xs;

			.icon {
				@apply w-3;
				fill: #797979;
				stroke: #797979;
				stroke-linecap: butt;
			}
		}
	}

	&__menu {
		@apply rounded-md mt-2 py-2;
		&-item {
			@apply text-xs px-4 py-1.5 hover:bg-light hover:bg-black hover:text-white;

			button {
				@apply text-left;
			}
		}
	}
}


/* Accordion
========================================================================== */

.list {
  .accordion {
    &:last-child {
      @apply border-b border-[#e4e4e4];
    } 
  }
}

.accordion {
  &:last-child {
    @apply border-b border-[#d9d9d9];
  }
  .accordion-title {
    @apply bg-transparent border border-[#d9d9d9] border-l-0 border-b-0 border-r-0 text-left cursor-pointer h-[60px] leading-[60px];
    span:first-child {
      font-weight:700;
      font-size: 15px;
      letter-spacing: normal;
      line-height: normal;
    }
    .accordion-control {
      @apply bg-transparent text-dark border-0;
      .icon {
        @apply w-[16px] h-[16px] text-black;
      }
    }
  }
  .accordion-panel {
    @apply text-sm pb-4 px-0 border-0;
    > div {}
    ul {}
    p:last-child {}
  }
  &-control {
    &:first-of-type {
      display:block;
    }
    &:last-of-type {
      display:none;
    }
    [open] > summary & {
      &:first-of-type {
        display:none;
      }
      &:last-of-type {
        display:block;
      } 
    }
  }
}

/* Micro Upsell
========================================================================== */
.micro-upsell {
  &__item {
    @apply flex-shrink-0 flex flex-col justify-between;

    .product-item {
      @apply w-full;
    }

    .product-item__actions button,
    .product-item__quick-add,
    .product-item:hover .product-item__quick-add {
      display: none;
    }

    .product-item__actions {
      opacity: 0.25;
      pointer-events: none;
    }
    &:has(input:checked) .product-item__actions {
      opacity: 1;
      pointer-events: auto;
    }

    .product-item__title-price-wrap:hover {
      @apply underline;
    }

    .field {
      @apply mb-0;
    }

    select {
      @apply border p-2 bg-transparent border-black rounded-md lg:mr-1.5 text-sm leading-none;
    }
  }

  &__separator {
    @apply flex-shrink-0;
  }
}

/* Tabs
========================================================================== */

.tabs {
  ul:not(.start) {
    @apply pl-0 flex justify-end w-full;
    li {
      @apply ml-0;
    }
  }

  ul.start {
    @apply space-x-4;
  }

  .tab-title {
    @apply border-0 rounded-none border-[#9e9f9e] border-b-2 bg-transparent  capitalize  text-[#9e9f9e] type-item type--lg;

    &:hover {
      @apply text-primary;
    }
    &:before {
      @apply bg-transparent;

      &:hover {
        @apply text-primary;
      }
    }
    &.active {
      @apply text-primary border-primary;

      &:before {
        @apply bg-transparent;
  
        &:hover {
          @apply text-primary;
        }
      }
    }
  }
  
  .tab-panel {
    > div {
      @apply py-6;
    }
  }
}

/* Pagination
========================================================================== */

.pagination--page {
  @apply bg-transparent;
  li {
    @apply flex justify-start items-start border-b border-[#d9d9d9];
    &:has([aria-current="page"]) {
      @apply border-b-[3px] border-black;
      a:not(.pp-control),
      button:not(.pp-control), > span {
        @apply text-body;
      }
    }
    a:not(.pp-control),
    button:not(.pp-control), > span {
      @apply block px-4 py-2 mx-[5px] text-[13px] font-subheading text-[#939393];
    }
    [aria-current="page"], .active {
      @apply relative block;
      // &:after {
      //   content: "";
      //   @apply block border-b-[2px] border-primary;
      // }
    }
  }
}

.swiper-pagination,
.pagination {
  .pagination-bullet, .swiper-pagination-bullet {
    @apply bg-[#E2E2E2] w-2.5 h-2.5 scale-100;

    &.active, &.swiper-pagination-bullet-active {
      @apply bg-black w-2.5 h-2.5 scale-100;
    }
  }

  @apply flex items-center;

  .prev, .next {
    .icon {
      height:18px;
      stroke-width:2px;
      color:#797979;
    }
  }
  .deco {
    color:#797979;
  }
  .page {
    @apply flex items-center justify-center font-bold w-8 h-8 rounded-full;
    color:#797979;
    &.current { 
      @apply  bg-secondary text-white; 
    }
  }

}

.btn-control {
  &.swiper-button-prev,
  &.swiper-button-next {
    @apply bg-white rounded-full h-10 w-10 shadow-md text-inherit flex p-0;
    svg {
      @apply w-5 h-5;
      stroke-width: 2px;
    }
    .icon {
      stroke-width:2px;
      @apply text-secondary;
    }
  }
}


/* Mini Form
========================================================================== */

details.no-close[open] {
  summary { pointer-events: none; }
}

.mini-form {
  summary {
    & > span {
      width: 100%;
      transition: width 400ms ease-out, opacity 400ms ease-out;
      z-index: 10;
    }
   
  }
  & > div {
    display:grid;
    transition:max-height 400ms ease-out;
    max-height:0px;
  }
  &[open] {
    & > div {
      max-height:200px;
    }
  }
  form {
    input {
      font-size:13px;
      padding:6px 12px;
      @apply h-14 rounded-r-none rounded-l-[3px] lg:h-[39px] border;
    }
    button {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      @apply lg:h-auto relative z-20;
    }
    input:not([valid="true"]) + button {
      cursor:not-allowed;
    }
  }
  &[open] {
    summary {
      span,
      span svg {
        opacity:0;
        width:0;
        @apply bg-dark text-dark;
      }
    }
    form {
      margin-top:-56px;
      @media only screen and (min-width: 1024px) {
        margin-top:-39px;
      }
    }
  }
  &__info {
    p {
      text-align: left;
      margin:0.75rem 0 ;
      &:first-child {
        font-family: var(--font-bold);
      }
      font-size:12px;
    }
  }
  .button {
    @apply max-lg:h-14 gap-3;
  }
  &__success {
    visibility:hidden;
    width:0;
    height:0;
    display:none;
    opacity:0;
    transition:opacity 300ms ease;
  }
  &--submitted {
    summary {
      pointer-events:none;
      span {
        display:none;
      }
    }
    .mini-form__success {
      visibility:visible;
      display:block;
      width:100%;
      height:auto;
      opacity:1;
    }
  }
  .icon {
    @apply text-black;
  }
}

.icon {
  stroke-linecap: square;
  stroke-linejoin: initial;
}

/* Article Item
========================================================================== */

.content-item--article {	
	.type-item {
		@apply text-[22px];
	}

	.content-item {
		&__meta {
			@apply uppercase;
		}

		&__excerpt {
			@apply font-body text-[15px] hidden lg:block;
		}

    &__title {
      @apply line-clamp-3;
    }

		&__content {
			@apply px-5 justify-start lg:justify-between;
		}

		&__media-container {
			@apply max-w-[40%] lg:min-w-[50%];
		}

		&__media {
			@apply aspect-1 xl:aspect-[4/3] w-full;
		}

    &__text-stack {
      @apply mb-5;
    }
	}
  
  &.content-item__article--horizontal {
    .content-item {
      &__button-set {
        @apply mt-2;
  
        button {
          @media only screen and (max-width: 1023px) {
            @apply button--link;
          }
          @media only screen and (min-width: 1024px) {
            @apply button--dark;
          }
        }			
      }
    }
  }

  &.content-item__article--vertical {
		.content-item__button-set {
      button {
				@apply button--link;
			}		
		}
		.content-item__media-container {
			@apply mb-4;
		}
		.content-item__text-stack {
			@apply gap-y-2;
		}
		.content-item__media {
			@apply aspect-[3/2];
		}
	}
}

.article-item {
  &__category,
  &__date {
    @apply text-[11px] font-body tracking-wider;
    color: #736b67;
  }
  &__category {
    @apply mr-1.5 pr-1.5 border-[#736b67] border-r;
  }
}

/* Reviews
========================================================================== */
:root {
  --reviews-star-color: var(--color-secondary);
  --reviews-text-color:  var(--color-primary);
}

.review-snippet {
	.ruk-rating-snippet {
    @apply text-secondary #{!important};

		.ruk-rating-snippet-count {
			@apply font-body text-[12px] ml-1.5;
		}
		i {
			@apply text-base;
		}
	}
}

/* Review Item
========================================================================== */

.review-item {
	&__product {
		@apply text-base pb-2 font-subheading;
	}

	&__type {
		@apply font-normal font-body text-[13px] text-[#77706c];
	}
}

.content-item--review {

	.content-item {
		&__header {
			@apply mb-6;
		}

		&__media {
			@apply relative;

			&:hover {
				.content-item__media-button {
					@apply opacity-100 transform duration-200 ease-in;
				}
			}
			&:not(:hover) {
				.content-item__media-button {
					@apply opacity-0 transform duration-200 ease-in;
				}
			}
			.content-item__media {
				@apply aspect-1;
			}
		}

		&__media-button {
			@apply absolute inset-0 bg-black/25 z-10 opacity-0;
		}

		&__button-media {
			@apply top-1/2 left-1/2 transform -translate-x-1/2;
		}

		&__review {
			@apply pt-6 mb-0 font-subheading line-clamp-2 text-[22px];
      line-height: 1.2;
		}

		&__author {
			@apply type-body mt-3;
		}

		&__button-set {
      @apply mt-5;
      button {
        @apply button--link;	
      }
		}
	}
}

/* Tooltips / Hotspots
========================================================================== */
.tooltip {
  &__price {
    @apply font-body text-[13px] text-[#736b67]
  }
  &__quickview {
    @apply button--link;
  }
  &__title {
    @apply font-subheading;
  }
}

.hotspot {
  &__button {
    --section-dot-inner-background: #fff;
    --section-dot-background: 0,0,0;
  }
}

/* Tables
========================================================================== */
table {
  border-top:1px solid #EEE;
  border-bottom:1px solid #EEE;
  td {
    border-bottom:1px solid #EEE;
    border-right:1px solid #EEE;
    text-align:center;
    padding:2rem;
  }
  &.legend {
    position:sticky;
    left:0;
    td {
      background:#FFF;
      border-right:1px solid #EEE;
      text-align:right;
      
    }
  }
  tr:nth-of-type(odd) {
    background:#f7f7f7;
  }
}


/*  Gift Card Style =====================================  */
.giftcard {
  display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;

  .giftcard__container{
    margin: 57px auto 0;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    max-width: 600px;
    padding: 0 20px;
    width: 100%;
    .giftcard__block--code{
      margin-bottom: 45px !important;
      .icon--logo {
        img{
          margin: 0 auto;
        }
      }
      .giftcard__title--page{
        margin: 54px auto 38px;
        font-size: 34px !important;
        font-family: interstate,sans-serif,Garamond,Baskerville,Caslon,serif;
        letter-spacing: .68px;
        font-weight: 700;
        line-height: 36px;
        width: 100%;
        max-width: 537px;
      }
      .giftcard__image-container{
        position: relative;
        .giftcard__title--value{
          font-family: interstate, sans-serif, Century Gothic, sans-serif;
          font-weight: 700;
          line-height: 19px;
          letter-spacing: .5px;
          left: 16px;
          top: 41px;
          position: absolute;
          margin: 0;
          font-size: 25px;
          color: #f9f9f9;
        } 
        span.giftcard__code{
          background: none;
          position: absolute;
          bottom: 48px;
          left: 12%;
          width: 76%;
          transform: none;
          p.giftcard__text{
            margin-bottom: 13px;
            line-height: 21px;
            letter-spacing: .36px;
            font-size: 18px;
            color: #000;
            font-family: interstate, sans-serif, Century Gothic, sans-serif;
          }
          span#GiftCardDigits {
            letter-spacing: .48px;
            font-size: 24px;
            font-weight: 700;
            font-family: interstate, sans-serif, Century Gothic, sans-serif;
          }
        }     
      }
    }
    .giftcard__block.giftcard__block--left {
      width: 100%;
      p.giftcard__text.giftcard__text--balance {
        display: none;
      }
      .giftcard__buttons {
        justify-content: center;
        gap: 5px;
        align-items: baseline;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        -webkit-box-pack: start;

        a.giftcard__button {
          max-width: 281px;
          height: 58px;
          width: 100%;
          line-height: 58px;
          background-color: #000;
          color: #fff;
          padding: 0;
          font-size: 18px;
          letter-spacing: 3.96px;
          font-family: interstate, sans-serif, Garamond, Baskerville, Caslon, serif;
          font-weight: 700;
          text-transform: uppercase;
          -webkit-transition: color .45s cubic-bezier(.785,.135,.15,.86), border .45s cubic-bezier(.785,.135,.15,.86);
          transition: color .45s cubic-bezier(.785,.135,.15,.86), border .45s cubic-bezier(.785,.135,.15,.86);
          z-index: 1;
          -webkit-tap-highlight-color: initial;
        }
        a#PrintGiftCard {
          font-size: 12px;
          line-height: 21px;
          letter-spacing: .24px;
          padding-left: 15px;
          text-decoration: none;
          text-transform: capitalize;
          font-family: interstate,sans-serif,Century Gothic,sans-serif;
        }
      }
    }
  }
}
