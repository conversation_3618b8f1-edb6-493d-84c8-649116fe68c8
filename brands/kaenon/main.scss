@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	@import './helpers.scss';
  @import './globals.scss';
}


@layer utilities {

	.scroll-snap-none {
		scroll-snap-type: none;
	}
	.scroll-snap-x {
		scroll-snap-type: x mandatory;
	}
	.scroll-snap-y {
		scroll-snap-type: y mandatory;
	}
	
	.snap-align-center > * {
		scroll-snap-align: center;
	}
	.snap-align-start > * {
		scroll-snap-align: start;
	}
	.snap-align-end > * {
		scroll-snap-align: end;
	}
	.flex-slider.snap-align-none > * {
		scroll-snap-align: none;
	}

	.no-scrollbar {
		overflow: -moz-scrollbars-none;
		-ms-overflow-style: none;
	}
	.no-scrollbar::-webkit-scrollbar { 
		width: 0 !important; 
		background-color: transparent;
		height: 0 !important
	}
	.no-scrollbar::-webkit-scrollbar-track {
		background-color: transparent;
	}
	.no-scrollbar::-webkit-scrollbar-thumb {
		background-color: transparent;
	}
}

@layer utilities {
	/* LAYOUT */

	.layout-stretch { @apply items-stretch; }

	.layout-spaced { @apply justify-between; }

	/* VERTICALS ON ROWS */

	.flex-row,.flex-row-reverse {
		&.layout-top { @apply items-start; }
		&.layout-bottom { @apply items-end; }
		&.layout-middle { @apply items-center; }

		@screen lg {
			&.lg\:layout-right { @apply justify-end; }
			&.lg\:layout-left { @apply justify-start; }
			&.lg\:layout-center { @apply justify-center; }
		}
	}


	/* HORIZONTALS ON ROWS */

	.flex-row{

		&.layout-right { @apply justify-end; }
		&.layout-left { @apply justify-start; }
		&.layout-center { @apply justify-center; }

		@screen lg {
			&.lg\:layout-right { @apply justify-end; }
			&.lg\:layout-left { @apply justify-start; }
			&.lg\:layout-center { @apply justify-center; }
		}

	}


	.flex-row-reverse {
		
		&.layout-right { @apply justify-start; }
		&.layout-left { @apply justify-end; }
		&.layout-center { @apply justify-center; }

		@screen lg {
			&.lg\:layout-right { @apply justify-end; }
			&.lg\:layout-left { @apply justify-start; }
		}
	}


	/* HORIZONTALS ON COLUMNS */

	.flex-col,.flex-col-reverse {
		
		&.layout-right { @apply items-end; }
		&.layout-left { @apply items-start; }
		&.layout-center { @apply items-center; }

		@screen lg {
			&.lg\:layout-right { @apply items-start; }
			&.lg\:layout-left { @apply items-end; }
			&.lg\:layout-center { @apply items-center; }
		}
	}

	/* VERTICALS ON COLUMNS */

	.flex-col {

		&.layout-top { @apply justify-start; }
		&.layout-bottom { @apply justify-end; }
		&.layout-middle { @apply justify-center; }

		@screen lg {
			&.lg\:layout-top { @apply justify-start; }
			&.lg\:layout-bottom { @apply justify-end; }
			&.lg\:layout-middle { @apply justify-center; }
		}
	}

	.flex-col-reverse {

		&.layout-top { @apply justify-end; }
		&.layout-bottom { @apply justify-start; }
		&.layout-middle { @apply justify-center; }

		@screen lg {
			&.lg\:layout-top { @apply justify-end; }
			&.lg\:layout-bottom { @apply justify-start; }
			&.lg\:layout-middle { @apply justify-center; }
		}
	}

}

.icon svg {
	width: 100%;
}

.field__buttons .field__button.field__buttons--colors-swatch{
	border-radius:50%;
	aspect-ratio: 1;
	padding:0px;
	max-width: 42px;
	max-height: 42px;
}
.product-form__option--color .field__buttons--colors-swatch.field__button:has(input:checked){
	border-radius:50% !important;
}
@media (max-width: 1024px) {
	.field__buttons.field__buttons--colors-swatch{
		gap:9px !important;
	}
	.field__buttons.field__buttons--colors.field__buttons--colors-wrap {
		margin-left: 0rem;
		margin-right: 0rem;

	}
	.field__buttons.field__buttons--colors.field__buttons--colors-wrap .field__button:first-of-type {
		margin-left: 0rem;
	}
	.product-form__option--color .field__button.field__buttons--colors-only {
		height: auto;
	}
}
