@import '../../components/product-item/theme-kaenon.scss';
@import '../../components/modal/theme.scss';
@import '../../components/search-suggestion/search-suggestion.scss';
@import '../shared/styles/footer.scss';
@import '../shared/styles/quick-add.scss';


/* Header
========================================================================== */

.header-bar {
	&--main-promo {
		@apply py-4 text-sm;
		.header-bar__container {
			@apply justify-center items-center;
		}

    .announcement {
      @apply text-[15px] text-dark font-normal tracking-widest uppercase;
			font-family: var(--font-condensed);
    }
	}
	&--main {
		@apply bg-white border-b;
		.header-bar__container {
			@apply w-full justify-start items-center lg:px-[30px] px-[20px];
			span.header-bar__block--menu {
				@apply lg:pb-0 pb-9; 
			}
		}

		.header-bar__block:not(.header-bar__block--menu) {
			height:60px;
		}
	}
	&__block {
		&--logo {
			@apply mr-[34px];
		}
		&--menu {
			height:auto;
			background:#fff;
			@apply max-lg:order-last bg-white max-lg:-mx-5 max-lg:w-screen; 
			
			> ul.flex-row {
				> li {
					> a, summary {
						height:60px;
						@apply flex items-center px-base relative overflow-hidden;

						&:after {
							@apply bg-primary;
							height:3px;
							position:absolute;
							bottom:0;
							transform:translateY(100%);
							transition:transform 200ms ease;
              left:17px; // match padding to center
							width:calc(100% - 34px);
							content:'';
						}

						&:hover {
							&:after {
								transform:translateY(0%);
							}
						}
					}
					details {
						@apply h-full;

						&[open] {
							summary:after {
								transform:translateY(0%);
							}
						}
					}
				}
			}

			> ul.flex-col {
				line-height:26px;
				details {
					summary {
						@apply p-base items-center border-b border-tertiary;
					}
					ul {
						details {
							summary {
								a {
									@apply font-body text-base font-normal;
								}
								& ~ ul li a {
									@apply font-body text-[13px] font-normal py-0;
								}
							}
							ul {
								@apply  pb-base px-base;
								li{
									@apply px-base;
									a {
										font-size: 13px;
									}
								}
							}
							&[open] {
								summary{
									@apply border-transparent;
								}
								ul {
									@apply border-b border-tertiary;
								}
							}
						}	
					}
				}
				.header-nav__item {
					@apply text-[#5c5c5c];
					& > a {
						@apply p-base block;
					}
					.type-nav-link {
						@apply font-body font-normal capitalize tracking-[0.4px] text-base;
					}
				}
			}

			.menu-item {
				
				ul {
					@apply text-[#5c5c5c];
					li {
						&:has(a + ul) {
							> a {
								@apply lg:font-bold lg:mb-2 text-[15px] lg:text-[13px];
							}
							> ul {
								@apply max-lg:pl-6 max-lg:mt-4 max-lg:border-l max-lg:border-[#d9d9d9];
								li {
									@apply max-lg:opacity-70;

									&:first-child {
										a {
											@apply max-lg:pt-0;
										}
									}
								}						
							}
						}
						& > ul > li {
							a {
								@apply pt-3 text-[15px];
							}
						}
					}
				}
			}
		}
		
		&--nav-tools {
			@apply justify-end lg:gap-[25px];
			height:60px;
			svg {
				stroke-width:0.5;
				@apply text-primary;
			}
			.nav-tools__account-greeting {
				font-size:12px;
			}
			.nav-tools__cart-count {
				position: absolute;
		    top: -4px;
		    right: -10px;
				display:flex;
				justify-content:center;
				align-items:center;
				width:20px;
				height:20px;
				font-size:12px;
				border-radius:100%;
        @apply bg-primary text-white;
			}
			button,a {
				position:relative;
			}
		}
		&--menu-toggle {
			margin-left:-20px;
			button {
				padding:1rem;
				.icon {
					stroke-width: 2;
				}
			}
		}

		&--search {
			.page-scroll--down:not(.page-scroll--top) & {

				@media only screen and (max-width: 1023px) {
					position: absolute;
			    z-index: 50;
			    width: calc(100% - 6rem);
			    right: 3rem;
			    top: 0;
			  }

			}
		}

	}
	&__search-field {
		label {
			@apply bg-[#f8f8f9] px-sm rounded-full;
			.icon {
				width:18px;
			}
			input {
				@apply bg-[#f8f8f9] p-[6px] text-[12px] w-full;
				&:focus {
					outline:none;
				}
			}
		}
	}
}

@media only screen and (max-width: 1023px) {
	.header-bar {
		&--main {
		}
		&__block {
			&--menu{
				border-bottom:1px solid #eee;
			}
			&--logo, &--menu-toggle, &--nav-tools {
				.header-bar--main & {
					position:sticky;
					top:0;
					z-index:41;
				}
			}
			&--logo {
				.header-bar--main & {
					flex-grow:1;
					margin-right:0;
				}
			}
			&--menu-toggle {
				&:before {
					z-index:40;
					pointer-events:none;
					position:absolute;
					top:0;
					left:0;
					height:100%;
					width:100vw;
					background:#fff;
					border-bottom:1px solid #eee;
					content:'';
				}
				button{
					z-index:41;
				}
			}
		}
		&--main {
			/* max-height:calc(100dvh - 56px); */ //remove announcement bar height
			max-height: 100dvh;
			overflow:auto;
		}
		
	}
}

#shopify-section-header {
	@media only screen and (max-width: 1023px) {
		max-height:100dvh;
		overflow-y:scroll;
	}
	&:before {
		pointer-events:none;
		position:fixed;
		z-index:0;
		content:'';
		background:rgba(0,0,0,0.5);
		top:0;
		left:0;
		right:0;
		height:100vh;
		opacity:0;
		transition:opacity 600ms ease;	
	}
}

.menu-item {	
	ul {
		li {
			a {
				@apply flex pt-0;
				font-size: 22px;
				font-family: var(--font-medium);
			}

			& > ul > li {
				a {
					@apply text-[13px] font-body pt-2.5;
				}
			}
		}
	}
}

.nav-item {
	@apply text-center rounded overflow-hidden;
	img {
		transition:scale 200ms ease;
	}
	&:hover {
		img.nav-item__media {
			scale:1.1
		}
	}
	&__text {
		@apply text-[13px] font-heading uppercase tracking-[0.4px];
	}
}

.mega-menu {
	@apply border-b border-[#d9d9d9];
	> article {
		@apply border-t border-[#d9d9d9];
	}
}


/* Footer
========================================================================== */

#shopify-section-footer {
	.menu-item {
		@apply p-xl max-lg:border-b max-lg:border-[#d9d9d9];

		ul {
			li {
				ul a {
					@apply text-[#939393];
					&:hover {
						@apply text-black;
					}
				}
				&:has(a + ul) {
					> a {
						@apply lg:mb-[18px] text-[14px] font-bold;
					}
	
					&.menu-active {
						> a {
							@apply mb-3;
						}
					}
	
					button {
						@apply max-lg:grow max-lg:flex max-lg:justify-end;
					}
				}
			}
		}
	}

	.content-item__custom-svg {
		&:hover {
			@apply text-black;
		}
	}
	
  .footer__credits {
    @apply flex-col items-start px-xl pb-xl lg:pb-2xl lg:pt-0 lg:mt-0 lg:px-6xl lg:border-0 text-[#939393] border-[#d9d9d9];
		a {
			&:hover {
				@apply text-black;
			}
		}
  }

  .footer__credit--copyright {
    @apply flex flex-col gap-1;
  }

  .footer__credits,
  .footer__credit--copyright,
  .footer__credit--links {
    @apply gap-1 text-left;
    .footer__credit--link {
      &:not(:last-child):after {
        content: " | ";
        padding: 0 4px;
      }
    }
  }
  
  .footer__credit--links {
    @apply flex-wrap justify-end;
  }
}



/* Newsletter
========================================================================== */

.section--newsletter {	
	@apply text-left lg:text-center;

	.newsletter__title {
		@apply mt-0 max-lg:px-2.5 mb-2.5;
	}

	.newsletter__subtitle {
		@apply max-lg:px-2.5 mb-10 lg:mb-8;
	}

	.section__mini-forms {
		@apply flex justify-center divide-white max-lg:divide-y max-lg:flex-col max-lg:items-center lg:divide-x;
	}

	.mini-form {
		@apply px-2.5 py-6 lg:px-8 lg:max-w-[360px] text-white;

		input {
			@apply border-black font-body;
		}
		button {
			@apply rounded-r-[3px];
		}

	}

	.section__mini-forms [hidden="hidden"] + script + div {
		border: 0;
	  }

	summary, input {
		@apply lg:max-w-[360px];
	}

	summary {
		span {
			@apply bg-white text-black fill-black border-0;
			.icon {
				@apply text-white;
			}
		}
		
	}
	.newsletter__error {
        display: none;
        color: #FF0000;
        font-family: GTA-Bold,Arial,Helvetica,sans-serif;
        font-size: 12px;
        margin-bottom: 2px;
        margin-top: -2px;
        padding: 12px 20px 15px;
        text-align: center;
    }
}

/* Article List
========================================================================== */

.section--article-list {
	.section {
		&__title {
			@apply mt-0 pb-0 mb-2xl;
		}
	}
}

/* Article
========================================================================== */
.article {
	@apply lg:pb-8;

  &__header {
    @apply relative z-10 max-w-none w-full lg:max-w-[52rem] lg:mt-[-5rem] lg:px-0 px-6 pt-16 border-t-8 border-primary;

    & > * {
      @apply lg:max-w-2xl lg:mx-auto;
    }

		.article__title {
			font-family: var(--font-medium);
			@apply max-lg:text-2xl;
		}
  }

  &__content,
	&__footer {
   @apply lg:max-w-2xl lg:mx-auto max-lg:px-6;
  }

	&__footer {
		@apply relative z-10 max-w-none w-full lg:max-w-[52rem] pb-8 lg:px-0 px-4 border-b-8 border-primary;
  }

	&__link {
		font-family: var(--font-medium);
		@apply mt-4;
	}

	&__content {
		font-size: 16px;
    letter-spacing: -0.12px;
		line-height: 25px;
    @apply font-body;
  }

  &__info {
    @apply justify-start mb-5 flex-wrap flex gap-y-2;

    & > * {
      @apply text-[10px] font-body tracking-widest uppercase;

      &:not(:last-child) {
        @apply mr-3 pr-3 border-dark border-r;
      }
    }
  }

	.social-sharing {
		@apply mb-4;
	}

	&__category,
	&__tag {
		@apply opacity-40;
	}

	&-video {
		&__eyebrow {
			@apply mb-8;
		}

		&__category,
		&__date {
			@apply text-[11px] text-[#736b67] uppercase;
		}

		&__rte {
			h1:first-of-type,
			h2:first-of-type,
			h3:first-of-type  {
				@apply mt-0;
			}
			p {
				@apply font-body text-[15px] leading-[1.45455] -tracking-[0.12px];
			}
		}
	}
}

.template-article {
	#MainContent {
		@apply bg-light;
	}
}


/* Content Carousel
========================================================================== */

.section--content-carousel {
	.type-section {
		@apply m-0;
	}
	.product-header {
		@apply mb-lg;
		&__titles {
			display:flex;
			align-items:center;
			gap:1rem;
		}
		&__title {

		}
		&__subtitle {
			color:#797979;
			font-size: 12px;
		}
	}

	.content-item {
		&--carousel-header {
			@apply flex-wrap mb-10;
			
			.content-item__content {
				@apply gap-y-4;
			}
		}
	}
}


/* Search Results
========================================================================== */

.search-results {
	@apply px-0 pb-2.5 pt-0 bg-tertiary text-body overflow-visible;
	
  &__header {
    @apply p-2.5;
    display:none;
  }

  &__type {
  	@apply px-sm;
  	font-size:14px;
  }

	&__type-label {
		@apply bg-tertiary font-body uppercase mb-0 py-2.5 px-2.5 text-center text-xs text-[#767676];
	}


	&__item--product {
		a {
			@apply flex items-start gap-2.5;
		}

		img {
			@apply w-auto h-auto max-w-[70px] max-h-[70px];
		}
	}
}

.search-result-item {
	@apply bg-white p-2.5 border-l border-r border-t border-gray-200 ;


	&:first-of-type {
		@apply rounded-t;
	}
	&:last-of-type {
		@apply border-b border-gray-200 rounded-b;
	}

	&__title {
		@apply text-[14px] leading-tight mb-1;
	}

	

	&__type,
	&__sku {
		font-size: 12px;
    color: #a6a6a6;
    letter-spacing: 0;
		@apply font-body mb-1;
	}
	
	&__price {
		@apply text-[15px] font-subheading;
	}
}

/* Slider Cart
========================================================================== */
.modal--slider-cart {
	.upsell-item {
		@apply text-sm;
		background-color: var(--color-light);
		@apply p-md;
		.upsell-item__price {
			font-weight : 600
		}
		&__media {
			width:6rem;
			height:6rem;
		}
		button[disabled] {
			background-color:rgba(100,100,100,0.35);
			color: var(--color-light);
		}
		&__header {
      @apply flex justify-between w-full items-start;
      * {
        font-weight:400;
      }
		}
		&__title {
			font-size:14px;
			margin:0;
		}
		&__subtitle {
			font-size:12px;
			margin:0px 0;
			color:#797979;
		}
		&__prices {
			line-height:1;
			margin:0;
		}
		&__body {
			@apply w-full flex flex-col pl-lg;
		}
		
		&__actions {
			@apply flex justify-between w-full;
	
			select {
				@apply border p-xs bg-transparent border-black rounded-md text-sm leading-none;
				line-height:1;
			}
	
			button {
				@apply p-sm ml-auto;
				svg {
					width:16px;
					stroke-width:3px;
				}
			}
		}
		
	
	}
}
.slider-cart {
  &__recs {
    &-title {
	@apply px-xl my-sm;
	color:#042c4b;
	font-size:14px;
	font-family:GTA-Medium,Arial,Helvetica,sans-serif;
	font-weight:600;
   }
 }
  &__header {
    @apply bg-white text-body py-8 border-b border-[#d9d9d9] text-[15px] text-dark font-normal tracking-widest uppercase;
		font-family: var(--font-condensed);
  }
	&__summary-price {
		@apply font-subheading;
	}
	&__summary-text {
		@apply  mx-0 border-b border-[#d9d9d9];
		span {
			@apply flex gap-x-4 justify-center;
		}
	}

	&__summary {
		@apply pt-4;
	}

	.cart {
		&__checkout-button {
			.button {
				@apply hover:bg-black hover:border-black;
				height: 52px;
			}
		}
		&__item-wrapper {
			@apply border-t-[1px] border-[#d9d9d9];
			
			#summary-heading {
				@apply text-base font-subheading;
			}
	
			.button {
				height: 52px;
			}
		}
		&__items {
			@apply border-transparent;
		}
		&__item {
			&:not([hidden]) {
				@apply border-[#d9d9d9];
			}
		}
	}

	.cart-item {
		&__title {
			font-family: var(--font-medium);	
			line-height: 21px;
			@apply mb-[3px];
		}

		&__line-item {
			@apply font-body text-xs tracking-[0.4px] text-[#77706c] capitalize font-normal mb-[3px] order-3 col-start-1;
			line-height: 15.6px;
		}
		&__quantity {
			@apply grow-0 basis-0;
		}
		&__badge {
			@apply font-body font-bold;
		}
	}
}


/* Product Essentials
========================================================================== */

.product-essentials {
	&__media {
		@apply relative;
	}

	&__media-item {
		@apply aspect-w-4 aspect-h-3;
	}

	&__media-play {
		@apply  bg-tertiary gap-1 px-2 h-5 rounded-none bottom-10 right-4 items-center z-10;
		span {
			@apply uppercase text-xs font-bold flex leading-none mt-0.5;
			font-family: var(--font-bold);
		}
		svg {
			@apply fill-black stroke-transparent;
		}
	}

	&__modal-close {
		@apply bg-white;
		color: #373F47;
	}
}


/* Product Form
========================================================================== */

.product-form {
	&__option {
		@apply mb-8 last:max-lg:mb-0 -mx-lg;

		.field__buttons {
			@apply grid px-lg;
		}

		&--frame-color,
		&--lens {
			@apply overflow-hidden lg:overflow-auto;

			.field__buttons {
				@apply flex flex-row overflow-x-auto lg:grid lg:grid-cols-4;
			
				-ms-overflow-style: none;  /* IE and Edge */
				scrollbar-width: none;  /* Firefox */
				
				&::-webkit-scrollbar {
					display: none;
				}
			}
		}

		

		&-selected {
			@apply mb-2.5 px-lg font-body text-dark text-[13px] font-normal tracking-normal;
		}
	}
	.button.button--primary {
		height: 52px;
	}
}

.product-form__option.product-form__option--size {
	label {
		img {
			display: none;
		}
		.field__button-text {
			align-items: center;
			bottom: 0;
			display: flex;
			justify-content: center;
			left: 0;
			position: absolute;
			right: 0;
			top: 0;
		}
	}
	.field__button {
		&:has(input:checked) {
			border-color: #000;
		}
	}
	.field__button.unavailable {
		input {
			&:checked~.field__button-text {
				background: #f4f4f5bf;
				color: var(--color-body);
				border-color: #f4f4f5bf;
			}
		}
	}
}

// .field__button {
//   @apply basis-full;
// 	&-text {
// 		@apply rounded-[3px];
// 	}
// }

// .field__buttons {
//   &--tight {
//     @apply gap-2;
//   }
// }




/* Product Header
========================================================================== */

.product-header {
	@apply w-full;
	
  &__top {
		@apply mb-2 flex items-start;
  }
	&__price {
		@apply font-normal;
	}
	&__price,
	&__compare-at-price {
		@apply text-[20px] font-body uppercase tracking-[.05rem];
	}
  &__type {
    @apply font-body text-[15px] font-normal text-[#736b67] tracking-normal;
  }
	&__prices {
		@apply flex-row-reverse flex max-lg:flex-wrap gap-2;
	}
	&__title {
		@apply text-[27px] font-subheading capitalize tracking-[-.38px] whitespace-normal;
    line-height: 33px;
	}
	&__description {
		@apply hidden lg:block font-body mb-[25px] leading-[1.125];
	}
	&__breadcrumbs {
		@apply font-body text-[0.75rem];
	
		a {
			&:last-child {
				@apply text-dark
			}
		}
	}
	
}

.product-swatch-color-tooltip{
    visibility: hidden;
    text-align: center;
    position: absolute;
    z-index: 1;
    margin-bottom: 14px;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    max-width: none;
    height: 36px;
    background: rgb(255, 255, 255);
    color: rgb(51, 51, 51);
    letter-spacing: 0.025rem;
    border-radius: 50px;
    padding: 8px 16px;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 16px -4px;
    font-size: 0.75rem;
    font-weight: normal;
    bottom: 100%;
    white-space: nowrap;
}

label.field__button:hover .product-swatch-color-tooltip{
    visibility: visible !important;
}

@media screen and (max-width: 1024px) {

	.collection-title-shop-all {
		margin-bottom: 35px;
    	margin-top: 27px;
	}

	.button-desktop{
		display: none;
	}
	.button-mobile{
		display: block;
	}
}
@media screen and (min-width: 1024px) {

.button-mobile{
	display: none;
}
}


/* Reviews
========================================================================== */


.ElementsWidget-prefix {
	.ElementsWidget {
		.R-TextHeading {
			font-family: var(--font-medium);
		}
		
		.R-TextBody {
			@apply font-body font-normal;
		}
	
		.item__slidersGroup {
			@apply font-normal;
		}
		
		.footer__reviewsLogo-container {
			img.R-PlatformLogo,
			img.R-PlatformIcon {
				@apply hidden;
			}	
		}

		.header__inner {
			@apply px-0 lg:pt-5;
		}

		.ElementsWidget__search {
			.R-Field__input {
				&:focus {
					@apply focus:border-2 focus:border-black transition-all duration-200 ease-in-out;
				}
			}
		}

		.R-TabControls {
			.R-TabControls__item,
			.R-TabControls__item.isActive {
				@apply text-body;
			}
			.R-TabControls__item.isActive {
				@apply border-body;
			}
		}

		.R-Button {
			@apply font-body;

			&.R-Button--secondary {
				@apply bg-tertiary text-body;
			}
		}

		.R-PaginationControls {
			.R-PaginationControls__item {
				@apply text-body;

				&.isActive > .R-TextHeading {
					@apply text-body;
				}

				&.isActive {
					@apply border-b-body;
				}
			}
		}
	}
}


/* Product Meta
========================================================================== */
.product-meta {
	@apply gap-y-1;
  &__title {
    @apply text-[14px];
		
  }

  &__type {
    @apply font-body tracking-normal text-[13px] font-normal text-[#77706c];
  }

  &__color {
    @apply font-body text-dark text-[12px];
  }

  .reviews-snippet {
    @apply mb-1;
  }

  .product-item__prices {
    @apply mt-0;
  }
  .product-item__price {
    @apply font-body;
  }
}


/* Promo Motivator
========================================================================== */

.promo {
	@apply flex items-center gap-x-4 py-3;

	&__icon-wrapper {
		@apply shrink-0;
	}
	&__icon {
		@apply max-w-[48px];
	}
	&__content {
		@apply text-dark text-sm leading-none;
	}
	&__headline {
		@apply text-sm inline-block my-0;
		font-family: var(--font-medium);
	}
	&__description {
		@apply font-body text-[12px];
	}
}


/* Fit Guide
========================================================================== */

.fit-guide {
	.grid {
		@apply mb-8;
	}

	&__block-title {
		@apply text-base;
	}

	&__title {
		@apply font-body text-body text-[13px] font-normal tracking-wider uppercase;
	}

	.progress-bar {
		@apply mt-1;
	}
}


/* Collection
========================================================================== */

.template-collection {
  #MainContent {
    @apply bg-light;
  }
}

.collection {
	@apply lg:pt-4xl;

	&__sidebar {
		@apply lg:sticky lg:top-7xl;
		&:not([open]) {
			@apply z-0;
		}
		&[open="true"] {
			@apply z-[60];
		}

		&-header {
			@apply bg-[#ededed] lg:hidden;
			
			.label,
			button {
				@apply py-base px-lg gap-2 text-sm;

				span {
					@apply text-sm;
				}

				.icon {
					@apply w-4 h-4;
				}
			}
			&--filtered {
				.apply {
					@apply bg-black text-white;
				}
			}
		}

		&-body { 
			// position: static;
			overflow-x: hidden;
			//   @apply text-sm flex-col gap-sm overflow-y-auto lg:overflow-hidden;
			//height: 100% !important;
			// @media only screen and (min-width: 1024px) {}
		}
	}

	&-filters {
		@apply divide-y-[1px] border-gray-200 gap-y-8 max-lg:order-1;

		&__accordion {
			@apply max-lg:px-lg;

			&-header {
				@apply pl-0 h-[54px];
			}

			&-title {
				@apply font-body uppercase text-[14px] text-body tracking-widest;
			}

			&-content {
				@apply mt-2;
			}

			&-icon {
				.collection-filters__accordion:not([open]) & {
					transform:rotate(180deg);
				}
			}
		}

		&__content {
			@apply gap-y-2 pb-6;

			.field__buttons {
				@apply gap-1 max-lg:flex max-lg:flex-wrap lg:grid-cols-4;

				.field__button {
					@apply grow shrink-0 max-w-[50px];
				}
			}
		}

		& > .field {
			@apply lg:pt-5 pb-6;
		}

		.field {
			@apply mb-0;

			&__colors {
				@apply gap-x-2 gap-y-4;
			}

			&__color {
				@apply p-0;
			}
		}
	}
}

/* Quick Add
========================================================================== */
.modal--quickadd {
  padding: 30px 20px 20px;
	@apply text-body;
	
	.quick-add {
		&__field-button {
			@apply rounded-none h-auto;

			.field__button-text {
				@apply hidden;
			}
			.quick-add__field-button-img {
				@apply block;
			}
		}
		&__info-button {
			@apply bg-black before:bg-black border-black before:border-black text-white before:text-white;
		}
	}
	
  .product-meta {
    .product-item__price {
      font-size: 14px;
			@apply mb-0;
    }
		&__footer {
			@apply flex flex-col gap-1;
		}
		.review-snippet {
			@apply order-last;
		}
  }
}


/* Login Modal
========================================================================== */
.modal--account {	
	@apply w-80 rounded-lg p-8;
 
	.account-modal {
		&__title {
			@apply text-primary text-[24px] mt-4 mb-2;
		}
	
		&__subtitle {
			@apply text-primary text-[13px] mb-4;
		}
	
		&__button {
			@apply w-full mt-2;
		}

		&__link {
			@apply flex justify-center text-[13px] text-primary mt-4 cursor-pointer;
		}
	}
}



/* Sticky Stuff
========================================================================== */
body {
	&.page-scroll--up {
		&.template-collection {
			.section--sticky {
				&.active {
					@media only screen and (max-width: 767px) {
						top: 106px !important; //106
					}
				}
			}
		}
	}
}
.template-product{
	.section--sticky {
		&.active {
			@media only screen and (max-width: 767px) {
				// top:var(--header-offset);
				top:var(--header-bottom);
			}
		}
	}
}
body {
	&.page-scroll--up {
		&.template-product {
			.section--sticky {
				&.active {
					@media only screen and (max-width: 767px) {
						top: 106px !important;
					}
				}
			}
		}
	}
}
.section--sticky {
	opacity:0;
	&.active {
		opacity:1;
		top:var(--header-bottom);
		@apply shadow-lg;
		&.lg\:top-main {
			@media only screen and (min-width: 1024px) {
				top:var(--header-bottom);
		  }
		}
		// &.bottom-0 {
		// 	top:var(--header-offset);
		// }
		// @media only screen and (min-width: 1024px) {
		// 	top:var(--header-bottom);
	  	// }
	}
	.section__blocks {
		@apply gap-4;
	}
	.product-header__bottom {
		margin-bottom:0;
	}
	.section__block--gate:not(:has(.button)){
		display:none;
	}

  transition:transform 300ms ease;
  will-change: transform;
	
	&.active {
	  transform:translateY(0%)!important;
	} 

	&.bottom-0 {
		@media only screen and (max-width: 1023px) {
	  	transform:translateY(100%);
	  }
	}
	&.top-main {
		@media only screen and (max-width: 1023px) {
	  	transform:translateY(-100%);
	  }
	}

	&.lg\:bottom-0 {
		@media only screen and (min-width: 1024px) {
	  	transform:translateY(100%);
	  }
	}
	&.lg\:top-main {
		@media only screen and (min-width: 1024px) {
	  	transform:translateY(-100%);
	  }
	}
	
}


/* Geolocation
========================================================================== */
.geolocation {
	&__header {
		@apply p-xl border-b;
	}

	&__footer {
		@apply p-xl;
	}

	article {
		@apply p-xl bg-light border-b;
	}

	.field:last-child {
		margin-bottom:0;
	}

	&__site_list_item {
		a {
			@apply flex items-center rounded-sm border mb-2 p-sm bg-white;
			&:hover {
				border-color:#CCC;
			}
			li:last-child & {
				margin-bottom:0;
			}
			img {
				width:4rem;
				height:2rem;
				object-fit:contain;
			}
		}
	}

	&__site_switcher {
		@apply p-xl gap-md;
	}
	&__site {
		@apply flex flex-col gap-xl items-center rounded-sm border mb-2 p-sm bg-white;
		&:hover {
			border-color:#CCC;
		}
	}
}

/* Subnav
========================================================================== */

.subnav {
	@apply overflow-hidden gap-x-4 bg-white border-t border-b border-gray-200 py-2 px-2.5 select-none;

  &__link {
    @apply border border-gray-200 rounded text-body block text-[13px] py-1.5 capitalize px-4 transition-all duration-300 ease-in-out text-center;
  }

  &__item {
    @apply opacity-100 pointer-events-none transition-opacity duration-[250ms];

    [id^='swiper-wrapper'] & {
      @apply opacity-100 pointer-events-auto;
    }

		&:hover,
    &.active {
      .subnav__link {
        @apply bg-primary border-primary text-white
      }
    }

    &:not(:first-child) {
      .subnav__link {
        @apply ml-1.5;
      }
    }
  }
  
  .swiper-slide {
    @apply w-auto;
  }
}


/* Account Pages
========================================================================== */

.section--account {
	@apply font-body;
	.order-detail {
		&__summary {
				@apply flex flex-col bg-[#ffffff]
				}
				}

	.section__header {
		.header__title {
			@apply capitalize text-2xl font-subheading;
		}
	}
	.section__sidebar {
		.sidebar {
			.sidebar__item {
				.sidebar__link {
					@apply text-[15px] font-bold hover:bg-transparent hover:underline-offset-8 hover:decoration-2 hover:underline;
				}	
				
				&:hover {
					.sidebar__link {
						text-decoration-color: currentColor;
					}	
				}
			}	
		}
	}
	.account-profiles {
		button {
			&.button--light {
				&:hover, button:hover & {
					@apply text-primary;
				}
			}
		}
		.account-profile {
			&__name {
				@apply text-[15px] font-body my-0;
			}
		}
	}
	button {
		&.button--simple {
			@apply text-primary;
		}
	}
	.account-block,
	.wishlist {
		&__title {
			@apply font-body text-[18px];
		}
	}

	.field__chip {
		@apply bg-white;
	}
}
@media screen and (max-width: 1024px) {

	.desk-clear-all{
	  display: none;
	}
	}
	@media screen and (min-width: 1024px) {
	.desk-clear-all .clear-all-text  {
	  color: #381300;
	  size: 12px;
	  font-family: var(--font-medium);
	  border-bottom: 1px solid #381300;
	}
	.desk-clear-all {
	padding-bottom: 11px;
	}
	}
