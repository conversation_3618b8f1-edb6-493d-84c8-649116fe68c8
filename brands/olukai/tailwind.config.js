// Start with shared tailwind config
const defaultConfig = require('../../utilities/tailwind.config.js')
const brandConfig = defaultConfig

// brandConfig.theme.colors.gray["500"] = "#77706c"

// Use the one of the predefined spacing scales
// brandConfig.theme.extend.spacing =  { 
//   ...brandConfig.theme.extend.spacing,
//   ...brandConfig.scales.minorThird
// }

// Manually write your own Spacing Scale
brandConfig.theme.extend.spacing =  { 
  ...brandConfig.theme.extend.spacing,
	'3xs': '.5rem',
    '2xs': '.625rem',
    'xs': '.688rem',
    'sm': '.938rem',
    'base': '1rem',
    'md': '1.125rem',
    'lg': '1.5rem',
    'xl': '2.25rem',
    '2xl': '3rem',
    '3xl': '3.75rem',
    '4xl': '5.25rem',
    '5xl': '6.25rem',
    '6xl': '10.625rem',
    '7xl': '16rem'
}


// Use the one of the predefined spacing scales
//brandConfig.theme.extend.fontSize =  { 
//  ...brandConfig.theme.extend.fontSize,
//  ...brandConfig.scales.minorThird
//}

// Manually write your own Spacing Scale
 brandConfig.theme.extend.fontSize =  { 
   ...brandConfig.theme.extend.fontSize,
    '2xs': '0.25rem',
    'xs': '0.579rem',
    'sm': '0.833rem',
    'base': '1rem',
    'md': '1rem',
    'lg': '1.2rem',
    'xl': '1.44rem',
    '2xl': '1.728rem',
    '3xl': '2.074rem',
    '4xl': '2.488rem',
    '5xl': '2.986rem',
    '6xl': '3.583rem',
    '7xl': '5.25rem',
    '8xl': '6.25rem'
 }

module.exports = brandConfig
