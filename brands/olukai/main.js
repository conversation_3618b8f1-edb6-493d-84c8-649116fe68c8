import 'regenerator-runtime/runtime';

import './main.scss';
import './globals.scss';
import './theme.scss';

import '../../components/aria';
import '../../components/cart';
import '../../components/carousels/carousels';
import '../../components/customer';
import '../../components/customer.segmentation';
import '../../components/focus';
import '../../components/forms/forms';
import '../../components/loyalty';
import '../../components/modal';
import '../../components/offers';
import '../../components/preload';
import '../../components/quick-add';
import '../../components/sections';
import '../../components/track';
import '../../components/wishlist';
import '../../components/async-video';

import '../../components/@integrations/alpine'

import '../../components/@integrations/archipelago'
import '../../components/@integrations/archipelago/wishlist'
import '../../components/@integrations/shopify'

import '../../components/@integrations/reviewsio'
import '../../components/@integrations/mParticle'

import Alpine from 'alpinejs'
import mask from '@alpinejs/mask'
import intersect from '@alpinejs/intersect'
Alpine.plugin(mask)
Alpine.plugin(intersect)
window.Alpine = Alpine

window.addEventListener('DOMContentLoaded', Alpine.start)

Alpine.directive('import', (el, { modifiers, expression }, { evaluateLater, effect }) => {
    
    const moveElement = evaluateLater(expression)
 
    effect(() => {
        moveElement(querySelector => {
            if(!!document.querySelector(querySelector)) {
              if(modifiers.includes('beforeparent')){ 
                el.parentNode.before(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('afterparent')){ 
                el.parentNode.after(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('before')){ 
                el.before(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('after')){ 
                el.after(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('inner')){ 
                el.innerHTML = document.querySelector(querySelector).innerHTML
              }
            }
        })
    })

})

window.money = {
	currencies: {
    default:{label:true},
    USD:{symbol:'$'},
    CAD:{symbol:'$',label:'CAD'},
    EUR:{symbol:'€'},
    JPY:{symbol:'¥'},
    GBP:{symbol:'£'}
  },
  format: v => {
    const currency = money.currencies[Shopify.currency.active] || money.currencies.default;
  	money.currency = currency

    let val = `${currency.symbol ? currency.symbol : ``}${(typeof v== 'string'&&v.includes('.')) ? v : (v / 100).toFixed(2)}${currency.label ? ` ${Shopify.currency.active}`:``}`.replace('.00','') 
  	
    return val
  
  }
}

window.image = {
  format: (url, config={} ) => {
    if(!url) return '';
    return `${url.split('?')[0]}?${Util.urlparams.build(config)}`;
  }
}

window.addEventListener('QuickAdd:opened', loadReviewsIoRatingSnippets)

window.addEventListener('Products:optionSelected', e=>{
  if (window.outerWidth >= 1024 && (e.detail.name == 'Color' || e.detail.name == 'color')) {
    window.scrollTo({top: 0, behavior: "smooth"});
  }
})