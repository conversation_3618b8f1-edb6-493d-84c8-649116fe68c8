@import '../../components/product-item/theme-olukai.scss';
@import '../../components/product-finder/theme-olukai.scss';
@import '../../components/modal/theme.scss';
@import '../../components/search-suggestion/search-suggestion.scss';
@import '../shared/styles/footer.scss';
@import '../shared/styles/quick-add.scss';
@import '../shared/styles/animations.scss';

/* Type
========================================================================== */
/* Header
========================================================================== */

.header-bar {
  &--main-promo {
    @apply py-2 text-sm;
    
    .header-bar__container {
      @apply justify-center items-center;
    }
  }
  &--main {
    @apply bg-white;

    .header-bar__container {
      @apply w-full justify-start items-center lg:px-3xl px-[20px];

      span.header-bar__block--menu {
        @apply lg:pb-0 pb-9;
      }
    }

    .header-bar__block:not(.header-bar__block--menu) {
      height: 58px;
    }
  }

  &__block {
    &--logo {
      @apply mr-[34px];
    }

    &--menu {
      height: auto;
      background: #fff;
      @apply max-lg:order-last bg-white max-lg:-mx-5 max-lg:w-screen;

      .header-nav {
        @apply max-lg:px-4 max-lg:overflow-x-scroll max-lg:w-full;
      }

      &:has(.flex-col) {

        flex-direction: column;
        align-items: start;

        ul {
          width: 100%;
        }
      }

      ul {
        @apply lg:justify-start;

        li {

          a:not(.button),
          summary {
            color: var(--color-primary);
            font-size: 16px;
            font-weight: normal;
	    letter-spacing: -.3px;
	    white-space: nowrap;
          }
        }
      }

      >ul.flex-row {
        >li {

          >a,
          summary {
            height: 58px;
            @apply flex items-center justify-center px-[17px] relative overflow-hidden lg:relative;
            flex-direction: column;

            &:after {
              @apply bg-[#ccc] absolute lg:relative;
              height: 2px;
              bottom: 0;
              transform: scaleY(0);
              transition: transform 200ms ease;
              left: 0;
              width: 100%;
              content: '';
            }

            &:hover {
              &:after {
                transform: scaleY(1);
              }
            }

            @media (max-width: 1023px) {
              &:after {
                height: 1px;
                background-color: #eee;
                transform: scaleY(1);
              }
            }
          }

          details {
            @apply h-full;

            @media (max-width: 1023px) {
              &[open] summary:after {
                @apply bg-primary;
                height:2px;
              }
            }
          }

          &.header-nav__item {
            @apply max-lg:w-1/3 max-lg:flex-shrink-0;
            @media (min-width: 1024px) {
              details summary:after,
              a:after {
                @apply bg-primary;
                transition: transform 200ms cubic-bezier(0.825, 0.005, 0.405, 1.220);
                transform-origin: left;
                transform: scaleX(0);
              }

              details[open] summary:after,
              a:hover:after,
              &.header-nav__item--active summary:after {
                transform: scaleX(1);
                height: 2px
              }
            }
          }

          &.header-nav__item--active {

            summary:after,
            a:after {
              @apply bg-primary;
              transform: scaleY(1);
            }

            @media (min-width: 1024px) {
              a:after {
                @apply bg-primary;
                transform: scaleY(1);
              }
            }
          }
        }
      }

      >ul.flex-col {
        line-height: 26px;

        .type-nav-link {
          font-family: var(--font-medium)
        }

        details {
          summary {
            border-bottom: 1px solid var(--color-tertiary);
            @apply py-3 px-6;
            font-size: 17px;
          }

          ul {
            details {
              summary {
                font-size: 16px;

                a {
                  font-family: var(--font-medium)
                }
              }

              ul {
                border-bottom: 1px solid var(--color-tertiary);
                @apply py-4 px-6;

                li {
                  @apply px-6;

                  a {
                    font-size: 13px;
                  }
                }
              }
            }
          }
        }

        &>li {
          &>a {
            @apply px-6 py-3 block;
          }
        }
      }
    }

    &--nav-tools {
      @apply justify-end lg:gap-[4px];
      height: 58px;

      svg {
        stroke-width: 1.75px;
        color: #000000; // #036;
        width: 18px;
        height: 18px;
      }

      .nav-tools__account-greeting {
        @apply hidden;
        font-size: 13px;
      }

      .nav-tools__cart-count {
        position: absolute;
        top: -3px;
        right: -8px;
        color: #fff;
        background-color: var(--color-secondary);
        display: flex;
        justify-content: center;
        align-items: center;
        width: 20px;
        height: 20px;
        font-size: 12px;

        border-radius: 100%;
      }

      button,
      a {
        position: relative;
        @apply p-[11px] hover:bg-[#f1f1f1] rounded-full; 
      }
    }

    &--menu-toggle {
      margin-left: -20px;

      button {
        padding: 1rem;
      }
    }

    &--search {
        @media only screen and (max-width: 1023px) {
        position: sticky;
        order: 1;
          z-index: 50;
        width: 40px;
        left: 50px;
          top: 0;
        overflow: hidden;

        label {
          background-color: transparent;
          &:after {
            content: '';
            @apply absolute bg-white left-[-50px] bottom-[-9px] transform translate-y-full h-[54px] w-screen z-0;
          }
        }

         .header-bar__search-field label {
           overflow: visible;
         }

         .header-bar__search-field label input {
          @apply absolute bottom-[-9px] bg-[#f1f1f1] transform translate-y-full left-[-40px] p-[10px] z-10;
          width: calc(100vw - 20px);
          border-radius: 9px;
        }

        .header-search__close {
          @apply absolute left-0 transform translate-x-[calc(100vw-90px)] bottom-[-9px] translate-y-[calc(100%+10px)] z-10 text-primary;
        }

        @keyframes height-after {
          from {
            height: 0;
          }
          to {
            height: 54px;
          }
        }
        @keyframes height-input {
          from {
            height: 0;
          }
          to {
            height: 44px;
          }
        }
        &:focus-within,
        body:has([data-modal=search]) & {
          overflow: visible;

          .header-bar {
            height: auto;
            --transparent-text-color: var(--color-primary);
          }

          .header-search__close { 
            display: block !important;
          }
        }

        .header-bar__search-field label.active input {
          @include animate(height-input);
          animation-duration: calc(44 * 4ms);
        }
        .header-bar__search-field label.active:after {
          @include animate(height-after);
          animation-duration: calc(54 * 3.2ms);
        }
      }

      body:not(:has([data-modal=search])) &:not(:focus-within) .header-search__close {
        @apply hidden;
      }
    }

  }

  &__search-field {
    @apply lg:mr-[4px];
    label {
      @apply rounded-full bg-transparent px-[11px] py-[8px];

      .icon {
        width: 18px;
      }

      input {
        @apply bg-transparent text-[12px] w-0 text-primary;

        &:focus {
          outline: none;
        }
      }

      &:hover {
        @apply lg:bg-[#f1f1f1];
      }

      @media(min-width: 1024px) {
        input {
          @apply transition-all;
          transition-duration: 50ms;
        }
        // .header-search__close { @apply hidden; }
        &:focus-within,
        &.active {
          @apply lg:bg-[#f1f1f1] px-sm py-[5px];

          .header-search__close { 
            display: block !important;
          }
          input { 
            @apply w-[150px] p-[6px]; 
          }
        }
      }
    }
  }

  @media (min-width: 1024px) {
   .page-scroll--top &--transparent-at-top:hover &__container {
      background-color: #fff;
    }

    .page-scroll--top:not(:has([data-modal=search])) &--transparent-at-top:focus-within {
       height: 0;

      .header-bar__container {
        background: white;
        --transparent-text-color: var(--color-body);
      }
    }

    .page-scroll--top &--transparent-at-top .header-bar__container {
      transform: translateY(-1px);
    }

  }
}

@media only screen and (max-width: 1023px) {
  .header-bar {
    &.active,
    &:focus-within.active {
      overflow-y: scroll;
    }
    &--main {
      body:has([data-modal=search]) & {
        height: auto;
        overflow: visible;
      }
    }

    &__block {
      &--menu {
        border-bottom: 1px solid #eee;
      }

      &--logo,
      &--menu-toggle,
      &--nav-tools {
        .header-bar--main & {
          position: sticky;
          top: 0;
          z-index: 41;
        }
      }

      &--logo {
        order: 2;
        .header-bar--main & {
          flex-grow: 1;
          margin-right: 0;
        }
      }

      &--menu-toggle {
        &:before {
          z-index: 40;
          pointer-events: none;
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          width: 100vw;
          background: #fff;
          border-bottom: 1px solid #eee;
          content: '';

          .header-bar:has(input:focus) & {
            border-bottom: none;
          }
        }

        button {
          z-index: 41;
        }

        svg {
          width: 18px;
        }
      }
    }

    &--main {
      /* max-height:calc(100dvh - 36px); */
      max-height: 100dvh;
      overflow: auto;
    }


   .page-scroll--top &--transparent-at-top {
     overflow: visible;

     &:not(.active) .header-bar__block--menu-toggle:before {
       background: transparent;
       border-bottom: none;
     }
    }

    &.active {
      .header-bar__block--menu {
        @include animate(slide-in-right);
        animation-duration: 200ms;
        animation-delay: 0ms;
      }

      .header-bar__block--menu-toggle {
        .icon-menu {
          display: block;
          width: 18px;
          height: 18px;
        }
        .icon-x {
          display: none;
        }
      }
    }

    &:focus-within,
    & .header-bar__block:focus-within,
    .page-scroll--top &:has(input:focus) {
      overflow: visible;
    }
  }

  #icon-def-menu {
    rect {
      transition: transform 200ms linear;
    }
    rect:first-child {
      transform-origin: 15px 22px;
    }
    rect:last-child {
      transform-origin: 15px 18px;
    }
  }
  body:has(.header-bar.active) {
    #icon-def-menu {
      rect:first-child {
        transform: rotate(-45deg);
      }
      rect:last-child {
        transform: rotate(45deg);
      }
    }
  }
}

#shopify-section-header {
	@media only screen and (max-width: 1023px) {
    max-height: 100dvh;
    overflow-y: scroll;
	}

	&:before {
    pointer-events: none;
    position: fixed;
    z-index: 0;
    content: '';
    background: rgba(0, 0, 0, 0.5);
    top: 0;
    left: 0;
    right: 0;
    height: 100vh;
    opacity: 0;
    transition: opacity 600ms ease;
				
	}
	

}

.nav-item {
	@apply text-center rounded overflow-hidden text-primary;
  font-size: 13px;

	img {
    transition: scale 200ms ease;
	}

	&:hover {
		img.nav-item__media {
      scale: 1.1
		}
	}

  &__text {
    @apply uppercase font-bold tracking-[0.3px];

  }
}



/* Mega Menu
========================================================================== */

.mega-menu,
#shopify-section-mega-menu {
  @media (min-width: 1024px) {
    border-top: 1px solid #eee;
  }

  [open]:hover & main>.flex-frame>* {
    @extend .animate--stagger;
    --animation-delay-increment: 20;
    // @extend .stagger--debug;

    &:is(.flex-frame),
    &:is(.content-item) {
      --animation-nth: calc(var(--animation-parent-nth) + 3);
      @extend .animate--fade-in-down;

      .menu-item__menu>ul>li {
        --animation-nth: calc(var(--animation-parent-nth) - 1);
      }

      @media (max-width: 1023px) {
        animation: none;
        animation-delay: 0ms;
      }
    }
  }

  .menu-item {
    @apply lg:my-[4rem] first:lg:mx-5;

    &__menu {

      &>ul>li,
      &>ul>li>ul>li {
        @extend .animate--stagger;
      }
	

      &>ul>li>a,
      &>ul>li>ul>li>a { 
        font-family: GTA-Medium;
        font-size: 15px;
        line-height: 15px;
        @apply lg:block py-[10px] lg:py-[0] px-[20px] lg:px-[2rem] text-[18px] leading-[18px] tracking-[-.3px] lg:text-[15px] lg:leading-[15px];
        // @extend .stagger--debug;
        &[href="javascript:void(0)"] {
          @apply cursor-text;
        }
        &:not([href="javascript:void(0)"]) {
          @apply hover:underline;
        }

        .icon {
          stroke-width: 3px;
          width: 18px;
          height: 18px;
        }

        [open]:hover & {
          @extend .animate--fade-in-down;
          @media (max-width: 1023px) {
            animation: none;
            animation-delay: 0ms;
          }
        }
      }
	  @media (max-width: 1023px){
		&>ul>li>a{
			@apply text-[33px] leading-[33px] tracking-[-.3px] ;
		  }
	  }
	  
			&>ul>li>ul{
				@apply lg:mt-[21px] lg:gap-y-[15px];
			}
      &>ul>li>ul>li>a {
        display: block;
        font-family: GTA-Regular;
      }
    }

    &:nth-child(-n+2)>.menu-item__menu {
     @apply max-lg:mt-[38px];
		 &>ul {
			@apply lg:gap-y-[21px];
		 }
      &>ul>li>a {
        @apply text-[33px] leading-[33px] tracking-[-.3px] lg:text-[15px] lg:leading-[15px] lg:pt-0 lg:pb-0;;
      }
    }

  }

  .content-item {
    @apply self-stretch max-w-screen-lg;

    &__text-stack {
      @apply px-0;
    }

    p {
      font-size: 18px;
      line-height: 18px;
      font-family: GTA-Medium;
			letter-spacing: -.3px;
    }

    .button {
      @apply mr-auto;
      font-size: 16px;
      line-height: 16px;
      font-family: GTA-Medium;
    }
  }

  &__footer {
    .menu-item {
      @apply my-[11px];

      &__menu>ul>li>a {
        font-family: GTA-Regular;
				padding-top: 12px !important;
				padding-bottom: 12px !important;
      }
    }
  }

  &__footer-col {
    align-self: stretch;
    border-right: 1px solid white;
    @apply mx-5;

    .type-item {
      font-family: GTA-Medium;
      font-size: 15px;
      line-height: 15px;
      @apply py-[0.75rem] px-[2rem] text-primary;
			letter-spacing : -.3px;
    }
  }
}

.menu-item {
	&:not(:last-child) {
		border-right:1px solid #ede9df;
	}
	&__media {

	}
	&__menu {
		& > ul {
			& > li {
				& > a {
					color:var(--color-primary);
					font-size:23px;
					font-family:Olukai-Bold,Arial,Helvetica,sans-serif;
					padding:1rem 1rem;
					display:flex;
				}
			}
		}
		a {
			color:var(--color-primary);
			font-size:13px;
			font-family:GTA-Regular,Arial,Helvetica,sans-serif;
			padding:0 1rem;
			line-height:26px;
		}
		button {
			@apply flex items-center justify-end w-[30px] lg:hidden;
		}
	}
}

/* Search Results
========================================================================== */

.search-results {
	@apply px-0 pb-2.5 pt-0 bg-light text-body;

  &__header {
    @apply p-2.5;
    display:none;
  }

  &__type {
  	@apply px-sm;
  	font-size:14px;
  }

	&__type-label {
		@apply bg-light mb-0 py-2.5 px-2.5;
	}


	&__item--product {
		a {
			@apply flex items-start gap-2.5;
		}

		img {
			@apply w-auto h-auto max-w-[70px] max-h-[70px];
		}
	}

  @media  (max-width: 1023px) {
    &.modal--underlay.modal--right {
      top: calc(var(--header-bottom) + 54px);
      height: calc(100vh - var(--header-bottom) - 54px);
    }
  }
}

.search-result-item {
	@apply bg-white hover:bg-gray-100 p-2.5 border-l border-r border-t border-gray-200 ;


	&:first-of-type {
		@apply rounded-t;
	}
	&:last-of-type {
		@apply border-b border-gray-200 rounded-b;
	}

	&__title {
		@apply text-[17px] leading-tight mb-2;
	}

	&__type {
		@apply mb-2;
	}

	&__type,
	&__sku {
		@apply text-sm text-[#736b67];
	}
	
	&__price {
		@apply text-[15px];
	}
}


/* Product Essentials
========================================================================== */

.product-essentials {
	&__container {
		@apply max-w-[1220px] mx-auto pb-16;

		.swiper-slide {
			@apply bg-tertiary;
		}
	}

	&__block {
		
	}
	
	&__promo {
		& ~ .product-essentials__promo {
			@apply my-0;
		}
	}

	&__product-form {
		button {
			@apply h-[59px];
		}
	}

	.product-gate__actions .button {
		@apply h-[59px];
	}

	&__media {
		&-item {
			@apply aspect-1;
		}

		&-play {
			@apply  bg-white gap-1 px-2 h-5 rounded-full bottom-[1.9rem] right-4 items-center z-30;
			span {
				@apply uppercase text-xs font-heading flex leading-none;
			}
			svg {
				@apply fill-black stroke-transparent;
			}
		}
	}

	&__modal-close {
		@apply bg-white;
		color: #373F47;
	}

  &__buy-box {
    @apply lg:pl-8 lg:pr-10 max-lg:static;

    .divider {
      @apply  max-lg:-mx-lg w-auto overflow-hidden;
    }
    a.promo {
      @apply max-lg:first-of-type:border-t border-[#DDDDDD] first-of-type:pt-xl max-lg:first-of-type:mt-xl last-of-type:border-b last-of-type:pb-xl max-lg:first-of-type:-mx-lg max-lg:first-of-type:px-lg max-lg:last-of-type:-mx-lg max-lg:last-of-type:px-lg;
    }
    .button--emphasis {
      @apply not-italic h-auto py-xl text-[15px];
      letter-spacing: -0.54px;
      .button__text {
        @apply text-[15px];
        letter-spacing: -0.54px;
      }
    }
  }

  &__description {
    @apply border-b border-[#DDDDDD] mb-3xl max-lg:-mx-lg max-lg:px-lg;
    .type-headline {
      @apply pb-3xl;
    }
  }

  &__key-features {
    .content-item__text-stack {
      & > :first-child {
        @apply text-[18px] lg:text-[15px];
      }
    }
  }
}

.payments-banner {
  @apply text-center;
}


/* Product Form
========================================================================== */

.product-form {

	&__options {
		@apply grid grid-cols-1 gap-xl lg:gap-lg;
	}

	&__option {

		.field__buttons {
			
		}

		.field__button {
			&-text {
				@apply rounded;
			}

			&.unavailable {
				@apply text-body rounded;
			}
			
			&:not(.unavailable) {
				input:checked	~ .field__button-text {
          @apply border-primary bg-primary text-light;
				}
				input:checked~.field__button-text ~ .field__table-data{
					span.field__table-cell {
						@apply bg-[#e5e7eb];
					}
				}
			}
		}

		&--color {
			@apply overflow-hidden lg:overflow-auto;

			.field__buttons {
				@apply flex flex-row overflow-x-auto lg:grid;
      }

			.field__button {
				@apply w-[auto] h-[auto] grow-0 basis-auto shrink-0 lg:w-full lg:h-full lg:grow lg:shrink border-2 border-transparent p-0;
				&.unavailable {
					@apply bg-transparent text-body;
					
						
						background-image: url(https://olukai.com/cdn/shop/t/405/assets/striped-unavailable.svg?v=124369087580223912691602802726);
						background-repeat: no-repeat;
						background-size: cover;
					
				}
				&-text {
					@apply hidden;
				}

				&:focus,
				&:has(input:checked) {
					@apply rounded border-primary;
				}

				img {
					@apply object-contain aspect-1;
				}
			}
		}
		&:not(&--color) {
			img {
				@apply hidden;
			}
		}

		&--size {
			.field__buttons {
				@apply flex flex-wrap lg:grid lg:grid-cols-5;
			}

			.field__button {
				@apply basis-[3.5rem] shrink grow-0;

				&.unavailable {
					@apply bg-[#eee] border-[#eee];
				}
				
				.field__button-text {
					@apply font-body text-sm;
				}

				&:hover {
					.field__button-text {
						@apply border-primary;
					}
				}
			}
		
			img {
				@apply hidden;
			}
		}

		&-selected {
			@apply mb-2.5;
		}
	}
}

.header-bar {
	border-bottom: 1px solid #eeeeee !important;
	}

.template-pdp-design-update {
	@media only screen and (max-width: 1024px) {
		.product-header {
			padding-bottom: 18px;
			}
		}
		.product-header__top {
			margin-bottom: 0px;
	}
	.promo__headline {
		padding-bottom: 0px;
	}
	.product-essentials__buy-box {
		.button--emphasis {
			padding-top: 18px;
			padding-bottom: 17px;
		}
	} 
	.text-gray-500 {
		color: #919191 !important;
	}
	.product-essentials__key-features {
		border: none;
		margin: 0px;
		padding-left: 0px;
		padding-right: 0px;
	}
	.modal__content {
		.product-form {
			&__option{
				&--size{
					.field__buttons {
						display: flex;
						flex-wrap: nowrap;
						gap: 0;
						overflow-x: scroll;
						-ms-overflow-style: inherit;
						label.field__button {
							flex-grow: 1;
							height: auto;
							flex-shrink: 1;
							flex-basis: 56px;
					}
				}
				}
			}
		}
	}
	.fit-guide {
		@apply pb-[24px];
		.progress-bar {
			margin-top: 0px;
		}
	}
	@media not all and (min-width: 1024px) {
	.product-essentials__buy-box {
		a.promo {
			&.promo:first-of-type,
			&:last-of-type {
					margin-left: -1.125rem;
					margin-right: -1.125rem;
					padding-left: 1.125rem;
					padding-right: 1.125rem;
			}
		}
		.divider {
			margin-left: -1.125rem;
			margin-right: -1.125rem;
		}
	} 
	}
  .product-form {

    &__options {
      @apply grid grid-cols-1 gap-lg;
			.button--link {
				letter-spacing: -.013px;
			}
    }
		&__option-selected {
			word-break: break-word;
		}
    &__option {
      .field__buttons {
        @apply gap-[5px]; 
        &--colors {
          overflow: -moz-scrollbars-none;
          -ms-overflow-style: none;
        }
      }

      .field__button {
        @apply lg:h-12;

        &-text {
          @apply rounded-md;
        }

        &.unavailable {
          @apply text-body rounded-md;
        }
        
        &:not(.unavailable) {
          input:checked	~ .field__button-text {
            @apply border-primary bg-primary text-light;
          }
          input:checked~.field__button-text ~ .field__table-data{
            span.field__table-cell {
              @apply bg-[#e5e7eb];
            }
          }
        }
      }

      &--color {
        @apply overflow-hidden lg:overflow-auto max-lg:-mx-md;
        .product-form__option-label-wrapper {
          @apply max-lg:px-md;
        }
        .field__buttons {
          @apply lg:grid pl-md lg:pl-0 flex flex-row overflow-x-auto;
        }

        .field__button {
          @apply w-[auto] h-[auto] grow-0 basis-auto shrink-0 lg:w-full lg:h-full lg:grow lg:shrink border border-transparent hover:border-primary p-0 max-lg:last-of-type:mr-md;
          background-color: var(--color-tertiary);
          &.unavailable {
            @apply bg-transparent text-body;
            
              
              background-image: url(https://olukai.com/cdn/shop/t/405/assets/striped-unavailable.svg?v=124369087580223912691602802726);
              background-repeat: no-repeat;
              background-size: cover;
            
          }
          &-text {
            @apply hidden;
          }

          &:focus,
          &:has(input:checked) {
            @apply rounded-md border-primary;
          }

          img {
            @apply object-contain aspect-1;
          }
        }
      }
      &:not(&--color) {
        img {
          @apply hidden;
        }
      }

      &--size {
        .field__buttons {
          @apply flex flex-wrap lg:grid lg:grid-cols-5;
        }

        .field__button {
          @apply basis-[60px] shrink grow-0;
					@media screen and (max-width:1023px) {
						flex-basis : calc(9% - 4px)
					}
					@media screen and (max-width:667px) {
						flex-basis : calc(11% - 4px)
					}

					@media screen and (max-width:500px) {
						flex-basis : calc(20% - 4px)
					}

          &.unavailable {
            @apply bg-[#eeeeee] border-[#eeeeee];
          }
          
          .field__button-text {
            @apply font-body text-base lg:text-[13px];
          }

          &:hover {
            .field__button-text {
              @apply border-primary border;
            }
          }
        }
      
        img {
          @apply hidden;
        }
      }

      &-label-wrapper {
        @apply items-start;

        button {
          @apply h-auto text-secondary font-heading;
        }

        @apply mb-[13px] text-[15px];
      }
      &-selected {
        @apply mb-0;
      }
    }
  }

  .field__button {
    @apply basis-full;
  }

  .field__buttons {
    &--tight {
      @apply gap-2;
    }
  }

}


/* Product Header
========================================================================== */

.product-header {
  // @apply px-lg;
  @apply max-lg:pb-6;

  &__top {
    @apply max-lg:mb-xs lg:mb-2xs;
  }
	&__bottom {
	}
	&__price {
		@apply text-[1.25rem] font-heading;
	}
  &__type {
    @apply font-body text-[13px] font-normal;
		.product-essentials__buy-stack & {
    	@apply lg:hidden;
    }
  }
  &__title {
    font-size: 1.25rem;
    line-height: 132%;
    letter-spacing: -0.6px;
    @apply font-heading;
    font-weight:500;
  }
	&__description {
		@apply hidden lg:block font-body mb-[25px] leading-[1.125];
	}
	&__breadcrumbs {
		@apply font-body text-[0.75rem];
	
		a {
			&:last-child {
				@apply text-dark
			}
		}
	}
	
}

.product-swatch-color-tooltip{
    visibility: hidden;
    text-align: center;
    position: absolute;
    z-index: 1;
    margin-bottom: 14px;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    max-width: none;
    height: 36px;
    background: rgb(255, 255, 255);
    color: rgb(51, 51, 51);
    letter-spacing: 0.025rem;
    border-radius: 50px;
    padding: 8px 16px;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 16px -4px;
    font-size: 0.75rem;
    font-weight: normal;
    bottom: 100%;
    white-space: nowrap;
}

label.field__button:hover .product-swatch-color-tooltip{
    visibility: visible !important;
}

@media screen and (max-width: 1024px) {

	.collection-title-shop-all {
		margin-bottom: 35px;
    	margin-top: 27px;
	}

	.button-desktop{
		display: none;
	}
	.button-mobile{
		display: block;
	}
}
@media screen and (min-width: 1024px) {

.button-mobile{
	display: none;
}
}


/* Reviews
========================================================================== */
:root {
  --reviews-star-color: #f1a307;
  --reviews-text-color: #381300;
}

.review-snippet {
	.ruk-rating-snippet {
		.ruk-rating-snippet-count {
			@apply font-body text-[10px] ml-1.5 -top-[1px] relative;
		}
		i {
			@apply text-[13px];
		}
	}
}

.ElementsWidget-prefix {
	.ElementsWidget {
		.R-TextHeading {
			font-family: var(--font-medium);
		}
		
		.R-TextBody {
			@apply font-body font-normal;
		}
	
		.item__slidersGroup {
			@apply font-normal;
		}
		
		.footer__reviewsLogo-container {
			img.R-PlatformLogo,
			img.R-PlatformIcon {
				@apply hidden;
			}	
		}

		.header__inner {
			@apply px-6 lg:px-8 lg:pt-5;
		}

		.ElementsWidget__search {
			.R-Field__input {
				&:focus {
					@apply focus:border-2 focus:border-black transition-all duration-200 ease-in-out;
				}
			}
		}

		.R-TabControls {
			.R-TabControls__item,
			.R-TabControls__item.isActive {
				@apply text-body;
			}
			.R-TabControls__item.isActive {
				@apply border-body;
			}
		}

		.R-Button {
			@apply font-body;

			&.R-Button--secondary {
				@apply bg-tertiary text-body;
			}
		}

		.R-PaginationControls {
			.R-PaginationControls__item {
				@apply text-body;

				&.isActive > .R-TextHeading {
					@apply text-body;
				}

				&.isActive {
					@apply border-b-body;
				}
			}
		}
	}
}


/* Product Meta
========================================================================== */
.product-meta {
	@apply gap-y-1;
  &__title {
    @apply text-primary text-[14px];
		font-family: var(--font-medium);
  }

  &__type {
    @apply text-dark font-body text-[12px];
		font-weight: 400;
  }

  &__color {
    @apply font-body text-dark text-[12px];
  }

  .reviews-snippet {
    @apply mb-1;
  }

  .product-item__prices {
    @apply mt-0;
  }
  .product-item__price {
    @apply font-body text-primary;
  }
}


/* Enunciation
========================================================================== */

.enunciation {
	@apply mt-4 font-body;

	&__translation,
	&__description {
		@apply text-dark;
	}
	&__translation {
		@apply mb-2.5;
	}
	&__button {
		@apply flex gap-x-sm mb-2.5;
		color: var(--color-primary);  /* #8f5733 */
	}
	&__pronunciation {
		@apply italic;
	}
}

.product-essentials__enunciation:not(:has(.enunciation)) {
	& + .product-essentials__horizontal-line {
		@apply hidden;
	}
}


/* Promo Motivator
========================================================================== */

.promo {
	@apply flex items-start gap-x-4 py-3;

	&__icon-wrapper {
		@apply shrink-0;
	}
	&__icon {
		@apply max-w-[48px];
	}
	&__content {
		@apply text-body text-sm leading-none;
	}
	&__headline {
		@apply font-heading text-[15px] inline-block my-0 pb-1;
	}
	&__description {
		@apply font-body text-[13px] leading-tight;
	}
}


/* Fit Guide
========================================================================== */

.fit-guide {
  @apply pb-[18px];

	.grid {
		@apply mb-8;
	}

	&__block-title {
		@apply text-base;
	}

	&__title {
		@apply font-heading text-[18px] lg:text-[15px] max-lg:col-span-1;
	}

	.progress-bar {
		@apply mt-1 gap-y-1 max-lg:col-span-4;
	}

	progress {
		@apply bg-gray-200 rounded-full lg:h-3 h-[22px] w-full;
	
		&::-webkit-progress-bar {
			@apply lg:h-3 h-[22px] rounded-full bg-gray-200 w-full;
		}
	
		&::-webkit-progress-value {
			@apply rounded-full bg-gradient-to-r from-[#dbdb84]  to-[#9dc876];
		}
	
		&::-moz-progress-bar {
			@apply rounded-full bg-gradient-to-r from-[#dbdb84]  to-[#9dc876];
		}
	}
}

.fit-guide__heading {
  &:has(~ .fit-guide) {
    @apply font-heading text-[20px] pb-xl;
  }
}

.product-essentials__fit-guide .fit-guide { 
	@apply mb-8;
}
.product-essentials__fit-guide h4{
	padding-bottom: 1rem !important;
}
.product-essentials__fit-guide .accordion-control{	
	padding-top: 10px;
    padding-bottom: 1rem;
}

/* Collection
========================================================================== */

.template-collection {
  #MainContent {
    background-color: var(--color-light);
  }
}
.product-finder__product-grid {
	.product-item  {
		&__meta {
			padding-top: .9375rem;
			display: flex;
			flex-direction: column;
		}
		&__title {

			font-size: .8125rem;

			line-height: 108%;

			letter-spacing: -0.13px;

			font-weight: 500;
			margin-bottom: 3px;
		}
		&__subtitle {
			margin-bottom: 3px;
		}
		&__type {
			margin-bottom: 3px;
		}
		&__subtitle, &__type, &__prices, &__price {

			font-size: .8125rem;

			line-height: 120%;

			letter-spacing: -0.13px;

		}
	}
}
.collection-grid {
	.review-snippet {
		margin-top: 3px;
		.ruk-rating-snippet {
			i {
				@apply text-[15px];
			}
		}
	}
	.product-item {
		&__swatches {
			margin-top: .9375rem;
		}
		&__meta {
			padding-top: .9375rem;
			display: flex;
			flex-direction: column;
		}
		&__title {

			font-size: .8125rem;

			line-height: 108%;

			letter-spacing: -0.13px;

			font-weight: 500;
			margin-bottom: 3px;
		}
		&__subtitle {
			margin-bottom: 3px;
		}
		&__type {
			margin-bottom: 3px;
		}
		&__subtitle, &__type, &__prices, &__price {

			font-size: .8125rem;

			line-height: 120%;

			letter-spacing: -0.13px;

		}

	}

}
.collection {

  &.container { @apply max-md:px-0; }

	&__header {
		font-family: var(--font-);
		letter-spacing: -0.5px;
	}

	&-header__scroll-nav {
		@apply max-lg:max-w-screen-sm;
	}

	&-product-grid {
		@apply max-lg:order-2;
	}

	&__sidebar {
		@apply border-gray-200 gap-y-8 lg:sticky top-0 overflow-y-hidden h-auto max-h-screen max-lg:order-1; 

		@media only screen and (min-width: 1025px) {
			position: sticky;
			transform: translateX(0%);
	    inset: auto;
	    z-index: 10!important;
	    background:transparent;
	    box-shadow:none;
	    top:var(--header-offset);
	    max-height:calc(100dvh - var(--header-offset));
			overflow-y: none;
	  }

	  &-header {
	  	background:#f2ede7;
	  	@apply text-sm gap-sm lg:hidden;
	  	svg {
	  		width:1rem;
	  	}
	  	.label {
	  		@apply gap-sm px-md py-lg;
	  	}
	  	span {
	  		@apply gap-sm
	  	}
	  	&--clear {
	  		button {
	  			@apply p-md;
	  		}
	  	}
	  	&--filtered {
		  	button {
		  		@apply gap-sm px-md py-lg w-1/2;
		  		color:#832407;
		  		&.apply {
		  			background:#832407; 
		  			color:var(--color-light);
		  		}
		  	}
	  	}
	  }
	  &-body { 
		position: static;
    	overflow-x: hidden;
	  	@apply px-md py-lg lg:px-0 text-sm flex-col gap-sm overflow-y-auto lg:overflow-hidden;
		//height: 100% !important;
		@media only screen and (min-width: 1024px) {}
	  }
	}

	.field__toggle {
	}
	
	&-filters {

		border-color:#F0EAE4;

		&__accordion {
			@apply border-t;
			&-header {
				@apply pl-0 py-lg;
			}
			&[open] {
				summary {
					@apply pb-sm;
				}
			}

			&-title {
				@apply text-sm text-body ;
				font-family: var(--font-medium);
			}

			&-content {
				@apply mt-2;
			}

			&-icon {
				transition:transform 300ms ease;
				.collection-filters__accordion:not([open]) & {
					transform:rotate(180deg);
				}
			}
		}

		&__content {
			@apply gap-y-[15px] pb-6;

			.field__buttons {
				@apply gap-1 max-lg:flex max-lg:flex-wrap lg:grid-cols-4;

				.field__button {
					@apply grow shrink-0 max-lg:max-w-[50px]; 
				}
			}
		}

		& > .field {
			@apply lg:pt-5 pb-6;
		}

		.field {
			@apply mb-0;

			&__colors {
				@apply gap-x-2 gap-y-4;
			}

			&__color {
				@apply p-0;
			}
		}
	}

	&-grid {
		scroll-margin-top: 200px;

    .content-item {
      @apply max-lg:m-4;
    }
	}
}


/* Content Carousel
========================================================================== */

.section--content-carousel {
	.review-snippet {
		margin-top: 3px;
		.ruk-rating-snippet {
			i {
				@apply text-[15px];
			}
		}
	}
	.product-item {
		&__images {
			margin-bottom: 0;
		}
		&__meta {
			padding: .9375rem 0 0 0;
			display: flex;
			flex-direction: column;
		}
		&__title {
			font-size: .8125rem;
			line-height: 108%;
			letter-spacing: -0.13px;
			font-weight: 500;
			margin-bottom: 3px;
		}
		&__type {
			margin-bottom: 3px;
		}
		&__subtitle {
			margin-bottom: 3px;
		}
		&__subtitle, &__type, &__prices, &__price {
			font-size: .8125rem;
			line-height: 120%;
			letter-spacing: -0.13px;
			&:empty {
					display: none
			}
		}
	}
	.tabs {
		@apply mb-lg;
	
			button,a {
				border: none;
				font-family: var(--font-medium);
				cursor: pointer;
				background-color: none;
				font-size: 16px;
		  margin: 0;
			}
		}
	.type-section {
		@apply m-0 text-primary;
	}
	.product-header {
		@apply mb-lg;
		&__titles {
			display:flex;
			align-items:center;
			gap:1rem;
		}
		&__title {

		}
		&__subtitle {
			color:#797979;
			font-size: 12px;
		}
	}
	.review-item {
		&__product {
			font-size: .8125rem;
			line-height: 108%;
			letter-spacing: -.13px;
			padding-bottom: 12px;
			text-overflow: ellipsis;
			text-wrap: nowrap;
		}
		&__type {
			letter-spacing: -.13px;
			line-height: 120%;
		}
	}
	.content-item {
		&--carousel-header {
			@apply flex-wrap mb-lg lg:mb-xl;
      			
			.content-item__content {
				@apply gap-y-4 justify-center;
				margin-top: .5rem;
				h1 {
					// font-family: var(--font-bold);
					font-family: var(--font-olukai-regular);
				}
			}
		}
		&--review {
			.content-item {
				&__review {
					font-size: .9375rem;
			    line-height: 120%;
					letter-spacing : -0.45px;
			    display: block;
			    display: -webkit-box;
			    overflow: hidden;
			    text-overflow: ellipsis;
			    -webkit-box-orient: vertical;
			    -webkit-line-clamp: 2;
			    margin-bottom: 0;
				}
				&__author {
					line-height: 108%;
					font-size: .8125rem;
					margin-top: 13px;
					letter-spacing:-0.13px ;
					font-family: var(--font-medium);
				}
				&__button-set {
					margin-top: 0;
					padding-top: 28px;
					line-height: 14.95px;
					.button--link {
						height: auto;
						line-height: 14.95px;
					}
				}
			}
		}
	}
  .tabs-wrapper {
    .content-item {
      &--carousel-header {
        @apply lg:mb-0;
        @media only screen and (min-width: 1024px) {
          .content-item__text-stack {
            &:has(.type-headline) {
              top: 50%;
              transform: translateY(50%);
            }
          }
        }
      }
    }
  }
}


/* Article List
========================================================================== */

.section--article-list {
	.section__blocks {
		@apply gap-10;
	}

	& > .type-section {
		@apply mt-0 mb-8 text-primary;
	}

	
}


/* Article Item
========================================================================== */

.content-item--article {
	@apply flex-row;
	
	.type-item {
		@apply mb-1.5 text-primary leading-[1.4];
		font-size: clamp(0.875rem, 3.5vw, 1.125rem);
		font-family: var(--font-medium);
		font-weight: 400;
		@media only screen and (min-width: 1024px) {
			font-family: var(--font-bold);
			font-weight: 700;
		}
	}

	.content-item {
		&__meta {
			@apply text-[11px] mb-2.5 pt-1;
			color: #77706c;

			& > *:not(:last-child) {
        @apply mr-3 pr-3 border-dark border-r;
      }
		}

		&__excerpt {
			@apply text-[15px] text-primary hidden lg:block;
		}

		&__content {
			@apply px-5 justify-start lg:justify-between;
		}

		&__media-container {
			@apply max-w-[40%] lg:min-w-[50%];
		}

		&__media {
			@apply aspect-1 xl:aspect-[4/3] w-full;
		}

		&__button-set {
			@apply mt-2;

			@media only screen and (max-width: 1023px) {
				.button {
					padding: 0;
					background-color: transparent;
					border: none;
					text-decoration: underline;
					height: auto;
					font-size: 12px;
					letter-spacing: normal;
				}
				.button:hover {
					background-color: transparent;
					color: var(--color-secondary);
				}
				.button:before {
					background-color: transparent;
					color: var(--color-secondary);
				}
			}	
		}
	}

	&.content-item__article--vertical {
		.content-item__meta {
			@apply uppercase;
		}
		.content-item__button-set {
			@apply hidden;
		}
		.content-item__media-container {
			@apply mb-4;
		}
		.content-item__text-stack {
			@apply gap-y-2;
		}
		.content-item__media {
			@apply aspect-[3/2];
		}
	}
}

/* Article
========================================================================== */
.article {
	@apply lg:pb-8;

  &__header {
    @apply relative z-10 max-w-none w-full lg:max-w-[52rem] lg:mt-[-5rem] lg:px-0 px-6 pt-16 border-t-8 border-primary;

    & > * {
      @apply lg:max-w-2xl lg:mx-auto;
    }

		.article__title {
			font-family: var(--font-medium);
			@apply max-lg:text-2xl;
		}
  }

  &__content,
	&__footer {
   @apply lg:max-w-2xl lg:mx-auto max-lg:px-6;
  }

	&__footer {
		@apply relative z-10 max-w-none w-full lg:max-w-[52rem] pb-8 lg:px-0 px-4 border-b-8 border-primary;
  }

	&__link {
		font-family: var(--font-medium);
		@apply mt-4;
	}

	&__content {
   @apply font-body text-[15px] leading-[1.57rem] -tracking-[0.12px];
		
	 	h1,h2,h3,h4,h5,h6 {
			@apply font-body m-0;
		}

		h1 {
			// font-family: var(--font-medium);
			font-family: var(--font-olukai-regular);
		}

		h2 {
			@apply text-[1.5rem];
		}

		h3 {
			@apply text-[1.17rem];
		}

		p {
			@apply m-0;
		}
  }

  &__info {
    @apply justify-start mb-5 flex-wrap flex gap-y-2;

    & > * {
      @apply text-[10px] font-body tracking-widest uppercase;

      &:not(:last-child) {
        @apply mr-3 pr-3 border-dark border-r;
      }
    }
  }

	.social-sharing {
		@apply mb-4;
	}

	&__category,
	&__tag {
		@apply opacity-40;
	}

	&-video {
		&__eyebrow {
			@apply mb-8;
		}

		&__category,
		&__date {
			@apply text-[11px] text-[#736b67] uppercase;
		}
		
		&__title {
			@apply hidden; // some olukai articles place the title inside of the content, so hiding this one
		}

		&__rte {
			h1:first-of-type,
			h2:first-of-type,
			h3:first-of-type  {
				@apply mt-0;
			}
			p {
				@apply font-body text-[15px] leading-[1.45455] -tracking-[0.12px];
			}
		}
	}
}

.template-article {
	#MainContent {
		@apply bg-light;

		.article {
			&__title {
				font-family: var(--font-olukai-bold);
			}
		}
	}
}


/* Review Item
========================================================================== */

.review-item {
	&__product {
		@apply text-base pb-1.5 text-primary font-normal;
		font-family: var(--font-medium);
	}

	&__type {
		@apply font-normal font-body text-[13px] text-[#021327];
	}
}

.content-item--review {

	.content-item {
		&__header {
			@apply mb-6;
		}

		&__media {
			@apply relative;

			&:hover {
				.content-item__media-button {
					@apply opacity-100 transform duration-200 ease-in;
				}
			}
			&:not(:hover) {
				.content-item__media-button {
					@apply opacity-0 transform duration-200 ease-in;
				}
			}
			.content-item__media {
				@apply aspect-1;
			}
		}

		&__media-button {
			@apply absolute inset-0 bg-black/25 z-10 opacity-0;
		}

		&__button-media {
			@apply top-1/2 left-1/2 transform -translate-x-1/2;
		}

		&__review {
			@apply pt-6 mb-3 text-primary;
			font-family: var(--font-regular);
		}

		&__author {
			@apply uppercase text-[#021327];	
		}

		&__button-set {
			@apply mt-5;
		}
	}
}


/* Subnav
========================================================================== */

.subnav {
	@apply overflow-hidden gap-x-4 bg-white border-t border-b border-gray-200 py-2 px-2.5 select-none;

  &__link {
    @apply border border-gray-200 rounded text-primary block text-sm py-1.5 px-4 transition-all duration-300 ease-in-out text-center;
  }

  &__item {
    @apply opacity-100 pointer-events-none transition-opacity duration-[250ms];

    [id^='swiper-wrapper'] & {
      @apply opacity-100 pointer-events-auto;
    }

		&:hover,
    &.active {
      .subnav__link {
        @apply bg-[#e8f6f4] border-[#e8f6f4];
      }
    }

    &:not(:first-child) {
      .subnav__link {
        @apply ml-1.5;
      }
    }
  }
  
  .swiper-slide {
    @apply w-auto;
  }
}

/* Newsletter
========================================================================== */

.section--newsletter {
	background: #EDFED1;
	// background: no-repeat url("//cdn.shopify.com/s/files/1/0015/9229/5523/t/405/assets/footer-background-mobile.png?v=179990698131244657351623712044"),linear-gradient(#eeece1,#bfded9);
	
	// @media screen and (min-width: 1024px) {
	// 	background: left top no-repeat url("//cdn.shopify.com/s/files/1/0015/9229/5523/t/405/assets/footer-background-desktop-left.png?v=165808726298001106391623712048"),right top no-repeat url("//cdn.shopify.com/s/files/1/0015/9229/5523/t/405/assets/footer-background-desktop-right.png?v=150963604540380143991623712048"),-webkit-gradient(linear,left top,left bottom,from(#bfded9),to(#eeece1));
	// }
	
	.newsletter__title {
		@apply mt-0 max-lg:px-2.5 mb-2.5;
	}

	.newsletter__subtitle {
		@apply max-lg:px-2.5 mb-10 lg:mb-8;
	}
}


/* Login Modal
========================================================================== */
.modal--account {
	
	@apply w-80 rounded-lg p-8;
	background-image: -webkit-gradient(linear,left top,left bottom,from(#bbdcd6),to(#dce6dd));
  background-image: linear-gradient(#bbdcd6,#dce6dd);
  
  &:before {
    background-image: url("//cdn.shopify.com/s/files/1/0015/9229/5523/t/405/assets/footer-background-mobile.png?v=179990698131244657351623712044");
    background-repeat: no-repeat;
    background-size: cover;
    content: "";
    height: 155px;
    left: 0;
    opacity: .4;
    position: absolute;
    top: 0;
    width: 194px;
		z-index: -1;
	}

	.account-modal {
		&__title {
			@apply text-primary text-[24px] mt-4 mb-2;
		}
	
		&__subtitle {
			@apply text-primary text-[13px] mb-4;
		}
	
		&__button {
			@apply w-full mt-2;
		}

		&__link {
			@apply flex justify-center text-[13px] text-primary mt-4 cursor-pointer;
		}
	}
}


/* Wishlist
========================================================================== */
.wishlist {
	&__title {
		font-family: var(--font-medium);
		@apply text-[1.125rem] mb-1;
	}
	&__item-count {
		span {
			@apply text-[11px] font-body text-[#736b67];
		}
	}
	&__items {
		@apply divide-y divide-[#e1d5c5] space-y-5;
	}

	&__message {
		@apply text-[0.875rem] font-body text-[#736b67];
		&--highlight {
			@apply text-secondary;
		}
	}

	&__footer {
		@apply pt-8 mt-5 border-t border-[#e1d5c5] block lg:hidden;
	}

	&__atc {
		&--desktop {
			@apply hidden lg:inline-flex;
		}
	}
}

.wishlist-item {
	@apply gap-y-1;

	img {
		@apply bg-tertiary;
	}

  &__title {
    @apply text-primary text-[14px] mb-1;
		font-family: var(--font-medium);
  }

  &__type,
	&__variant {
    @apply text-[#797979] leading-4 font-body text-[12px] mb-1;
		font-weight: 400;
  }

  &__color {
    @apply font-body text-dark text-[12px];
  }

  &__price {
    @apply font-body text-[14px] text-primary;
  }

	&__buttons {
		.wishlist-item__remove {
			@apply flex justify-end;
		}
		.wishlist-item__atc {
			@apply text-secondary font-body text-[13px] flex gap-1 underline;

			svg {
				@apply w-5 h-5;
			}
		}
	}
}

template + .wishlist-item {
	@apply pt-0;
}

.wishlist-toggle {
	@apply pl-3;

	.icon {
		@apply stroke-secondary;

		&.active {
			@apply fill-secondary;
		}
	}
}




/* Quick Add
========================================================================== */
.modal--quickadd {
  
  padding: 30px 20px 20px;
	
	.quickadd {
		&__title {
			margin:-12px 0 12px;
			color: var(--color-body);
			font-size:18px;
		}
	}

	.field__buttons {
		@apply gap-2 max-lg:flex max-lg:flex-wrap;

		.field__button {
			@apply grow shrink-0 max-lg:max-w-[50px];
			&[disabled]{
				background:#EEE;
				cursor:not-allowed;
				pointer-events:none;
			}
		}
	}
  .product-meta {
    .product-item__price {
      font-size: 14px;
    }
    &__footer {
      @apply flex flex-row-reverse items-center gap-2 mt-2 justify-end;
    }
  }
}


/* Footer
========================================================================== */

#shopify-section-footer {
	.menu-item {
		@apply lg:mb-4;

		&:not(:last-child) {
			@apply border-r-0 max-lg:border-b max-lg:border-[#c8c4ba];
		}

		&__menu {
			& > ul {
				& > li {
					& > a {
						@apply font-heading;
						font-size: 1.25rem;
						line-height: 132%;
						letter-spacing: -0.6px;
						font-weight:500;
						@media (max-width:1023px){
							font-size: 1rem;
							line-height: 126%;
							letter-spacing: -0.48px;
							@apply px-0 py-6;

						}
					}
				}
			}

			ul:has(.group.menu-active) {
				@apply max-lg:mb-4;
			}

			a {
				@apply px-4 lg:pr-4 lg:pl-0 whitespace-nowrap;			
				font-size: .9375rem;
				line-height: 120%;
				letter-spacing: -0.45px;
				@media (max-width:1023px){
					@apply px-0

				}
			}
		}
	}

	.content-item {
		@apply hidden lg:block;
	}

	.footer__credits {
		@apply mt-0 flex-col lg:flex-row lg:justify-between gap-2  text-[#8e867f] opacity-100 border-[#c8c4ba] max-lg:pb-16;
		padding:.9375rem 0 0 0;
		@media (max-width:1023px){
			padding:1.5rem 0 0;

		}
		.footer__credit {
			span {
				font-size: .9375rem;
				line-height: 120%;
				letter-spacing: -0.45px;
				font-family: var(--font-regular);
				font-weight: 400;
				color: rgba(0, 0, 0, 1);
				@media (max-width:1023px){
					color: rgba(145, 145, 145, 1);
					font-size: 13px;
					margin-bottom: 1.5rem;
					display: block;
				}

			}
			&--links {
				@apply flex flex-wrap gap-y-1 divide-x divide-[#8e867f] items-center;
				@media (max-width:1023px){
					max-width: 90%;
					column-gap: 4px;
				}
			}

			&--link {
				@apply first:pl-0 px-4 underline leading-none text-[13px] font-body font-normal lg:font-normal;
				color: rgba(0, 0, 0, 1);
				line-height: 14.04px;
				letter-spacing: -0.13px;
				@media (max-width:1023px){
					color: rgba(145, 145, 145, 1);
					border-right: 1px solid #919191 !important;
					border-left: none;
					padding-left: 0;
					padding-right: 4px;
					&:last-child {
						border-right: none !important;
					}

				}
			}
		}
	}
}

/* Login
========================================================================== */

.template-login, .template-register {
	section {
		.type-headline {
			font-family: var(--font-bold);
		}
	}
}

.login,
.register,
.reset,
.recover {
	&__form {
		@apply lg:p-12 bg-white p-8;
	}
}

.login,
.register,
.reset {
	&.bg-image {
		@apply  grid grid-cols-1 lg:grid-cols-2 min-h-main;
	}

	&__content {
		@apply container col-span-full flex flex-col p-4 w-full lg:max-w-lg justify-self-end lg:col-[2_/_3] lg:p-[30px];
	}

	&__title {
		font-family: var(--font-medium);
		@apply mb-6;
	}

	&__subtitle {
		@apply text-[11px] font-body mb-6;
	}

	&__link {
		@apply text-secondary text-[13px] font-body;
	}

	&__names {
		@apply  flex max-lg:flex-wrap lg:gap-2;
	}

	.field__checkbox {
		input {
			@apply border-[#ded1be] min-w-[1rem] min-h-[1rem];
		}
		input + span {
			@apply text-[11px] font-body;
	
			& > span {
				@apply font-heading;
			}
		}
	}
}

.recover,
.reset {
	&__title {
		font-family: var(--font-medium);
		@apply mb-2;
	}

	&__subtitle {
		@apply text-[11px] font-body mb-6;
	}

	&__link {
		font-family: var(--font-medium);
		@apply tracking-wide text-body;

		&-container {
			@apply mt-6;
		}
	}
}

.create-account-link {
	@apply hidden;
}

.form-success {
	@apply text-[11px] text-primary font-body mb-6;
}

.form-errors {
	@apply text-[11px] text-secondary font-body mb-2;
}

#MainContent {
	&:has(.login:not(.bg-image), .register:not(.bg-image), .recover:not(.bg-image), .reset:not(.bg-image)) {
		@apply bg-light;
	}
}

.callout {
	@apply  p-8 lg:p-12 bg-tertiary;

	&__title {
		font-family: var(--font-medium);
		@apply mb-2;
	}

	&__subtitle {
		@apply text-[11px] font-body;
	}

	&__button {
		@apply w-full;
	}
}

.rewards {
	background-image: url('https://olukai.com/cdn/shop/files/blue_gradient-desktop_8a259fbc-b8ea-4ad7-a47d-382321e90ab9.png?v=**********');
	@apply flex flex-col text-white p-6;

	&:has(.field) {
		.rewards__header {
			@apply border-b-2 border-white/10 pb-8 mb-8;
		}
	}

	&__header {
		@apply flex-col gap-y-3 gap-x-6 text-white bg-no-repeat bg-cover bg-center pb-4 lg:flex-row;
	}

	&__logo {
		@apply w-full max-w-[150px];
	}

	&__message {
		@apply text-[11px] font-body lg:mt-1 w-full;
	}

	.field {
		label {
			@apply items-start;
		}

		input {
			@apply bg-[#042c4b] border-primary;

			&:checked {
				@apply bg-[#C5B982];

				&:after {
					@apply border-body;
				}
			}
		}
	}
}
	
/* Account Page
========================================================================== */

.section--account {
	.section__header {
		@apply border-[#e1d5c5];
	}
	.account-profiles {
		legend {
			@apply border-[#e1d5c5];
		}
		.account-profile {
			@apply border-[#e1d5c5];
			&__name {
				@apply text-[15px] font-body my-0;
			}
		}
	}
}


/* Stockist
========================================================================== */

.template-page {
	#stockist-widget {
		.stockist-search-form {
			@apply mb-8;
		}

		.stockist-horizontal {
			@apply flex flex-col-reverse;

			.stockist-map {
				@apply relative left-[inherit];
			}

			.stockist-result-list {
				ul {
					@apply relative flex flex-wrap justify-start gap-x-4;
				}

				.stockist-result {
					@apply border-b border-x-0 border-t-0 border-solid border-transparent w-full lg:w-[calc(25%_-_1rem)];
				

					> div {
						@apply mt-0;
					}

					.stockist-result-phone {
						@apply mt-2.5;
					}

					.stockist-result-distance,
					.stockist-result-address,
					.stockist-result-details,
					.stockist-result-directions-link,
					.stockist-result-phone a,
					.stockist-result-website a {
						
						@apply font-body text-sm text-body tracking-normal leading-[22px] capitalize text-left;
					}

					.stockist-result-name {
						@apply relative font-heading text-[17px] mb-1 text-left tracking-normal text-body;
					}

					&.stockist-selected,
					&:hover {
						@apply border-[#b33c0d];
					}
				}

				.stockist-list-result {
					cursor: pointer;

					.stockist-result-distance {
						span {
							@apply text-[#b33c0d]
						}
					}
				}
			}

			.stockist-side-panel {
				@apply w-full mt-4 lg:mt-8;
			}
		}

		.gm-style-iw {
			.stockist-result {
				> div {
					@apply mt-0;
				}

				.stockist-result-name {
					@apply relative font-body text-[17px] mb-1 text-left tracking-normal text-body;
				}

				.stockist-result-phone {
					@apply mt-2.5;
				}

				.stockist-result-distance *,
				.stockist-result-address *,
				.stockist-result-details *,
				.stockist-result-directions-link *,
				.stockist-result-phone *,
				.stockist-result-website * {
					@apply relative font-body text-sm capitalize text-left tracking-normal leading-[22px] text-body;
				}
			}
		}

		.stockist-powered-by-link {
			@apply hidden;
		}

		.stockist-query-entry {
			@apply lg:max-w-7xl mx-auto;

			.stockist-search-field {
				@apply bg-light text-[#555] placeholder:text-[#555];

				height: 52px;
				line-height: 1.428571429;
				vertical-align: middle;
				border-radius: 0;
				transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
				box-shadow: none;
				border-radius: 0px;
				border: 1px solid #b5b5b5;
				padding: 25px 15px;
				font-size: 14px;

				&:focus {
					@apply border-2 border-black;
				}
			}

			.stockist-search-button {
				button {
					@apply relative flex items-center justify-center bg-[#b33c0c] font-heading text-[18px] m-0 rounded-none;

					height: 52px;
					line-height: 22px;
					width: 150px;
					letter-spacing: 1.8px;

					span {
						@apply hidden;
					}

					&:before {
						content: "SEARCH";
						position: absolute;							
					}
				}
			}
		}
	}
}

/* Filtered Content
========================================================================== */

.section--filtered-content {
	@apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
	[steps] {
		article {
			padding: 0 0 34px;
			margin: 28px 0 0;
			@media (min-width:1024px){
				padding: 0 0 6rem;
				margin: 50px 0 0;
			}
			&:first-child {
				margin: 0;
				border-bottom: 1px solid #e3e3e3;
			}
		}
	}
	article[result] {
    border-top: 1px solid #e3e3e3;
	}
	.grid-cols-2 {
		.field__image--horizontal {
			@media (max-width:1024px){
				grid-column: span 2 / span 2;
			}
		}
	}
	#FilteredContentSearch {
		@apply flex items-center flex-col;
		max-width: 530px;
    margin: 41px auto 60px;
    position: relative;
    input {
    	border: none;
	    border-bottom: 1px solid var(--color-body);
	    border-radius: 3px;
	    color: var(--color-body);
	    font-family: var(--font-body-family);
	    width: 100%;
	    font-size: 16px;
	    line-height: 18px;
	    letter-spacing: -.2px;
	    padding: 11px 11px 11px 30px;
	    background: 0 0;
	    @media (min-width:1024px){
	    	font-size: 14px;        
				padding-left: 40px;
	    }
    }
    .icon-search {
    	stroke-width: 3px;
	    position: absolute;
	    width: 18px;
			top: 50%;
	    right: 8px;
      transform: translateY(-50%);
	    @media (min-width:1024px){
	    	font-size: 14px;
	    	right: inherit;
	    	left: 8px;
	    }
    }
    > p {
	    font-size: 13px;
	    line-height: 18px;
	    color: #736b67;
	    margin: 30px 0 0;
    }
	}
	.search-results {
		top: 41px;
		width: 100%;
		max-height: 380px;
		height: auto;
		z-index: 10;
		padding: 0;
		background: #fff;
		border: 1px solid rgb(229, 231, 235);
		border-radius: 6px;
		.search-results__type-label {
			display: none;
		}
		.search-result-item {
			border-right: 0;
			border-left: 0;
			&:first-of-type {
				border-top: 0;
			}
			&:last-of-type {
				border-bottom: 0;
			}
		}
		.search-result-item__title {
			font-size: 15px;
	    line-height: 19px;
	    margin: 0 0 9px;
	    color: var(--color-body);
	    font-family: var(--font-body-family);
		}
		.search-result-item__type, .search-result-item__sku {
			display: block;
	    font-size: 13px;
	    line-height: 15px;
	    color: #736b67;
	    margin: 0 0 10px;
		}
		.search-result-item__price {
			font-size: 15px;
	    line-height: 18px;
	    color: #3a1603;
	    letter-spacing: 0;
	    font-family: var(--font-heading-family);
		}
	}
}

/* Newsletter
========================================================================== */

.section--newsletter {
	@apply text-center;
	.mini-form {
		padding: 20px 30px;
		max-width: 360px;
	}

	summary, input {
		max-width: 300px;
	}

	.section__mini-forms {
		@apply flex justify-center divide-white max-lg:divide-y max-lg:flex-col max-lg:items-center lg:divide-x;
	}

	.newsletter__error {
        display: none;
        color: var(--color-secondary);
        font-family: GTA-Medium,Arial,Helvetica,sans-serif;
        font-size: 12px;
        margin-bottom: 2px;
        margin-top: -2px;
        padding: 12px 20px 15px;
        text-align: center;
    }
}



/* Sticky Stuff
========================================================================== */
body {
  &.template-collection {
    &:has(.header-bar__search-field label[for="header_search"].active) {
      .section--sticky {
        &.active {
          @media only screen and (max-width: 767px) {
            top:53px;
          }
        }
      }
    }
    .section--sticky {
      &.active {
        @media only screen and (max-width: 767px) {
          top:53px;
        }
      }
    }
  }
}
.template-product{
	.section--sticky {
		&.active {
			@media only screen and (max-width: 767px) {
				top:var(--header-offset);
			}
		}
	}
}
.section--sticky {
	opacity:0;
	&.active {
		opacity:1;
		top:var(--header-bottom);
		@apply shadow-lg;
		&.lg\:top-main {
			@media only screen and (min-width: 1024px) {
				top:var(--header-bottom);
		  }
		}
		// &.bottom-0 {
		// 	top:var(--header-offset);
		// }
		// @media only screen and (min-width: 1024px) {
		// 	top:var(--header-bottom);
	  	// }
	}
	.section__blocks {
		@apply gap-4;
	}
	.product-header__bottom {
		margin-bottom:0;
	}
	.button {
		// min-width:200px;		
		// min-height: 45px;
	}
	.section__block--gate:not(:has(.button)){
		display:none;
	}

  transition:transform 300ms ease;
  will-change: transform;
	
	&.active {
	  transform:translateY(0%)!important;
	} 

	&.bottom-0 {
		@media only screen and (max-width: 1023px) {
	  	transform:translateY(100%);
	  }
	}
	&.top-main {
		@media only screen and (max-width: 1023px) {
	  	transform:translateY(-100%);
	  }
	}

	&.lg\:bottom-0 {
		@media only screen and (min-width: 1024px) {
	  	transform:translateY(100%);
	  }
	}
	&.lg\:top-main {
		@media only screen and (min-width: 1024px) {
	  	transform:translateY(-100%);
	  }
	}
	
}




/* Geolocation
========================================================================== */
.geolocation {
	&__header {
		@apply p-xl border-b;
	}

	&__footer {
		@apply p-xl;
	}

	article {
		@apply p-xl bg-light border-b;
	}

	.field:last-child {
		margin-bottom:0;
	}

	&__site_list_item {
		a {
			@apply flex items-center rounded-sm border mb-2 p-sm bg-white;
			&:hover {
				border-color:#CCC;
			}
			li:last-child & {
				margin-bottom:0;
			}
			img {
				width:4rem;
				height:2rem;
				object-fit:contain;
			}
		}
	}

	&__site_switcher {
		@apply p-xl gap-md;
	}
	&__site {
		@apply flex flex-col gap-xl items-center rounded-sm border mb-2 p-sm bg-white;
		&:hover {
			border-color:#CCC;
		}
	}
}

.slider-cart {
  @apply lg:max-w-[500px] lg:w-full;
	&__recs {
	  @apply pb-[33px];

		&-title {
			@apply px-md pb-md my-0;
			color:#042c4b;
			font-size:14px;
			font-family:GTA-Medium,Arial,Helvetica,sans-serif;
			font-weight:500;
		}
	}
	.offer {
		color:#042c4b;
    &.offer--combinable {
      @apply pb-[18px];

      .progress {
        &--fill {
          background: linear-gradient(to right, var(--progress-fill-color), transparent) !important;
        }
        &--last-tier {
          background: var(--progress-fill-color) !important;
        }
      }
    }
	}
	&__payment-widget {
		padding: 0;
		margin: 0;
	}
  &-swiper {
    .swiper-wrapper {
      @apply px-md; 
    }
  }
  .cart__item-wrapper {
    @apply px-0 mb-[33px];
  }
}

.offer-summary {
  @apply flex flex-col-reverse gap-2 w-full;
  &__item {
    @apply flex items-center justify-between gap-2;
  }
  &__header {
    @apply flex items-center gap-2;
  }
  &__icon {
    @apply w-[13px] h-[13px] p-[3px] rounded-full;
  }
  &__title {
    font-family: var(--font-body-family);
    font-size: 12px;
  }
  &__value {
    font-family: var(--font-heading-family);
    font-size: 13px;
  }
}


.progress {
  &__threshold-markers {
    @apply absolute w-full top-1/2 -translate-y-1/2 left-0 flex justify-between items-start;
  }
  &__threshold-marker {
    @apply flex flex-col items-center justify-center w-8 relative; 
    &-icon {
      @apply w-8 h-8 mb-1 p-[5px] rounded-full;
    }
    &-label {
      @apply text-[9px] leading-none text-center w-[50px] overflow-visible absolute -bottom-[18px];
    }
  }
  &--fill {
    @apply pb-1.5 relative;
    // background: linear-gradient(
    //   to right,
    //   var(--progress-fill-color) 50%,
    //   transparent
    //   ) !important;

    // &:after {
    //   content: "";
    //   width: 100%;
    //   height: 6px;
    //   opacity: 1;
    //   position: absolute;
    //   right: 0;
    //   background: linear-gradient(to right, transparent, var(--progress-bg-color));
    // }
  }
}

.cart-item {
  @apply border-b bg-white px-md py-md;
  .review-snippet {
    @apply order-last; 
  }
  &__info {
    @apply justify-start;
  }
  &__info-container {
    &:has(.cart-item__badge) {
      .review-snippet {
        @apply pb-1;
      }
    }
   
  }
  &__info-start {
     @apply pb-2;
    &:has(.review-snippet) {
      @apply pb-0;
    }
  }
  &__prices {
    @apply flex flex-col-reverse items-end gap-1;
  }
  &__compare-at-price {
    @apply text-[#919191] mr-[5px] mb-0 leading-none text-[13px];
  }
  &__price_with_compare {
    @apply text-primary;
  }
  &__price {
    @apply mb-0 leading-none text-[13px];
  }
  &__info-end {
    @apply justify-end relative w-full;
    &:has(.cart-item__quantity) {
      @apply justify-between mt-[12px];
    }
    &:not(:has(.cart-item__quantity)) {
      @apply mt-0;
    }
  }
  &__quantity {
    @apply bg-white rounded-[50px] w-auto px-4 py-1.5 border mb-0;

    input {
      font-size: 13px !important;
      @apply font-heading;
    }
  }
  &__image {
    @apply h-24 w-24 bg-tertiary rounded-none;
  }
  &__titles {
    @apply flex flex-col;
  }
  &__title-link {
    font-family: var(--font-medium);
    @apply text-[12px] text-primary;
  }
  &__line-item {
    @apply text-[12px] text-primary;
  }
  &__badges {
    @apply flex justify-start;
  }
  &__badge {
    @apply p-2 text-[12px] leading-none font-heading;
  }
}

.section--account {
	.field {
		&__chip {
			&:has(input:checked) {
				span {
					@apply font-heading;
					font-weight:normal;
				}
			}
		}
	}
}

.section--page .rte {
	font-size:16px;
	line-height:1.5;
}

.content-item__text-stack.same_height_review {
    min-height: 92.27px;
}
@media only screen and (min-width:1025px) and (max-width:1365px) {
	.custom-review-alignment{
		align-items: flex-start;
		flex-direction: column;
	}
}

.field__button .sr-only-hide[type="checkbox"]:focus + span, .field__button .sr-only-hide[type="checkbox"]:focus + span{
	outline: #381300 solid 2px;
}

.field__color .sr-only-hide[type="checkbox"]:focus + span, .field__color .sr-only-hide[type="checkbox"]:focus + span{
	outline: #381300 solid 2px;
}

.field__color .sr-only-hide[type="checkbox"],.field__button .sr-only-hide[type="checkbox"]{
	width: 0px;
	height: 0px;
	padding: 0px;
	margin: 0px;
	opacity: 0;
	border:none;
	z-index:-1 !important;
	appearance: none !important;
}

.template-pdp-design-update {
	.form {
	  &.more-options {
      .shopify-payment-button__button,
      .accelerated-checkout-button {
        display: none !important;
      }

      .accelerated-checkout-button {
        display: none  !important;
      }
      shop-pay-wallet-button {
        display: none !important;
      }
      shopify-google-pay-button {
        display: none !important;
      }
      shopify-apple-pay-button {
        display: none !important;
      }
	  }
	  &.dynamic-form-pdp {
      .shopify-payment-button__more-options {
        display: none !important;
      }
	  }

	  .shopify-payment-button__more-options {
      @apply text-[13px] text-secondary font-medium mt-0 pt-3;
	  }
	}
}
  
  
  /* Tabs
  ========================================================================== */
  
  .tabs, .tabs .field__toggle {
    @apply rounded-full bg-tertiary flex; 
    
    span,
    a,
    button {
      @apply text-base text-body font-heading font-medium leading-none rounded-full;
    }
    span,
    button.active,
    label input:checked+.toggle__label {
      @apply items-center flex bg-pop py-3.5 px-6 border-none text-base text-body;
    }
    a,
    a:not(.active),button:not(.active),
    label input:not(:checked)+.toggle__label {
      @apply inline py-3.5 px-6 border-none text-base text-body;
    }
    label {
      span.toggle__label--unselected {
      @apply hidden;
      }
    }
  }


.page-careers__embed{
    box-shadow: rgba(56, 49, 47, 0.05) 2px 2px 0px 2px;
    background: #f6f6f4;
    border-radius: 16px;
    padding: 32px;
		div#BambooHR-Footer {
			justify-content: flex-end;
			display: flex;
			align-items: center;
			padding-top: 20px;
	}
		h2 {
				margin: 0;
				font-size: 26px;
				border-bottom: 1px solid rgb(198, 194, 191, .75);
				padding-bottom: 12px;
		}
		ul.BambooHR-ATS-Department-List>li {
			padding-top: 24px;
			padding-bottom: 24px;
			border-bottom: 1px solid rgba(198,194,191,.75);
			display: flex;
			flex-direction: row-reverse;
			align-items: flex-start;
			justify-content: flex-end;
			@media screen and (max-width:991px) {
				flex-wrap : wrap;
				flex-direction : column-reverse;
				gap : 10px
			}
			.BambooHR-ATS-Department-Header {
				flex: 0 0 30%;
				background: url('data:image/svg+xml,<svg class="fabric-fbhpyj-svg" fill="%2338312f" viewBox="0 0 640 512" xmlns="http://www.w3.org/2000/svg"><path xmlns="http://www.w3.org/2000/svg" d="M224 48a80 80 0 1 1 0 160 80 80 0 1 1 0-160zm0 208A128 128 0 1 0 224 0a128 128 0 1 0 0 256zm-45.7 96h91.4c65.7 0 120.1 48.7 129 112H49.3c8.9-63.3 63.3-112 129-112zm0-48C79.8 304 0 383.8 0 482.3C0 498.7 13.3 512 29.7 512H418.3c16.4 0 29.7-13.3 29.7-29.7C448 383.8 368.2 304 269.7 304H178.3zm431 208c17 0 30.7-13.8 30.7-30.7C640 392.2 567.8 320 478.7 320H417.3c-4.4 0-8.8 .2-13.2 .5c46.4 38.6 75.9 96.7 75.9 161.8c0 10.8-2.8 20.9-7.6 29.7H609.3zM432 256c61.9 0 112-50.1 112-112s-50.1-112-112-112c-24.8 0-47.7 8.1-66.3 21.7C377.4 75.9 384 101.2 384 128c0 35.6-11.6 68.5-31.3 95.1C373 243.4 401 256 432 256z"></path></svg>');
				background-repeat: no-repeat;
				background-size: 17px;
				padding-left: 22px;
				background-position: center left;
				@media screen and (max-width:991px) {
					flex: 0 0 100%;
				}
		}
		ul.BambooHR-ATS-Jobs-List {
			flex: 0 0 70%;
			display: flex;
			row-gap: 10px;
			flex-direction: column;
			@media screen and (max-width:991px) {
				flex: 0 0 100%;
			}
			li {
				display: flex;
				align-items: flex-start;
				@media screen and (max-width:991px) {
					flex-direction: column;
					gap: 10px;
				}
				a {
					flex: 0 0 65%;
					padding-right: 30px;
					font-weight: 600;
					color: #145288;
					&:hover {
						text-decoration: underline;
					}
				}			
				span.BambooHR-ATS-Location {
					flex: 0 0 35%;
					background: url('data:image/svg+xml,<svg class="fabric-fbhpyj-svg" fill="%2338312f" viewBox="0 0 384 512" xmlns="http://www.w3.org/2000/svg"><path xmlns="http://www.w3.org/2000/svg" d="M336 192c0-79.5-64.5-144-144-144S48 112.5 48 192c0 12.4 4.5 31.6 15.3 57.2c10.5 24.8 25.4 52.2 42.5 79.9c28.5 46.2 61.5 90.8 86.2 122.6c24.8-31.8 57.8-76.4 86.2-122.6c17.1-27.7 32-55.1 42.5-79.9C331.5 223.6 336 204.4 336 192zm48 0c0 87.4-117 243-168.3 307.2c-12.3 15.3-35.1 15.3-47.4 0C117 435 0 279.4 0 192C0 86 86 0 192 0S384 86 384 192zm-160 0a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zm-112 0a80 80 0 1 1 160 0 80 80 0 1 1 -160 0z"></path></svg>');
					background-repeat: no-repeat;
					background-size: 16px;
					padding-left: 20px;
					background-position: center left;
				}
		}
		}
	}
	
	
}

@media screen and (min-width : 1024px) and (max-width:1100px) {
	footer#shopify-section-footer {
		.lg\:gap-3xl {
			gap: .25rem;
	}
	.tapcart-container {
		padding: 0;
		.tapcart-button {
			width: 170px;
			padding: 0 15px;
			font-size: .75rem;
		}
	}
	} 
}



/* Segment Tabs
========================================================================== */

.variant-tabs {

  &__nav {
    @apply justify-evenly mb-6;
  }

  &__tab {
    @apply text-center justify-center flex-1 whitespace-nowrap text-ellipsis;
  }

}

.product-form__option-label-wrapper {
  @apply items-center mb-2.5;

  .product-form__option-selected {
    @apply m-0;
  }

  .button {
    @apply h-auto;
  }
}