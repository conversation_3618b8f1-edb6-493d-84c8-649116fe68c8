{%- capture checkout_header -%}
  <a href="/" class="checkout__logo">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="129.814"
      height="30"
      viewBox="0 0 607.1 125.02">
      <defs>
        <style>
          .cls-1 {
            fill: #001630;
          }
        </style>
      </defs>
      <g id="Layer_2" data-name="Layer 2">
        <g id="Layer_1-2" data-name="Layer 1"><rect
            class="cls-1"
            x="253.15"
            y="2.25"
            width="29.3"
            height="120.19" /><path class="cls-1" d="M183.3,0c-35.61,0-60.4,25.84-60.4,62.5S147.69,125,183.3,125c35.9,0,61-25.84,61-62.49S219.2,0,183.3,0Zm0,97.2c-18,0-30.8-14.27-30.8-34.55s12.77-34.86,30.8-34.86c18.47,0,31.39,14.43,31.39,34.86S201.77,97.2,183.3,97.2Z" /><path class="cls-1" d="M323,87.89v-53H293.72V95.7c0,18.33,9.76,28.84,25.09,28.84,8.56,0,15.32-3.75,17.87-9.61L345,95.7c-4.06,2.85-8.27,4.35-11.87,4.35C328.27,100.05,323,98,323,87.89Z" /><path class="cls-1" d="M536.94,44.5A33.53,33.53,0,0,0,512.61,34c-21.19,0-38.46,19.83-38.46,45.22s15.92,45.37,35.3,45.37c7.36,0,13.52-3.75,16.23-9.61l8.86-19.23a26.57,26.57,0,0,1-13.37,4.35c-9.77,0-17.43-9.61-17.43-21.93,0-10.67,7.21-18.93,16.38-18.93s16.82,8.41,16.82,18v45.22h29.3V34.85h-29.3Z" /><rect
            class="cls-1"
            x="577.81"
            y="34.85"
            width="29.3"
            height="87.59" /><rect
            class="cls-1"
            x="577.81"
            y="2.25"
            width="29.3"
            height="24.49" /><polygon class="cls-1" points="486.31 2.25 452.21 2.25 417.51 59.64 417.51 2.25 388.21 2.25 388.21 122.44 417.51 122.44 417.51 65.5 446.65 122.44 480.15 122.44 446.95 61.59 486.31 2.25" /><rect
            class="cls-1"
            x="347.35"
            y="34.85"
            width="29.3"
            height="87.59" /><path class="cls-1" d="M46,108.9c-18.42,0-32.24-21.43-24.44-45.17C26.66,49.38,35.87,35.39,39.59,29c2.83-4.78,1.24-7.09,1.24-10.47,0-2.83,1.77-4.07,4.6-4.07h8.15c3.9,0,4.43,3,3,5.13-2.31,3-6.56,3.73-9.57,8.69C43.13,34.84,37.82,48,35.69,60c-2.83,15,3.19,28.69,13.64,29.58,11.69.89,17.36-11.52,17.36-25,0-6.55-2.13-14.35-4.07-19.49-.18-.35,0-.52.17-.35a35.43,35.43,0,0,1,11,11,44.91,44.91,0,0,1,6.73,19.13c0,.17-.18.35-.36.17-1.77-2.3-8-9.56-7.79-5.31C73.42,88.35,64.74,108.9,46,108.9ZM46.85,125C78,125,98.93,100.58,92.39,67.1l-6.2-31.54C82.12,15,74,2.26,52.17,2.26H41.53C19.75,2.26,10.89,15,7,35.56L1.15,67.1C-5.23,101.47,15.67,125,46.85,125Z" /></g>
      </g>
    </svg>
  </a>

  <div class="checkout-breadcrumbs breadcrumb-container">
    <div class="breadcrumb-container__wrap">
      {{ breadcrumb }}
    </div>
  </div>
{%- endcapture -%}
{%- capture icon -%}
  {%- comment -%}don't adjust whitespace{%- endcomment -%}
  <svg
    width="20"
    height="19"
    xmlns="http://www.w3.org/2000/svg"
    class="order-summary-toggle__icon">
    <path d="M17.178 13.088H5.453c-.454 0-.91-.364-.91-.818L3.727 1.818H0V0h4.544c.455 0 .91.364.91.818l.09 1.272h13.45c.274 0 .547.09.73.364.18.182.27.454.18.727l-1.817 9.18c-.09.455-.455.728-.91.728zM6.27 11.27h10.09l1.454-7.362H5.634l.637 7.362zm.092 7.715c1.004 0 1.818-.813 1.818-1.817s-.814-1.818-1.818-1.818-1.818.814-1.818 1.818.814 1.817 1.818 1.817zm9.18 0c1.004 0 1.817-.813 1.817-1.817s-.814-1.818-1.818-1.818-1.818.814-1.818 1.818.814 1.817 1.818 1.817z" />
  </svg>
{%- endcapture -%}
{%- capture new_icon -%}
  {%- render 'checkout-olukai-icon-bag', fill: '#042C4B' -%}
{%- endcapture -%}
{%- capture total_span -%}
  <span class="order-summary__emphasis total-recap__final-price skeleton-while-loading"></span>
{%- endcapture -%}
{%- capture new_total_span -%}
  <span class="order-summary__currency-code">{{ checkout.currency }}</span>
  <span class="order-summary__emphasis total-recap__final-price skeleton-while-loading"></span>
{%- endcapture -%}
<!doctype html>
<!--[if IE 9]> <html class="ie9 no-js supports-no-cookies {{ checkout_html_classes }}" lang="{{ locale }}" dir="{{ direction }}"> <![endif]-->
<!--[if (gt IE 9)|!(IE)]><!-->
  <html class="no-js supports-no-cookies {{ checkout_html_classes }}"
  lang="{{ locale }}"
  dir="{{ direction }}"> <!--<![endif]-->
  <head>
    {% if settings.google_optimize_id != blank %}
      <!-- Anti-flicker snippet (recommended)  -->
      <style>
        .async-hide {
          opacity: 0 !important
        }
      </style>
      <script>
        (function(a, s, y, n, c, h, i, d, e) {
          s.className += ' ' + y;
          h.start = 1 * new Date;
          h.end = i = function() {
            s.className = s.className.replace(RegExp(' ?' + y), '')
          };
          (a[n] = a[n] || []).hide = h;
          setTimeout(function() {
            i();
            h.end = null
          }, c);
          h.timeout = c;
        })(window, document.documentElement, 'async-hide', 'dataLayer', 4000, {'{{ settings.google_optimize_id }}': true});
      </script>

      <script type="text/javascript">
        /**
        * Please ensure a cookie named "optVal" is set with a random value between 1-10
                * This should be set from the server to ensure no issues from ITP rules or similar
                */
                (function() {
                    function optGetCookie(cname) {
                        var name = cname + "=";
                        var decodedCookie = decodeURIComponent(document.cookie);
                        var ca = decodedCookie.split(';');
                        for (var i = 0; i < ca.length; i++) {
                            var c = ca[i];
                            while (c.charAt(0) == ' ') {
                                c = c.substring(1);
                            }
                            if (c.indexOf(name) == 0) {
                                return c.substring(name.length, c.length);
                            }
                        }
                        return "";
                    }
                    var optSetCrossDomainCookie = function(name, value, days) {
                        var expires;
                        if (days) {
                            var date = new Date();
                            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                            expires = "; expires=" +
                                date.toGMTString();
                        } else {
                            expires = "";
                        }
                        document.cookie = name + "=" + value + expires + "; path=/; domain={{ request.host | replace: 'www.', '' }}";
        };
                    var retrieve = optGetCookie("optVal");
                    if (!retrieve) {
                        var optVal = Math.floor(Math.random() * 10) + 1;
                        optSetCrossDomainCookie("optVal", optVal, 1000);
                    }
                })();
      </script>
      <script src="https://www.googleoptimize.com/optimize.js?id={{ settings.google_optimize_id }}"></script>
    {% endif %}

    <!-- Google Tag Manager -->
    <script>
      (function(w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({'gtm.start': new Date().getTime(), event: 'gtm.js'});
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer'
            ? '&l=' + l
            : '';
        j.async = true;
        j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'dataLayer','{{ settings.gtm_container_id }}');
    </script>
    <!-- End Google Tag Manager -->

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="theme-color" content="{{ settings.color_primary }}">
    <link rel="canonical" href="{{ canonical_url }}">
    <title>{{ page_title }}</title>
    {% comment %} https://fonts.adobe.com/fonts/franklin-gothic-urw {% endcomment %}
    <link rel="stylesheet" href="https://use.typekit.net/tkv5ooi.css">

    <style>
      @font-face {
        font-family: 'GTA-Regular';
        src: url("{{ 'GT-America-Standard-Regular.woff2' | asset_url }}") format('woff2'), url("{{ 'GT-America-Standard-Regular.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: normal;
      }

      @font-face {
        font-family: 'GTA-Medium';
        src: url("{{ 'GT-America-Standard-Medium.woff2' | asset_url }}") format('woff2'), url("{{ 'GT-America-Standard-Medium.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: normal;
      }

      @font-face {
        font-family: 'GTA-Bold';
        src: url("{{ 'GT-America-Standard-Bold.woff2' | asset_url }}") format('woff2'), url("{{ 'GT-America-Standard-Bold.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: normal;
      }

      @font-face {
        font-family: 'GTA-Compressed-Bold';
        src: url("{{ 'GT-America-Compressed-Bold.woff2' | asset_url }}") format('woff2'), url("{{ 'GT-America-Compressed-Bold.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: normal;
      }

      @font-face {
        font-family: 'GTA-Condensed-Bold';
        src: url("{{ 'GT-America-Condensed-Bold.woff2' | asset_url }}") format('woff2'), url("{{ 'GT-America-Condensed-Bold.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: normal;
      }

      @font-face {
        font-family: 'Moret';
        src: url("{{ 'moret-1.woff2' | asset_url }}") format('woff2'), url("{{ 'moret-1.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: 400;
      }

      @font-face {
        font-family: 'Moret';
        src: url("{{ 'moret-2.woff2' | asset_url }}") format('woff2'), url("{{ 'moret-2.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: 700;
      }

      @font-face {
        font-family: 'Moret';
        src: url("{{ 'moret-3.woff2' | asset_url }}") format('woff2'), url("{{ 'moret-3.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: 800;
      }

      @font-face {
        font-family: 'Olukai-Regular';
        src: url("{{ 'Olukai_Set_v1.0-Regular.woff2' | asset_url }}") format('woff2'), url("{{ 'Olukai_Set_v1.0-Regular.woff' | asset_url }}") format('woff'), url("{{ 'Olukai_Set_v1.0-Regular.otf' | asset_url }}") format('otf');
        font-style: normal;
        font-weight: normal;
      }
      @font-face {
        font-family: 'Olukai-Bold';
        src: url("{{ 'Olukai_Set_v1.0-Bold.woff2' | asset_url }}") format('woff2'), url("{{ 'Olukai_Set_v1.0-Bold.woff' | asset_url }}") format('woff'), url("{{ 'Olukai_Set_v1.0-Bold.otf' | asset_url }}") format('otf');
        font-style: normal;
        font-weight: normal;
      }

      @font-face {
        font-family: 'Olukai-Regular-Italic';
        src: url("{{ 'Olukai_Set_v1.0-Regular_Italic.woff2' | asset_url }}") format('woff2'), url("{{ 'Olukai_Set_v1.0-Regular_Italic.woff' | asset_url }}") format('woff'), url("{{ 'Olukai_Set_v1.0-Regular_Italic.otf' | asset_url }}") format('otf');
        font-style: normal;
        font-weight: normal;
      }

      @font-face {
        font-family: 'Olukai-Bold-Italic';
        src: url("{{ 'Olukai_Set_v1.0-Bold_Italic.woff2' | asset_url }}") format('woff2'), url("{{ 'Olukai_Set_v1.0-Bold_Italic.woff' | asset_url }}") format('woff'), url("{{ 'Olukai_Set_v1.0-Bold_Italic.otf' | asset_url }}") format('otf');
        font-style: normal;
        font-weight: normal;
      }

      .shipping_address_notice {
        padding: 0.5285714286em;
        margin-bottom: 10px;
      }
      .section.section--shipping-address .section__content .fieldset > .field {
        display: none;
      }
      label.checkbox__label b,
      .fieldset-description label.checkbox__label,
      .logged-in-customer-newsletter label.checkbox__label {
        font-size: 11px;
        color: #736B67;
        font-family: 'GTA-Bold';
      }
      label.checkbox__label p,
      label.checkbox__label a {
        font-size: 11px;
        color: #444444
      }
      {% if settings.enable_discount_code_checkout == false %}
        .checkout-step--contact_information #order-summary .fieldset {
          display: none;
        }
      {% endif %}
    </style>

    {% if false %}
      {{ content_for_header }}
    {% endif %}
    {{ content_for_header | replace: "<body onload='document._boomrl();'>", "<bodx onload='document._boomrl();'>" }}
    {{ checkout_stylesheets }}

    {% render 'checkout-olukai-css' %}

    {{ checkout_scripts }}
    <script
      src="https://code.jquery.com/jquery-3.7.0.min.js"
      integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g="
      crossorigin="anonymous"></script>

    {% render 'checkout-exponea' %}
    {% if settings.gtm_container_id != blank %}
      {% render 'checkout-analyzify-gtm-checkout' %}
    {% endif %}

    {%- if settings.outbrain_pixel_enabled -%}
      {% render 'checkout-outbrain-pixel' %}
    {%- endif -%}
  </head>
  <body class="template-checkout">


    <script>
      if (window.Shopify.Checkout.hasOwnProperty('isOrderStatusPage') && window.location.hash.includes('#gift=')) {
        document.body.classList.add('gift-wrap')
      }
    </script>
    {% comment %} Code for Canella Media Pixel {% endcomment %}
    {% if settings.canella_media_pixel %}
      {{ settings.canella_pixel_code }}
    {% endif %}
    <noscript>
      <iframe
        src="https://www.googletagmanager.com/ns.html?id={{ settings.gtm_container_id }}"
        height="0"
        width="0"
        style="display:none;visibility:hidden"
        title="Google tag manager"></iframe>
    </noscript>
    <!-- End Google Tag Manager (noscript) -->

    {{ skip_to_content_link }}

    <div class="content checkout__content" data-content>
      <div class="wrap">
        <header class="checkout__header checkout__header--mobile">
          {{ checkout_header }}
        </header>

        {{ order_summary_toggle | replace: icon, new_icon | replace: total_span, new_total_span }}

        <div class="main" role="main">
          <div class="checkout__main-inner">
            <header class="checkout__header checkout__header--desktop">
              {{ checkout_header }}

            </header>

            <div class="main__header">
              {% comment %} Hide or Show Express Checkout based on cart total {% endcomment %}
              {%- if settings.fraud_filter_express_checkout_enabled -%}
                {%- assign totalCartValue = checkout.total_price | money_without_currency | remove: ',' | plus: 0 -%}
                {%- if totalCartValue < 999 -%}
                  {{ alternative_payment_methods }}
                {%- endif -%}
              {%- else -%}
                {{ alternative_payment_methods }}
              {%- endif -%}
            </div>

            <div class="main__content">
              {% for line_item in checkout.line_items %}
                <div class="lineItem"></div>
                {% if line_item.product.type == "Gift Card" %}

                  {% assign show = true %}
                {% endif %}

              {% endfor %}
              {{ content_for_layout }}
            </div>

            {%- if settings.checkout_sticky_cta_enabled -%}
              {% render 'checkout-olukai.sticky-ctas' %}
            {%- endif -%}

            {% render 'checkout-olukai-footer' %}
          </div>
        </div>
        <div class="sidebar" role="complementary">
          <div class="checkout__sidebar-inner">
            <div class="sidebar__content">
              {%- capture sidebar_header -%}
                <header class="sidebar__cart-header">
                  <span class="sidebar__cart-header-title">Your Bag</span>
                </header>
              {%- endcapture -%}
              {{ content_for_order_summary | replace: '<h2 class="visually-hidden-if-js">Order summary</h2>', sidebar_header }}
            </div>
          </div>
        </div>
      </div>
    </div>

    {% render 'checkout-exponea-tracking' %}

    {% if settings.estimated_shipping_enabled %}
      {% render 'checkout-shipping-dates' %}
    {% endif %}
    {% if settings.address_validation_enable %}
      {% render 'checkout-address-validation' %}
    {% endif %}

    {% if settings.profile_split_enable %}
      {% render 'checkout-profilesplit' %}
    {% endif %}

    <script src="{{ 'vendors.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'runtime.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'track.js' | asset_url }}" defer="defer"></script>
    {% render 'accepts-marketing-trigger' %}
    {% render 'order-placed-trigger' %}
    {{ tracking_code }}



    {% comment %} Hide or Show Express Checkout based on cart total {% endcomment %}
    {%- if settings.fraud_filter_express_checkout_enabled -%}
      <script>
        // Wait for DOM element to be available
        function waitForElm(selector) {

// console.log('>>>>>>>>>>> WAITING');
          return new Promise(resolve => {
            if (document.querySelector(selector)) { // console.log('>>>>>>>>>>> RESOLVED');
              return resolve(document.querySelector(selector));
            }

            const observer = new MutationObserver(mutations => {
              if (document.querySelector(selector)) { // console.log('>>>>>>>>>>> OBSERVED');
                resolve(document.querySelector(selector));
                observer.disconnect();
              }
            });

            observer.observe(document.body, {
              childList: true,
              subtree: true
            });
          });
        }

        waitForElm('[data-payment-subform="required"]').then((elm) => {

// console.log('>>>>>>>>>>> FOUND:', elm);
          const totalPrice = '{{ checkout.total_price | amount_with_apostrophe_separator }}' / 100;

// if total price is greater than 1000 remove elements with data-gateway-name names
          if (totalPrice > 1000) {
            const paypal = document.querySelector('[data-gateway-name="paypal"]');
            const amazon = document.querySelector('[data-gateway-name="amazon_pay"]');
            const afterpay = document.querySelector('[data-gateway-name="offsite_v2"]');

            if (paypal) {
              paypal.remove();
            }

            if (amazon) {
              amazon.remove();
            }

            if (afterpay) {
              afterpay.remove();
            }
            return;
          }
          return elm;
        });
      </script>
    {%- endif -%}

    {% if settings.checkout_giftcard_modal_enabled == 'vip' %}
      <script>
        window.gift_validation_endpoint = "{{ settings.giftcard_validation_endpoint }}";
      </script>
      {% render 'checkout-giftcard-message' %}
      {% render 'checkout-others' %}
    {% else %}
      <script>
        window.gift_code_contains = {{ settings.giftcard_code_contains }};
        window.gift_code_starts = {{ settings.giftcard_code_starts }};
      </script>
      {% render 'checkout-standard' %}
    {% endif %}

    {%- render 'checkout-olukai.vip-redirect' -%}

    <script>

      window.giftOrder = {
        "customer": {{ checkout.customer.name | json }},
        "display": {{ settings.enable_gift_order | json }},
        "checkoutToken": {{ order.id | md5 | json }},
        "orderName": {{ checkout.order_name | json }},
        "orderStatusUrl": {{ order.order_status_url | json }}
      }

      $(document).on('page:load page:change', function() {
        (function() {
          document.documentElement.classList.add(`checkout-step--${
            Shopify.Checkout.step
          }`)
        })()

        if ($('.shipping_address_notice')[0]) {} else {
          if (Shopify.Checkout.step == "contact_information") {
            {% if settings.twitter_pixel_enable %}

              {{ settings.twitter_pixel_code }}

            {% endif %}

          }
        }
      });

      document.addEventListener('page:load', handleRedirects('page:load'))
      document.addEventListener('page:change', handleRedirects('page:change'))

      function handleRedirects(page) {

// console.log('handleRedirects - page', page)
        const storedRedirects = sessionStorage.getItem('redirects')
        if (storedRedirects) {
          const redirects = JSON.parse(storedRedirects)

// console.log('handleRedirects - redirects', redirects)
          redirects.forEach(redirect => {
            if (redirect.conditions && redirect.conditions.includes('document.location')) {
              const pathMatch = redirect.conditions.match(/'([^']+)'/)
              const pathname = pathMatch
                ? pathMatch[1]
                : null

              if (pathname) {
                const links = document.querySelectorAll(`a[href="${pathname}"]`)
                links.forEach(link => {
                  link.setAttribute('href', redirect.destination)
                })
              }
            }
          })
        }
      }
    </script>

    {% render 'checkout-olukai-gift-order' %}
    {%- if settings.cybba_pixel_enable -%}
      {{ settings.cybba_pixel_code }}
    {%- endif -%}

    {%- if settings.mntn_pixel_enable -%}
      {{ settings.mntn_pixel_code }}
    {%- endif -%}
  </body>

</html>