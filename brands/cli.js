#!/usr/bin/env node
require('dotenv').config({override:true})
const controller = require('./controller')
const args = process.argv.slice(2)
const inquirer = require('inquirer');
const config = require('./brands.config.js');
const shopList = []
const cp = require('child_process')
const path = require('path')

for (const [brandName, value] of Object.entries(config)) { 
	const shops = value.shop_names

	if (Array.isArray(shops)) {
		shops.map((name) => {
			shopList.push(name)
		})
	}

	if (typeof shops === 'string') shopList.push(shops)
}


if (args.length > 0) {
	switch (args[0]) {
    case 'serve':
      const options = {stdio: "inherit", cwd: './theme'}

      console.log(process.env.SHOP_NAME);

      let spawn = cp.spawn(
        'shopify', 
        ['theme', 'dev', `--store=${process.env.SHOP_NAME}.myshopify.com`, `--theme-editor-sync`, '--path='], 
        options
      );
      console.log(spawn)


        
      break;
    case 'current':
      controller.getCurrentBrand().then(console.log)
      break;
		case 'shop':
			if (!!args[1]) {
				controller.setShop(args[1])
			} else {
				console.log('Pass in a Shopify store name.')
			}
			break;
	}
} else {
  setBrand()
}

function setBrand() {
	runPrompts().then(answers => {
    controller.setShop(answers.store)
	})
}


async function runPrompts() {
	const loggedIn = await controller.getShop()
	const shop = loggedIn.shop
	const questions = [
		{
			type: 'list',
			name: 'store',
			message: 'Which store would you like to login to?',
			choices: [...shopList]
		}
	]
	let answers = await inquirer.prompt(questions)

	return answers
}
