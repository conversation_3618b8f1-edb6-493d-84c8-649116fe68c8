// Start with shared tailwind config
const defaultConfig = require('../../utilities/tailwind.config.js')
const brandConfig = defaultConfig

// Use the one of the predefined spacing scales
brandConfig.theme.extend.spacing =  { 
  ...brandConfig.theme.extend.spacing,
  ...brandConfig.scales.perfectFourth
}

// Manually write your own Spacing Scale
// brandConfig.theme.extend.spacing =  { 
//   ...brandConfig.theme.extend.spacing,
//     'xs': '0.5rem',
//     'sm': '0.707rem',
//     'base': '1rem',
//     'lg': '1.414rem',
//     'xl': '1.999rem',
//     '2xl': '2.827rem',
//     '3xl': '3.998rem',
//     '4xl': '5.653rem',
//     '5xl': '7.993rem',
//     '6xl': '11.302rem',
//     '7xl': '15.981rem'
// }


// Use the one of the predefined spacing scales
brandConfig.theme.extend.fontSize =  { 
  ...brandConfig.theme.extend.fontSize,
  ...brandConfig.scales.perfectFourth
}

// Manually write your own Spacing Scale
// brandConfig.theme.extend.fontSize =  { 
//   ...brandConfig.theme.extend.fontSize,
//     'xs': '0.5rem',
//     'sm': '0.707rem',
//     'base': '1rem',
//     'lg': '1.414rem',
//     'xl': '1.999rem',
//     '2xl': '2.827rem',
//     '3xl': '3.998rem',
//     '4xl': '5.653rem',
//     '5xl': '7.993rem',
//     '6xl': '11.302rem',
//     '7xl': '15.981rem'
// }

module.exports = brandConfig
