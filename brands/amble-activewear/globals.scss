@import '../shared/globals.scss';



/* Color Guidelines
  ==========================================================================
  primary: Highest level user attention.
  secondary: Second highest level user attention.
  tertiary: Third highest level user attention.
  light: Most prominent light background color. Must be able to overlay on top of dark.
  dark: Most prominent dark background color. Must be able to overlay on top of light.
  pop: Usage examples are badges.
  highlight: Think about this as using a highlighter pen.
  body: Most common text color.
  header: Most common text color for headers.
*/

:root {

  --color-primary: #00426A;
  --color-secondary: #3E3D3D;
  --color-tertiary: #e4e4e4;
  --color-light: #fff;
  --color-dark: #00426A0;
  --color-pop: #00426A;
  --color-highlight: #00426A;
  --color-body: #00426A;

  --font-heading-weight: 400;
  --font-subheading-weight: 400;
  --font-body-weight: 400;


  /*

  --swiper-theme-color: #000;

  --header-height:76px;
  --preheader-height: 34px;
  --header-height-half:38px;
  --unscrolled-header-height: 100px;
  --scrolled-header-height: 76px;

  @media screen and (max-width: 1024px) {
    --header-height:100px;
    --preheader-height: 32px;
    --header-height-half: 34px;
    --unscrolled-header-height: 100px;
    --scrolled-header-height: 68px;
  }

  --font-body-weight: 400;
  --font-body-style: normal;
  --font-heading-weight: 700;
  --font-heading-style: normal;
  --font-subheading-weight: 600;
  --font-subheading-style: normal;


  --header-height:48px;

  */
}


.top-main {
  top:var(--header-offset)
}
@media screen and (min-width: 1025px) {
  .lg\:top-main {
    top:var(--header-offset);
  }
}

 

   /* Type Styles
========================================================================== 
 Guidelines
==========================================================================
  **Type Styles**

  primary: Primary Headline or Title type.
  secondary: Commonly used as Subtitle type, compliments Primary.
  tertiary: Third highest level user attention, smaller but stylistically similar to primary.

  **Type Layouts / Spacing**

  page: Most common text color.
  section: Most common text style for headers.
  article: Most common text style for headers.

*/

.type {

  &-hero {
    font-size: 3.583rem;
    line-height: 1;
    @apply font-heading;
    
    &.type--sm {
      font-size: 2.488rem;
    }
    &.type--lg {
      font-size: 5.16rem;
    }
  }
  &-headline {
    font-size: 34px;

    @apply font-heading;

    &.type--sm {
      font-size: 26px;
    }
    &.type--lg {
      font-size: 40px;
    }
  }
  &-subline {
    font-size: 18px;

    font-family: var(--font-medium);

    &.type--sm {
      font-size: 16px;
    }
    &.type--lg {
      font-size: 22px;
    }
  }
  &-item {
    font-size: 16px;

    letter-spacing: normal;
    @apply font-heading;

    &.type--sm {
      font-size: 14px;
    }
    &.type--lg {
      font-size: 18px;
    }
  }
  
  &-section {
    font-size: 22px;

    @apply font-heading;

    &.type--sm {
      font-size: 18px;
    }
    &.type--lg {
      font-size: 26px;
    }
  }
  &-eyebrow {
    font-size: 12px;
    letter-spacing: 0.48px;
    @apply font-body;
 
    &.type--sm {
      font-size: 10px;
    }
    &.type--lg {
      font-size: 16px;
    }
  }
  &-body {
    font-size: 15px;
    letter-spacing: -0.12px;
    @apply font-body;

    &.type--sm {
      font-size: 14px;
    }
    &.type--lg {
      font-size: 18px;
    }
  }
  &-nav-link {

    @apply font-heading text-xs tracking-[0.25em] uppercase;
  }
  &-micro {
    font-size: 13px;
    letter-spacing: -.1px;
    line-height: 15px;
    border-width: 0;
    padding:0;
    margin:0;
    height: auto;
    @apply font-body capitalize;
  }
}

p {
  font-family: var(--font-body-family);
  font-weight: var(--font-body-weight);
  font-weight: var(--font-body-style);
  letter-spacing: -0.12px;
  
  strong {
    font-family: var(--font-bold);
  }
}

.rte a {
  @apply underline text-pop;
}

h1, .h1 {
  @apply headings text-2xl;
}

h2, .h2 { 
  @apply headings text-xl; 
}

h3, .h3 { 
  @apply headings text-lg;
}

h4, .h4 { 
  @apply headings text-base; 
}

h5, .h5 {  
  @apply headings text-base;  
}

h6, .h6 {  
  @apply headings text-base;  
}



/* Buttons and Controls
========================================================================== */

.button, .btn {
  @apply whitespace-nowrap items-center border rounded-[3px] tracking-[1.3px] inline-flex text-[14px] h-[39px] justify-center opacity-100 overflow-hidden py-0 px-[18px] relative uppercase cursor-pointer;
  font-family: var(--font-bold);
  &--primary { @apply  bg-primary border-primary text-white;}
  &--secondary { @apply border-primary bg-primary text-white; }
  &--tertiary { @apply border-primary bg-transparent text-primary; 
    &:before {
      @apply bg-primary;
    }
  }
  &--light { 
    @apply border-primary bg-transparent text-primary; 
    &:before {
      @apply bg-primary;
    }
  }
  &--dark { 
    @apply border-secondary bg-transparent text-secondary;
    &:before {
      @apply bg-secondary
    }
  }
  &--pop { 
    @apply bg-pop border-pop text-white; 
    &:before {
      @apply bg-pop;
    }
  }
  &--tertiary, &--light, &--dark, &--pop, &--highlight {
    &:before {
      border-radius: 50%;
      content: '';
      height: 400px;
      left: 50%;
      pointer-events: none;
      position: absolute;
      top: 50%;
      transform: translate(-50%, -50%) scale(0);
      transform-origin: center center;
      transition: transform 0.25s ease, opacity 0.25s ease-out, color 0.1s ease;
      width: 400px;
      opacity: 0;
      z-index: 1;
      will-change: transform, opacity, color;
    }
    &:hover , a:hover & {
      @apply text-white;
      z-index: 1; //required for hover background effect to work
      
      &:before {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease;
      }
    }
  }
  &--tertiary {
    &:hover, a:hover & {
      @apply border-primary;
    }
  }
  &--dark {
    &:hover, a:hover & {
      @apply text-primary;
      z-index: 1; //required for hover background effect to work
      
      &:before {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease;
      }
    }
  }
  &--light {
    &:hover, a:hover & {
      @apply text-white;
      z-index: 1; //required for hover background effect to work
      
      &:before {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease;
      }
    }
  }
  &--highlight { 
    @apply bg-transparent border-white text-white hover:text-primary; 
    a:hover & {
      @apply text-primary;
    }
    &:before {
      @apply bg-white;
    }
  }
  &--link { @apply font-heading tracking-[0.4px] uppercase bg-transparent text-[13px] text-primary p-0 border-0 underline h-auto; }
  &--w-icon { 
    @apply flex justify-center items-center border-primary bg-primary text-white gap-3;
    &:before {
      @apply bg-secondary;
    }
    .icon {
      @apply text-white;
    }
  }
  &--simple {
    // font-family: var(--font-medium);
		@apply tracking-wide text-primary text-sm gap-1 capitalize border-transparent bg-transparent px-0;
  }
  &--disabled,[disabled] {
    @apply bg-dark border-[#eee] text-white cursor-not-allowed opacity-20;
  }
  &--action { 
    @apply flex justify-center items-center border-black bg-transparent rounded-full text-[13px] tracking-normal capitalize font-normal h-[35px];
    font-family: var(--font-regular);
    &:before {
      @apply bg-secondary;
    }
    .icon {
      @apply text-black mr-0 w-4 h-3;
    }
  }
  &--large {
    height:56px; padding:0 50px; border-width:2px;
  }
  > span {
    @apply relative z-10;
  }

  &--micro-link {
    font-size: 13px;
    letter-spacing: -.1px;
    line-height: 15px;
    text-decoration: underline;
    border-width: 0;
    padding:0;
    margin:0;
    height: auto;
    @apply font-body capitalize;
  }

  &--primary-hover { 
    @apply bg-primary border-primary text-white;
    &:hover , a:hover & {
      @apply opacity-70;
    }
  }

  &--secondary-hover { 
    @apply bg-secondary border-secondary text-black;
    &:hover , a:hover & {
      @apply opacity-70;
    }
  }

  &--tertiary-hover { 
    @apply bg-tertiary border-tertiary text-black;
    &:hover , a:hover & {
      @apply opacity-70;
    }
  }

  @media screen and (max-width: 1023px) {
    &--tertiary{
      @apply bg-primary text-black;
      @apply text-white;
    }
  }
}

.wishlist-toggle {
	@apply pl-3;

	.icon {
		@apply stroke-primary;

		&.active {
			@apply fill-primary;
		}
	}
}

.product-item {
  &__quick-add {
    @media only screen and (max-width: 1023px) {
      margin-right: 0px !important;
      margin-top: 0px !important;
      left: auto !important;
      right: -15px !important;
      bottom: -22px !important;
      top: auto !important;
      min-width: auto !important;
      padding: 4px 6px !important;
      gap: 6px !important;
      z-index:20;
    }
  }
}

/* Forms
========================================================================== */

.field {
  @apply font-body;

  label {
    @apply text-[13px] text-body mb-1;
  }
  &__input,
  &__textarea,
  &__select {
    @apply border-tertiary text-sm rounded-none;
   
  }

  &__select {
    @apply h-12 py-0 text-sm appearance-none;

    background-position: right 12px center;
    background-image: url(https://olukai.com/cdn/shop/t/405/assets/select-icon.svg?v=43184182532122065261626389908);
    background-repeat: no-repeat;
    cursor: pointer;
  }

  &__toggle {
    @apply bg-tertiary;

    label {
      @apply inline-flex items-center mb-0 whitespace-nowrap;

      input {
        &:not(:checked) {
          & ~ .toggle__label {
            @apply cursor-pointer text-[12px] px-3 py-1 border bg-transparent border-transparent rounded-full text-[#999];
          }
        }

        &:checked {
          & ~ .toggle__label {
            @apply text-[12px] px-3 py-1 border bg-white border-primary text-body rounded-full;
          }
        }
      }
    }
  }

  &__image {
    @apply border rounded p-[15px] relative flex flex-col items-center justify-end border-tertiary;
    font-weight: 700;
    line-height: 18px;
    letter-spacing: 1.5px;
    input {
      @apply absolute inset-0 opacity-0;
    }
    img {
      width: 155px;
      max-width: 100%;
    }
    label {
      @apply flex items-center flex-col justify-end text-center uppercase text-[15px] gap-2.5;
    }
    &--horizontal {
      @apply justify-start items-start;
      label {
        @apply flex-row;
        img {
          width: 72px;
        }
      }
    }
    &:hover, &:focus {
      @apply border-primary;
      -webkit-box-shadow: 0 5px 6px rgba(0,0,0,.1);
      box-shadow: 0 5px 6px #0000001a;
    }
    &:has(input:checked) {
      @apply border-2 border-primary;
      background: #f0f8f8;
      -webkit-box-shadow: 0 5px 6px rgba(0,0,0,.1);
      box-shadow: 0 5px 6px #0000001a;
    }
  }
}

.field {
  
  input {
    &[type="checkbox"],
    &[type="radio"] {
      @apply h-[18px] w-[18px];

      & + span {
        @apply capitalize font-body text-[13px] border-tertiary;
      }
    }  

    &[type="radio"] {
      &:checked {
        @apply border-0 shadow-[inset_0_0_0_7px] shadow-black;
      }
    }
  }

  &__checkbox {

    span { 
      text-transform: capitalize;
    }
    input[type="checkbox"] {
      @apply relative rounded-[4px];

      &:checked {
        @apply border-0 bg-black shadow-none;
    
        &:after {
          content: "";
          display: block;
          width: 5px;
          height: 8px;
          border: solid white;
          border-width: 0 1.5px 1.5px 0;
          position: absolute;
          transform: rotate(45deg);
          top: 50%;
          left: 50%;
          translate: -50% -65%;
          background: transparent;
        }
      }
    }
  }
  
  &__color {
    &-swatch {
      @apply outline-offset-[1px] outline-[2px];
    }
    
    &:hover &-swatch {
      @apply outline-black;
    }

    span {
      font-family: var(--font-regular);
      font-weight: 400;
      @apply text-dark text-[12px];
    }

    &:has(input:checked),
    &:hover {
      span {
        @apply text-black;
      }
    }
  }

  &--chip {
    label {
      @apply mb-3;
    }
  }

  &__chip {
    @apply bg-tertiary rounded-[3px] px-5 py-2.5 font-heading flex-row items-center;

    span {
      font-family: var(--font-regular);
      font-weight: 400;
      font-size: 13px;
      text-transform: capitalize;
      line-height: normal;
    }

    &:has(button) {
      @apply pr-3;
    }

    button {
      @apply pl-2.5;
    }

    &-swatch {
      @apply outline-offset-0 outline-[4px];
    }
    
    &:hover {
      &-swatch {
        @apply outline-primary;
      }
    }
    
    &:has(input:checked) {
      @apply bg-primary text-white;
      span {
        @apply font-heading;
      }
    }
  }


  &__buttons {
    @apply gap-[9px];
  }

  &__button {
    @apply w-full h-full aspect-1 flex-auto;

    input[type="checkbox"]:checked,
    input[type="radio"]:checked {
      & + span {
        @apply border-black;
      }
    } 

    &-text {
      @apply rounded-[4px] uppercase font-body text-[13px] border [input:checked_~_&]:border-black [input:checked_~_&]:bg-black [input:checked_~_&]:text-white;
    }

    &:hover &-text {
      @apply border-black border-2 rounded-[4px];
    }

    &[disabled],&.disabled {
      @apply bg-dark text-white cursor-not-allowed opacity-20;
    }
  }

  &-plus-minus {
    @apply flex flex-row items-center;
    input {
      @apply w-12 p-1 text-center;
    }
    button {
      @apply w-8 h-8 flex items-center justify-center bg-white;
    }
    svg { 
      stroke-width: 3px;
    }
  }
}

progress {
  @apply bg-white rounded-lg h-2.5 w-full;

  &::-webkit-progress-bar {
    @apply h-2.5 rounded-lg bg-[#f8f8f9] w-full;
  }

  &::-webkit-progress-value {
    @apply rounded-lg bg-gradient-to-r from-[#808080] to-[#3e3e3e];
  }

  &::-moz-progress-bar {
    @apply rounded-lg bg-gradient-to-r from-[#808080] to-[#3e3e3e];
  }
}

.progress-bar {
  &__label {
    @apply font-body text-body text-[13px] font-normal tracking-wider uppercase;
  }
}

.dropdown-menu {
	@apply border border-solid rounded-sm text-current border-[#e4e4e4] text-sm;
	
	&__trigger {
		@apply rounded-[2px] h-8 text-sm appearance-none py-0 pl-3;

		.dropdown-menu__label {
			@apply text-xs text-[#797979];
		}

		.dropdown-menu__value {
			@apply text-xs pl-2;
		
		}
		
		&-icon {
			@apply ml-3 pl-3 pr-3 border-l text-xs;

			.icon {
				@apply w-3;
				fill: #797979;
				stroke: #797979;
				stroke-linecap: butt;
			}
		}
	}

	&__menu {
		@apply rounded-md mt-2 py-2;
		&-item {
			@apply text-xs px-4 py-1.5 hover:bg-light hover:bg-black hover:text-white;

			button {
				@apply text-left;
			}
		}
	}
}


/* Accordion
========================================================================== */

.list {
  .accordion {
    &:last-child {
      @apply border-b border-[#e4e4e4];
    } 
  }
}

.accordion {
  &:last-child {
    @apply border-b border-[#e4e4e4];
  }
  .accordion-title {
    @apply bg-transparent border border-[#e4e4e4] border-l-0 border-b-0 border-r-0 text-left cursor-pointer h-[60x] leading-[60px];
    font-family: var(--font-medium);
    span:first-child {}
    .accordion-control {
      @apply bg-transparent text-dark border-0;
      .icon {}
    }
  }
  .accordion-panel {
    @apply text-sm pb-4 px-0 border-0;
    > div {}
    ul {}
    p:last-child {}
  }
  &-control {
    &:first-of-type {
      display:block;
    }
    &:last-of-type {
      display:none;
    }
    [open] > summary & {
      &:first-of-type {
        display:none;
      }
      &:last-of-type {
        display:block;
      } 
    }
  }
}

/* Cart Upsell item
========================================================================== */
.upsell-item { 
  background-color:#FFFFFF;
  @apply text-sm p-[15px] border rounded-md flex-col gap-3;
  border-color: var(--color-secondary);
  &-skeleton {
    @apply h-[200px] w-full flex; 
  }
  &__content {
    @apply flex pb-1.5;
  }
  &__media {
    img {
      width: 4.5rem;
      height: 4.5rem;
    }
  }
  button[disabled] {
    background-color:rgba(100,100,100,0.35);
    color: var(--color-light);
  }
  &__header {
    @apply grid grid-cols-[1fr_auto] w-full; 
    * {
      font-weight:400;
    }

  }
  &__titles {
    @apply col-start-1 col-end-3 row-start-1 row-end-2;
  }
  &__prices {
    @apply col-start-2 col-end-3 row-start-1 row-end-2;
  }
  &__title {
    font-family: var(--font-medium);
    font-size:12px;
    margin:0;
  }
  .review-snippet {
    @apply pt-1;
  }
  &__subtitle,
  &__type {
    font-size:12px;
    margin:0px 0;
  }
  &__price {
    font-family: var(--font-medium);
    font-size:12px;
    line-height:1;
    margin:0;
  }
  &__body {
    @apply w-full flex flex-col pl-[12px] flex-1 gap-1;
  }
  
  &__actions {
    @apply flex justify-between w-full gap-[9px];

    label {
      width: 50%;
    }

    select {
      @apply border bg-transparent px-3 rounded-md text-[12px] leading-none w-full h-[30px] appearance-none;
      line-height:1;
      background-position: right 12px center;
      background-image: url("data:image/svg+xml,%3Csvg width='7' height='4' viewBox='0 0 7 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.5 0.5L3.5 3.5L6.5 0.5' stroke='black' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
      background-color: #FFFFFF;
      background-repeat: no-repeat;
      cursor: pointer;
      font-family: var(--font-regular);
    }

    button {
      @apply w-1/2 h-[30px] text-[12px];
      .icon {
        @apply hidden;
      }
    }
  }

  &__swatch {
    @apply w-[43px] h-[43px] rounded-md; 

    &es {
      @apply flex overflow-x-auto;

      &--visible {
        @apply flex gap-[4px] lg:gap-[5px];
      }

      &-container {
        @apply pb-sm relative;
      }
    }

    &-more {
      @apply flex pl-1.5 text-[12px] font-heading text-left;
      line-height: 1;
    }

    &.active {  
      @apply border border-black;
    }

  } 

}
.swiper-slide:has(.upsell-item[class*="sibling-item-"]) {
  display: none;
}

/* Micro Upsell
========================================================================== */
.micro-upsell {
  &__item {
    @apply flex-shrink-0 flex flex-col justify-between;

    .product-item {
      @apply w-full;
    }

    .product-item__actions button,
    .product-item__quick-add,
    .product-item:hover .product-item__quick-add {
      display: none;
    }

    .product-item__actions {
      opacity: 0.25;
      pointer-events: none;
    }
    &:has(input:checked) .product-item__actions {
      opacity: 1;
      pointer-events: auto;
    }

    .product-item__title-price-wrap:hover {
      @apply underline;
    }

    .field {
      @apply mb-0;
    }

    select {
      @apply border p-2 bg-transparent border-black rounded-md lg:mr-1.5 text-sm leading-none;
    }
  }

  &__separator {
    @apply flex-shrink-0;
  }
}

/* Tabs
========================================================================== */

.tabs {
  ul:not(.start) {
    @apply pl-0 flex justify-end w-full;
    li {
      @apply ml-0;
    }
  }

  ul.start {
    @apply space-x-4;
  }

  .tab-title {
    @apply border-0 rounded-none border-[#9e9f9e] border-b-2 bg-transparent  capitalize  text-[#9e9f9e] type-item type--lg;

    &:hover {
      @apply text-primary;
    }
    &:before {
      @apply bg-transparent;

      &:hover {
        @apply text-primary;
      }
    }
    &.active {
      @apply text-primary border-primary;

      &:before {
        @apply bg-transparent;
  
        &:hover {
          @apply text-primary;
        }
      }
    }
  }
  
  .tab-panel {
    > div {
      @apply py-6;
    }
  }
}

/* Pagination
========================================================================== */

.pagination--page {
  @apply bg-transparent;
  li {
    @apply flex justify-start items-start;
    a:not(.pp-control),
    button:not(.pp-control), > span {
      @apply block px-2 py-0.5 pb-1 mx-[5px];
    }
    [aria-current="page"], .active {
      @apply relative block;
      &:after {
        content: "";
        @apply block border-b-[2px] border-primary;
      }
    }
  }
}

.swiper-pagination,
.pagination {
  .pagination-bullet, .swiper-pagination-bullet {
    @apply bg-[#d7d2cb] w-[5px] h-[5px] rounded-full mx-2;
    &.active, &.swiper-pagination-bullet-active {
      @apply scale-[1.9];
    }
  }
}

.btn-control {
  &.swiper-button-prev,
  &.swiper-button-next {
    @apply bg-white rounded-full h-10 w-10 shadow-md text-inherit flex p-0;
    svg {
      @apply w-5 h-5;
      stroke-width: 3;
    }
  }
}

/* Mini Form
========================================================================== */

details.no-close[open] {
  summary { pointer-events: none; }
}

.mini-form {
  summary {
    & > span {
      width: 100%;
      transition: width 400ms ease-out, opacity 400ms ease-out;
      z-index: 10;
    }
   
  }
  & > div {
    display:grid;
    transition:max-height 400ms ease-out;
    max-height:0px;
  }
  &[open] {
    & > div {
      max-height:200px;
    }
  }
  form {
    input {
      font-size:13px;
      padding:6px 12px;
      @apply h-14 rounded-r-none rounded-l-[3px] lg:h-[39px] border;
    }
    button {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      @apply lg:h-auto relative z-20;
    }
    input:not([valid="true"]) + button {
      cursor:not-allowed;
    }
  }
  &[open] {
    summary {
      span,
      span svg {
        opacity:0;
        width:0;
        @apply bg-dark text-dark;
      }
    }
    form {
      margin-top:-56px;
      @media only screen and (min-width: 1024px) {
        margin-top:-39px;
      }
    }
  }
  &__info {
    p {
      text-align: left;
      margin:0.75rem 0 ;
      &:first-child {
        font-family: var(--font-bold);
      }
      font-size:12px;
    }
  }
  .button {
    @apply max-lg:h-14 gap-3;
  }
  &__success {
    visibility:hidden;
    width:0;
    height:0;
    display:none;
    opacity:0;
    transition:opacity 300ms ease;
  }
  &--submitted {
    summary {
      pointer-events:none;
      span {
        display:none;
      }
    }
    .mini-form__success {
      visibility:visible;
      display:block;
      width:100%;
      height:auto;
      opacity:1;
    }
  }
  .icon {
    @apply text-black;
  }
}

.icon {
  stroke-linecap: square;
  stroke-linejoin: initial;
}

/* Article Item
========================================================================== */

.content-item--article {	
	.type-item {
		@apply mb-2.5;
	}

	.content-item {
		&__meta {
			@apply mb-2.5 type-eyebrow uppercase;
		}

		&__excerpt {
			@apply text-[15px] hidden lg:block;
		}

		&__content {
			@apply px-5 justify-start lg:justify-between;
		}

		&__media-container {
			@apply max-w-[40%] lg:min-w-[50%];
		}

		&__media {
			@apply aspect-1 xl:aspect-[4/3] w-full;
		}

		&__button-set {
			@apply mt-2;

			button {
				@media only screen and (max-width: 1023px) {
					@apply button--link;
				}
				@media only screen and (min-width: 1024px) {
					@apply button--light;
				}
			}			
		}
	}

  &.content-item__article--vertical {
		.content-item__button-set {
			@apply hidden;
		}
		.content-item__media-container {
			@apply mb-4;
		}
		.content-item__text-stack {
			@apply gap-y-2;
		}
		.content-item__media {
			@apply aspect-[3/2];
		}
    .content-item__excerpt {
      @apply hidden;
    }
	}
}

.article-item {
  &__category,
  &__date {
    @apply text-[11px];
    color: #736b67;
  }
  &__category {
    @apply mr-1.5 pr-1.5 border-[#736b67] border-r;
  }
}



/* Reviews
========================================================================== */

.content-item--review {

	.content-item {
		&__header {
			@apply mb-6;
		}

		&__media {
			@apply relative;

			&:hover {
				.content-item__media-button {
					@apply opacity-100 transform duration-200 ease-in;
				}
			}
			&:not(:hover) {
				.content-item__media-button {
					@apply opacity-0 transform duration-200 ease-in;
				}
			}
			.content-item__media {
				@apply aspect-1;
			}
		}

		&__media-button {
			@apply absolute inset-0 bg-black/25 z-10 opacity-0;
		}

		&__button-media {
			@apply top-1/2 left-1/2 transform -translate-x-1/2;
		}

		&__review {
			@apply pt-6 mb-0;
			font-family: var(--font-regular);
		}

		&__author {
			@apply uppercase text-[#77706c] font-body;	
		}

		&__button-set {
			@apply mt-5;

			button {
				@apply button--link;
			}			
		}
	}
}

:root {
  --reviews-text-color:  var(--color-primary);
}
 
.review-snippet {
	.ruk-rating-snippet {
		&.ruk-rating-snippet-count,
    &.ruk_rating_snippet--loaded .ruk-rating-snippet-count {
			@apply font-body text-[10px] ml-1.5;
		}
		i {
			@apply text-[13px];
		}
	}
}

/* Tooltips / Hotspots
========================================================================== */
.tooltip {
  &__price {
    @apply font-body text-[13px] text-[#736b67]
  }
  &__quickview {
    @apply button--link;
  }
  &__title {
    @apply font-subheading;
  }
}

.hotspot {
  &__button {
    --section-dot-inner-background: #fff;
    --section-dot-background: 0,0,0;
  }
}

/* Tables
========================================================================== */
table {
  border-top:1px solid #EEE;
  border-bottom:1px solid #EEE;
  td {
    border-bottom:1px solid #EEE;
    border-right:1px solid #EEE;
    text-align:center;
    padding:2rem;
  }
  &.legend {
    position:sticky;
    left:0;
    td {
      background:#FFF;
      border-right:1px solid #EEE;
      text-align:right;
      
    }
  }
  tr:nth-of-type(odd) {
    background:#f7f7f7;
  }
}



/*  Gift Card Style =====================================  */
.giftcard {
  display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;

  .giftcard__container{
    margin: 50px auto 150px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    max-width: 600px;
    padding: 0 20px;
    width: 100%;
    .giftcard__block--code{
      margin-bottom: 35px !important;
      .icon--logo {
        img{
          margin: 0 auto;
          height: 19px;
          width: auto;
        }
      }
      .giftcard__title--page{
        margin: 38px auto 30px;
        font-size: 25px !important;
        font-family: Avenir Book, Arial, Helvetica, sans-serif;
        letter-spacing: -.01em;
        font-weight: inherit;
        line-height: 1.2285714286;
        width: 100%;
      }
      .giftcard__image-container{
        position: relative;
        .giftcard__title--value{
          font-family: Avenir Book,Arial,Helvetica,sans-serif;
          font-weight: inherit;
          line-height: 1.2285714286;
          letter-spacing: 0;
          left: 20px;
          top: 20px;
          position: absolute;
          margin: 0;
          font-size: 25x;
          color: #f9f9f9;
        } 
        span.giftcard__code{
          position: absolute;
          left: 50%;
          -webkit-transform: translateX(-50%);
          transform: translate(-50%);
          bottom: 75px;
          width: 80%;
          background: #fff;
          border-radius: 8px;
          p.giftcard__text{
            margin-bottom: 0;
            line-height: 31px;
            letter-spacing: 0;
            font-size: 18px;
            color: #000;
            font-family: Avenir Book,Arial,Helvetica,sans-serif;
          }
          span#GiftCardDigits {
            letter-spacing: 0px;
            font-size: 24px;
            font-weight: 700;
            font-family: Avenir Black,Arial,Helvetica,sans-serif;
            line-height: 31px;
          }
        }     
      }
    }
    .giftcard__block.giftcard__block--left {
      width: 100%;
      p.giftcard__text.giftcard__text--balance {
        font-size: 18px;
        color: #000;
        letter-spacing: 0;
        margin: 0 0 35px;
        font-family: Avenir Book,Arial,Helvetica,sans-serif;
        line-height: 31px;
        text-align: left;
      }
      .giftcard__buttons {
        gap: 0px;
        align-items: baseline;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        -webkit-box-pack: start;

        a.giftcard__button {
          max-width: 305px;
          height: 55px;
          width: 100%;
          line-height: 55px;
          background-color: #000;
          color: #fff;
          padding: 0;
          font-size: 13px;
          letter-spacing: 1.3px;
          font-family: Avenir Black,Arial,Helvetica,sans-serif;
          font-weight: 400;
          text-transform: capitalize;
          -webkit-transition: color .45s cubic-bezier(.785,.135,.15,.86), border .45s cubic-bezier(.785,.135,.15,.86);
          transition: color .45s cubic-bezier(.785,.135,.15,.86), border .45s cubic-bezier(.785,.135,.15,.86);
          z-index: 1;
          -webkit-tap-highlight-color: initial;
          &:hover{
            background-color: #000;
            color: #fff;
          }
        }
        a#PrintGiftCard {
          color: #000;
          font-family: Avenir Book,Arial,Helvetica,sans-serif;
          font-size: 10px;
          letter-spacing: 1.07px;
          line-height: 18px;
          margin-left: 15px;
          text-decoration: underline;
          text-transform: uppercase;
        }
      }
    }
  }
}
