{%- capture checkout_header -%}
  <a href="/" class="checkout__logo">
    <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="130px" height="34px" viewBox="0 0 130 34" xml:space="preserve">
      <g>
        <path d="M10.775,16.722c0.859-1.132,1.318-2.518,1.302-3.939v-1.888c0-1.855-0.488-3.353-1.302-4.362C9.115,4.482,6.478,4.58,4.655,4.58H0v24.905h3.548V18.742h2.117l3.45,10.742h3.679L8.887,17.992C9.616,17.741,10.267,17.303,10.775,16.722z M8.529,12.491c0,0.976-0.033,1.562-0.749,2.246c-0.596,0.563-1.396,0.857-2.214,0.814H3.548v-7.78h1.856c0.749,0,1.595-0.065,2.311,0.65c0.549,0.63,0.84,1.444,0.814,2.279V12.491z"/>
        <path d="M24.97,4.223c-1.856,0-3.321,0.586-4.395,1.595c-1.726,1.595-1.856,3.418-1.856,5.306v11.817c0,1.888,0.131,3.711,1.856,5.306c1.209,1.071,2.78,1.642,4.395,1.595c1.856,0,3.321-0.586,4.395-1.595c1.726-1.595,1.856-3.418,1.856-5.306V11.124c0-1.888-0.131-3.711-1.856-5.306C28.156,4.747,26.585,4.176,24.97,4.223z M27.639,23.495c0,0.749-0.065,1.497-0.749,2.181c-1.102,0.955-2.739,0.955-3.841,0c-0.684-0.683-0.749-1.432-0.749-2.181V10.603c0-0.749,0.065-1.497,0.749-2.181c1.102-0.955,2.739-0.955,3.841,0c0.684,0.683,0.749,1.432,0.749,2.181V23.495z"/>
        <path d="M42.191,4.58l-5.436,24.937h3.548l1.172-5.339h4.558l1.172,5.339h3.548L45.316,4.58H42.191z M41.833,20.988l1.856-9.473l1.92,9.473H41.833z"/>
        <path d="M67.942,16.722c0.859-1.132,1.318-2.518,1.302-3.939v-1.888c0-1.855-0.488-3.353-1.302-4.362c-1.66-2.051-4.297-1.953-6.12-1.953h-4.687v24.904h3.548V18.741h2.116l3.451,10.743h3.711l-3.907-11.492C66.783,17.741,67.434,17.303,67.942,16.722z M65.696,12.491c0,0.976-0.033,1.562-0.749,2.246c-0.596,0.563-1.396,0.857-2.214,0.814h-2.018V7.77h1.856c0.748,0,1.595-0.065,2.311,0.651c0.549,0.63,0.84,1.444,0.814,2.279V12.491z"/>
        <polygon points="89.298,4.58 85.554,4.58 79.89,16.593 79.89,4.58 76.374,4.58 76.374,29.516 79.922,29.516 79.922,22.224 82.136,18.383 86.368,29.517 90.242,29.517 84.252,14.802 	"/>
        <path d="M110.328,18.676c-6.316,7.325-12.631,14.617-12.729,14.715c-0.195,0.391,0.131,0.521,0.391,0.521h3.516l10.515-12.208l0.033,12.208h3.32l-0.033-9.604L110.328,18.676z"/>
        <path d="M104.371,16.398c0-1.6-1.297-2.897-2.897-2.897s-2.897,1.297-2.897,2.897s1.297,2.897,2.897,2.897S104.371,17.998,104.371,16.398z"/>
        <path d="M123.318,16.3c0,1.6,1.297,2.897,2.897,2.897c1.6,0,2.897-1.297,2.897-2.897s-1.297-2.897-2.897-2.897C124.615,13.403,123.318,14.7,123.318,16.3z"/>
        <path d="M115.895,17.179l0.033-0.033L103.85,3.18h8.204v6.837l5.209,5.599l12.599-14.65c0.196-0.213,0.183-0.545-0.031-0.741c-0.09-0.083-0.206-0.131-0.328-0.138H98.055c-0.294,0.028-0.51,0.289-0.482,0.584c0.01,0.109,0.054,0.212,0.124,0.295L126.02,33.88h3.516c0.228,0,0.521-0.131,0.358-0.489C129.796,33.261,122.667,25.024,115.895,17.179z M115.375,3.213l8.301-0.033c0,0-3.711,4.297-8.301,9.636V3.213z"/>
      </g>
    </svg>
  </a>

  <div class="checkout-breadcrumbs breadcrumb-container">
    <div class="breadcrumb-container__wrap">{{ breadcrumb }}</div>
  </div>
{%- endcapture -%}

{%- capture icon -%}
{%- comment -%}don't adjust whitespace{%- endcomment -%}
  <svg width="20" height="19" xmlns="http://www.w3.org/2000/svg" class="order-summary-toggle__icon">
    <path d="M17.178 13.088H5.453c-.454 0-.91-.364-.91-.818L3.727 1.818H0V0h4.544c.455 0 .91.364.91.818l.09 1.272h13.45c.274 0 .547.09.73.364.18.182.27.454.18.727l-1.817 9.18c-.09.455-.455.728-.91.728zM6.27 11.27h10.09l1.454-7.362H5.634l.637 7.362zm.092 7.715c1.004 0 1.818-.813 1.818-1.817s-.814-1.818-1.818-1.818-1.818.814-1.818 1.818.814 1.817 1.818 1.817zm9.18 0c1.004 0 1.817-.813 1.817-1.817s-.814-1.818-1.818-1.818-1.818.814-1.818 1.818.814 1.817 1.818 1.817z" />
  </svg>
{%- endcapture -%}

{%- capture new_icon -%}
  {%- include 'icon-bag', fill: '#000000' -%}
{%- endcapture -%}

{%- capture total_span -%}
  <span class="order-summary__emphasis total-recap__final-price skeleton-while-loading"
{%- endcapture -%}

{%- capture new_total_span -%}
  <span class="order-summary__currency-code">{{ checkout.currency }}</span><span class="order-summary__emphasis total-recap__final-price skeleton-while-loading"
{%- endcapture -%}

<!doctype html>
  <!--[if IE 9]> <html class="ie9 no-js supports-no-cookies {{ checkout_html_classes }}" lang="{{ locale }}" dir="{{ direction }}"> <![endif]-->
  <!--[if (gt IE 9)|!(IE)]><!--> <html class="no-js supports-no-cookies {{ checkout_html_classes }}" lang="{{ locale }}" dir="{{ direction }}"> <!--<![endif]-->
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="{{ settings.color_primary }}">
    <link rel="canonical" href="{{ canonical_url }}">
    <title>{{ page_title }}</title>

    {% if settings.favicon_light != blank %}
      <!-- Favicon Light Mode -->
      <link rel="icon" type="image/png" href="{{ settings.favicon_light | img_url: '64x64' }}" sizes="192x192">
      <link rel="shortcut icon" href="{{ settings.favicon_light | img_url: '64x64' }}o" type="image/x-icon">
      <link rel="appletouchicon" href="{{ settings.apple_touch_light | img_url: '180x180' }}">
      <meta name="msapplicationsquare70x70logo" content="{{ settings.ms_square_sm_light | img_url: '70x70' }}">
      <meta name="msapplicationsquare150x150logo" content="{{ settings.ms_square_md_light | img_url: '150x150' }}">
      <meta name="msapplicationsquare310x310logo" content="{{ settings.ms_square_lg_light | img_url: '310x310' }}">
      <meta name="msapplicationwide310x150logo" content="{{ settings.ms_rec_sm_light | img_url: '310x150' }}">
      <meta name="msapplicationTileColor" content="{{ settings.ms_fav_tile_color_light }}">
      <script>
        var darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        handleDarkmode(darkModeMediaQuery);
        function handleDarkmode(e) {
          var darkModeOn = e.matches; // true if dark mode is enabled
          var favicon = document.querySelector('link[rel="shortcut icon"]');
          var largeFavicon = document.querySelector('link[rel="icon"]');
          var appletouchicon = document.querySelector('link[rel="appletouchicon"]');
          var square70x70logo = document.querySelector('meta[name="msapplicationsquare70x70logo"]');
          var square150x150logo = document.querySelector('meta[name="msapplicationsquare150x150logo"]');
          var square310x310logo = document.querySelector('meta[name="msapplicationsquare310x310logo"]');
          var wide310x150logo = document.querySelector('meta[name="msapplicationwide310x150logo"]');
          var TileColor = document.querySelector('meta[name="msapplicationTileColor"]');
          if (!favicon || !largeFavicon) {
            return;
          }
          // replace icons with dark/light themes as appropriate
          if (darkModeOn) {
            favicon.href = '{{ settings.favicon_dark | img_url: "64x64" }}';
            largeFavicon.href = '{{ settings.favicon_dark | img_url: "64x64" }}';
            appletouchicon = '{{ settings.apple_touch_dark | img_url: "180x180" }}';
            square70x70logo = '{{ settings.ms_square_sm_dark | img_url: "70x70" }}';
            square150x150logo = '{{ settings.ms_square_md_dark | img_url: "150x150" }}';
            square310x310logo = '{{ settings.ms_square_lg_dark | img_url: "310x310" }}';
            wide310x150logo = '{{ settings.ms_rec_sm_dark | img_url: "310x150" }}';
            TileColor.content = '{{ settings.ms_fav_tile_color_dark }}';
          } else {
            favicon.href = '{{ settings.favicon_light | img_url: "64x64" }}';
            largeFavicon.href = '{{ settings.favicon_light | img_url: "64x64" }}';
            appletouchicon = '{{ settings.apple_touch_light | img_url: "180x180" }}';
            square70x70logo = '{{ settings.ms_square_sm_light | img_url: "70x70" }}';
            square150x150logo = '{{ settings.ms_square_md_light | img_url: "150x150" }}';
            square310x310logo = '{{ settings.ms_square_lg_light | img_url: "310x310" }}';
            wide310x150logo = '{{ settings.ms_rec_sm_light | img_url: "310x150" }}';
            TileColor.content = '{{ settings.ms_fav_tile_color_light }}';
          }
        }
        darkModeMediaQuery.addListener(handleDarkmode);
      </script>
    {% endif %}
    
    {% if settings.roark_google_optimize_id != blank %}
      <!-- Anti-flicker snippet (recommended)  -->
      <style>.async-hide { opacity: 0 !important} </style>
      <script>(function(a,s,y,n,c,h,i,d,e){s.className+=' '+y;h.start=1*new Date;
      h.end=i=function(){s.className=s.className.replace(RegExp(' ?'+y),'')};
      (a[n]=a[n]||[]).hide=h;setTimeout(function(){i();h.end=null},c);h.timeout=c;
      })(window,document.documentElement,'async-hide','dataLayer',4000,
      {'{{ settings.roark_google_optimize_id }}':true});</script>

      <script type="text/javascript">
        /**
        * Please ensure a cookie named "optVal" is set with a random value between 1-10
        * This should be set from the server to ensure no issues from ITP rules or similar
        */
        (function() {
          function optGetCookie(cname) {
            var name = cname + "=";
            var decodedCookie = decodeURIComponent(document.cookie);
            var ca = decodedCookie.split(';');
            for (var i = 0; i < ca.length; i++) {
              var c = ca[i];
              while (c.charAt(0) == ' ') {
                c = c.substring(1);
              }
              if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
              }
            }
            return "";
          }
          var optSetCrossDomainCookie = function(name, value, days) {
            var expires;
            if (days) {
              var date = new Date();
              date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
              expires = "; expires=" + date.toGMTString();
            } else {
              expires = "";
            }
            document.cookie = name + "=" + value + expires + "; path=/; domain={{ request.host | replace: 'www.', '' }}";
          };
          var retrieve = optGetCookie("optVal");
          if (!retrieve) {
            var optVal = Math.floor(Math.random() * 10) + 1;
            optSetCrossDomainCookie("optVal", optVal, 1000);
          }
        })();
      </script>
      <script src="https://www.googleoptimize.com/optimize.js?id={{ settings.roark_google_optimize_id }}"></script>
    {% endif %}

    {% if settings.roark_gtm_container_id != blank %}
    {%- comment -%} Google Tag Manager {%- endcomment -%}
      {% include 'checkout-roark-analyzify-gtm-body' %}
    {% endif %}

      {%- comment -%} PIXELS START {%- endcomment -%}
      {%- comment -%} Place all Pixel snippets here and ensure conditions are added {%- endcomment -%}
      {%- if settings.path_2_response_enable -%}
        {% render 'checkout-roark-pixel-path-2-response' %}
      {%- endif -%}

      {%- comment -%} PIXELS END {%- endcomment -%}

    

    <style data-styles="custom-properties">
      :root {
        --button-text-payment: "{{ 'shopify.checkout.general.continue_to_payment_method' | t }}";
        --button-text-shipping: "{{ 'shopify.checkout.general.continue_to_shipping_method' | t }}";
        --button-text-complete: "{{ 'shopify.checkout.general.complete_purchase_button_label' | t }}";
        --button-text-thanks-continue: "{{ 'shopify.checkout.thank_you.return_to_store_link_label' | t }}";
      }
    </style>

    <style>
      @font-face {
        font-family: 'Adobe Caslon Pro Bold';
        src: url("{{ 'adobe-caslon-pro-bold.woff2' | asset_url }}") format('woff2'), url("{{ 'adobe-caslon-pro-bold.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: normal;
      }

      @font-face {
        font-family: 'Adobe Caslon Pro Regular';
        src: url("{{ 'adobe-caslon-pro-regular.woff2' | asset_url }}") format('woff2'), url("{{ 'adobe-caslon-pro-regular.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: normal;
      }

      @font-face {
        font-family: 'Roboto Bold';
        src: url("{{ 'roboto-bold.woff2' | asset_url }}") format('woff2'), url("{{ 'roboto-bold.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: normal;
      }

      @font-face {
        font-family: 'Roboto Medium';
        src: url("{{ 'roboto-medium.woff2' | asset_url }}") format('woff2'), url("{{ 'roboto-medium.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: normal;
      }

      @font-face {
        font-family: 'Roboto Regular';
        src: url("{{ 'roboto-regular.woff2' | asset_url }}") format('woff2'), url("{{ 'roboto-regular.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: normal;
      }

      .shipping_address_notice {
        padding: 0.5285714286em;
        margin-bottom: 10px;
      }
     label.checkbox__label b{ color: #000000;font-family: "Roboto Regular",Arial,Helvetica,sans-serif;font-size: 13px;line-height: 19px}
     label.checkbox__label p, label.checkbox__label a {line-height: 16px;font-size: 11px; color: #444444;}
    </style>

    {% if false %}
      {{ content_for_header }}
    {% endif %}

    {{ content_for_header | replace: "<body onload='document._boomrl();'>", "<bodx onload='document._boomrl();'>" }}
    {{ checkout_stylesheets }}

    {% render 'checkout-roark-css' %}

    {{ checkout_scripts }}
    <script src="https://code.jquery.com/jquery-3.7.0.min.js" integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g=" crossorigin="anonymous"></script>

    <script>
      window.freeShipping = ''

      {% if settings.free_shipping_amount != blank %}
        {% assign amount = settings.free_shipping_amount | split: '.' %}

        {% if amount != blank %}
          window.freeShipping = {
            amount: '{{ amount[0] | plus: 0 | times: 100 }}'
          };
        {% endif %}
      {% endif %}

      document.documentElement.className.replace('no-js', 'js');

      window.theme = {
        strings: {
          addToCart: {{ 'products.product.add_to_cart' | t | json }},
          soldOut: {{ 'products.product.sold_out' | t | json }},
          unavailable: {{ 'products.product.unavailable' | t | json }}
        },
        moneyFormat: {{ shop.money_format | json }}
      };

      window.exponeaConsents = [{{ settings.roark_exponea_consents }}];
      window.exponeaSmsConsents = [{{ settings.roark_sms_exponea_consents }}];
      window.isCustomerEmail = {{ customer.email | json }}
    </script>

    {%- comment -%} Exponea Initilization Script {%- endcomment -%}
    {% render 'checkout-exponea' %}

    {% if settings.roark_gtm_container_id != blank %}
    {%- comment -%} Google Tag Manager {%- endcomment -%}
      {% render 'checkout-roark-analyzify-gtm-checkout' %}
    {% endif %}

    {%- if settings.roark_enable_ambassador -%}
      {% render 'checkout-roark-ambassador-init' %}
    {%- endif -%}
  </head>

  <body class="template-checkout">
    {% if settings.roark_gtm_container_id != blank %}
    {%- comment -%} Google Tag Manager No Script {%- endcomment -%}
      <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ settings.roark_gtm_container_id }}"
      height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    {% endif %}

    {{ skip_to_content_link }}

    <div class="content checkout__content" data-content>
      <div class="wrap">
        <header class="checkout__header checkout__header--mobile">
          {{ checkout_header }}
        </header>

        {{ order_summary_toggle | replace: icon, new_icon | replace: total_span, new_total_span }}

        <div class="main" role="main">
          <div class="checkout__main-inner">
            <header class="checkout__header checkout__header--desktop">
              {{ checkout_header }}
            </header>

            <div class="main__header">{{ alternative_payment_methods }}</div>

            <div class="main__content">
              {{ content_for_layout }}
            </div>

            {% render 'checkout-roark-footer' %}
          </div>
        </div>

        <div class="sidebar" role="complementary">
          <div class="checkout__sidebar-inner">
            <div class="sidebar__content">
              {%- capture sidebar_header -%}
                <header class="sidebar__cart-header">
                  <span class="sidebar__cart-header-title">Your Bag</span>
                </header>
              {%- endcapture -%}
              {{ content_for_order_summary | replace: '<h2 class="visually-hidden-if-js">Order summary</h2>', sidebar_header }}
            </div>
          </div>
        </div>
      </div>
    </div>

    {{ tracking_code }}
    {% render 'checkout-plus_show_discount' plus_show_discount:settings.roark_plus_show_discount_enable %}
    {% render 'checkout-roark-vip-redirect' %}
    {% render 'checkout-roark-pobox' %}
    <script>
      /* set window var to used in checkout.js */
      window.totalPrice = {{ checkout.total_price | amount_with_apostrophe_separator }} / 100;
    </script>
    <script src="{{ 'bundle.vendor.js' | asset_url }}"></script>
    <script src="{{ 'vendor.js' | asset_url }}"></script>
    <script src="{{ 'theme.js' | asset_url }}"></script>
    <script src="{{ 'bundle.index.js' | asset_url }}"></script>

    {% if settings.roark_site_mode != "vip" and shop.domain != 'roark.ca' %}
      <script src="{{ 'bundle.vip.js' | asset_url }}"></script>
    {% else %}
      {% render 'checkout-giftcard-message' %}
      <script src="{{ 'bundle.checkout.js' | asset_url }}"></script>
    {% endif %}

    {% if settings.roark_site_mode == 'standard' and settings.roark_vip_redirect_modal_enabled %}
      {% render 'checkout-roark-redirecttovip-message' %}
      <script src="{{ 'bundle.redirecttovip_checkout.js' | asset_url }}"></script>
    {% endif %}
    {% render 'checkout-roark-newsletter' %}

    {% if settings.roark_estimated_shipping_enabled %}
      {% render 'checkout-roark-shipping-dates' %}
    {% endif %}

    {% if settings.roark_address_validation_enable %}
      {% render 'checkout-roark-address-validation' %}
    {% endif %}

    <script>
      $(document).ready(function() {
        $('[name="checkout[reduction_code]"]').attr('autocorrect', 'off');
      });

      {% if checkout.customer %}
        exponea.identify({
          {% if checkout.customer.id %}"registered": {{ checkout.customer.id | json }},{% endif %}
          {% if checkout.customer.email %}"email_id": {{ checkout.customer.email | json }}{% endif %}
        });

        document.addEventListener('DOMContentLoaded', function() {
          var emailField = document.querySelector('[data-shopify-pay-email-flow]');
          if (emailField && emailField.classList.contains('field--error')) {
            // don't update if email field exists and has errors
          } else {
            exponea.update({
              {% if checkout.customer.email %}"email": {{ checkout.customer.email | json }},{% endif %}
              {% if checkout.customer.first_name %}"first_name": {{ checkout.customer.first_name | json }},{% endif %}
              {% if checkout.customer.last_name %}"last_name": {{ checkout.customer.last_name | json }},{% endif %}
              {% if shop %}"domain": {{ shop.permanent_domain|json }}{% endif %}
            });
          }
        })

        $(document).on('page:load', function() {
          if (Shopify && Shopify.Checkout && Shopify.Checkout.step) {
            exponea.track('checkout', {
              referrer: document.referrer,
              location: window.location.href,
              path: window.location.pathname,
              step: Shopify.Checkout.step
            });
          }

          var $phone = $('#checkout_shipping_address_phone');

          if ($phone[0]) {
            $('form.edit_checkout').on('submit', function() {
              var number = $phone.val();

              if (number === '') return

              exponea.update({
                phone: number
              });

            })
          }
        });
      {% endif %}

      $(document).on('page:load page:change', function() {
        if ($('.shipping__notice')[0]) {} else {
          if (Shopify.Checkout.step == "shipping_method") {
            $('<p class="shipping__notice">{{ settings.roark_shipping_text_message }}</p>').insertBefore('[data-shipping-methods]');
          }
        }

        if ($('.shipping_address_notice')[0]) {} else {
          if (Shopify.Checkout.step == "contact_information") {
            $('<p class="shipping_address_notice">{{ settings.roark_shipping_address_notice }}</p>').insertBefore('[data-address-fields]');
          }
        }

        if ($('.shipping__notice')[0]) {} else {
          if (Shopify.Checkout.step == "shipping_method") {
            $('<p class="shipping__notice">{{ settings.roark_shipping_text_message }}</p>').insertBefore('[data-shipping-methods]');
          }
        }
      });

      $(document).ready(function() {
        var cookie = window.Cookies.get('gc_value');

        if (typeof cookie !== 'undefined') {
          $('#checkout_reduction_code').val(cookie);
          $('[data-trekkie-id="apply_discount_button"]').attr('disabled', false).removeClass('btn--disabled').click();
        }
      })

      
      if (Shopify.Checkout.step == 'contact_information') {
        document.addEventListener('DOMContentLoaded', function() {
        function formatPhoneNumber(value) {
        var phoneNumber = value.replace(/[^\d]/g, "")
        var phoneNumberLength = phoneNumber.length

        if (phoneNumberLength < 4) return phoneNumber

        if (phoneNumberLength < 7) return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3)}`;

        return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
        }

        var sms_form_input_text = document.querySelector('[data-phone-formatter]');
        // Format the phone number in input on keyup
        sms_form_input_text.addEventListener('keyup', function(){
        var phoneValue = sms_form_input_text.value;
        var output;
        phoneValue = phoneValue.replace(/[^0-9]/g, '');
        var area = phoneValue.substr(0, 3);
        var pre = phoneValue.substr(3, 3);
        var tel = phoneValue.substr(6, 4);
        if (area.length < 3) {
        output = "(" + area;
        } else if (area.length == 3 && pre.length < 3) {
        output = "(" + area + ")" + " " + pre;
        } else if (area.length == 3 && pre.length == 3) {
        output = "(" + area + ")" + " " + pre + "-"+tel;
        }
        sms_form_input_text.value = output;
        })

        $('[data-phone-formatter]').on('keydown', function() {
        $(this).val(formatPhoneNumber($(this).val()))
        })
        })
      }

      {% if settings.roark_site_mode == 'vip' %}
        $(document).ready(function() {
          if ($('#order-summary .tags-list').length > 0) {
            var str = $('#order-summary .tags-list .tag__text .reduction-code span[aria-hidden=true]').text();
            var pattern = new RegExp("^•••• ....$");
            var result = pattern.test(str);

            if ($('#order-summary .payment-due__price').attr('data-checkout-payment-due-target') > 0 && result) {
              $('#order-summary .tag__button').click();
            }
          }
        });
      {% endif %}
    </script>
    {%- if settings.roark_enable_consent_checkout -%}
    {%- render 'checkout-roark-sms-checkout' -%}
    {%- endif -%}
 
    {% if settings.roark_site_mode == 'standard' and settings.roark_vip_redirect_modal_enabled %}
      <input type="hidden" name="vip_startswith_array" id="vip_startswith_array" value="{{ settings.roark_vip_startswith_array }}">
      <input type="hidden" name="vip_redirect_url" id="vip_redirect_url" value="{{ settings.roark_vip_redirect_url }}">
    {% endif %}

    {% render 'checkout-roark-avant-tracking-checkout' %}
    {% if settings.roark_site_mode == 'standard' %}
      <script>
        if (Shopify.Checkout.step == 'contact_information') {
          document.querySelector('#order-summary .edit_checkout .fieldset').style.display = 'none';
        }
      </script>
      {% render 'checkout-roark-hide-messages' %}
    {% endif %}
    {%- if settings.roark_spotify_pixel_enable -%}
      {% render 'checkout-roark-spotify-pixel' %} 
    {%- endif -%}
  </body>
</html>
