import 'regenerator-runtime/runtime';
import './main.scss';
import './globals.scss';
import './theme.scss';

import '../../components/carousels/carousels';
import '../../components/forms/forms';
import '../../components/cart'
import '../../components/customer'
import '../../components/customer.segmentation'
import '../../components/offers'
import '../../components/track'
import '../../components/wishlist'
import '../../components/quick-add'
import '../../components/sections'
import '../../components/preload'
import '../../components/aria'
import '../../components/focus'
import '../../components/modal'
import '../../components/loyalty'
import '../../components/menus' 
import '../../components/async-video';

import '../../components/@integrations/alpine'

import '../../components/@integrations/archipelago'


import '../../components/@integrations/reviewsio'
import '../../components/@integrations/mParticle'

import Alpine from 'alpinejs'
import mask from '@alpinejs/mask'
import intersect from '@alpinejs/intersect'
Alpine.plugin(mask)
Alpine.plugin(intersect)
window.Alpine = Alpine

window.addEventListener('DOMContentLoaded', Alpine.start)

Alpine.directive('import', (el, { modifiers, expression }, { evaluateLater, effect }) => {
    
    let moveElement = evaluateLater(expression)
 
    effect(() => {
        moveElement(querySelector => {
            if(!!document.querySelector(querySelector)) {
              if(modifiers.includes('beforeparent')){ 
                el.parentNode.before(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('afterparent')){ 
                el.parentNode.after(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('before')){ 
                el.before(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('after')){ 
                el.after(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('inner')){ 
                el.innerHTML = document.querySelector(querySelector).innerHTML
              }
            }
        })
    })

})

Alpine.directive('max', (el, { modifiers, expression }, { evaluateLater, effect }) => {
    
    let max = evaluateLater(expression)
 
    effect(() => {
        moveElement(querySelector => {
            if(!!document.querySelector(querySelector)) {
              if(modifiers.includes('beforeparent')){ 
                el.parentNode.before(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('afterparent')){ 
                el.parentNode.after(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('before')){ 
                el.before(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('after')){ 
                el.after(document.querySelector(querySelector), el.parentNode)
              }
            }
        })
    })

})



window.money = {
	currencies: {
    default:{label:true},
    USD:{symbol:'$'},
    CAD:{symbol:'$',label:'CAD'},
    EUR:{symbol:'€'},
    JPY:{symbol:'¥'}
  },
  format: v => {
  	const currency = money.currencies[Shopify.currency.active] || money.currencies.default;
  	money.currency = currency
  	let val = `${currency.symbol ? currency.symbol : ``}${(typeof v== 'string'&&v.includes('.')) ? v : (v / 100).toFixed(2)}${currency.label ? ` ${Shopify.currency.active}`:``}`.replace('.00','') 
  	
    return val
  }
}

window.image = {
  format: (url, config={} ) => {
    if(!url) return '';
    return `${url.split('?')[0]}?${Util.urlparams.build(config)}`;
  }
}


window.addEventListener('Products:optionSelected', e=>{
    if(e.detail.name=='Color') {
        Util.wait(500).then(()=>{          
          Sections.fetch(sections.filter(
            (s,i)=>{
              return i > sections.findIndex(_s=>_s.id.includes('essentials')) && s.id.includes('template') && s.type != 'modal' && !s.el.innerHTML.toLowerCase().includes("https://widget.reviews.io/polaris/build.js")
            }).map(s=>s.id).slice(0,6))
        })
    }
})
window.addEventListener('QuickAdd:opened', loadReviewsIoRatingSnippets)

window.addEventListener('QuickAdd:select', e=>{
  if (!quickadd.config.onSelect){
    quickadd.config.onSelect = () => {/*nada*/};
    quickadd.config.hold = true;
  }
})


window.addEventListener('Modal:open', e=>{
  document.querySelector('.navbar').classList.remove('active')
  document.querySelector('.menus').classList.add('hidden')
  document.querySelector('.menus').classList.add('lg:hidden')
})

window.addEventListener('Modal:close', e=>{
  document.querySelector('.navbar').classList.remove('active')
  document.querySelector('.menus').classList.add('hidden')
  document.querySelector('.menus').classList.add('lg:hidden')
})

document.querySelector('.navigation-container').addEventListener('mouseleave', e=>{
  if(document.querySelector('.menus').getAttribute('data-active-menu') != null) {
    document.querySelectorAll('.main-navigation-bar-desktop .active-menu').forEach(function(elm){
      elm.classList.remove('active-menu');
    });
    Menu.close()
    document.querySelector('.modal-overlay').click();
  }
})

document.querySelector('.nav-tools__search').addEventListener('click', e=>{
  document.querySelectorAll('.main-navigation-bar-desktop .active-menu').forEach(function(elm){
    elm.classList.remove('active-menu');
  });
  Menu.close()
  document.querySelector('.modal-overlay').click();
})
