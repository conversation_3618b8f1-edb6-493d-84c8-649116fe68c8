@import '../shared/globals.scss';



/* Color Guidelines
  ==========================================================================
  primary: Highest level user attention.
  secondary: Second highest level user attention.
  tertiary: Third highest level user attention.
  light: Most prominent light background color. Must be able to overlay on top of dark.
  dark: Most prominent dark background color. Must be able to overlay on top of light.
  pop: Usage examples are badges.
  highlight: Think about this as using a highlighter pen.
  body: Most common text color.
  header: Most common text color for headers.
*/

:root {

  --color-primary: #f15a31;
  --color-secondary: #fff;
  --color-tertiary: #f5f2ef;
  --color-light: #f9f9f9;
  --color-dark: #000;
  --color-pop: #f15a31;
  --color-highlight: #fbac4c;
  --color-body: #000;

  /*

  --swiper-theme-color: #000;

  --header-height:76px;
  --preheader-height: 34px;
  --header-height-half:38px;
  --unscrolled-header-height: 100px;
  --scrolled-header-height: 76px;

  @media screen and (max-width: 1024px) {
    --header-height:100px;
    --preheader-height: 32px;
    --header-height-half: 34px;
    --unscrolled-header-height: 100px;
    --scrolled-header-height: 68px;
  }

  --font-body-weight: 400;
  --font-body-style: normal;
  --font-heading-weight: 700;
  --font-heading-style: normal;
  --font-subheading-weight: 600;
  --font-subheading-style: normal;


  --header-height:48px;

  */
}


.top-main {
  top:var(--header-offset)
}
@media screen and (min-width: 1025px) {
  .lg\:top-main {
    top:var(--header-offset);
  }
}

.rte a {
  @apply underline text-body;
}

 

   /* Type Styles
========================================================================== 
 Guidelines
==========================================================================
  **Type Styles**

  primary: Primary Headline or Title type.
  secondary: Commonly used as Subtitle type, compliments Primary.
  tertiary: Third highest level user attention, smaller but stylistically similar to primary.

  **Type Layouts / Spacing**

  page: Most common text color.
  section: Most common text style for headers.
  article: Most common text style for headers.

*/

.type {

  &-hero {
    font-size: 3.583rem;
    line-height: 1;
    letter-spacing: -0.9px;
    @apply font-heading;
    &.type--sm {
      font-size: 2.488rem;
    }
    &.type--lg {
      font-size: 5.16rem;
    }
  }
  &-headline {
    font-size: 36px;
    letter-spacing: -0.9px;
    @apply font-heading;
    &.type--sm {
      font-size: 32px;
    }
    &.type--lg {
      font-size: 40px;
    }
  }
  &-subline {
    font-size: 18px;
    line-height: 1.5;
    letter-spacing: 0.4px;
    @apply font-body;
    &.type--sm {
      font-size: 16px;
    }
    &.type--lg {
      font-size: 22px;
    }
  }
  &-item {
    font-size: 18px;
    line-height: 1.5;
    @apply font-heading;
    &.type--sm {
      font-size: 14px;
    }
    &.type--lg {
      font-size: 22px;
    }
  }
  
  &-section {
    font-size: 22px;
    line-height: 1;
    @apply font-heading;
    &.type--sm {
      font-size: 18px;
    }
    &.type--lg {
      font-size: 26px;
    }
  }
  &-eyebrow {
    @apply font-body;
    font-size: 15px;
    line-height: 1.5;
    letter-spacing: 0.4px;
    &.type--sm {
      font-size: 12px;
    }
    &.type--lg {
      font-size: 17px;
    }
  }
  &-body {
    @apply font-body;
    line-height: 1.5;
    font-size: 16px;
    &.type--sm {
      font-size: 14px;
    }
    &.type--lg {
      font-size: 18px;
    }
  }
  &-nav-link {
    @apply font-heading text-body text-[16px] leading-6 tracking-[0.4px];
  }
  &-micro { 
    font-size: 11px;
    letter-spacing: .5px;
    line-height: 18px;
    @apply font-body capitalize;
  }
}

p {
  font-family: var(--font-body-family);
  font-weight: var(--font-body-weight);
  font-weight: var(--font-body-style);
  strong {
    font-family: var(--font-bold);
  }
}


h1, .h1 {
  @apply headings text-2xl;
}

h2, .h2 { 
  @apply headings text-xl; 
}

h3, .h3 { 
  @apply headings text-lg;
}

h4, .h4 { 
  @apply headings text-base; 
}

h5, .h5 {  
  @apply headings text-base;  
}

h6, .h6 {  
  @apply headings text-base;  
}


/* Buttons and Controls
========================================================================== */

.button, .btn {
  @apply whitespace-nowrap items-center border rounded-[3px] tracking-[1.3px] inline-flex text-[14px] h-[39px] justify-center opacity-100 overflow-hidden py-0 px-[18px] relative uppercase cursor-pointer;
  font-family: var(--font-bold);
  &--primary { @apply  bg-primary border-primary text-white;}
  &--secondary { @apply border-secondary bg-secondary text-black; }
  &--tertiary { @apply border-[#d3c7c1] bg-transparent text-black; 
    &:before {
      @apply bg-primary;
    }
  }
  &--light { 
    @apply border-black bg-transparent text-black; 
    &:before {
      @apply bg-black;
    }
  }
  &--dark { 
    @apply border-secondary bg-transparent text-secondary;
    &:before {
      @apply bg-secondary
    }
  }
  &--pop { 
    @apply bg-transparent border-primary text-primary; 
    &:before {
      @apply bg-primary;
    }
  }
  &--tertiary, &--light, &--dark, &--pop, &--highlight {
    &:before {
      border-radius: 50%;
      content: '';
      height: 400px;
      left: 50%;
      pointer-events: none;
      position: absolute;
      top: 50%;
      transform: translate(-50%, -50%) scale(0);
      transform-origin: center center;
      transition: transform 0.25s ease, opacity 0.25s ease-out, color 0.1s ease;
      width: 400px;
      opacity: 0;
      z-index: 1;
      will-change: transform, opacity, color;
    }
    &:hover , a:hover & {
      @apply text-white;
      z-index: 1; //required for hover background effect to work
      
      &:before {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease;
      }
    }
  }
  &--tertiary {
    &:hover, a:hover & {
      @apply border-primary;
    }
  }
  &--dark {
    &:hover, a:hover & {
      @apply text-primary;
      z-index: 1; //required for hover background effect to work
      
      &:before {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease;
      }
    }
  }
  &--highlight { 
    @apply bg-transparent border-white text-white hover:text-primary; 
    a:hover & {
      @apply text-primary;
    }
    &:before {
      @apply bg-white;
    }
  }
  &--link { @apply font-heading tracking-[0.4px] capitalize bg-transparent text-[13px] text-primary p-0 border-0 underline; }
  &--w-icon { 
    @apply flex justify-center items-center border-primary bg-primary text-white gap-3;
    &:before {
      @apply bg-secondary;
    }
    .icon {
      @apply text-white;
    }
  }
  &--simple {
    // font-family: var(--font-medium);
		@apply tracking-wide text-primary text-sm gap-1 capitalize border-transparent bg-transparent px-0;
    .icon {
      @apply text-current;
    }
  }
  &--disabled {
    @apply bg-dark border-[#eee] text-white cursor-not-allowed opacity-20;
  }
  &--action { 
    @apply flex justify-center items-center border-black bg-transparent rounded-full text-[13px] tracking-normal capitalize font-normal h-[35px];
    font-family: var(--font-regular);
    &:before {
      @apply bg-secondary;
    }
    .icon {
      @apply text-black mr-0 w-4 h-3;
    }
  }
  &--large {
    @media screen and (min-width: 1025px) {
      height:56px; padding:0 50px; border-width:2px;
    }
  }
  > span {
    @apply relative z-10;
  }

  &--micro-link {
    font-size: 13px;
    letter-spacing: -.1px;
    border-width: 0;
    padding:0;
    margin:0;
    height: auto;
    @apply font-body text-black capitalize underline;
  }
}

.wishlist-toggle {
	@apply pl-3;

	.icon {
		@apply stroke-primary;

		&.active {
			@apply fill-primary;
		}
	}
}


/* Forms
========================================================================== */

.field {
  label {
    @apply text-[13px] text-[#736b67] mb-2.5;
  }
  &__input,
  &__textarea,
  &__select {
    @apply border-[#e4e4e4] text-sm;
  }

  &__select {
    @apply rounded-[2px] h-12 py-0 text-sm appearance-none;

    background-position: right 12px center;
    background-image: url(https://olukai.com/cdn/shop/t/405/assets/select-icon.svg?v=43184182532122065261626389908);
    background-repeat: no-repeat;
    cursor: pointer;
  }

  &__toggle {
    @apply bg-tertiary;

    label {
      @apply inline-flex items-center mb-0 whitespace-nowrap;

      input {
        &:not(:checked) {
          & ~ .toggle__label {
            @apply cursor-pointer text-[12px] px-3 py-1 border bg-transparent border-transparent rounded-full text-body;
          }
        }

        &:checked {
          & ~ .toggle__label {
            @apply text-[12px] px-3 py-1 border bg-white border-tertiary text-body rounded-full;
          }
        }
      }
    }
  }

  &__image {
    @apply border rounded p-[15px] relative flex flex-col items-center justify-end border-tertiary;
    font-weight: 700;
    line-height: 18px;
    letter-spacing: 1.5px;
    input {
      @apply absolute inset-0 opacity-0;
    }
    img {
      width: 155px;
      max-width: 100%;
    }
    label {
      @apply flex items-center flex-col justify-end text-center uppercase text-[15px] gap-2.5;
    }
    &--horizontal {
      @apply justify-start items-start;
      label {
        @apply flex-row;
        img {
          width: 72px;
        }
      }
    }
    &:hover, &:focus {
      @apply border-primary;
      -webkit-box-shadow: 0 5px 6px rgba(0,0,0,.1);
      box-shadow: 0 5px 6px #0000001a;
    }
    &:has(input:checked) {
      @apply border-2 border-primary;
      background: #f0f8f8;
      -webkit-box-shadow: 0 5px 6px rgba(0,0,0,.1);
      box-shadow: 0 5px 6px #0000001a;
    }
  }
}

.field {
  
  input {
    &[type="checkbox"],
    &[type="radio"] {
      @apply h-[18px] w-[18px];

      & + span {
        @apply capitalize font-body text-[13px];
      }
    }  

    &[type="radio"] {
      &:checked {
        @apply border-0 shadow-[inset_0_0_0_7px] shadow-black;
      }
    }
  }

  &__chip {
    span {
      font-family: var(--font-regular);
      font-weight: 400;
      font-size: 13px;
      text-transform: capitalize;
      line-height: normal;
    }

    &-swatch {
      @apply outline-offset-0 outline-[4px];
    }
    
    &:hover &-swatch {
      @apply outline-primary;
    }

    @apply bg-white text-black rounded-[3px] px-5 py-2.5 font-heading flex-row items-center;

    &:has(input:checked) {
      @apply bg-black text-white;
    }

    &:has(button) {
      @apply pr-3;
    }

    button {
      @apply pl-2.5;
    }
  }

  &__checkbox {

    span { 
      text-transform: capitalize;
    }
    
    input[type="checkbox"] {
      @apply rounded-none relative rounded-[4px];

      &:checked {
        @apply border-0 bg-black shadow-none;
    
        &:after {
          content: "";
          display: block;
          width: 5px;
          height: 8px;
          border: solid white;
          border-width: 0 1.5px 1.5px 0;
          position: absolute;
          transform: rotate(45deg);
          top: 50%;
          left: 50%;
          translate: -50% -65%;
          background: transparent;
        }
      }
    }
  }
  
  &__color {
    &-swatch {
      @apply outline-offset-[1px] outline-[2px];
    }
    
    &:hover &-swatch {
      @apply outline-black;
    }

    span {
      font-family: var(--font-regular);
      font-weight: 400;
      @apply text-dark text-[12px];
    }

    &:has(input:checked),
    &:hover {
      span {
        @apply text-black;
      }
    }
  }

  &__buttons {
    @apply gap-[9px];
  }

  &__button {
    @apply w-full h-full aspect-1 flex-auto;

    &-text {
      @apply font-body text-[13px] [input:checked_~_&]:border-black [input:checked_~_&]:bg-black [input:checked_~_&]:text-white;
    }

    &:hover &-text {
      @apply border-black border-2;
    }
  }

  &-plus-minus {
    @apply flex flex-row items-center;
    input {
      @apply w-12 p-1 text-center;
    }
    button {
      @apply w-8 h-8 flex items-center justify-center bg-white;
    }
    svg { 
      stroke-width: 3px;
    }
  }
}

progress {
  @apply bg-gray-200 rounded-lg h-2.5 w-full;

  &::-webkit-progress-bar {
    @apply h-2.5 rounded-lg bg-gray-200 w-full;
  }

  &::-webkit-progress-value {
    @apply rounded-lg bg-gradient-to-r from-[#f15a31] to-[#fbac4c];
  }

  &::-moz-progress-bar {
    @apply rounded-lg bg-gradient-to-r from-[#f15a31] to-[#fbac4c];
  }
}

.progress-bar {
  &__label {
    @apply font-body text-dark text-[13px] font-normal text-[#736b67] tracking-normal;
  }
}

.dropdown-menu {
	@apply border border-solid rounded-sm text-current border-[#e4e4e4] text-sm;
	
	&__trigger {
		@apply rounded-[2px] h-8 text-sm appearance-none py-0 pl-3;

		.dropdown-menu__label {
			@apply text-xs text-[#797979];

			&:after {
				content: ':'
			}
		}

		.dropdown-menu__value {
			@apply text-xs pl-2;
		
		}
		
		&-icon {
			@apply ml-3 pl-3 pr-3 border-l text-xs;

			.icon {
				@apply w-3;
				fill: #797979;
				stroke: #797979;
				stroke-linecap: butt;
			}
		}
	}

	&__menu {
		@apply rounded-md mt-2 py-2;
		&-item {
			@apply text-xs px-4 py-1.5 hover:bg-light hover:bg-black hover:text-white;

			button {
				@apply text-left;
			}
		}
	}
}


/* Accordion
========================================================================== */

.list {
  .accordion {
    &:last-child {
      @apply border-b border-[#e4e4e4];
    } 
  }
}

.accordion {
  &:last-child {
    @apply border-b border-[#e4e4e4];
  }
  .accordion-title {
    @apply bg-transparent border border-[#e4e4e4] border-l-0 border-b-0 border-r-0 text-left cursor-pointer h-[60x] leading-[60px];
    font-family: var(--font-medium);
    span:first-child {}
    .accordion-control {
      @apply bg-transparent text-dark border-0;
      .icon {}
    }
  }
  .accordion-panel {
    @apply text-sm py-4 px-0 border-0;
    > div {}
    ul {}
    p:last-child {}
  }
  &-control {
    &:first-of-type {
      display:block;
    }
    &:last-of-type {
      display:none;
    }
    [open] > summary & {
      &:first-of-type {
        display:none;
      }
      &:last-of-type {
        display:block;
      } 
    }
  }
}

/* Cart Upsell item
========================================================================== */
.upsell-item {
  @apply text-sm;
  background-color:#f9f9f9;
  @apply p-md;
  &__media {
    width:6rem;
    height:6rem;
  }
  button[disabled] {
    background-color: #64646459;
    color: #fff;
    cursor: not-allowed;
  }
  &__header {
    @apply flex justify-between w-full items-start;
    * {
      font-weight:400;
    }
  }
  &__title {
    font-size:14px;
    margin:0;
    font-family: 'Roboto Medium', sans-serif;
  }
  &__subtitle {
    font-size:12px;
    margin:0px 0;
    color:#797979;
    font-family: 'Roboto Regular', sans-serif;
  }
  &__prices {
    line-height:1;
    margin:0;
    .type-item {
      font-size: 14px;
    }
  }
  &__body {
    @apply w-full flex flex-col pl-5;
    
  }
  
  &__actions {
    @apply flex justify-between w-full;

    select {
      @apply border p-2 bg-transparent border-black rounded-md lg:mr-1.5 text-sm leading-none;
      line-height:1;
    }

    button {
      @apply p-sm ml-auto;      
      border: 0;
      border-radius: 8px;
      svg {
        width:16px;
        stroke-width:3px;
      }
    }
  }
  

}

/* Tabs
========================================================================== */

.tabs {
  ul:not(.start) {
    @apply pl-0 flex justify-end w-full;
    li {
      @apply ml-5;
    }
  }

  ul.start {
    @apply space-x-4;
  }

  .tab-title {
    @apply btn btn--tertiary;
    @apply border-[#d3c7c1] bg-transparent text-[13px] font-body capitalize tracking-normal text-[#381300]; 
    &.active {
      @apply btn--primary; 
    }
  }
  
  .tab-panel {
    > div {
      @apply py-6;
    }
  }
}

/* Pagination
========================================================================== */

.pagination--page {
  @apply bg-transparent;
  li {
    @apply flex justify-start items-start;
    a:not(.pp-control),
    button:not(.pp-control), > span {
      @apply block px-2 py-0.5 pb-1 mx-[5px];
    }
    [aria-current="page"], .active {
      @apply relative block;
      &:after {
        content: "";
        @apply block border-b-[2px] border-primary;
      }
    }
  }
}

.swiper-pagination,
.pagination {
  @apply w-full;

  .pagination-bullet, .swiper-pagination-bullet {
    @apply bg-tertiary flex-1 rounded-none;

    &.active, &.swiper-pagination-bullet-active {
      @apply scale-100 bg-black;
    }
  }
  &.swiper-pagination-bullets.swiper-pagination-horizontal {
    @apply relative;
  }
}

.btn-control {
  &.swiper-button-prev,
  &.swiper-button-next {
    @apply bg-white rounded-full h-10 w-10 shadow-md text-inherit flex p-0;
  }
}

/* Mini Form
========================================================================== */

details.no-close[open] {
  summary { pointer-events: none; }
}

.mini-form {
  summary {
    & > span {
      width: 100%;
      transition: width 400ms ease-out, opacity 400ms ease-out;
      z-index: 10;
    }
   
  }
  & > div {
    display:grid;
    transition:max-height 400ms ease-out;
    max-height:0px;
  }
  &[open] {
    & > div {
      max-height:200px;
    }
  }
  form {
    input {
      font-size:13px;
      padding:6px 12px;
      @apply h-14 rounded-r-none rounded-l-[3px] lg:h-[39px] border;
    }
    button {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      @apply lg:h-auto relative z-20;
    }
    input:not([valid="true"]) + button {
      cursor:not-allowed;
    }
  }
  &[open] {
    summary {
      span,
      span svg {
        opacity:0;
        width:0;
        @apply bg-dark text-dark;
      }
    }
    form {
      margin-top:-56px;
      @media only screen and (min-width: 1024px) {
        margin-top:-39px;
      }
    }
  }
  &__info {
    p {
      text-align: left;
      margin:0.75rem 0 ;
      &:first-child {
        font-family: var(--font-bold);
      }
      font-size:12px;
      color:#444;
    }
  }
  .button {
    @apply max-lg:h-14 gap-3;
  }
  &__success {
    visibility:hidden;
    width:0;
    height:0;
    display:none;
    opacity:0;
    transition:opacity 300ms ease;
  }
  &--submitted {
    summary {
      pointer-events:none;
      span {
        display:none;
      }
    }
    .mini-form__success {
      visibility:visible;
      display:block;
      width:100%;
      height:auto;
      opacity:1;
    }
  }
}

.icon {
  stroke-linecap: square;
  stroke-linejoin: initial;
}

/* Article Item
========================================================================== */

.content-item--article {	
	.type-item {
		font-size: clamp(0.875rem, 3.5vw, 1.125rem);
	}

	.content-item {
		&__meta {
			@apply mb-2.5;
		}

		&__excerpt {
			@apply text-[15px] hidden lg:block;
		}

		&__content {
			@apply px-5 justify-start lg:justify-between;
		}

		&__media-container {
			@apply max-w-[40%] lg:min-w-[50%];
		}

		&__media {
			@apply aspect-1 xl:aspect-[4/3] w-full;
		}

		&__button-set {
			@apply mt-2;

			button {
				@media only screen and (max-width: 1023px) {
					@apply button--link;
				}
				@media only screen and (min-width: 1024px) {
					@apply button--dark;
				}
			}			
		}
	}

  &.content-item__article--vertical {
		.content-item__meta {
			@apply uppercase;
		}
		.content-item__button-set {
			@apply hidden;
		}
		.content-item__media-container {
			@apply mb-4;
		}
		.content-item__text-stack {
			@apply gap-y-2;
		}
		.content-item__media {
			@apply aspect-[3/2];
		}
	}
}

.article-item {
  &__category,
  &__date {
    @apply text-[11px];
    color: #736b67;
  }
  &__category {
    @apply mr-1.5 pr-1.5 border-[#736b67] border-r;
  }
}

/* Reviews
========================================================================== */
:root {
  --reviews-star-color: var(--color-primary);
  --reviews-text-color:  var(--color-dark);
}
.review-snippet {
	.ruk-rating-snippet {
		.ruk-rating-snippet-count {
			@apply font-body text-[10px] ml-1.5;
		}
		i {
			@apply text-[13px];
		}
	}
}

/* Tooltips / Hotspots
========================================================================== */
.tooltip {
  &__price {
    @apply font-body text-[13px] text-[#736b67]
  }
  &__quickview {
    @apply button--link;
  }
}

/* Tables
========================================================================== */
table {
  border-top:1px solid #EEE;
  border-bottom:1px solid #EEE;
  td {
    border-bottom:1px solid #EEE;
    border-right:1px solid #EEE;
    text-align:center;
    padding:2rem;
  }
  &.legend {
    position:sticky;
    left:0;
    td {
      background:#FFF;
      border-right:1px solid #EEE;
      text-align:right;
      
    }
  }
  tr:nth-of-type(odd) {
    background:#f7f7f7;
  }
}
