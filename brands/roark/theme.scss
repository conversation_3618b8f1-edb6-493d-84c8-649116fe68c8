@import '../../components/product-item/theme-roark.scss';
@import '../../components/modal/theme.scss';
@import '../../components/search-suggestion/search-suggestion.scss';
@import '../shared/styles/footer.scss';


html {
	-scroll-snap-type: y;
}

body {
	background: #f3f3f3;
}

#MainContent {
	position: relative;
	background: #f3f3f3;
}

.difference {
	mix-blend-mode: difference;
}

.rounded-buttons {
	.button {
		@apply rounded-md;
	}
}

.nav-tool {
	position: relative;

	&__count {
		display: block;
		width: 6px;
		height: 6px;
		top: 1px;
		right: -4px;
		position: absolute;
		background: #F05931;
		border-radius: 100%;
		content: '';
		font-size: 0;

		&:after {
			position: absolute;
			display: block;
			content: '';
			background: #F05931;
			transform: scale(5);
			opacity: 0.5;
			inset: 0;
			border-radius: 100%;
			animation: ping 3s infinite;
		}
	}
}

.nav-tools {

	&__account {
		margin-left: 11px;
	}

	&__cart {
		margin-left: 18px;
		position: relative;
	}
}

@keyframes ping {
	0% {
		transform: scale(1);
		opacity: 1;
	}

	25% {
		transform: scale(1);
		opacity: 1;
	}

	99% {
		transform: scale(5);
		opacity: 0;
	}

	99.999% {
		transform: scale(5);
		opacity: 0;
	}

	100% {
		transform: scale(1);
		opacity: 1;
	}
}


.lg\:flex-col-reverse {}

.lg\:self-end {}

.slider-cart {

	@media(min-width:1025px) {
		max-width: 40%;
		width: 40%;
		background: transparent;
		top: .875rem;
		bottom: .875rem;
		right: 0.875rem;
		height: calc(100dvh - 1.75rem);
		border-radius: 0.5rem;
		overflow: hidden;
	}

	background-color: var(--color-light);

	.section-blocks {
		background-color: var(--color-light);
		@apply flex flex-col h-full;

		&>* {
			border-bottom: 1px solid var(--color-tertiary);
		}
	}

	.cart__item-wrapper {}

	&__header {
		@apply py-0 flex flex-row-reverse w-full sticky top-0 z-50;
		display: flex !important;

		.swiper {
			@apply w-full;
		}

		.announcement {
			@apply py-sm lg:py-md;
		}
	}

	&__header-content--announcements {
		@apply w-full overflow-hidden;
	}

	&__header-content--close {
		z-index: 65;
	}

	&__summary-text {
		border: 0;
	}

	.field-plus-minus {
		margin-bottom: 0;

		button {
			height: 1rem;
			width: 1rem;

			svg {
				height: 12px;
			}
		}
	}

	.cart__item-wrapper {
		height: 100%;
		overflow: auto;
		border-top: 0;

		&:has(.cart-item) {
			background: #FFF;
		}
	}

	.cart__items {
		border-bottom: 0;
	}

	.cart-item {
		padding-top: 0.25rem;
	}

	.cart-item__image {
		width: 7rem;
		height: 8rem;
		background: #f7f7f7;
	}

	.cart-item__price,
	.cart-item__compare-at-price {
		margin-bottom: 0;
	}

	.cart-item__info-end {
		margin-top: 0;
	}

	.cart-item__quantity.field input {
		font-size: 13px !important;
		pointer-events: none;
	}

	&__summary {
		padding: 0.5rem;
		margin: 0;

		&:has(+ .slider-cart__payment-widget) {
			border-bottom: 0 !important;
			padding-bottom: 0;
		}
	}

	&__summary-text {
		padding: 0;
		margin: 0;
	}

	&__summary-price {
		font-weight: normal;
	}

	&__payment-widget {
		padding: 0;
		margin: 0;
		padding-bottom: 0.5rem;
	}

	&__gift-message {
		padding: 0.5rem 1rem 0.25rem;
		margin: 0;

		.gift-trigger {

			.field,
			.field__checkbox {
				margin: 0;
				display: flex;
				align-items: center;
			}

			button {
				@apply text-[#736b67];
				font-size: 13px;
				display: flex;
				align-items: center;
			}
		}
	}

	.gift-form {
		margin-top: 8px;
	}

	.cart-item__remove {
		svg {
			width: 16px;
			margin-top: -6px;
			color: #666;
		}
	}
}
.slider-cart {
	&__recs {
		padding-bottom: 1.44rem;
    	padding-top: 1.44rem;	
		&-title {
			margin-top: 0.833rem;
			margin-bottom: 0.833rem;
			padding-left: 1.44rem;
			padding-right: 1.44rem;
			color: #000;
			font-family: 'Adobe Caslon Pro Bold', serif;
			font-size: 14px;
			line-height: 1;
			text-transform: capitalize;
		}
	}
}

.modal-overlay {
	z-index: 20;
	backdrop-filter: blur(3px) grayscale(50%) contrast(50%);
	background-color: rgba(160, 160, 150, 0.4);
	/*
	&:after {
		inset:0;
		position:absolute;
		background-image:url(https://dev-roark.myshopify.com/cdn/shop/files/topography_light.png);
		content:'';
		mix-blend-mode:multiply;
		opacity:0.25;
	}
	*/
}

body:has(.navbar.active) .modal-overlay {
	opacity: 1;
	pointer-events: all;
}

body:has(.menus[data-active-menu]) .modal-overlay {
	opacity: 1;
	pointer-events: all;
}

.frosty-bottom {

	@media(max-width:1024px) {
		&:before {
			position: absolute;
			left: 0;
			bottom: 0;
			width: 100%;
			height: 5rem;
			content: '';
			backdrop-filter: blur(2px);
			mask: linear-gradient(to bottom,
					rgba(0, 0, 0, 0) 0%,
					rgba(0, 0, 0, 1) 50%,
					rgba(0, 0, 0, 1) 100%);
		}

		&:after {
			position: absolute;
			left: 0;
			bottom: 0;
			width: 100%;
			height: 5rem;
			z-index: 1;
			backdrop-filter: blur(6px);
			mask: linear-gradient(to bottom,
					rgba(0, 0, 0, 0) 0%,
					rgba(0, 0, 0, 0) 50%,
					rgba(0, 0, 0, 1) 100%);
		}
	}

}

.product-header {

	* {
		font-weight: normal;
	}

	&__title {
		margin: 0;
		padding: 0;
		font-size: 24px;
		font-weight: normal;
	}
	&__prices {
        display: flex;
        align-items: center;
        flex-direction: row-reverse;
        gap: 5px;
    }
}

.product-swatch-color-tooltip{
    visibility: hidden;
    text-align: center;
    position: absolute;
    z-index: 1;
    margin-bottom: 14px;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    max-width: none;
    height: 36px;
    background: rgb(255, 255, 255);
    color: rgb(51, 51, 51);
    letter-spacing: 0.025rem;
    border-radius: 50px;
    padding: 8px 16px;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 4px 16px -4px;
    font-size: 0.75rem;
    font-weight: normal;
    bottom: 100%;
    white-space: nowrap;
}

label.field__button:hover .product-swatch-color-tooltip{
    visibility: visible !important;
}

.product-essentials {
	max-width: 100vw;
	background: #eaeaea;

	&__media-item {
		background: #eaeaea;

		img {
			background: #FFF;
			aspect-ratio: 619/700;
		}
	}

	&__media--grid &__media-item img {
		object-fit: cover;
	}

	&__modal-close {
		@apply bg-white;
		color: #373F47;
	}
}

.product-form {
	&__options {
		@apply grid grid-cols-1 gap-sm lg:gap-xl;
	}

	&__option {
		@apply grid grid-cols-1 gap-0 relative;

		&:after {
			@media(max-width:1024px) {
				pointer-events: none;
				position: absolute;
				content: '';
				display: block;
				width: 2rem;
				right: -1rem;
				top: 0;
				height: 100%;
				background-image: linear-gradient(to right, rgba(234, 234, 234, 0), rgba(234, 234, 234, 1));
			}
		}

		.field {
			&__buttons {
				gap: 6px;

				@media(max-width:1024px) {
					@apply relative flex -mx-md overflow-x-auto;
					-ms-overflow-style: none;
					/* IE and Edge */
					scrollbar-width: none;

					/* Firefox */
					&::-webkit-scrollbar {
						display: none;
					}
				}

				@media(min-width:1025px) {
					display: grid;
					gap: 8px;
					grid-template-columns: repeat(8, minmax(0, 1fr));
				}
			}

			&__button {
				@apply rounded-full;

				@media(min-width:1025px) {
					@apply rounded-sm py-xl w-full;
				}

				width:4rem;
				flex-shrink:0;
				flex-grow:0;

				&:first-of-type {
					@media(max-width:1024px) {
						margin-left: 1rem;
					}
				}

				&:last-of-type {
					@media(max-width:1024px) {
						margin-right: 1rem;
					}
				}

				img {
					display: none;
					border: none;
					aspect-ratio: 1;
					object-fit: contain;
				}
			}

			&__button-text {
				@apply flex items-center justify-center;
			}
		}

		&--color {
			@apply gap-0;

			.field {
				&__buttons {
					@media(max-width:1024px) {
						@apply gap-0 overflow-y-hidden pt-px;
					}
				}

				&__button {
					@media(max-width:1024px) {
						width: 22%;
					}

					@apply rounded-sm border-none py-xs;

					&:has(input:checked) {
						background-color: rgba(0, 0, 0, 0);
						border: 1px solid rgba(0, 0, 0, 1);
					}

					img {
						display: block;
						aspect-ratio: 1;
						object-fit: contain;
					}
				}

				&__button-text {
					display: none;
				}
			}
		}

		&:not(.product-form__option--color) {
			.field__button {
				&.unavailable {
					background-color: rgba(0, 0, 0, 0.25);
					color: #FFF;
				}
			}
		}
	}

	&__option-label-wrapper {
		.type-item {
			@apply lg:text-lg;
			font-family: 'Roboto Regular';
			font-size: 1rem;
			margin-bottom: 8px;
			font-weight: 600;
		}
	}
}

.product-actions,
.product-form__actions,
.product-actions button {
	width: 100%;
}


.fit-guide {
	@apply gap-y-1;

	&__title {
		@apply uppercase;
		font-family: var(--font-subheading-family);
		font-weight: normal;
		font-size: 1rem;
		letter-spacing: 0.125rem;
	}
}



.modal {
	&--right {
		@apply lg:top-4xl;
	}

	&--underlay {
		&.modal--right {
			@apply top-0 lg:top-4xl lg:w-2/5 bg-transparent;
			padding: 1rem !important;
			overflow-y: hidden;
			box-shadow: none;

			@media(max-width:1024px) {
				bottom: 5rem;
				left: 50%;
				transform: translateX(-50%);
				padding: 1rem 0.7rem !important;
			}

			@media(min-width:1025px) {
				height: calc(100dvh - 4.25rem);
			}

			&>div {
				@apply bg-white rounded-md w-full flex flex-col;
				height: 100%;
				overflow-y: hidden;

				&:not(:has(main)) {
					footer {
						position: absolute;
					}
				}
			}

			&>* {
				position: relative;
			}
		}
	}
}

.search {
	&-results {
		header {
			order: 2;
			display: none;
		}

		main {
			order: 4;
			height: 100%;
			overflow-y: auto;
		}

		footer {
			order: 10;
			position: sticky;
			width: 100%;

			@media(max-width:1024px) {
				position: sticky;
			}

			bottom:0;

			@media(min-width:1025px) {
				bottom: auto;
				top: 0;
				order: 1;
			}
		}

		form {
			bottom: 0;

			.field {
				@apply relative items-center gap-0 px-lg;
				flex-direction: row;
				margin-bottom: 0 !important;
				background: #EEE;

				label {
					visibility: hidden;
					width: 0px;
				}

				input {
					background: #EEE;
					padding: 1rem;
					font-size: 16px;
					@apply rounded-sm w-full;

					&:focus {
						outline: none;
						background: #EEE;

						box-shadow: none;
					}
				}

				.icon {}
			}
		}

		.search-results__type-label {
			font-family: var(--font-heading-family);
			@apply p-md text-lg border-b m-0 leading-none pt-xl;
		}

		.search-results__type--products {
			.search-results__type-label {
				display: none;
			}
		}

		&__item {
			border-bottom: 1px solid rgba(0, 0, 0, 0.1);

			&>a {
				@apply p-sm gap-sm;
			}

			.search-result-item {
				&__title {
					font-family: var(--font-heading-family);
				}

				&__type,
				&__sku {
					@apply text-xs;
					color: #999;
				}
			}

			img {
				width: 100px;
				height: 100px;
			}
		}
	}
}


.empty\:hidden {
	&:empty {
		display: none;
	}
}


.modal--quickadd {

	@media(max-width:1024px) {
		top: auto;
		bottom: 0;
	}
}

.product-item__quick-add {
	@media(max-width:1024px) {
		top: auto !important;
		bottom: -15px !important;
		right: -4px !important;
		left: auto !important;
		width: 2rem !important;
		height: 2rem !important;
		border-radius: 100% !important;
		overflow: visible !important;
		padding: 0 !important;
		margin: 0 !important;
		background: var(--background-color) !important;
		background: #f7f7f7 !important;
		clip-path: none !important;

		.button__icon--leading,
		.button__text {
			display: none !important;
		}

		.icon-quick-add {
			display: block !important;
			width: 2rem !important;
			height: 2rem !important;
			position: absolute;
			top: 0;
			left: 2px;
		}
	}

	/*
	@media(max-width:1024px){
		top:auto!important;
		bottom:-8.5px!important;
		left:50%!important;
		transform:translateX(-50%);
		width:auto!important;
		height:auto!important;
		overflow:visible!important;
		padding:0 0.25rem!important;
		margin:0!important;
		background:var(--background-color)!important;
		.button__icon--trailing.icon-quick-add {
			display:none!important;
		}
		.icon {
			height:auto!important;
		}
		.icon-quick-add {
			display:none!important;
			position:absolute;
			top:0;
			left:2px;
		}
	}
	*/
}

.product-item__quick-add--full {
	@media(max-width:1024px) {
		opacity: 0;
		inset: 0;
		top: 0;
		left: 0 !important;
		right: 0 !important;
		width: 100% !important;
		bottom: 0 !important;
		height: 100% !important;
	}
}


.collection {


	&__sidebar {

		background: transparent;


		@media(min-width:1025px) {
			position: sticky;
			top: 85px;
			max-height: calc(100vh - 85px);
			overflow-y: auto;
			z-index: 10;
		}
	}

	&__sidebar-header {
		display: none;
	}

	&__sidebar-body {
		height: 100%;
		@apply justify-end lg:justify-start bg-white lg:bg-transparent;
	}

	.desk-clear-all {
		@apply max-lg:hidden lg:px-2xl lg:pt-lg text-xs font-body;
	}
}



.collection__sidebar:not([open]) {
	width: 1px;
	overflow: visible;
	display: none;
}

.collection--filter .collection__sidebar:not([open]) {
	display: block;
	width: 30%;
	overflow: visible;
}

.collection-filters {
	@apply p-lg lg:p-lg flex flex-col lg:grid lg:grid-cols-1 gap-md bg-white mt-sm rounded-sm;

	max-height: 100%;

	@media(min-width:1025px) {
		max-height: calc(100% - 2rem);
	}

	overflow: auto;

	&__accordion {
		@apply lg:px-lg;
	}

	&__accordion-header {
		@apply lg:pt-lg pb-sm lg:pb-sm border-b border-black;
	}

	&__accordion-title {
		font-family: var(--font-body-family);
		font-size: 1.25rem;
	}

	&__content {
		@apply py-lg lg:p-lg;
	}

  .field__button {
    height: auto;
  }
}



/* Reviews.io styling */
.shopify-app-block .ElementsWidget-prefix .ElementsWidget {
	--base-font-size: 16px;
	--common-button-font-family: inherit;
	--common-button-font-size: 16px;
	--common-button-font-weight: 500;
	--common-button-letter-spacing: 0;
	--common-button-text-transform: none;
	--common-button-vertical-padding: 10px;
	--common-button-horizontal-padding: 20px;
	--common-button-border-width: 2px;
	--common-button-border-radius: 0px;
	--primary-button-bg-color: #3f4b40;
	--primary-button-border-color: rgba(0, 0, 0, 0);
	--primary-button-text-color: #ffffff;
	--secondary-button-bg-color: ;
	--secondary-button-border-color: rgba(0, 0, 0, 0);
	--secondary-button-text-color: #000000;
	--common-star-color: #3f4b40;
	--common-star-disabled-color: rgba(0, 0, 0, 0.25);
	--medium-star-size: 22px;
	--small-star-size: 19px;
	--heading-text-color: #000000;
	--heading-text-font-weight: 600;
	/*
  --heading-text-font-family: Helvetica, 'Helvetica Neue', Arial, 'Lucida Grande', sans-serif;
  */
	--heading-text-line-height: 1.4;
	--heading-text-letter-spacing: 0;
	--heading-text-transform: none;
	--body-text-color: #000000;
	--body-text-font-weight: 400;
	--body-text-font-family: inherit;
	--body-text-line-height: 1.4;
	--body-text-letter-spacing: 0;
	--body-text-transform: none;
	--inputfield-text-font-family: inherit;
	--input-text-font-size: 14px;
	--inputfield-text-font-weight: 400;
	--inputfield-text-color: #0E1311;
	--inputfield-border-color: rgba(0, 0, 0, 0.2);
	--inputfield-background-color: transparent;
	--inputfield-border-width: 1px;
	--inputfield-border-radius: 0px;
	--common-border-color: #000000;
	--common-border-width: 1px;
	--common-sidebar-width: 190px;
	--slider-indicator-bg-color: rgba(0, 0, 0, 0.1);
	--slider-indicator-button-color: #3f4b40;
	--slider-indicator-width: 190px;
	--badge-icon-color: #3f4b40;
	--badge-icon-font-size: inherit;
	--badge-text-color: #3f4b40;
	--badge-text-font-size: inherit;
	--badge-text-letter-spacing: inherit;
	--badge-text-transform: inherit;
	--author-font-size: inherit;
	--author-text-transform: none;
	--avatar-thumbnail-size: 60px;
	--avatar-thumbnail-border-radius: 100px;
	--avatar-thumbnail-text-color: #3f4b40;
	--avatar-thumbnail-bg-color: rgba(0, 0, 0, 0.1);
	--photo-video-thumbnail-size: 80px;
	--photo-video-thumbnail-border-radius: 0px;
	--mediaslider-scroll-button-icon-color: #0E1311;
	--mediaslider-scroll-button-bg-color: rgba(255, 255, 255, 0.85);
	--mediaslider-overlay-text-color: #ffffff;
	--mediaslider-overlay-bg-color: rgba(0, 0, 0, 0.8);
	--mediaslider-item-size: 110px;
	--pagination-tab-text-color: #3f4b40;
	--pagination-tab-text-transform: none;
	--pagination-tab-text-letter-spacing: 0;
	--pagination-tab-text-font-size: 16px;
	--pagination-tab-text-font-weight: 600;
	--pagination-tab-active-text-color: #3f4b40;
	--pagination-tab-active-text-font-weight: 600;
	--pagination-tab-active-border-color: #3f4b40;
	--pagination-tab-border-width: 3px;

	.R-Button {
		text-transform: uppercase;
		letter-spacing: 1px;
		font-weight: normal;
		font-size: 1rem;
		border-radius: 3px;
	}

	.R-PlatformLogo {
		display: none !important;
	}
}


.modal.modal--quickadd {
	@apply fixed bottom-0 lg:left-auto lg:right-0 w-full lg:w-2/5 p-0 rounded-lg overflow-auto;
	z-index: 60;
	background: #f7f7f7;

	@media(min-width:1025px) {
		right: 1rem;
		bottom: 1rem;
		width: calc(40% - 2rem);
	}

	.product-media-object,
	.product-media-object img,
	video {
		aspect-ratio: 1 / 1;
		object-fit: cover;
	}

	.quickadd {
		&__close {
			display: none;
		}

		&__container {
			@apply flex flex-col gap-md;
		}

		&__product-option-title {
			@apply lg:text-lg;
			font-family: 'Roboto Regular';
			font-size: 1rem;
			margin-bottom: 8px;
			margin-top: 0;
			font-weight: 600;
		}

		&__product-info {
			@apply px-lg;
		}

		&__product-siblings {
			@apply px-lg;

			&>a {
				max-width: 4rem;
				@apply flex items-center justify-center rounded;

				&.selected {
					border: 1px solid black;
				}
			}
		}

		&__product-options {
			@apply px-lg;
		}

		.swiper-slide--description {
			display: flex;
		}

		&__description {
			height: 100%;
			aspect-ratio: 1 / 1;
			overflow-y: auto;

			&>div {
				@apply flex flex-col gap-sm py-lg;
			}

			h4,
			h5 {
				margin: 0;
			}

			hr {
				margin: 0;
				width: 75%;
				border-color: #000;
			}
		}

		&__footer {
			@apply gap-sm p-sm;
		}

		&__footer-close {
			svg {
				@apply w-full;
			}
		}
	}


	.field__buttons {
		@media(max-width:1024px) {
			display: flex;
			flex-wrap: nowrap;
			width: 100%;

			&:has(:nth-child(5)) {
				margin-right: -1rem;
				width: calc(100% + 1.25rem);

				&> :last-child {
					margin-right: 1.25rem;
				}
			}

			overflow-x:auto;

			&>* {
				flex-shrink: 1;
				flex-grow: 0;
				min-width: 20%;
			}
		}

		.field__button {
			&.selected {
				background: #000;
				color: #FFF;
			}

			&.unavailable {
				background-color: rgba(0, 0, 0, 0.25);
				color: #FFF;
			}
		}
	}

	.quick-add__img-container {
		display: none;
	}
}

.field__button .sr-only-hide[type="checkbox"]:focus + span, .field__button .sr-only-hide[type="checkbox"]:focus + span{
	outline: #381300 solid 2px;
}

.field__color .sr-only-hide[type="checkbox"]:focus + span, .field__color .sr-only-hide[type="checkbox"]:focus + span{
	outline: #381300 solid 2px;
}

.field__color .sr-only-hide[type="checkbox"],.field__button .sr-only-hide[type="checkbox"]{
	width: 0px;
	height: 0px;
	padding: 0px;
	margin: 0px;
	opacity: 0;
	border:none;
	z-index:-1 !important;
	appearance: none !important;
}


.footer {
	&-menus {
		&>ul>li:first-child>a {
			font-size: 1.5rem;
			margin-bottom: 0.5rem;
		}
	}
}

.navigation-container {
	@media (min-width: 1024px) {
		max-width: max-content;
	}
}

.navigation-container__buttons-desktop button {
	@media (min-width: 1024px) {
		padding: 1rem;
	}

	@media (min-width: 1024px) and (max-width: 1285px) {
		font-size: .9rem;
	}

	@media (min-width: 1024px) and (max-width: 1150px) {
		font-size: 0.7rem;
	}
}

.logo-container {
	@media (min-width: 1024px) and (max-width: 1260px) {
		position: relative;
		right: 5rem;
	}

	@media (min-width: 1261px) and (max-width: 1285px) {
		position: relative;
		right: 3rem;
	}

	@media (min-width: 1286px) and (max-width: 1450px) {
		position: relative;
		right: 4rem;
	}
}

.content-item__text-stack.same_height_review {
    min-height: 108px;
}

@media (min-width: 1024px) {
	.product-essentials__icons {
		grid-template-columns: repeat(auto-fit, minmax(15rem, 1fr));
	}
}

.section--content-carousel, .toggle {
  .tabs, .field__toggle {
    @apply rounded-md bg-tertiary flex max-lg:mt-2.5;

    span,
    a,
    button {
      @apply text-base text-body rounded-md mx-0;
    }
    span,
    button.active,
    label input:checked+.toggle__label {
      @apply items-center flex bg-primary text-white py-3.5 px-6 border-none text-base;
    }
    a,
    a:not(.active),button:not(.active),
    label input:not(:checked)+.toggle__label {
      @apply inline py-3.5 px-6 border-none text-base text-body rounded-md;
    }
    label input:checked~.toggle__label {
      @apply rounded-md;
    }
    label {
      span.toggle__label--unselected {
      @apply hidden;
      }
    }
  }
}
