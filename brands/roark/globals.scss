@import '../shared/globals.scss';



/* Color Guidelines
  ==========================================================================
  primary: Highest level user attention.
  secondary: Second highest level user attention.
  tertiary: Third highest level user attention.
  light: Most prominent light background color. Must be able to overlay on top of dark.
  dark: Most prominent dark background color. Must be able to overlay on top of light.
  pop: Usage examples are badges.
  highlight: Think about this as using a highlighter pen.
  body: Most common text color.
  header: Most common text color for headers.
*/

:root {

  --color-primary: #3F4B40;
  --color-secondary: #fff;
  --color-tertiary: #f5f2ef;
  --color-light: #f9f9f9;
  --color-dark: #000;
  --color-pop: #F05931;
  --color-highlight: #fbac4c;
  --color-body: #000;

  /*

  --swiper-theme-color: #000;

  --header-height:76px;
  --preheader-height: 34px;
  --header-height-half:38px;
  --unscrolled-header-height: 100px;
  --scrolled-header-height: 76px;

  @media screen and (max-width: 1024px) {
    --header-height:100px;
    --preheader-height: 32px;
    --header-height-half: 34px;
    --unscrolled-header-height: 100px;
    --scrolled-header-height: 68px;
  }

  --font-body-weight: 400;
  --font-body-style: normal;
  --font-heading-weight: normal;
  --font-heading-style: normal;
  --font-subheading-weight: 600;
  --font-subheading-style: normal;


  --header-height:48px;

  */
}


.top-main {
  top:var(--header-offset)
}
@media screen and (min-width: 1025px) {
  .lg\:top-main {
    top:var(--header-offset);
  }
}

.rte a {
  @apply underline text-body;
}

 

   /* Type Styles
========================================================================== 
 Guidelines
==========================================================================
  **Type Styles**

  primary: Primary Headline or Title type.
  secondary: Commonly used as Subtitle type, compliments Primary.
  tertiary: Third highest level user attention, smaller but stylistically similar to primary.

  **Type Layouts / Spacing**

  page: Most common text color.
  section: Most common text style for headers.
  article: Most common text style for headers.

*/

h1,h2,h3,h4,h5 { font-weight:normal;}

.type {

  text-wrap: balance;

  &-hero {

    @apply font-heading font-normal;
    font-weight:normal;
    font-size: 4rem;
    line-height: 1;
    @media screen and (min-width: 1025px) {
      font-size: 6em;
    }
    &.type--sm {
      font-size: 2.488rem;
    }
    &.type--lg {
      font-size: 5.16rem;
    }
  }
  &-headline {
    @apply font-heading font-normal;
    font-weight:normal;
    font-size: 2.75rem;
    line-height: 1;
    @media screen and (min-width: 1025px) {
      font-size: 3.5rem;
    }
    &.type--sm {
      font-size: 2.488rem;
    }
    &.type--lg {
      font-size: 5.16rem;
    }
  }
  &-subline {
    font-size: 18px;
    letter-spacing: 0.4px;
    @apply font-body;
    &.type--sm {
      font-size: 16px;
    }
    &.type--lg {
      font-size: 22px;
    }
  }
  &-item {
    font-size: 1rem;
    @media screen and (min-width: 1025px) {
      font-size: 1.5rem;
    }
    @apply font-heading font-normal;
    font-weight:normal;
    &.type--sm {
      font-size: 0.75rem;
      @media screen and (min-width: 1025px) {
        font-size: 1rem;
      }
    }
    &.type--lg {
      font-size: 1.5rem;
      @media screen and (min-width: 1025px) {
        font-size: 1.75rem;
      }
    }
  }
  
  &-section {
    font-size: 2rem;
    @media screen and (min-width: 1025px) {
      font-size: 2.5rem;
    }
    line-height: 1;
    @apply font-heading font-normal;
    &.type--sm {
      font-size: 18px;
    }
    &.type--lg {
      font-size: 26px;
    }
  }
  &-eyebrow {
    @apply uppercase;
    font-family: var(--font-subheading-family);
    font-weight:normal;
    font-size: 1rem;
    letter-spacing: 0.125rem;
    @media screen and (min-width: 1025px) {
      font-size: 1.25rem;
      letter-spacing: 0.275rem;
    }

    line-height: 1;
    &.type--sm {
      font-size: 12px;
    }
    &.type--lg {
      font-size: 17px;
    }
  }
  &-body {
    @apply font-body;
    line-height: 1.5;
    font-size: 16px;
    &.type--sm {
      font-size: 14px;
    }
    &.type--lg {
      font-size: 18px;
    }
  }
  &-nav-link {
    @apply font-heading font-normal text-body text-[16px] leading-6 tracking-[0.4px];
  }
  &-micro { 
    font-size: 11px;
    letter-spacing: .5px;
    line-height: 18px;
    @apply font-body capitalize;
  }
}

p {
  font-family: var(--font-body-family);
  font-weight: var(--font-body-weight);
  font-weight: var(--font-body-style);
  strong {
    font-family: var(--font-bold);
  }
  margin-bottom:unset;
}


/* Buttons and Controls
========================================================================== */

.button, .btn, .announcement {

  @apply whitespace-nowrap items-center rounded-sm tracking-widest text-md justify-center opacity-100 overflow-hidden py-sm lg:py-md px-lg relative uppercase cursor-pointer gap-sm;

  :not(.hidden){
    @apply inline-flex;
  }

  &--narrow {
    @apply py-2xs lg:py-sm  
  }

  .icon {
    margin:0 !important;
    width:18px;
  }

  &:has(.icon){
    @apply gap-xl;
    &.button--tight {
      @apply gap-xs;
    }
    &.gap-0 {
      gap:0;
    }
    &.gap-1 {
      gap:0.25rem;
    }
  }

  &:has(.button__text ~ .button__icon--trailing) {
    padding-right:0.875rem;
    clip-path: polygon(0 0, calc(100% - 48px) 0, calc(100% - 44px) 4px, calc(100% - 40px) 0, 100% 0, 100% 100%, calc(100% - 40px) 100%, calc(100% - 44px) calc(100% - 4px), calc(100% - 48px) 100%, 0 100%);
  }

  &:has(.button__icon--leading ~ .button__text) {
    padding-left:0.875rem;
    clip-path: polygon(0 0, 40px 0, 44px 4px, 48px 0, 100% 0, 100% 100%, 48px 100%, 44px calc(100% - 4px), 40px 100%, 0 100%);
  }

  &--primary { @apply lg:py-sm bg-primary border-primary text-white;}
  &--secondary { @apply lg:py-sm border-secondary bg-secondary text-black; }
  
  &--tertiary, &--emphasis { 
    /* @apply flex flex-col justify-center items-center rounded-full border border-black; */
    @apply flex justify-center items-center rounded-full border border-black;
    flex-direction:column; 
    padding:0;
    padding-right:1rem!important;
    padding-left:1rem!important;
    gap:0!important;
    white-space:wrap;
    text-align:center;
    width:8rem;
    height:8rem;
    font-size:0.75rem;
    @media screen and (min-width: 1025px) {
      width:10rem;
      height:10rem;
      font-size:1rem;
    }
    clip-path:none;
    &:has(.button__icon--leading ~ .button__text) { clip-path:none; }
    &:has(.button__text ~ .button__icon--trailing) { clip-path:none; }

  }
  &--emphasis {
    @apply text-white border-white;
  }

  &--light { 
    &:before {
      @apply bg-black text-white lg:py-xs ;
    }
  }
  &--dark { 
    @apply bg-black text-white lg:py-xs ;
  }
  &--pop { 
    background-color:var(--color-pop);
    color:#FFF;
  }
  &--light, &--dark, &--pop, &--highlight {
    &:before {
      border-radius: 50%;
      content: '';
      height: 400px;
      left: 50%;
      pointer-events: none;
      position: absolute;
      top: 50%;
      transform: translate(-50%, -50%) scale(0);
      transform-origin: center center;
      transition: transform 0.25s ease, opacity 0.25s ease-out, color 0.1s ease;
      width: 400px;
      opacity: 0;
      z-index: 1;
      will-change: transform, opacity, color;
    }
    &:hover , a:hover & {
    }
  }
  &--dark {
    &:hover, a:hover & {
    
    }
  }
  &--highlight { 
    @apply bg-transparent border-white text-white hover:text-primary; 
    a:hover & {
      @apply text-primary;
    }
    &:before {
      @apply bg-white;
    }
  }
  &--link { @apply font-heading font-normal tracking-[0.4px] capitalize bg-transparent text-[13px] text-primary p-0 border-0 underline; }
  &--w-icon { 
    @apply flex justify-center items-center border-primary bg-primary text-white gap-3;
    &:before {
      @apply bg-secondary;
    }
    .icon {
      @apply text-white;
    }
  }
  &--simple {
    // font-family: var(--font-medium);
		@apply tracking-wide text-primary text-sm gap-1 capitalize border-transparent bg-transparent px-0;
    .icon {
      @apply text-current;
    }
  }
  &--disabled, &:disabled {
    @apply bg-dark border-[#eee] text-white cursor-not-allowed;
    backdrop-filter:blur(12px);
    background-color:rgba(0,0,0,0.25);
  }
  &--action { 
    @apply flex justify-center items-center border-black bg-transparent rounded-full text-[13px] tracking-normal capitalize font-normal h-[35px];
    font-family: var(--font-regular);
    &:before {
      @apply bg-secondary;
    }
    .icon {
      @apply text-black mr-0 w-4 h-3;
    }
  }
  &--large {
    @media screen and (min-width: 1025px) {
      height:56px; padding:0 50px; border-width:2px;
    }
  }
  > span {
    @apply relative z-10;
  }

  &--micro-link {
    font-size: 13px;
    letter-spacing: -.1px;
    border-width: 0;
    padding:0;
    margin:0;
    height: auto;
    @apply font-body text-black capitalize underline;
  }
  &--icon {
    @apply py-md px-md;
    svg {
      height:22px;
    }
  }

  &--primary-hover { 
    @apply bg-secondary border-secondary text-black;
    &:hover , a:hover & {
      @apply opacity-70;
    }
  }

  &--secondary-hover { 
    @apply bg-primary border-primary text-white;
    &:hover , a:hover & {
      @apply opacity-70;
    }
  }

  &--tertiary-hover { 
    @apply bg-tertiary border-tertiary text-black;
    &:hover , a:hover & {
      @apply opacity-70;
    }
  }
}

.announcement {
  @apply lg:px-2xl max-lg:py-[6px] max-lg:text-xs max-lg:text-center;
}

.wishlist-toggle {
	@apply pl-3;

	.icon {
		@apply stroke-primary;

		&.active {
			@apply fill-primary;
		}
	}
}

.product-item {
  &__images {
    aspect-ratio:619/700; 
  }
  &__image {
    aspect-ratio:619/700; 
  }
  &__meta {
    @apply flex flex-col pt-0 px-sm;
    padding-top:0.25rem !important;
    padding-bottom:0.25rem !important;
    gap:3px;
    @media(min-width:1025px){
      gap:6px;
      margin-bottom:1rem;
    }
    position:relative;
    padding-bottom:0!important;
    .review-snippet {
      @apply right-sm;
      position:absolute;
      bottom:0;
      top:auto;
      margin-top:0;
    }
  }

  &__title-price-wrap {
    order:1!important;
  }

  &__title {
    order:1!important;
    flex-basis:100% !important;
    font-family: var(--font-regular);
    font-size:0.875rem!important;
    @media(min-width:1025px){
      font-size:1.125rem!important;
    }
  }

  &__subtitle {
    order:2!important;
    margin-bottom:0!important;
  }

  &__prices {
    order:3!important;
    line-height:1;
  }

  &__quick-add {
    left: 1rem !important;
    right: auto !important;
    bottom: 1rem !important;
    min-width: auto !important;
    padding: 4px 6px !important;
    gap: 6px !important;
    clip-path: polygon(0 0, 20px 0, 24px 4px, 28px 0, 100% 0, 100% 100%, 28px 100%, 24px calc(100% - 4px), 20px 100%, 0 100%) !important;
    z-index:20;
    height:auto;
  }
  .review-snippet {
    margin-top:0 !important;
    .ruk-rating-snippet {
      @apply flex items-center;
      &:not([title*="Stars"]){ display:none}
      i { order:2; }
      span {order:1; margin-right:3px;color:#333; }
    }
  }
  .icon-quick-add {
    display:none !important;
  }
}


/* Forms
========================================================================== */

.field {
  label {
    @apply text-[13px] text-[#736b67] mb-2.5;
  }
  &__input,
  &__textarea,
  &__select {
    @apply border-[#e4e4e4] text-sm;
  }

  &__select {
    @apply rounded-[2px] h-12 py-0 text-sm appearance-none;

    background-position: right 12px center;
    background-image: url(https://olukai.com/cdn/shop/t/405/assets/select-icon.svg?v=43184182532122065261626389908);
    background-repeat: no-repeat;
    cursor: pointer;
  }

  &__toggle {
    @apply bg-tertiary;

    label {
      @apply inline-flex items-center mb-0 whitespace-nowrap;

      input {
        &:not(:checked) {
          & ~ .toggle__label {
            @apply cursor-pointer text-[12px] px-3 py-1 border bg-transparent border-transparent rounded-full text-body;
          }
        }

        &:checked {
          & ~ .toggle__label {
            @apply text-[12px] px-3 py-1 border bg-white border-tertiary text-body rounded-full;
          }
        }
      }
    }
  }

  &__image {
    @apply border rounded p-[15px] relative flex flex-col items-center justify-end border-tertiary;
    font-weight: 700;
    line-height: 18px;
    letter-spacing: 1.5px;
    input {
      @apply absolute inset-0 opacity-0;
    }
    img {
      width: 155px;
      max-width: 100%;
    }
    label {
      @apply flex items-center flex-col justify-end text-center uppercase text-[15px] gap-2.5;
    }
    &--horizontal {
      @apply justify-start items-start;
      label {
        @apply flex-row;
        img {
          width: 72px;
        }
      }
    }
    &:hover, &:focus {
      @apply border-primary;
      -webkit-box-shadow: 0 5px 6px rgba(0,0,0,.1);
      box-shadow: 0 5px 6px #0000001a;
    }
    &:has(input:checked) {
      @apply border-2 border-primary;
      background: #f0f8f8;
      -webkit-box-shadow: 0 5px 6px rgba(0,0,0,.1);
      box-shadow: 0 5px 6px #0000001a;
    }
  }
}

.field {
  
  input {
    &[type="checkbox"],
    &[type="radio"] {
      @apply h-[18px] w-[18px];

      & + span {
        @apply capitalize font-body text-[13px];
      }
    }  

    &[type="radio"] {
      &:checked {
        @apply border-0 shadow-[inset_0_0_0_7px] shadow-black;
      }
    }
  }

  &__chip {
    span {
      font-family: var(--font-regular);
      font-weight: 400;
      font-size: 13px;
      text-transform: capitalize;
      line-height: normal;
    }

    &-swatch {
      @apply outline-offset-0 outline-[4px];
    }
    
    &:hover &-swatch {
      @apply outline-primary;
    }

    @apply bg-white text-black rounded-[3px] px-5 py-2.5 font-heading font-normal flex-row items-center;

    &:has(input:checked) {
      @apply bg-black text-white;
    }

    &:has(button) {
      @apply pr-3;
    }

    button {
      @apply pl-2.5;
    }
  }

  &__checkbox {

    span { 
      text-transform: capitalize;
    }
    
    input[type="checkbox"] {
      @apply rounded-none relative rounded-[4px];

      &:checked {
        @apply border-0 bg-black shadow-none;
    
        &:after {
          content: "";
          display: block;
          width: 5px;
          height: 8px;
          border: solid white;
          border-width: 0 1.5px 1.5px 0;
          position: absolute;
          transform: rotate(45deg);
          top: 50%;
          left: 50%;
          translate: -50% -65%;
          background: transparent;
        }
      }
    }
  }
  
  &__color {
    &-swatch {
      @apply outline-offset-[1px] outline-[2px];
    }

    input:checked{
      & + .field__color-swatch{
        @apply outline;
      }
    }

    
    &:hover &-swatch {
      @apply lg:outline-black;
      @media(max-width:1023px){
        outline-style: none;
      }
    }

    span {
      font-family: var(--font-regular);
      font-weight: 400;
      @apply text-dark text-[12px];
    }

    &:has(input:checked),
    &:hover {
      span {
        @apply text-black;
      }
    }
  }

  &__buttons {
    @apply gap-[9px];
  }

  &__button {
    @apply w-full h-full flex-auto py-md;
    border:1px solid rgba(0,0,0,0.1);
    &:has(input:checked) {
      background:#000000;
      color: #ffffff;
    }
    &:hover {
      @media(min-width:1024px){
        border:1px solid rgba(0,0,0,0.3);    
      }
    }

    &-text {
      @apply font-body text-[13px] border-none;
      input:checked ~ & {
      }
    }

    &:hover &-text {
      @apply border-none;
    }
  }

  &-plus-minus {
    @apply flex flex-row items-center;
    input {
      @apply w-12 p-1 text-center;
    }
    button {
      @apply w-8 h-8 flex items-center justify-center bg-white;
    }
    svg { 
      stroke-width: 3px;
    }
  }
}

progress {
  @apply bg-gray-200 w-full;
  height:1px;

  &::-webkit-progress-bar {
    @apply w-full relative;
    height:1px;
  }

  &::-webkit-progress-value {
    @apply bg-black;
    background:black;
    height:1px;
    &:after {
      width:6px;
      height:6px;
      content:'';
      position:absolute;
      right:0px;
      top:-2px;
      background:inherit;
      border-radius:3px;
      display:block;
    }
  }

  &::-moz-progress-bar {
    background:black;
    @apply bg-black relative;
    height:1px;
  }
}

.progress-bar {
  &__label {
    @apply font-body text-dark text-[13px] font-normal text-[#736b67] tracking-normal;
  }
}

.progress {
  height:2px;
  overflow:visible;
  display:flex;
  span {
    display:inline-block;
    padding-bottom:2px;
    background:#000;
    max-width:100%;
    position:relative;
    &:after {
      width:6px;
      height:6px;
      content:'';
      position:absolute;
      right:0px;
      top:-2px;
      background:inherit;
      border-radius:3px;
      display:block;
    }
  }
}


.swiper-pagination-progressbar-fill {
  transform:scaleX(1)!important;
  max-width:100%;
  min-width:0%;
}

.swiper-pagination-progressbar-fill[style*="scaleX(0.0"] { width:0% }
.swiper-pagination-progressbar-fill[style*="scaleX(0.1"] { width:10% }
.swiper-pagination-progressbar-fill[style*="scaleX(0.16"] { width:15% }
.swiper-pagination-progressbar-fill[style*="scaleX(0.2"] { width:20% }
.swiper-pagination-progressbar-fill[style*="scaleX(0.3"] { width:30% }
.swiper-pagination-progressbar-fill[style*="scaleX(0.33"] { width:33% }
.swiper-pagination-progressbar-fill[style*="scaleX(0.4"] { width:40% }
.swiper-pagination-progressbar-fill[style*="scaleX(0.5"] { width:50% }
.swiper-pagination-progressbar-fill[style*="scaleX(0.6"] { width:60% }
.swiper-pagination-progressbar-fill[style*="scaleX(0.66"] { width:66% }
.swiper-pagination-progressbar-fill[style*="scaleX(0.7"] { width:70% }
.swiper-pagination-progressbar-fill[style*="scaleX(0.75"] { width:75% }
.swiper-pagination-progressbar-fill[style*="scaleX(0.8"] { width:80% }
.swiper-pagination-progressbar-fill[style*="scaleX(0.9"] { width:90% }
.swiper-pagination-progressbar-fill[style*="scaleX(1)"] { width:100% }

.swiper-pagination--progressbar {
  width:90%;
  left:5%;
  margin:0;
  height:2px;
  background:rgba(0,0,0,0.2);
  overflow:visible;
  position:relative;
  top: -0.75rem;
  z-index:2;
  .swiper-pagination-progressbar-fill {
    height:2px;
    transform-origin:left;
    background:#000;
    // display:inline-block;
    position:absolute;
    top:0;
    left:0;
    &:after {
      width:6px;
      height:6px;
      content:'';
      position:absolute;
      right:0px;
      top:-2px;
      background:inherit;
      border-radius:3px;
      display:block;
    }
  }

}


.frame-carousel {
  .swiper-pagination {
    &.swiper-pagination--progressbar {
      position: absolute;
      bottom: -0.75rem;
      top: unset;
    }
    &.swiper-pagination--bullets {
      position: absolute;
      bottom: -1.75rem;
      top: unset;
      @apply inset-x-0;
    }
  }
}

.swiper {
  .btn-control {
    box-shadow: 0 0 2rem rgba(0, 0, 0, 0.125) important;
    border-radius: 1rem!important;
    right: 1rem;
    svg {
      width:18px;
    }
    &.swiper-button-prev {
      .icon {
        margin-left:-3px;
      }
    }
    &.swiper-button-next {
      .icon {
        margin-left:1px;
      }
    }
  }

}

.dropdown-menu {
	@apply border border-solid rounded-sm text-current border-[#e4e4e4] text-sm;
	
	&__trigger {
		@apply rounded-[2px] h-8 text-sm appearance-none py-0 pl-3;

		.dropdown-menu__label {
			@apply text-xs text-[#797979];

			&:after {
				content: ':'
			}
		}

		.dropdown-menu__value {
			@apply text-xs pl-2;
		
		}
		
		&-icon {
			@apply ml-3 pl-3 pr-3 border-l text-xs;

			.icon {
				@apply w-3;
				fill: #797979;
				stroke: #797979;
				stroke-linecap: butt;
			}
		}
	}

	&__menu {
		@apply rounded-md mt-2 py-2;
		&-item {
			@apply text-xs px-4 py-1.5 hover:bg-light hover:bg-black hover:text-white;

			button {
				@apply text-left;
			}
		}
	}
}


/* Accordion
========================================================================== */

.list {
  .accordion {
    &:last-child {
      @apply border-b border-[#e4e4e4];
    } 
  }
}

.accordion {
  &:last-child {
    @apply border-b border-[#e4e4e4];
  }
  .accordion-title {
    @apply bg-transparent border border-[#e4e4e4] border-l-0 border-b-0 border-r-0 text-left cursor-pointer h-[60x] leading-[60px];
    font-family: var(--font-medium);
    span:first-child {}
    .accordion-control {
      @apply bg-transparent text-dark border-0;
      .icon {}
    }
  }
  .accordion-panel {
    @apply text-sm py-4 px-0 border-0;
    > div {}
    ul {}
    p:last-child {}
  }
  &-control {
    &:first-of-type {
      display:block;
    }
    &:last-of-type {
      display:none;
    }
    [open] > summary & {
      &:first-of-type {
        display:none;
      }
      &:last-of-type {
        display:block;
      } 
    }
  }
}

/* Cart Upsell item
========================================================================== */
.upsell-item {
  @apply text-sm;
  background-color:#f9f9f9;
  @apply p-md;
  &__media {
    width:6rem;
    height:6rem;
  }
  button[disabled] {
    background-color: #64646459;
    color: #fff;
    cursor: not-allowed;
  }
  &__header {
    @apply flex justify-between w-full items-start;
    * {
      font-weight:400;
    }
  }
  &__title {
    font-size:14px;
    margin:0;
    font-family: 'Roboto Medium', sans-serif;
  }
  &__subtitle {
    font-size:12px;
    margin:0px 0;
    color:#797979;
    font-family: 'Roboto Regular', sans-serif;
  }
  &__prices {
    line-height:1;
    margin:0;
    .type-item {
      font-size: 14px;
    }
  }
  &__body {
    @apply w-full flex flex-col pl-5;
    
  }
  
  &__actions {
    @apply flex justify-between w-full;

    select {
      @apply border p-2 bg-transparent border-black rounded-md lg:mr-1.5 text-sm leading-none;
      line-height:1;
    }

    button {
      @apply p-sm ml-auto;      
      border: 0;
      border-radius: 8px;
      svg {
        width:16px;
        stroke-width:3px;
      }
    }
  }
  

}

/* Micro Upsell
========================================================================== */
.micro-upsell {
  &__item {
    @apply flex-shrink-0 flex flex-col justify-between;

    .product-item {
      @apply w-full;
    }

    .product-item__actions button,
    .product-item__quick-add,
    .product-item:hover .product-item__quick-add {
      display: none;
    }

    .product-item__actions {
      opacity: 0.25;
      pointer-events: none;
    }
    &:has(input:checked) .product-item__actions {
      opacity: 1;
      pointer-events: auto;
    }

    .product-item__title-price-wrap:hover {
      @apply underline;
    }

    .field {
      @apply mb-0;
    }

    select {
      @apply border p-2 bg-transparent border-black rounded-md lg:mr-1.5 text-sm leading-none;
    }
  }
  
  &__separator {
    @apply flex-shrink-0;
  }
}

/* Tabs
========================================================================== */

.tabs {
  ul:not(.start) {
    @apply pl-0 flex justify-end w-full;
    li {
      @apply ml-5;
    }
  }

  ul.start {
    @apply space-x-4;
  }

  .tab-title {
    @apply btn btn--tertiary;
    @apply border-[#d3c7c1] bg-transparent text-[13px] font-body capitalize tracking-normal text-[#381300]; 
    &.active {
      @apply btn--primary; 
    }
  }
  
  .tab-panel {
    > div {
      @apply py-6;
    }
  }
}

/* Pagination
========================================================================== */

.pagination--page {
  @apply bg-transparent;
  li {
    @apply flex justify-start items-start;
    a:not(.pp-control),
    button:not(.pp-control), > span {
      @apply block px-2 py-0.5 pb-1 mx-[5px];
    }
    [aria-current="page"], .active {
      @apply relative block;
      &:after {
        content: "";
        @apply block border-b-[2px] border-primary;
      }
    }
  }
}


/*
.swiper-pagination,
.pagination {
  @apply w-full;

  .pagination-bullet, .swiper-pagination-bullet {
    @apply bg-tertiary flex-1 rounded-none;

    &.active, &.swiper-pagination-bullet-active {
      @apply scale-100 bg-black;
    }
  }
  &.swiper-pagination-bullets.swiper-pagination-horizontal {
    @apply relative;
  }

}
*/


.btn-control {
  &.swiper-button-prev,
  &.swiper-button-next {
    @apply bg-white rounded-full h-10 w-10 shadow-md text-inherit flex p-0;
  }
}

/* Mini Form
========================================================================== */

details.no-close[open] {
  summary { pointer-events: none; }
}

.mini-form {
  summary {
    & > span {
      width: 100%;
      transition: width 400ms ease-out, opacity 400ms ease-out;
      z-index: 10;
    }
   
  }
  & > div {
    display:grid;
    transition:max-height 400ms ease-out;
    max-height:0px;
  }
  &[open] {
    & > div {
      max-height:200px;
    }
  }
  form {
    input {
      font-size:16px;
      padding:6px 12px;
      @apply rounded-r-none rounded-l-[3px] h-full border;
      min-height:52px;
    }
    button {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      @apply lg:h-auto relative z-20;
    }
    input:not([valid="true"]) + button {
      cursor:not-allowed;
    }
  }
  &[open] {
    summary {
      span,
      span svg {
        opacity:0;
        width:0;
        @apply bg-dark text-dark;
      }
    }
    form {
      margin-top:-56px;
      @media only screen and (min-width: 1024px) {
        margin-top:-39px;
      }
    }
  }
  &__info {
    p {
      text-align: left;
      margin:0.75rem 0 ;
      &:first-child {
        font-family: var(--font-bold);
      }
      font-size:12px;
      color:#444;
    }
  }
  .button {
    @apply max-lg:h-14 gap-3;
  }
  &__success {
    visibility:hidden;
    width:0;
    height:0;
    display:none;
    opacity:0;
    transition:opacity 300ms ease;
  }
  &--submitted {
    summary {
      pointer-events:none;
      span {
        display:none;
      }
    }
    .mini-form__success {
      visibility:visible;
      display:block;
      width:100%;
      height:auto;
      opacity:1;
    }
  }
}

.icon {
  stroke-linecap: square;
  stroke-linejoin: initial;
}

/* Article Item
========================================================================== */

.content-item--article {	
	.type-item {
		font-size: clamp(0.875rem, 3.5vw, 1.125rem);
	}

	.content-item {
		&__meta {
			@apply mb-2.5;
		}

		&__excerpt {
			@apply text-[15px] hidden lg:block;
		}

		&__content {
			@apply px-5 justify-start lg:justify-between;
		}

		&__media-container {
			@apply max-w-[40%] lg:min-w-[50%];
		}

		&__media {
			@apply aspect-1 xl:aspect-[4/3] w-full;
		}

		&__button-set {
			@apply mt-2;

			button {
				@media only screen and (max-width: 1023px) {
					@apply button--link;
				}
				@media only screen and (min-width: 1024px) {
					@apply button--dark;
				}
			}			
		}
	}

  &.content-item__article--vertical {
		.content-item__meta {
			@apply uppercase;
		}
		.content-item__button-set {
			@apply hidden;
		}
		.content-item__media-container {
			@apply mb-4;
		}
		.content-item__text-stack {
			@apply gap-y-2;
		}
		.content-item__media {
			@apply aspect-[3/2];
		}
	}
}

.article-item {
  &__category,
  &__date {
    @apply text-[11px];
    color: #736b67;
  }
  &__category {
    @apply mr-1.5 pr-1.5 border-[#736b67] border-r;
  }
}

/* Reviews
========================================================================== */
:root {
  --reviews-star-color: var(--color-primary);
  --reviews-text-color:  var(--color-dark);
}
.review-snippet {
	.ruk-rating-snippet {
		.ruk-rating-snippet-count {
			@apply font-body text-[10px] ml-1.5;
		}
		i {
			@apply text-[13px];
		}
	}
}

/* Tooltips / Hotspots
========================================================================== */
.tooltip {
  &__price {
    @apply font-body text-[13px] text-[#736b67]
  }
  &__quickview {
    @apply button--link;
  }
}

/* Tables
========================================================================== */
table {
  border-top:1px solid #EEE;
  border-bottom:1px solid #EEE;
  td {
    border-bottom:1px solid #EEE;
    border-right:1px solid #EEE;
    text-align:center;
    padding:2rem;
  }
  &.legend {
    position:sticky;
    left:0;
    td {
      background:#FFF;
      border-right:1px solid #EEE;
      text-align:right;
      
    }
  }
  tr:nth-of-type(odd) {
    background:#f7f7f7;
  }
}


.link-list {
  &--panels {
    @apply flex-col w-full justify-center;
    font-size:1.5rem;
    
    details { 
      & > summary {
        @apply items-center gap-xl;
        & > a {
          pointer-events:none;
          /* remove this to allow direct link to nested parents */
        }
        & > .icon {
          width:18px;
        }
      }
      & > div {
        position:absolute;
        top:0;
        height:100%;
        background-color:#FFF;
        left:100%;
        opacity:0;
        @apply flex flex-col w-full justify-center h-full;
      }
      &[open] {
        & > div {
          left:0;
          opacity:1;
        }
      }
    }
  }
  &__back {
    display: flex;
    padding: 1rem 2rem;
    gap: 1rem;
    align-items: center;
    font-size: 1rem;
    .icon {
      width:18px;
    }
  }
}
.link-list__item--customer-service > details > summary > .icon {
  display:none;
}

@keyframes fadeIn {
  0% {opacity: 0;}
  100% {opacity: 1;}
}
@keyframes fadeOut {
  0% {opacity: 1;}
  100% {opacity: 0;}
}


.content-item {
  &--review {
    @apply flex flex-col relative;
    .review-item {
      &__product {
        flex-basis:100% !important;
        font-family: var(--font-regular);
        font-size:0.875rem!important;
        margin-bottom: 6px;
        @media(min-width:1025px){
          font-size:1.125rem!important;
        }
      }
      &__type {
        @apply font-body text-xs tracking-[0.4px] text-[#77706c] capitalize font-normal mb-[0] mt-[5px] mr-[10px];
      }
/*       &__rating {
        @apply xl:absolute bottom-0 right-0;
      } */
    }
    .content-item {
      &__header {
        @apply pt-xs pb-2xs;
      }
      &__media {
        @apply order-first aspect-[4/3];
      }
      &__content {
        @apply gap-2xs;
      }
      &__text-stack {
        gap:3px;
      }
      &__review {
        font-size:14px;
        text-wrap:pretty;
      }
      &__author {
        font-family: var(--font-body-family);
        font-size: 14px;
        letter-spacing: .4px;
        @apply italic;
      }
    }
    .button {
      background:var(--color-primary);
      color:#FFF;
      padding:0.5rem 2rem;
    }
  }
}

/*  Gift Card Style =====================================  */
.giftcard {
  display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;

  .giftcard__container{
    margin: 50px auto 150px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    max-width: 600px;
    padding: 0 20px;
    width: 100%;
    .giftcard__block--code{
      margin-bottom: 35px !important;
      .icon--logo {
        img{
          margin: 0 auto;
          height: 20.5px;
          width: auto;
        }
      }
      .giftcard__title--page{
        margin: 38px auto 30px;
        font-size: 35px !important;
        font-family: Roboto Regular,Arial,Helvetica,sans-serif;
        letter-spacing: -.01em;
        font-weight: inherit;
        line-height: 1.2285714286;
        width: 100%;
      }
      .giftcard__image-container{
        position: relative;
        .giftcard__title--value{
          font-family: Roboto Regular,Arial,Helvetica,sans-serif;
          font-weight: inherit;
          line-height: 1.2285714286;
          letter-spacing: 0;
          left: 20px;
          top: 20px;
          position: absolute;
          margin: 0;
          font-size: 30px;
          color: #f9f9f9;
        } 
        span.giftcard__code{
          position: absolute;
          left: 50%;
          -webkit-transform: translateX(-50%);
          transform: translate(-50%);
          bottom: 75px;
          width: 80%;
          background: #fff;
          border-radius: 8px;
          p.giftcard__text{
            margin-bottom: 0;
            line-height: 31px;
            letter-spacing: 0;
            font-size: 18px;
            color: #f15d2a;
            font-family: Roboto Regular,Arial,Helvetica,sans-serif;
          }
          span#GiftCardDigits {
            letter-spacing: 0px;
            font-size: 24px;
            font-weight: 700;
            font-family: Roboto Bold,Arial,Helvetica,sans-serif;
            line-height: 31px;
          }
        }     
      }
    }
    .giftcard__block.giftcard__block--left {
      width: 100%;
      p.giftcard__text.giftcard__text--balance {
        font-size: 18px;
        color: #000;
        letter-spacing: 0;
        margin: 0 0 35px;
        font-family: Roboto Regular, Arial, Helvetica, sans-serif;
        line-height: 31px;
        text-align: left;
      }
      .giftcard__buttons {
        gap: 0px;
        align-items: baseline;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        -webkit-box-pack: start;

        a.giftcard__button {
          max-width: 305px;
          height: 55px;
          width: 100%;
          line-height: 55px;
          background-color: #f15d2a;
          color: #fff;
          padding: 0;
          font-size: 13px;
          letter-spacing: 1.3px;
          font-family: Roboto Bold,Arial,Helvetica,sans-serif;
          font-weight: 400;
          text-transform: capitalize;
          -webkit-transition: color .45s cubic-bezier(.785,.135,.15,.86), border .45s cubic-bezier(.785,.135,.15,.86);
          transition: color .45s cubic-bezier(.785,.135,.15,.86), border .45s cubic-bezier(.785,.135,.15,.86);
          z-index: 1;
          -webkit-tap-highlight-color: initial;
          &:hover{
            background-color: #000;
            color: #fff;
          }
        }
        a#PrintGiftCard {
          color: #000;
          font-family: Roboto Regular, Arial, Helvetica, sans-serif;
          font-size: 10px;
          letter-spacing: 1.07px;
          line-height: 18px;
          margin-left: 15px;
          text-decoration: underline;
          text-transform: uppercase;
        }
      }
    }
  }
}

@media only screen and (max-width:1023px) {
  .content-carousel__tabs-mb-0{
    margin: 0.6rem !important;
  } 
}
/* Feature Item
========================================================================== */

.feature-item {
  &__icon {
    width: 32px;
    height: auto;
    aspect-ratio: 1 / 1;
    object-fit: contain;

  }
}

@media only screen and (min-width: 1024px) {
  .tabs-wrapper .tab-content .swiper {
    &:has([slide-visible="true"] .content-carousel__show-all) .swiper-button-next { 
      display: flex;
    }
  }

  .navigation-container{
    gap:0;
  }
  .menu-hover-wrap {
    padding-top: 15px;
  }
  .menu-hover-wrap .menus[data-active-menu]:before {
    content: "";
    position: absolute;
    height: 60px;
    width: 100%;
    top: -30px;
  }
  .main-navigation-bar-desktop button:after {
    background-color: #fff;
    // transition: transform .2s cubic-bezier(.825,.005,.405,1.22);
    transform-origin: left;
    transform: scaleX(0);
    height: 2px;
    bottom: 0;
    transition: transform .2s ease;
    left: 0;
    width: 100%;
    content: "";
    display: block;
  }
  .main-navigation-bar-desktop button.active-menu:after {
    transform: scaleX(1);
  }
  .active-menu {
    position : relative
  }
  .navigation-container li.link-list__item:not(:has(> [open])):hover a.link-list__link {
    text-decoration: underline;
  }
}


.justify-flex-start{
  justify-content: flex-start !important;
}
