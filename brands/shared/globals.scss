
/* Color Guidelines
  ==========================================================================
  primary: Highest level user attention.
  secondary: Second highest level user attention.
  tertiary: Third highest level user attention.
  light: Most prominent light background color. Must be able to overlay on top of dark.
  dark: Most prominent dark background color. Must be able to overlay on top of light.
  pop: Usage examples are badges.
  highlight: Think about this as using a highlighter pen.
  body: Most common text color.
  header: Most common text color for headers.
*/

 


:root {

  --color-primary: #000000; //#0C4066
  --color-secondary: #E36662;
  --color-tertiary: #F8F1E3;
  --color-light: #FCF9F3;
  --color-dark: #666666;
  --color-pop: #B0DFE1;
  --color-highlight: #F1A407;
  --color-body: #000000; //#381300

  --swiper-theme-color: #000;

  --header-height:76px;
  --preheader-height: 34px;
  --header-height-half:38px;
  --unscrolled-header-height: 100px;
  --scrolled-header-height: 76px;

  @media screen and (max-width: 1024px) {
    --header-height:100px;
    --preheader-height: 36px;
    --header-height-half: 34px;
    --unscrolled-header-height: 100px;
    --scrolled-header-height: 68px;
  }


  --header-height:48px;

}



/* Document
========================================================================== */

@import './styles/typography.scss';
@import './styles/buttons.scss';
@import './styles/swiper.scss';
@import './styles/accordion.scss';
@import './styles/tabs.scss';
@import './styles/pagination.scss';
@import './styles/cart.scss';
@import  './styles/header.scss';
@import './styles/account-pages.scss';
@import './styles/forms.scss';
@import './styles/progress.scss';
@import './styles/product-item.scss';
@import './styles/layout.scss';
@import './styles/content-item.scss';
@import './styles/slider-cart.scss';
@import './styles/page.scss';
@import '../../components/absolute-content/absolute-content.scss';
@import '../../components/quick-add/quick-add.scss';
@import './styles/tabs.scss';
@import './styles/variant-selector.scss';
@import '../../components/product-badges/shared.scss';
@import '../../components/gift-card-recipient-form/gift-card-recipient-form.scss';
@import '../../components/@helper-styles/helpers';


/* Global Helpers
========================================================================== */

.container {
  @apply mx-auto px-4 sm:px-6 lg:px-8;
}
@media (min-width: 1024px) {
  .w-content {
    width: fit-content;
  } 
}

[tooltip]:not([tooltip=""]){
  @apply before:content-[attr(tooltip)] before:hidden before:bg-gray-800 before:py-2 before:px-3 before:text-light before:rounded-md before:bottom-full before:leading-tight before:w-48 hover:before:block before:absolute;

  &[tooltip-center]:before {
    @apply text-center left-1/2 transform -translate-x-1/2;
  }
}

// Swatch styles
input[data-availability="false"] + label,
input[data-availability="0"] + label,
.swatch--disabled {
  color: #c2c2c2;
  background: #efefef;
  cursor:not-allowed;
}

details, details summary {
  background-image:none;
  -webkit-appearance:none;
  list-style: none;
}
details summary::-webkit-details-marker, details summary::marker {
  display:none;
}
details > summary {
  cursor:pointer;
}

[x-cloak] { display: none !important; }

.swiper {
  .swiper-button-prev,
  .swiper-rtl .swiper-button-prev,
  .swiper-button-next,
  .swiper-rtl .swiper-button-next {
    display:block;
    &:after {
      content: '';
    }
  }
}


/* Z-Index 
  ==========================================================================
  Base: 1
  
  Stack:
  Nested Element requiring modification: 10-20
  
  Popover:
  Functionally higher z-index within content: 20

  Underlay: 
  Full Screen Blocking element that lays under header: 30
  
  Sticky:
  Header / Sticky Elements: 40
  
  Overlay: 50
  Full Screen Blocking element that lays under header: 50

  Dialog
  Modal / Dialog: 60
*/

.z-base {
  z-index:1;
}
.z-stack {
  z-index:10;
}
.z-popover {
  z-index:20;
}
.z-underlay, html:has(.modal[open].modal--underlay) .modal-overlay {
  z-index:30; 
}
.z-sticky, #shopify-section-header, .header-bar {
  z-index:40;
}
.z-overlay, html:has(.modal[open]:not(.modal--underlay)) .modal-overlay {
  z-index:50; 
}
.z-dialog, .modal {
  z-index:60;
}

/* Scroll Locking 
  ========================================================================== */
@media only screen and (max-width: 1023px) {
  html:has(.header-bar--main.active),
  html:has(.modal[open]) {
    overflow:hidden;
    height:100%;
    height:100dvh;
  }
}

@media only screen and (min-width: 1024px) {
  html:has(.header-menu--main details[open] ),
  html:has(.modal[open]) {
    overflow:hidden;
    height:100%;
    height:100dvh;

  }
}

/* Layout Debugging
========================================================================== */
[design-mode] {
	[class*="layout"][name] {
		box-shadow: 0 0 0 1px rgba(0, 123, 255, .5);
		  -moz-box-decoration-break:clone;
	  -webkit-box-decoration-break:clone;
	  box-decoration-break:clone;
    position:relative;
    &.absolute {
      position: absolute;
    }
		&:after {
			position:absolute;
			top:1px;
			left:1px;
			background-color:rgba(0, 123, 255, .75);
			padding:0;
			font-size:12px;
			line-height:1;
			min-width:18px;
			height:18px;	
			content: attr(name) ' →';
			display:flex;
			align-items:center;
			justify-content:center;
			border-radius: 0 0 3px 0;
			color:#FFF;

		}
		&.flex-row {
			&after{
				content: attr(name) ' →';
			}
		}
		&.flex-row-reverse {
			&after{
				content: attr(name) ' ←';
			}
		}
		&.flex-col {
			&:after{
				content: attr(name) ' ↓';
			}
		}
		&.flex-col-reverse {
			&:after{
				content: attr(name) ' ↑';
			}
		}
		&.flex-col-reverse {
			&:after{
				content: attr(name) ' ↑';
			}
		}

		&.grid-cols-2 , &.grid-cols-3, &.grid-cols-4   {
			&:after{
				content: attr(name) ' ☷';
				padding:0;
			}
		}

		&.grid-cols-1  {
			&:after{
				content: attr(name) ' ☰';
			}
		}
		
		
		&:hover {
			&:before {
				position:absolute;
				top:0; left:0; right:0; bottom: 0;
				background-color:rgba(0, 123, 255, .125);
				content:'';
				display:block;
        pointer-events:none;
			}
		}
	}
}

.rte ul {
  list-style:disc;
  @apply my-md mx-lg;
}

.accordion[open].collapse-padding summary {
  padding-bottom:0;
}

.accordion[open].collapse-padding .accordion-panel,
.accordion[open].collapse-padding .x-accordion-panel {
  padding-top:0;
}

.account-block {
  &:not(:last-child) {
    .accordion {
      @apply border-b-0;
    }
  }
  &:last-child {
    .accordion {
      @apply border-b;
    }
  }
}

video {
	width:100%;
}
/* Reviews IO 
  ========================================================================== */

:root {
  --reviews-star-color: #f1a307;
  --reviews-text-color: #000000;
}

.review-snippet {
	.ruk-rating-snippet {
    .product-item &:not(.ruk_rating_snippet--loaded) {
      margin: 8px 0;
      height: 8px;
      width: 89px;
      background-color: var(--reviews-text-color);
      opacity: 0.1;
      & * { @apply hidden;}
    }
  }
}

.divider {
  border-bottom:1px solid;
}



  .scroll-snap-none {
    scroll-snap-type: none;
  }
  .scroll-snap-x {
    scroll-snap-type: x mandatory;
  }
  .scroll-snap-y {
    scroll-snap-type: y mandatory;
  }
  
  .snap-align-center > * {
    scroll-snap-align: center;
  }
  .snap-align-start > * {
    scroll-snap-align: start;
  }
  .snap-align-end > * {
    scroll-snap-align: end;
  }
  .flex-slider.snap-align-none > * {
    scroll-snap-align: none;
  }

  .no-scrollbar {
    overflow: -moz-scrollbars-none;
    -ms-overflow-style: none;
  }
  .no-scrollbar::-webkit-scrollbar { 
    width: 0 !important; 
    background-color: transparent;
    height: 0 !important
  }
  .no-scrollbar::-webkit-scrollbar-track {
    background-color: transparent;
  }
  .no-scrollbar::-webkit-scrollbar-thumb {
    background-color: transparent;
  }

.scroll {
	&-normal {
		@apply no-scrollbar overflow-x-scroll snap-none overscroll-x-contain;
	}
	&-snap-start {
		@apply no-scrollbar overflow-x-scroll snap-x snap-mandatory;
		& > * {
			@apply snap-always snap-start;
		}
	}
	&-snap-center {
		@apply no-scrollbar overflow-x-scroll snap-x snap-mandatory;
		& > * {
			@apply snap-always snap-center;
		}
	}
}

.addressMap.google-maps.store-locator iframe{
  width:100%;
}

section.section--filtered-content form article fieldset .field__image{
	padding:0px;
	cursor:pointer;
}
section.section--filtered-content form article fieldset .field__image label{
	padding:15px;
	width: 100%;
	justify-content: flex-start;
}
