.slider-cart {
  @apply fixed bg-white z-50 top-0 right-0 left-auto w-screen lg:max-w-[480px] lg:w-1/3 h-full transform translate-x-full open:translate-x-0 transition delay-75 p-0;

  &__header {
    @apply bg-primary text-white py-8;
  }

  &__summary {
    @apply bg-light;

    &-text {
      @apply text-center text-base font-body w-full my-0 border-b-2 border-tertiary mx-4 pb-4;
    }

    &-price {
      @apply font-heading;
    }
  }

  &__gift-message {
    @apply bg-light;
  }

  &__buttons {
    @apply bg-light pb-8;

    .cart__checkout-button a {
      @apply py-8;
    }

    .cart__continue-button {
      @apply hidden;
    }
  }

  &__payment-widget {
    @apply flex items-center justify-center bg-light pb-4;
  }

  .cart__item-wrapper {
    @apply border-t-2 border-tertiary;
  }
}

.cart {
  &__item-wrapper {
    @apply relative flex-1 px-4 sm:px-6;

    #summary-heading {
      @apply text-lg text-center my-4 font-medium text-body;
    }
  }

  &__item-header {
    @apply lg:col-span-7;
  }

  &__items {
    @apply divide-y divide-gray-200 border-b border-tertiary;
  }

  &__item,
  &__items > &__item:not([hidden]) {
    @apply flex py-3 border-tertiary first:border-t-0;
  }
}

.cart-item {
  &__image-wrapper {
    @apply flex-shrink-0;
  }

  &__image {
    @apply h-24 w-24 rounded-lg object-contain object-center sm:h-32 sm:w-32;
  }

  &__info {
    @apply relative ml-4 flex flex-1 flex-col justify-center;

    &-start {
      @apply flex justify-between;
    }

    &-end {
      @apply mt-2 flex justify-between items-center;
    }
  }

  &__title {
    @apply text-sm my-0;

    &-link {
      @apply font-medium text-black hover:text-gray-800;
    }
  }

  &__line-item {
    @apply my-0 text-sm text-gray-500;
    text-transform: initial !important;
  }

  &__price {
    @apply text-right text-sm font-medium text-gray-900;
  }

  &__quantity {
    @apply flex flex-row w-full justify-start;

    input {
      @apply w-8 text-center;
    }
  }

  &__remove {
    @apply flex self-start ml-4 text-sm font-medium text-black;
  }

  &__badge {
    @apply text-sm font-medium font-body text-gray-900;
  }

  &__price_with_compare {
		color: red;
		margin-right: 5px;
	}

	&__compare-at-price{
		font-size: 14px;
		letter-spacing: .4px;
		font-weight: 600;
		margin-bottom: 1rem;
		font-family: var(--font-body-family);

	}
}
