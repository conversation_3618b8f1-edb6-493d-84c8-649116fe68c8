.tabs {

  button,a {

    &:not(.active) {
      color:#b8b8b8;
    }
  }
  
  li {
    &:first-child {
      margin-left: auto;
    }
    &:last-child {
      margin-right: auto;
    }
  } 

  ul:not(.start) {
    @apply pl-0 flex justify-end w-full;
    li {
      @apply ml-5;
    }
  }

  ul.start {
    @apply space-x-4;
  }

  .tab-title {
    @apply btn btn--tertiary;
    &.active {
      @apply btn--light; 
    }
  }
  .tab-panel {
    > div {
      @apply py-6;
    }
  }
}

/* Tabs
========================================================================== */
.section--content-carousel {
	.tabs {

		button,a {
			
			border: none;
			font-family: var(--font-medium);
			cursor: pointer;
			padding: 8px 11px;
			margin: 0px 5px 0px;
			background-color: none;
			display: inline-block;
			border-radius: 2rem;
			font-size: 15px;

			@media only screen and (min-width: 1024px) {
				font-size: 20px;
				padding: 3px 16px;
			}

			&:active {
				
			}
		  &:not(.active) {
			color:#000;
		  }
		}
	}
	// .carousel-container.hidden {
	// 	display: none;
	// } 
}
