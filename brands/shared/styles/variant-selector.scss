
.product-form__option--color .field__buttons--colors {
  &.field__buttons--colors-wrap {
    @media screen and (max-width : 1023px) {
      flex-wrap: wrap;
      overflow-x: visible
    }
  }
  .field__button:hover {
    border: 1px solid #626262
  }
}

.variant-selector--table {
  .product-form__option--size,
  .product-form__option--color {
    .field__buttons {
      @apply flex gap-0 items-stretch flex-nowrap overflow-x-scroll relative border;
    }

    .field__button:not(.unavailable) {
      &:hover {
        .field__button-text {
          @apply border-none;
        }
      }
    }

    .field__button,
    .field__button-text {
      @apply rounded-none hover:bg-gray-200;
    }

    .field__button-text {
      @apply relative w-full bg-gray-200;
    }
    .field__button {
      @apply overflow-visible p-0 flex flex-col self-stretch h-auto;
    }

    .field__button.unavailable {
      @apply pointer-events-none bg-gray-100 text-gray-400;

      .field__button-text {
        @apply bg-transparent;
      }
    }

    .field__button-text,
    .field__table-cell {
      @apply px-2 py-2 border-none relative min-h-[70px] min-w-[100px];

      &:not(:last-child):after {
        content: '';
        @apply border inset-0 absolute;
        height: calc(100% + 1px);
        width: calc(100% + 1px);
        top: -1px;
        left: -1px;
      }
    }

    .field__table-cell:last-of-type:after {
      @apply border-b-0;
    }

    .field__button,
    .field__table-data {
      @apply flex flex-col flex-grow;
    }

    .field__table-cell {
      @apply border w-full whitespace-nowrap text-center flex items-center justify-center;
      font-size: 13px;
      font-family: var(--font-body-family);
    }

    .field__table-data:has(.field__table-cell--empty) {
      background-image: linear-gradient(45deg, 
        #fff 25%, 
        #e5e7eb 26%, 
        #fff 26%, 
        #fff 50%, 
        #e5e7eb 50%, 
        #e5e7eb 51%, 
        #fff 52%, 
        #fff
      );
      background-size: 10px 10px;
      @apply w-full;
    }


    .field__table-headers {
      @apply sticky top-0 left-0 bg-white z-10 border-r-2 border-black;

      .field__table-cell:first-child { 
        @apply bg-gray-200
      }
    }
  }

  .product-form__option-label-wrapper {
    @apply hidden;
  }
}
.product-form__option--color .field__buttons--colors .field__buttons--colors-swatch .color_swatch {
	width: 100%;
	height: 100%;
	-ms-overflow-style: none;
    scrollbar-width: none; 
}
.product-form__option--color .field__buttons--colors .field__buttons--colors-swatch .color_swatch::-webkit-scrollbar { 
    display: none; 
}
