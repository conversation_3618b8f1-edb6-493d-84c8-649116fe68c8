.field,
.floating-label {
  @apply flex flex-col mb-2;

  &__input,
  &__textarea,
  &__select {
    @apply border border-solid border-gray-300 rounded-sm text-current;

    &:focus,
    &:focus-visible {
      @apply border-black border-2 outline-none;
    }

    &::placeholder {
      @apply text-[#797979];
    }

    &.visited:invalid {
      @apply border-red-500 border-2;

      ~ .error-message {
        @apply block text-current text-xs mt-1;
      }
    }
  }

  &__input,
  &__select {
    @apply h-12 py-0 px-3;
  }
  &__textarea {
    @apply py-2 px-3;
  }
}

.field--floating-label {
  @apply bg-transparent relative mb-4 text-gray-700;

  label {
    @apply absolute text-[11px] left-3 top-1.5 text-gray-400 opacity-100 pointer-events-none transition-all duration-200 ease-linear;
  }

  input, textarea {
    @apply pt-5;
  }

  &:has(:placeholder-shown) {
    label {
      @apply opacity-0;
    }
    input {
      @apply pt-1 pb-1;
    }
  }

  &:has(label ~ select) {
    label {
      @apply opacity-0;
    }
  }

  &:has(.field__select option:checked:not([value=""])) {
    label {
      @apply opacity-100;
    }
  }

  &:has( > label ) {
    .field__select:has(option:checked:not([value=""])) {
      @apply pt-5;
    }
  }
}

.field {
  input {
    &[type="checkbox"],
    &[type="radio"] {
      @apply appearance-none h-4 w-4 p-0 bg-white border border-gray-300;

      &:checked {
        @apply border-0 shadow-[inset_0_0_0_5px_rgba(0,0,0,1)];
      }

      & + span {
        @apply text-sm;
      }
    }

    &[type="checkbox"] {
      @apply rounded-[3px];
    }

    &[type="radio"] {
      @apply rounded-full;
    }
  }

  &__checkbox,
  &__radio {
    @apply flex gap-x-2.5;
    input {
      @apply shrink-0;
    }
  }

  &__buttons {
    @apply grid gap-xs grid-cols-5;
  }

  &__colors {
    @apply grid gap-xs grid-cols-5 lg:grid-cols-4;
  }

  &__button {
    @apply cursor-pointer flex-1 p-2 relative inline-flex flex-col items-center justify-center;

    &-text {
      @apply block border absolute inset-0 [input:checked_~_&]:border-2 [input:checked_~_&]:border-black flex items-center justify-center;
    }

    &:hover &-text {
      @apply border-black border-2;
    }
  }

  &__color {
    @apply flex-1 gap-y-2 p-2 max-w-[48px] inline-flex flex-col items-center justify-center; 

    &-swatch {
      @apply cursor-pointer block h-8 w-8 rounded-full [input:checked_~_&]:outline outline-offset-2 outline-2 border;
    }

    &-label {
      @apply text-sm;
    }

    &:hover &-swatch {
      @apply outline;
    }
  }

  &__chip {
    
    @apply inline-flex items-center p-2 mr-sm mb-sm rounded-md bg-gray-100 text-black;

    &:has(input:checked) {
      @apply bg-gray-900 text-white;
    }
    
  }

  &__toggle {
    @apply inline-flex items-center bg-gray-100 rounded-full w-content;

    label {
      @apply inline-flex items-center;

      input {
        &:not(:checked) {
          & ~ .toggle__label {
            @apply text-sm px-6 py-1 border-2 border-transparent rounded-full inline;
          }

          & ~ .toggle__label--selected {
            @apply hidden;
          }
        }

        &:checked {
          & ~ .toggle__label {
            @apply text-sm px-6 py-1 border-2 border-transparent rounded-full border-gray-300 bg-white inline;
            
            & ~ .toggle__label--unselected {
              @apply hidden;
            }
          } 
        }
      }
    }
  }

  &__description {
    @apply mt-xs;
  }
}



.error-message {
  @apply hidden;
}

form.submit-attempted {
  &__input,
  &__textarea,
  &__select {
    &.visited:invalid {
      @apply border-red-500 border-2;

      ~ .error-message {
        @apply block text-current text-xs mt-1;
      }
    }
  }
}

.floating-label {
  @apply bg-transparent relative mb-4 text-gray-700;

  > label {
    @apply absolute text-[11px] left-3 top-1.5 text-gray-400 opacity-0 pointer-events-none transition-all duration-200 ease-linear;
  }

  &--filled {
    > label {
      @apply opacity-100;
    }

    > input,
    > textarea {
      @apply pt-5 pb-1;
    }

    &.floating-label--errors {
      > label {
        @apply text-current;
      }
    }
  }

  &:has(.field__select) {
    > label {
      @apply opacity-100;
    }
    
    > select {
      @apply pt-5 pb-1;
    }
  }

  &--errors {
    @apply text-red-500;
  }
}

form {
  .form-success, .success {
    display:none;
  }
  &.submitted {
    .form-success, .success {
      display:block;
    }
    &:has(.form-success--hide-fields) {
      & > *:not(.form-success, .success){
        display:none;  
      }
    }
  }
}

@media(max-width:1024px){
  input[type="text"], input[type="search"], input[type="email"], input[type="tel"] {
    font-size:16px!important; 
  }
}
