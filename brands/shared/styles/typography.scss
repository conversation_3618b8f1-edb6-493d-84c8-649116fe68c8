/* Type Scale (Build your scale https://type-scale.com/) - This theme is using the Perfect Fourth
========================================================================== */

html {font-size: 100%;} /*16px*/

body, .body {
  color: var(--color-body);
}

p {

  margin-bottom: 1rem;  
  
  .reset-p & {
    margin-bottom: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }

  &--xs {
    @apply text-xs;
  }

  &--sm {
    @apply text-sm;
  }

  &--lg {
    @apply text-lg;
  }

}

h1, h2, h3, h4, h5, h6 {
  margin: 3rem 0 1.38rem;
  font-family: var(--font-heading-family);
  line-height: 1.3;
  color: var(--color-heading);
}

.headings {
  margin: 3rem 0 1.38rem;
  font-family: var(--font-heading-family);
  font-weight: var(--font-heading-weight);
  font-style: var(--font-heading-style);
  line-height: 1.3;
  color: var(--color-heading);
}

.text {
  &-wrap {
    text-wrap:wrap;
  }
  &-nowrap {
    text-wrap:nowrap;
  }
  &-balance {
    text-wrap:balance;
  }
  &-pretty {
    text-wrap:pretty;
  }
}

 h1, .h1 {
  @apply headings text-4xl;
}

h2, .h2 { 
  @apply headings text-3xl; 
}

h3, .h3 { 
  @apply headings text-2xl;
}

h4, .h4 { 
  @apply headings text-xl; 
}

h5, .h5 {  
  @apply headings text-lg;  
}

h6, .h6 {  
  @apply headings text-base;  
}

small, .text_small {  
  @apply text-sm;  
}

.font-body {
  font-family: var(--font-body-family);
  font-weight: var(--font-body-weight);
  font-weight: var(--font-body-style);
}

.font-heading {
  font-family: var(--font-heading-family);
  font-weight: var(--font-heading-weight);
  font-style: var(--font-heading-style);
}

.font-subheading {
  font-family: var(--font-subheading-family);
  font-weight: var(--font-subheading-weight);
  font-style: var(--font-subheading-style);
} 
.font-olukai-bold {
  font-family: var(--font-olukai-bold);
  font-weight: var(--font-olukai-bold);
  font-style: var(--font-olukai-bold);
}
.font-olukai-regular {
  font-family: var(--font-olukai-regular);
  font-weight: var(--font-olukai-regular);
  font-style: var(--font-olukai-regular);
}