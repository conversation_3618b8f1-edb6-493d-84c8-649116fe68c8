/* Account Pages
  ========================================================================== */

.errors {
  @apply text-red-600 text-sm py-sm;
}

.section--account {
	.section__container {
		@apply gap-x-20 gap-y-8;
	}

	.section__header {
		@apply max-lg:order-2 border-b uppercase pb-4;

		.header__title {
			@apply text-[1.125rem];
		}
	}

	.section__sidebar {
		@apply overflow-x-hidden -mx-4;

		.sidebar {
			@apply max-lg:order-1 w-full flex-col;

			.sidebar__list {
				@apply  max-lg:overflow-x-scroll max-lg:flex px-4;
			}

			.sidebar__item {
				@apply first:pl-0 max-lg:text-[13px] lg:text-[1.125rem] max-lg:pb-4 max-lg:w-auto max-lg:px-2 max-lg:whitespace-nowrap font-body text-primary lg:pt-0 lg:mb-2.5;

				.sidebar__link {
					@apply text-primary transition-all ease-in duration-300;
				}

				&:hover {
					.sidebar__link {
						@apply transition-all ease-in duration-300;
						text-decoration-color: transparent;
					}	
				}
			}

			.sidebar__link {
				@apply p-1 hover:bg-sky-500/10 active:underline-offset-8 active:decoration-2 active:underline;
			}	
		}
	}

	.section__main {
		@apply  items-start max-lg:order-3;
	}

	.order-history__title, .address-book__title {
		font-family: var(--font-medium);
		@apply text-[1.125rem] mb-1;
	}

	.order-item {
		@apply bg-white border rounded-[3px] px-4 pt-4 pb-8 mt-4;

		&__line-items {
			@apply gap-2;
		}

		&__name {
			font-family: var(--font-medium);
			@apply text-[.875rem] mb-4;
		}

		&__footer-item {
			@apply py-4 px-1 lg:border-t;

			&:nth-child(3),
			&:nth-child(4) {
				@apply max-lg:border-t;
			}

			&-label {
				@apply text-[11px] font-body text-[#736b67] mb-1.5;
			}
			&-value {
				@apply text-[.875rem] font-body;
			}
		}

		&__view-detail {
			@apply col-span-full ml-auto;

			&--desktop {
				@apply lg:flex lg:items-center lg:pr-6;
			}

			a {
				@apply w-full;
			}
		}
	}

	.order-detail {
		&__header {
			@apply mb-5;
		}
		&__title,
		.line-item__title {
			font-family: var(--font-medium);
			@apply text-[1.125rem] mb-1;
		}
		&__meta,
		&__label,
		&__address-label {
			@apply text-[11px] font-body text-[#736b67] mb-1;
		}

		&__summary {
			@apply flex flex-col bg-[#F6F6F6] py-6 px-4 max-lg:-mx-4;
			.order-detail__title {
				@apply mb-2;
			}
		}
		&__fulfillment {
			@apply mb-3;
		}
		&__main,
		&__line-items {
			@apply flex flex-col divide-y divide-[#F6F6F6];
		}
		&__prices {
			@apply pt-8;
		}
		&__price {
			@apply mt-4;
		}
		.order-detail__price-label,
		.order-detail__price-value {
			font-family: var(--font-medium);
			@apply text-[.875rem];
		}
		&__total {
			@apply border-t;

			.order-detail__price-symbol {
				@apply text-[0.75rem] font-body;
			}

			.order-detail__price-label,
			.order-detail__price-value {
				@apply text-[1.125rem] font-heading mb-1;
			}
		}
		.line-item {
			&__image {
				@apply w-32 h-32;
			}
		}
		&__detail {
			.order-detail__title {
				@apply mt-4 mb-6;
			}
		}
		
		&__transactions,
		&__addresses {
			@apply bg-white border p-4;
		}

		&__address-value,
		&__payment-method {
			@apply text-[.875rem] font-body;
		}
		&__payment-method {
			@apply capitalize;
		}
		&__address--shipping {
			@apply border-b pb-4;
		}
		&__address--billing {
			@apply pt-4;
		}
	}

	.address-item {
		@apply bg-white border rounded-[3px] px-4 pt-4 pb-8;
		
		&__name, &__role {
			font-family: var(--font-medium);
			@apply text-primary;
		}
		&__action {
			@apply text-secondary px-md;
			font-size:11px;
		}

		address {
			color:#797979;
			@apply py-xs;
		}

	}
}


.account-block {

	&--password {
		.content-item__text-stack {
			@apply mb-6;

			p {
				@apply text-[11px] font-body text-[#736b67];
			}
		}
	}

	&--subscription {
		@apply lg:px-8 max-lg:border-b max-lg:pt-8 pb-8;
		
		&:first-child {
			@apply pt-0;
		}

		&:nth-child(odd) {
			@apply lg:border-r;
		}
		
		&:nth-child(n + 3) {
			@apply border-t pt-8;
		}
		
		&:nth-child(4) {
			@apply border-r-0;
		}
	
		.account-subscription__title {
			font-family: var(--font-bold);
			@apply text-[1.125rem];
		}

		.account-subscription__subtext {
			@apply text-[11px] font-body text-[#736b67] mb-4 mt-2;
		}

		.account-subscription__input[disabled] {
			@apply text-[15px] font-bold text-body bg-transparent;
		}

		.account-subscription__option-label {
			@apply text-[14px] font-body text-body;
		}

		.account-subscription__option-desc {
			@apply text-[11px] font-body text-[#736b67];
		}
		
		.account-subscription__cancel {
			@apply mt-8;
		}

		.field__checkbox {
			@apply py-4 pr-8 border-b last:border-b-0 items-start;

			input {
				@apply shrink-0 mt-1;
			}
		}

		.group {
			summary {
				@apply mt-5;
			}
		}

		.mini-form__info {
			@apply hidden;
		}
	}
}

.account-profiles {
	&__list-container {
		@apply flex gap-4 flex-wrap;
	}
	
	&__list {
		@apply flex gap-4 items-start flex-wrap grow shrink;
	}
	
	legend {
		@apply border-t pt-6 mt-8 mb-4;

		h4 {
			@apply text-[1.125rem] mb-1 mt-0;
			font-family: var(--font-medium);
		}

		p {
			@apply text-[12px] text-dark;
		}
	}
}

.account-profile {
	@apply bg-white border rounded-[3px] p-4;
	
	&__container {
		@apply grow shrink basis-[250px];
	}

	&__title {
		@apply text-[11px] font-body text-[#736b67] mb-4 mt-2;
	}

	&__actions {
		@apply flex gap-4;
	}


	& + .account-profile {
		@apply bg-tertiary;

		.account-profile__fields {
			@apply border-white;
		}
	}

	&__header {
		@apply justify-between items-center;

		button {
			@apply h-auto;
		}
	}

	&__button-set {
		@apply flex gap-x-4;
	}

	address {
		@apply not-italic;
	}

	&__label {
		@apply text-[11px] font-body text-[#736b67] mb-1 mt-5;
	}

	&__value {
		@apply text-[.875rem] font-body;
	}

	&__name {
		@apply text-[12px] font-heading my-0;
	}

	&__fields {
		@apply pt-5 mt-5 border-t gap-x-5;
	}

	&__field {
		@apply flex-1;

		&-label {
			@apply text-[11px] font-body text-[#736b67] mb-1;
		}

		&-value {
			@apply text-[.875rem] font-body capitalize;
		}
	}
}

.account-fields {
	.account-field {
		@apply lg:mr-4;
	}

	.account-field {
		&--radio {
			div {
				@apply gap-x-8 mb-3;
			}
		}

		&--chips {
			.field {
				@apply mb-0;

				span {
					@apply text-[12px];
				}
			}
		}
		
		&--swatches {
			.field__color-swatch {
				@apply w-[40px] h-[40px];
			}
		}
	}

	&--profile {
		footer {
			@apply  max-lg:flex-col-reverse max-lg:w-full mt-8;
		}
	}

	.field {
		&__subtext,
		&__liquid {
			@apply mb-6 text-[12px] text-dark;
		}
	}
}

.account-profile-editor {
	@apply lg:mt-5;

	&__title {
		@apply text-[1.125rem];
		font-family: var(--font-medium);
	}
}	