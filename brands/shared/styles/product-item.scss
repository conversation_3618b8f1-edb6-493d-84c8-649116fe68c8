.product-item {

  &__quick-add.button {
    font-size:11px;

    svg:first-child {
      stroke-width:2px;
      width:12px;
    }

    span + svg {
      @apply hidden;
    }

    .no-quickadd & {
      display:none!important;
    }
  }

  &__images  &__quick-add.button {
    @apply absolute top-0 right-0 left-auto lg:bottom-4 lg:inset-x-4 lg:top-auto w-auto lg:hidden group-hover:lg:flex; 
    @apply max-lg:bg-transparent max-lg:p-0 max-lg:border-none max-lg:text-primary max-lg:w-12 max-lg:h-12;

    svg:first-child,
    span {
      @apply hidden lg:block;
    }

    span + svg {
      @apply block lg:hidden ml-0;
    }
  
  }

  &.group {
    .product-item__image-main {
      opacity: 1;
    }

    .product-item__hover-image {
      opacity: 0;
    }

    @media (hover: hover) {
      &:not(.no-model-image):hover:not(:has(.product-item__swatches:hover)) {
        .product-item__image-main {
          opacity: 0;
        }

        .product-item__hover-image {
          opacity: 1;
        }
      }
    }
  }
}
