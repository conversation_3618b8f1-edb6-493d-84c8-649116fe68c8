.content-item {
  &__article {
    &--vertical,
    &--vertical.content-item--article {
      @apply flex-col gap-2;

      & > .content-item__content {
        @apply px-0;
      }

      .content-item__media-container {
        @apply max-w-full;
      }

      .content-item__meta--top {
        @apply block;
      }

      .content-item__meta--text-stack {
        @apply hidden;
      }
    }
  }

  &__text-stack {
    h1, .h1,
    h2, .h2,
    h3, .h3,
    h4, .h4,
    h5, .h5,
    h6, .h6,
    p, .p {
      @apply my-0;
    }
  }
}
