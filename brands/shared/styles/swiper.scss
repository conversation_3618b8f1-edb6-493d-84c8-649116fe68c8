.btn-control {
  @apply bg-transparent p-3 transform z-10 transition-all select-none items-center flex-col justify-center;

  &.swiper-button-next, 
  &.swiper-button-prev {
    &.swiper-button-disabled {
      @apply hidden opacity-0;
    }
  }
}


.swiper-pagination--bullets,
.pagination {
  @apply text-center flex items-center justify-center p-2;
  .pagination-bullet, .swiper-pagination-bullet {
    @apply bg-current w-1 h-1 rounded-full mx-1.5 cursor-pointer relative opacity-70 transition-transform;
    &.active, &.swiper-pagination-bullet-active {
      @apply opacity-100 scale-[2];
    }
  }
  &.pagination-dash {
    .pagination-bullet, .swiper-pagination-bullet {
      @apply bg-dark w-20 h-1 rounded-none mx-3;
      &.active, &.swiper-pagination-bullet-active {
        @apply opacity-100 bg-light;
      }
    }
  }
  &.swiper-pagination-progressbar.swiper-pagination-horizontal {
    height: 2px;
    @apply relative p-0 bg-gray-100;
    .swiper-pagination-progressbar-fill {
      @apply bg-dark;
    }
  }
  &.swiper-pagination-bullets.swiper-pagination-horizontal {
    @apply absolute left-0 bottom-0 z-20;
  }
}
.section--content-carousel {
  div.swiper-pagination,
  div.pagination {
    &.swiper-pagination-bullets {
      bottom: -2rem;
      width: 100% !important;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      left: 0;
      right: 0;
      margin: auto;
      @media only screen and (max-width: 767px) {
        bottom: -1rem;
      }
    }
  }
}

.carousel-outer {

  overscroll-behavior:contain;
  
  @apply overflow-x-hidden;

  .swiper.swiper--overflow-visible {
    overflow:visible;
  }

  .swiper.swiper--overflow-visible .swiper-wrapper {
    overflow:visible;
  }

  [carousel-visible="false"] { 
    @apply transition-opacity;
    opacity: 0; 
  }

  .swiper:hover [carousel-visible="false"] { opacity: 100; }
}

@media only screen and (max-width: 991px) {

  .carousel-outer {
  
    overscroll-behavior:contain;
    
    @apply overflow-x-hidden;
  
    .swiper.swiper--overflow-visible {
      overflow:visible;
    }
  
    .swiper.swiper--overflow-visible .swiper-wrapper {
      overflow:visible;
    }
  
    [carousel-visible="false"] { 
      @apply transition-opacity;
      opacity: 100; 
    }
  
  }
}

@media only screen and (min-width: 1024px) {
  .swiper {
    &:has([slide-visible="true"] .content-carousel__show-all) .swiper-button-next { 
      @apply hidden;
    }
  } 
}
.swiper-wrapper:has(.collection-hidden-arrow-mob) ~ button.swiper-button-next.btn-control { 
  display: none;
}

.swiper {
  .swiper-wrapper-skeleton {
    display: flex;
  }

  &.swiper-initialized:has(.swiper-wrapper .swiper-slide) .swiper-wrapper-skeleton {
    display: none;
  }

  .swiper-slide:not([style]),
  .swiper-wrapper-skeleton .swiper-slide {
    @apply pr-[var(--gap-mobile)] lg:pr-[var(--gap)];
  }

  &:not(:has(.swiper-wrapper .swiper-slide)),
  &:not(.swiper-initialized) {
    .swiper-wrapper {
      @apply flex;
    }

    .swiper-wrapper-skeleton {
      display: flex;
    }

    $max-slides: 12; // Define the maximum width

    @for $i from 1 through $max-slides {
      .swiper-slide.w-1\/#{$i} {
        width: percentage(calc(1 / $i));
      }
    }
  }
}

