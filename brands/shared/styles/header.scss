#shopify-section-header {
  
  @apply sticky top-0 z-40 w-full;
  transition: transform 100ms linear;
  overflow: visible !important;

  [scroll-direction="down"] & {
    top:calc(0px - var(--header-height));
  }
  [scroll-direction="down"][scroll-segment="0"] & {
    top:calc(0px - var(--preheader-height));
  }
  [scroll-direction="up"] & {
    top:calc(0px - var(--preheader-height));
  }
  [scroll-direction="up"][scroll-segment="0"] & {
    top:0px;
  }  
}

.page-scroll--down:not(.page-scroll--top) #shopify-section-header {
  transform: translateY(calc(var(--header-offset) * -1));
}

.page-scroll--up #shopify-section-header {
  transform: translateY(0);
}

.nav__off_canvas {
  li {
    ul {
      @apply absolute lg:static lg:translate-x-0 transform translate-x-full top-0 right-0 lg:h-auto h-full max-h-main overflow-hidden lg:overflow-visible w-full z-10 transition-transform duration-500 ease-in-out;
    }
    &.active {
      ul {
        @apply translate-x-0;
      }
    }
  }
}

.header {
  @apply flex flex-row justify-center w-full h-auto px-4 py-4 lg:py-0 lg:px-8 text-dark bg-white;
  &__logo {
    @apply flex flex-col items-start justify-center flex-1 w-1/3 lg:mr-auto lg:w-auto;
    &--link {
      @apply flex flex-col items-start justify-center w-2/4 max-w-xs lg:mr-auto lg:w-auto;
    }
    &--extra {
      @apply pb-1;
    }
  }
  &__nav {
    @apply fixed top-0 left-0 z-40 flex flex-col justify-start w-full px-0 overflow-x-hidden transition-transform duration-300 ease-in-out lg:static h-main lg:w-auto lg:h-auto lg:flex lg:flex-1 lg:items-center lg:self-stretch lg:px-0 lg:py-0 lg:overflow-visible lg:visible invisible;
  }
  &__tools {
    @apply flex items-center justify-end w-1/3 px-2 ml-auto lg:flex-1 lg:w-auto;
  }
  
}

@mixin transparent-header {
  color: var(--transparent-text-color);
  background: transparent;

  .header-bar__block--menu ul li a,
  svg {
    color: var(--transparent-text-color);
  }

  .nav-tools__account-greeting b {
    color: var(--transparent-text-color) !important;
  }

  svg,
  svg path,
  svg rect,
  svg polygon { 
    fill: currentColor; 
  }

  .header-bar__block--menu {
    background: transparent;
  }
}
.page-scroll--top .header-bar--transparent-at-top {
  height: 0;
}

@media (min-width: 1024px) {
  .page-scroll--top .header-bar--transparent-at-top {
    @include transparent-header;

    &:hover,
    &:has(*:hover),
    &:has(details[open]),
    &:focus-within {
       &,
      .header-bar__block--menu ul li a,
      .nav-tools__account-greeting b,
      svg,
      input {
        --transparent-text-color: var(--color-primary);
      }
    }

    &:has(details[open]),
    &:focus-within {
      height: auto;
    }
  }
}

@media (max-width: 1023px) {
  .page-scroll--top:not(:has([data-modal=search])) .header-bar--transparent-at-top:not(.active):not(:has(input:focus)) {
    height: 0px;
    @include transparent-header;
  }
  .page-scroll--top .header-bar--transparent-at-top.active,
  body:has([data-modal=search]).page-scroll--top .header-bar--transparent-at-top {
    height: auto;
    overflow: auto;
  }

  .page-scroll--top .header-bar--transparent-at-top:has(input:focus) {
    .header-bar__container {
      background-color: white;
    }
  }
}

[data-active-modal="search"] .search-results {
  @apply block;
}

.nav {
  
  @apply w-full flex flex-col items-center pt-4 tracking-wide list-none border-light lg:py-0 lg:border-none lg:flex-1 lg:flex-row;
  
  &__item {
    @apply w-full lg:w-auto lg:h-full whitespace-nowrap;
    &--back {
      @apply lg:hidden;
    }
  }

  &__link {
    
    @apply relative flex flex-row items-center lg:flex-col justify-between lg:justify-start w-full h-full px-8 lg:px-2 py-4 lg:py-6 overflow-hidden cursor-pointer text-dark lg:overflow-visible;
  
    &--back {
      @apply justify-start
    }

  }
  
  &__trigger {
    @apply absolute top-0 right-0 lg:py-0 lg:px-0 w-full py-4 px-8 h-full bg-transparent flex items-center justify-center;
  }
  
  &__icon {
    width:1.5rem;
    height:1.5rem;
  }
}

.mega-menu {
  &__item {
    @apply w-full;
  }

  &__list {
    @apply w-full;
  }
}

.tools {
  &__search {
    @apply flex items-center p-2;
    &--mobile {
      @apply flex justify-center;
    }
    &--trigger {
      @apply justify-center hidden lg:flex;
    }
  }
  &__account {
    @apply px-2 py-2 hidden lg:block;
  }
  &__cart {
    &--button {
      @apply relative flex items-center p-2 pr-0;
    }
    &--bubble {
      @apply text-2xs flex flex-col items-center justify-center w-4 h-4 leading-none rounded-full text-dark bg-pop ml-1;
    }
  }
  &__menu {
    @apply p-2 m-0 bg-transparent border-none text-dark leading-none font-heading lg:hidden;
  }
}
