@mixin animate ($animation-name) {
  animation: $animation-name 500ms;
  -webkit-animation: $animation-name 500ms;
  animation-fill-mode: both;
  animation-delay: calc(var(--animation-delay, 0) * 1ms);
  animation-timing-function: linear;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate--fade-in {
  animation: fade-in 500ms;
  animation-fill-mode: both;
  animation-delay: calc(var(--animation-delay, 0) * 1ms);
  animation-timing-function: linear;
}

@keyframes slide-in-down-sm {
  from {
    transform: translateY(-5px);
  }
  to {
    transform: translateY(0);
  }
}

.animate--fade-in-down {
  animation: slide-in-down-sm 150ms, fade-in 350ms;
  animation-fill-mode: both;
  animation-delay: calc(var(--animation-delay, 0) * 1ms);
  animation-timing-function: linear;
}

@keyframes slide-in-right {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0%);
  }
}

.animate--slide-in-right {
  animation: slide-in-right 500ms;
  animation-fill-mode: both;
  animation-delay: calc(var(--animation-delay, 0) * 1ms);
  animation-timing-function: linear;
}

@keyframes slide-in-down {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0%);
  }
}

.animate--slide-in-down {
  animation: slide-in-down 500ms;
  animation-fill-mode: both;
  animation-delay: calc(var(--animation-delay, 0) * 1ms);
  animation-timing-function: linear;
}

@keyframes slide-in-down-out {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

.animate--slide-in-down-out {
  animation: slide-in-down-out 500ms;
  animation-fill-mode: both;
  animation-delay: calc(var(--animation-delay, 0) * 1ms);
  animation-timing-function: linear;
}

.animate--stagger {
  // --animation-delay-start: var(--animation-delay-start, 0);
  --animation-delay: var(--animation-delay-start, 0);

  @for $i from 1 through 20 {
    &:nth-child(#{$i}) {
      --animation-delay: calc(var(--animation-delay-start, 0) + (var(--animation-nth, #{$i}) * var(--animation-delay-increment, 100)));
        --animation-parent-nth: #{$i};
      animation-delay: calc(var(--animation-delay) * 1ms);
      & > * {
        --animation-delay-start: var(--animation-delay, 0);
      }
    }
  }
}

.stagger--debug:before {
  counter-reset: animation-delay var(--animation-delay);
  content: counter(animation-delay);
}
