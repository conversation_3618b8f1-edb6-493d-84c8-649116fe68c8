const { promisify } = require('util')
const cp = require('child_process')
const path = require('path')
const exec = promisify(cp.exec)
const brands = require('./brands.config.js')
const env = require('dotenv').config({override: true})
const fs = require('fs')


const controller = {
	getShop: async () => {
		try {
			const data = await exec('echo $SHOP_NAME')
			const output = data.stdout.trim()
			const currentShop = output.split(' ').reverse()[0]
				
			return { message: output, shop: currentShop, shopShort: currentShop.replace('.myshopify.com','') }
		} catch (err) { console.log(err) }
	},
	setShop: async (newShop) => {
		return controller.setENV(controller.getBrand(newShop), newShop)
	},
	getBrand: (shopName) => {
		let brand = false

		for (const [brandName, value] of Object.entries(brands)) {
			if (value.shop_names.includes(shopName)) {
				brand = brandName
				break
			}
		}

		if (brand) {
   //    console.log('Writing from getBrand')
			// controller.setENV(brand, shopName)
			return brand
		}
	},
	getCurrentBrand: async () => {
		const env_shop = process.env.BRAND_NAME
    return env_shop
	},
	setENV: async (brand, shop) => {
    let envContent = [];
    try {
      envContent = await fs.promises.readFile('.env', 'utf8').then(content => content.split('\n'))
    } catch (err) {
      console.log(err)
    }

    let brandIndex = envContent.findIndex(entry => entry.includes('BRAND'))
    let shopIndex = envContent.findIndex(entry => entry.includes('SHOP'))

    if (!!brand && brandIndex >= 0)  envContent[brandIndex] = `BRAND_NAME=${brand}`
    if (!!brand && brandIndex <= 0)  envContent.push(`BRAND_NAME=${brand}`) 

    if (!!shop && shopIndex >= 0)  envContent[shopIndex] = `SHOP_NAME=${shop}`
    if (!!shop && shopIndex <= 0)  envContent.push(`SHOP_NAME=${shop}`) 

		fs.writeFileSync('.env', envContent.join('\n'))
		return { brand: brand, shop: shop }
	} 
}

module.exports = controller
