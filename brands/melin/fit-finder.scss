.product-finder {
    display: block;
  }
  @media (min-width: 1024px) {
    .product-finder {
      display: flex;
    }
  }
  @media (max-width: 1024px) {
    .product-finder {
      display: block;
    }
  }
  .product-finder__quiz {
    position: relative;
    overflow: hidden;
    padding: 72px 0 36px;
    text-align: center;
    display: flex;
    flex-direction: column;
    background: transparent linear-gradient(0deg, #f5ead8 0%, #b0dfe1 100%) 0% 0% no-repeat padding-box;
  }
  @media (min-width: 1024px) {
    .product-finder__quiz {
      position: sticky;
      top: 0px;
      padding: 0;
      height: 100vh;
      width: 36%;
    }
  }
  .product-finder__decoration-melin {
    height: 100%;
    left: 0;
    position: absolute;
    width: 100%;
    z-index: -1;
    background: url(https://melin.com/cdn/shop/files/Logo_Background_Mask_Group_2.png?v=1680501437) right center no-repeat;
  }
  .product-finder__decoration {
    display: none;  
  }
  @media (max-width: 1024px) {
    .product-finder__decoration {
      display: none;
    }
    .product-finder__decoration-melin {
        height: 72px;
        bottom: -36px;
      }
  }
  .product-finder__quiz-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 2;
  }
  .product-finder__quiz-progress {
    background: rgba(255, 255, 255, 0.5);
  }
  .product-finder__quiz-progress-bar {
    height: 10px;
    background-color: var(--content-color);
    transition: width 300ms ease-in-out;
  }
  .product-finder__quiz-navigation {
    display: flex;
    justify-content: space-between;
  }
  .product-finder__quiz-navigation nav {
    position: relative;
    width: 50%;
    margin: 1.5rem 0 0 1rem;
  }
  .product-finder__quiz-navigation nav button {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    color: var(--content-color);
    font-family: GTA-Bold, Arial, Helvetica, sans-serif;
    font-weight: bold;
    font-size: 13px;
    text-transform: uppercase;
  }
  .product-finder__quiz-navigation nav button span {
    margin-top: 1px;
  }
  .product-finder__quiz-pagination {
    display: flex;
    justify-content: end;
    color: var(--content-color);
    font-family: GTA-Bold, Arial, Helvetica, sans-serif;
    font-weight: bold;
    font-size: 13px;
    margin: 1.5rem 2rem 0 0;
    padding: 5px;
  }
  .product-finder__quiz-pagination-number {
    margin: 0 1px;
  }
  .product-finder__quiz-panes {
    display: block;
    background: transparent !important;
  }
  [product-finder="fit_finder"] .product-finder__quiz-pane {
    height: 100%;
    width: 100%;
    padding: 0 79px;
    position: absolute;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    background: rgba(0,0,0,0);
  }
  @media (max-width: 1024px) {
    .product-finder__quiz-pane.active {
      position: relative;
      padding: 36px 2rem 48px;
    }
  }
  .product-finder__question, .product-finder__prompt {
    color: var(--content-color);
    font-family: GTA-Bold, Arial, Helvetica, sans-serif;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 14px;
  }
  @media (min-width: 1024px) {
    .product-finder__question, .product-finder__prompt {
      font-size: 21px;
    }
  }
  .product-finder__instruction {
    color: var(--content-color);
    font-size: 12px;
    margin-bottom: 12px;
  }
  .product-finder__options {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto;
    grid-gap: 8px;
  }
  @media (max-width: 1024px) {
    .product-finder__options {
      grid-template-columns: 1fr;
    }
  }
  .product-finder__options--compact {
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  }
  .product-finder__options input[type=checkbox] {
    display: none;
  }
  .product-finder__options label {
    background: #fff;
    border: 1px solid #dddddd;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--content-color);
    font-family: GTA-Bold, Arial, Helvetica, sans-serif;
    text-transform: uppercase;
    font-weight: bold;
    font-size: 13px;
    height: 45px;
  }
  .product-finder__options label:hover {
    border: 2px solid var(--content-color);
  }
  .product-finder__options input[type=checkbox]:checked + label {
    background: var(--content-color);
    color: #fff;
  }
  .product-finder__options input[type=checkbox]:disabled + label,
  .product-finder__options input[type=checkbox][disabled] + label {
    display: none;
  }
  .product-finder__disclaimer {
    color: #6f6e6e;
    font-size: 12px;
    margin-bottom: 12px;
    max-width: 75%;
    line-height: 1.5;
  }
  .product-finder__restart {
    cursor: pointer;
    color: var(--content-color);
    font-family: GTA-Bold, Arial, Helvetica, sans-serif;
    font-size: 15px;
    letter-spacing: 0.6px;
    text-decoration: underline;
    margin-bottom: 12px;
    display: inline-block;
  }
  @media (min-width: 1024px) {
    .product-finder__restart {
      margin-top: 10vh;
    }
  }
  .product-finder form {
    width: 100%;
    margin-bottom: 2rem;
  }
  .product-finder form input[type=email] {
    width: 100%;
    text-indent: 1rem;
    margin-bottom: 12px;
    font-size: 15px;
    padding: 12px 0;
    border-radius: 3px;
  }
  .product-finder form input[type=email]:placeholder {
    color: #797979;
  }
  .product-finder form button {
    display: flex;
    width: 100%;
    text-transform: uppercase;
    height: 45px;
    border-radius: 5px;
    align-items: center;
    justify-content: center;
    color: #fff;
    background-color: var(--content-color);
    font-family: GTA-Bold, Arial, Helvetica, sans-serif;
    font-size: 15px;
    letter-spacing: 0.6px;
  }
  .product-finder form button[disabled] {
    opacity: 0.8;
  }
  .product-finder form p {
    display: none;
  }
  .product-finder form.submitted input,
  .product-finder form.submitted button {
    display: none;
  }
  .product-finder form.submitted p {
    display: block;
  }
  @media (min-width: 1024px) {
    .product-finder__products {
      width: 64%;
    }
  }
  .product-finder__products .swiper-button {
    margin: 0;
  }
  .product-finder__product-grid {
    display: none;
    grid-template-rows: auto 1fr;
    grid-template-columns: 1fr 1fr;
    grid-gap: 20px;
    padding: 20px;
  }
  @media (min-width: 1024px) {
    .product-finder__product-grid {
      grid-template-columns: 1fr 1fr 1fr;
      grid-gap: 24px;
      padding: 55px;
      padding-bottom: 75vh;
    }
  }
  .product-finder__product-grid .product-tile__image-container {
    background: #f9f3ea;
    border-bottom: 0;
  }
  .product-finder__product-grid .product-tile__title {
    font-size: 12px;
    line-height: 22px;
    color: #797979;
  }
  .product-finder__product-grid .product-tile__type {
    font-size: 12px;
    color: #797979;
  }
  .product-finder__selection-header {
    display: none;
    padding: 26px 55px 0px 55px;
  }
  [product-finder="fit_finder"] .product-finder__back {
	border: 2px solid #231f20;
  	color: #231f20;
  	background: none;
    font-family: "Avenir Black", Arial, Helvetica, sans-serif;
	text-transform: uppercase;
	padding: 8px 12px;
    border-radius: 1000px;
    font-size: 13px;
    display: flex;
    align-items: center;
    font-weight: 700;
  }
  @media (max-width: 1024px) {
    [product-finder="fit_finder"] .product-finder__back {
	  background: #231f20;
	  color: #fff;
    }
  }
  @media (min-width: 1024px) {
    [product-finder="fit_finder"] .product-finder__back:hover {
	  background: #231f20;
  	  color: #fff;
    }
  }
  .product-finder__back svg {
    margin-right: 6px;
  }
  .product-finder__back span {
    margin-bottom: -1px;
  }
  .product-finder__recommendation-header {
    display: none;
  }
  .product-finder__product-detail {
    display: none;
  }
  .product-finder[data-state=grid] .product-finder__product-grid {
    display: grid;
  }
  .product-finder__sticky-header {
    display: none;
    position: sticky;
    top: 0;
    z-index: 6;
    background: #fff;
    padding: 0px 20px 0 20px;
    height: 55px;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0px 5px 5px 0px rgba(173, 173, 173, 0.5);
  }
  .product-finder__sticky-header nav {
    height: 16px;
  }
  .product-finder[data-state=recommend] .product-finder__recommendation-header {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 55px;
    text-transform: uppercase;
  }
  .product-finder[data-state=recommend] .product-finder__product-detail {
    display: block;
  }
  @media (max-width: 1024px) {
    .product-finder[data-state=select] .product-finder__sticky-header {
      display: flex;
    }
  }
  .product-finder[data-state=select] .product-finder__selection-header {
    display: flex;
  }
  @media (max-width: 1024px) {
    .product-finder[data-state=select] .product-finder__selection-header {
      justify-content: center;
    }
  }
  .product-finder[data-state=select] .product-finder__product-detail {
    display: block;
  }
  .product-finder .transitional-element {
    opacity: 0;
    pointer-events: none;
    visibility: hidden;
    transform: translateY(1rem);
    transition: all 300ms ease-in-out;
  }
  .product-finder .transitional-element.active {
    opacity: 1;
    pointer-events: all;
    visibility: visible;
    transform: translateY(0rem);
  }
  .product-finder .product-summary__row {
    margin: 0;
    padding: 22px;
  }
  .product-finder .product-summary__row .desktop:hover .swiper-button {
    opacity: 1;
    visibility: visible;
  }
  .product-finder .product-summary__row .desktop:hover .swiper-button.swiper-button-prev {
    left: 15px;
  }
  .product-finder .product-summary__row .desktop:hover .swiper-button.swiper-button-next {
    right: 15px;
  }
  .product-finder .product-summary__row-toggle {
    padding: 0 22px 12px 22px !important;
  }
  .product-finder .product-summary__row-toggle > span:first-of-type {
    font-size: 15px;
    line-height: 18px;
    display: inline-block;
  }
  .product-finder .product-summary__row-toggle > span:last-of-type span:first-child {
    display: inline-block;
  }
  .product-finder .product-summary__row-toggle > span:last-of-type span:last-child {
    display: none;
  }
  .product-finder .product-summary__row-toggle.active > span:last-of-type span:first-child {
    display: none;
  }
  .product-finder .product-summary__row-toggle.active > span:last-of-type span:last-child {
    display: inline-block;
  }
  .product-finder .product-summary__row--bottom {
    border-top: none;
  }
  @media (max-width: 1024px) {
    .product-finder .product-summary__row--bottom {
      flex-direction: column;
    }
  }
  .product-finder .product-summary__row .product-fit-guide__header-link {
    display: none;
  }
  @media (max-width: 1024px) {
    .product-finder .product-summary__row > article {
      padding: 0;
    }
  }
  .product-finder .product-summary__row .product-fit-guide {
    padding-bottom: 20px;
  }
  .product-finder .product-bundle__header {
    background: #eeece1;
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    padding: 22px;
    align-items: center;
  }
  @media (max-width: 1024px) {
    .product-finder .product-bundle__header {
      padding: 0;
      flex-wrap: wrap;
    }
  }
  .product-finder .product-bundle__header h1 {
    font-size: 18px;
    letter-spacing: -0.38px;
    line-height: 33px;
  }
  .product-finder .product-bundle__header > div {
    font-size: 13px;
    letter-spacing: 0px;
    line-height: 15px;
    margin-top: 4px;
  }
  @media (max-width: 1024px) {
    .product-finder .product-bundle__header > div {
      margin: 10px 0 0;
    }
    .product-finder .product-bundle__header > div:nth-child(1), .product-finder .product-bundle__header > div:nth-child(2) {
      padding: 0 22px;
    }
  }
  .product-finder .product-bundle__price {
    font-size: 13px;
    letter-spacing: 0;
    line-height: 17px;
  }
  @media (min-width: 1024px) {
    .product-finder .product-bundle__price {
      font-size: 20px;
      letter-spacing: -0.02px;
      line-height: 24px;
    }
  }
  .product-finder .product-bundle__value {
    color: #e36662;
    font-size: 13px;
    letter-spacing: 0px;
    line-height: 15px;
    margin-top: 4px;
  }
  .product-finder .product-bundle__actions {
    margin-left: 20px;
  }
  @media (max-width: 1024px) {
    .product-finder .product-bundle__actions {
      width: 100%;
      flex-shrink: 0;
    }
  }
  .product-finder .product-bundle__buy, .product-finder .product-bundle__select-prompt {
    border-radius: 4px;
    max-width: 100%;
    width: 124px;
    text-transform: uppercase;
  }
  @media (max-width: 1024px) {
    .product-finder .product-bundle__buy, .product-finder .product-bundle__select-prompt {
      padding: 0;
      width: 100%;
      border-radius: 0;
      height: 46px;
    }
  }
  @media (min-width: 1024px) {
    .product-finder .product-bundle__buy, .product-finder .product-bundle__select-prompt {
      font-size: 15px;
      height: 50px;
      letter-spacing: 1.5px;
      max-width: 100%;
      width: 250px;
    }
  }
  .product-finder .product-bundle__buy--disabled, .product-finder .product-bundle__select-prompt--disabled {
    pointer-events: none;
    opacity: 0.6;
  }
  .product-finder .product-bundle__select-prompt {
    pointer-events: none;
    opacity: 0.6;
  }
  .product-finder [product-detail] .product-summary {
    padding: 2rem;
  }
  .product-finder [product-detail] .product-summary__image {
    background-color: var(--color-light);
    mix-blend-mode: multiply;
  }

  .product-bundle ~ form.product__info,
  .product-bundle ~ .product__row--shipping-sku {
    display: none;
  }

  .product-finder__parent + .product-summary {
    display: none;
  }



[product-finder="fit_finder"] > aside.product-finder__quiz {
	background: transparent linear-gradient(210deg, #E2E2E2 0%, #FFFFFF 100%) 0 0 no-repeat padding-box;
	article.initial-finder-slide {
		align-items: flex-start;
		padding: 0 54px;
		p.product-finder__initial_slide_copy {
			text-align: left;
			max-width: 260px;
			font-size: 13px;
			line-height: 21px;
			margin-bottom: 30px;
			font-family: "Avenir Medium", Arial, Helvetica, sans-serif;
		}
		a.product-finder__continue {
			font-size: 16px;
			text-transform: uppercase;
			letter-spacing: 1.6px;
			line-height: 22px;
			text-align: center;
			border: 1px solid #000;
			padding: 8px 20px;
			cursor: pointer;
			border: 2px solid #231F20;
			border-radius: 3px;
			font-family: "Avenir Medium", Arial, Helvetica, sans-serif;
			&:hover {
				background: #E3E3E3;
			}
		}
		p.product-finder__question {
			text-transform: capitalize;
		}
	}
	article.imagebox-finder-slide {
		p.product-finder__question {
			font-size: 20px;
			margin-bottom: 8px;
		}
		p.product-finder__instruction {
			margin-bottom: 16px;
			font-family: "Avenir Medium", Arial, Helvetica, sans-serif;
			color: #000;
			font-size: 13px;
			line-height: 21px;
		}
		.product-finder__options {
			display: flex;
			justify-content: center;
			flex-wrap: wrap;
			label {
				img {
					width: 59%;
					margin-bottom: 15px;
				}
				border: 2px solid #000;
				border-radius: 3px;
				font-family: "Avenir Medium", Arial, Helvetica, sans-serif;
				line-height: 22px;
				background: none;
				width: 48%;
				font-size: 16px;
				text-transform: capitalize;
				letter-spacing: 1.6px;
				font-weight: 500;
				color: #000;
				display: flex;
				flex-direction: column;
				height: auto;
				padding: 10px 0 16px;
				&:hover {
					background: #E3E3E3;
				}
			}
		}
	}
	article.selectbox-finder-slide {
		.product-finder__options {
			display: flex;
			justify-content: center;
			flex-wrap: wrap;
			label {
				background: none;
				border: 2px solid #000;
				border-radius: 3px;
				padding: 11px 16px;
				color: #000;
				font-size: 16px;
				text-transform: capitalize;
				width: 100%;
				font-family: "Avenir Medium", Arial, Helvetica, sans-serif;
				line-height: 22px;
				margin-bottom: 8px;
				height: auto;
				font-weight: 500;
				letter-spacing: 1.6px;
				&:hover {
					background: #E3E3E3;
				}
			}
		}
		p.product-finder__instruction {
			font-family: "Avenir Medium", Arial, Helvetica, sans-serif;
			color: #000;
			font-size: 13px;
			line-height: 21px;
		}
		p.product-finder__question {
			margin-bottom: 8px;
			font-size: 20px;
			line-height: 21px;
		}
	}
	a.product-finder__not_sure {
		background: none;
		border: 2px solid #000;
		border-radius: 3px;
		padding: 11px 16px;
		color: #000;
		font-size: 16px;
		letter-spacing: 1.6px;
		font-family: "Avenir Black", Arial, Helvetica, sans-serif;
		text-transform: capitalize;
		width: 100%;
		font-weight: 700;
		display: block;
		cursor: pointer;
	}
	article.recommendation-finder-slide {
		align-items: center;
		p.product-finder__prompt {
			font-size: 20px;
			line-height: 21px;
			margin-bottom: 21px;
		}
		form {
			width: 90%;
			margin-bottom: 16px;
			input {
				border: 0;
				border-radius: 3px;
				padding: 11px 0;
				margin-bottom: 16px;
				&::placeholder {
					font-size: 16px;
					letter-spacing: 1.6px;
					font-weight: 500;
					color: #858383;
				}
			}
			button {
				opacity: 1;
				background: #000;
				border-radius: 3px;
				color: #F8F8F9;
				padding: 11px;
				font-size: 16px;
				letter-spacing: 1.6px;
				line-height: 22px;
			}
		}
		p.product-finder__disclaimer {
			font-size: 13px;
			line-height: 21px;
			color: #000;
			font-family: "Avenir Medium", Arial, Helvetica, sans-serif;
			margin-bottom: 12px;
		}
		a.product-finder__restart {
			margin: 0;
			font-size: 17px;
			font-family: "Avenir Black", Arial, Helvetica, sans-serif;
			font-weight: bold;
			letter-spacing: 0;
			color: #000;
			line-height: 21px;
		}
	}
	header.product-finder__quiz-header {
		.product-finder__quiz-progress-bar {
			background: #3E3E3E;
		}
		.product-finder__quiz-navigation {
			svg {
				color: #231F20;
			}
			span {
				font-size: 12px;
				line-height: 18px;
				color: #231F20;
				text-transform: capitalize !important;
				font-family: "Avenir Black", Arial, Helvetica, sans-serif;
			}
		}
	}
}


[product-finder="fit_finder"] .product-finder__question {
	font-size: 21px;
	color: #000;
	font-family: "Avenir Black", Arial, Helvetica, sans-serif;
}
[product-finder="fit_finder"] .product-finder__prompt {
	font-size: 21px;
	color: #000;
	font-family: "Avenir Black", Arial, Helvetica, sans-serif;
}
[product-finder="fit_finder"] .perfect-fit {
	display: flex;
	align-items: center;
	padding-bottom: 25px;
	.perfect-fit-model {
		margin-right: 16px;
		img {
			width: 160px;
		}
	}
	.perfect-fit-detail {
		h3 {
			font-size: 16px;
			text-align: left;
			margin: 0 0 6px;
			line-height: 21px;
			font-family: "Avenir Black", Arial, Helvetica, sans-serif;
		}
		.cal_form {
			.form-control {
				input {
					width: 49%;
					height: 54px;
					border: 2px solid #fff;
					padding: 10px;
					border-radius: 3px;
					margin-right: 16px;
					font-family: "Avenir Medium", Arial, Helvetica, sans-serif;
					box-shadow: none;
					&::placeholder {
						font-size: 16px;
						letter-spacing: 1.6px;
						font-family: "Avenir Medium", Arial, Helvetica, sans-serif;
						line-height: 22px;
						color: #D8D7D8;
					}
					&:-webkit-autofill {
						border: 0 !important;
						box-shadow: 0 0 0px 1000px #fff inset !important;
					}
				}
				select {
					background: none;
					border-radius: 3px;
					border: 2px solid #858383;
					padding: 10px;
					height: 54px;
					width: 45%;
					font-size: 16px;
					background-image: url('https://cdn.shopify.com/s/files/1/1175/0278/files/select-box-dropdown-melin-PRO.png?v=1680183445');
					background-repeat: no-repeat;
					background-position: 90% center;
				}
				margin-bottom: 8px;
				display: flex;
				input.error {
					border: 2px solid #E73950;
				}
			}
			button {
				font-size: 16px;
				line-height: 22px;
				text-transform: uppercase;
				color: #fff;
				padding: 11px 28px;
				border: 0;
				font-family: "Avenir Medium", Arial, Helvetica, sans-serif;
				background-color: #231F20;
				border-radius: 3px;
				width: 100%;
			}
			button[disabled] {
				background: #858383;
			}
		}
	}
}
[product-finder="fit_finder"] article.imagebox-finder-slide.color-option-slide.active {
	.product-finder__options {
		label {
			font-size: 12px;
			line-height: 16px;
			color: #231F20;
		}
	}
}
[product-finder="fit_finder"] .rec-calc-size {
	>p {
		margin: 0 0 8px;
		font-size: 12px;
		line-height: 21px;
		color: #000;
		font-family: "Avenir Light", Arial, Helvetica, sans-serif;
	}
	h2 {
		margin: 0 0 16px;
		font-size: 20px;
		line-height: 21px;
		color: #000;
		font-family: "Avenir Black", Arial, Helvetica, sans-serif;
		text-transform: uppercase;
	}
	button {
		font-size: 16px;
		line-height: 22px;
		text-transform: uppercase;
		color: #fff;
		padding: 11px 28px;
		border: 0;
		background: #000000;
		border-radius: 3px;
		width: 100%;
		font-family: "Avenir Medium", Arial, Helvetica, sans-serif;
		letter-spacing: 1.6px;
	}
	input[type=checkbox] {
		display: none;
	}
	>label {
		color: #fff;
		text-transform: uppercase;
		margin-bottom: 8px;
		height: auto;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		padding: 11px 28px;
		font-size: 16px;
		line-height: 22px;
		border: 0;
		background: #231F20;
		border-radius: 3px;
		width: 100%;
		font-family: "Avenir Medium", Arial, Helvetica, sans-serif;
		letter-spacing: 1.6px;
	}
	p.availability_error { 
		color: #e73950;
		font-family: Avenir Black,Arial,Helvetica,sans-serif;
		font-size: 14px;
	}
}
[product-finder="fit_finder"] main.product-finder__products {
	.product-finder__product-detail {
		article {
			footer.product-summary__actions {
				button.product-summary__button.product-summary__button--secondary {
					&:hover {
						background: none;
					}
					border-radius: 3px;
					font-size: 16px;
					line-height: 22px;
					letter-spacing: 1.6px;
					color: #3E3E3E;
					font-family: 'Avenir Medium';
					border: 2px solid #363636;
					height: auto;
					padding: 15px;
					margin-bottom: 10px;
				}
				button.product-summary__button.product-summary__button--primary {
					background: #231F20;
					height: auto;
					padding: 15px;
					font-size: 16px;
					line-height: 22px;
					letter-spacing: 1.6px;
					font-family: 'Avenir Medium';
					border-radius: 3px;
				}
			}
		}
		.product-summary__row--bottom {
			article.product-summary__enunciation {
				h2.product-header__title {
					font-size: 16px;
					line-height: 22px;
					font-family: 'Avenir Black';
					letter-spacing: 0.8px;
					text-transform: capitalize;
				}
				.product__enunciation {
					padding-top: 8px;
					p {
						color: #231F20;
						font-size: 14px;
						line-height: 22px;
						font-family: 'Avenir Medium';
						letter-spacing: 0;
					}
				}
				padding-left: 22px;
			}
			max-width: 830px;
			margin: 0 auto;
			border-top: 1px solid #707070;
			article.product-summary__fit-guide.product-summary__fit-guide_custom {
				section.product-fit-guide {
					padding: 0;
				}
			}
			article.product-summary__fit-guide {
				.product-fit-guide {
					h2.product-fit-guide-header__title {
						font-size: 16px;
						line-height: 22px;
						font-family: 'Avenir Black';
						letter-spacing: 0.8px;
						text-transform: capitalize;
					}
					header.product-fit-guide-header {
						margin-bottom: 8px;
					}
					.product-fit-guide__label {
						font-size: 16px;
						letter-spacing: 0.8px;
						line-height: 22px;
						color: #231F20;
						margin-bottom: 8px;
					}
					.product-fit-guide__description {
						font-size: 14px;
						line-height: 22px;
						font-family: 'Avenir Medium';
						color: #231F20;
					}
					.product-fit-guide__link-holder {
						margin-top: 0;
						margin-bottom: 16px;
						a.product-fit-guide__link {
							color: #231F20;
							font-size: 14px;
							font-family: 'Avenir Light';
							line-height: 22px;
						}
					}
					.product-fit-guide__row {
						margin: 0;
						flex-direction: column;
						p.product-fit-guide__title {
							font-size: 16px;
							line-height: 22px;
							letter-spacing: 0.8px;
							color: #231F20;
							font-family: 'Avenir Medium';
							margin-bottom: 8px;
						}
						.product-fit-guide__track-holder {
							width: 100%;
							.product-fit-guide__value-names {
								margin-top: 5px;
							}
						}
					}
				}
				.product-fit-guide__value-names {
					.product-fit-guide__value-name {
						letter-spacing: 1.3px;
						color: #231F20;
						line-height: 18px;
					}
				}
			}
			article {
				.product-fit-guide__track-holder {
					.product-fit-guide__progress {
						background: transparent linear-gradient(90deg, #7A7878 0%, #201D1D 100%) 0 0 no-repeat padding-box;
						border-radius: 7px;
						height: 14px;
					}
					.product-fit-guide__track {
						height: 14px;
						border-radius: 7px;
					}
				}
			}
		}
		.product-summary__row {
			padding: 32px 56px;
		}
	}
	section.pro_finder_wrap {
		.product-tile-wrap {
			.product-tile__container {
				button.product-tile__quick-add {
					display: none !important;
				}
				.product-tile__quick-add-mobile {
					display: block !important;
				}
				.product-tile__image-container {
					background: none;
					border-bottom: 1px solid #707070;
				}
			}
			a.product-tile__info {
				h3.product-tile__title {
					font-size: 14px;
					margin-bottom: 4px;
					font-weight: bold;
					color: #000;
					line-height: 18px;
					font-family: "Avenir Black", Arial, Helvetica, sans-serif;
				}
			}
		}
	}
	background: #EDEDED;
	.product-summary__image {
		img {
			background: none;
		}
	}
	.product__colors {
		a.product__list-item--color {
			border: 0;
			background: #fff;
		}
	}
}
[product-finder="fit_finder"] article.finding-perfect-fit {
	.perfect-fit {
		.cal_form {
			margin-bottom: 0;
		}
	}
	align-items: center;
	.product-finder__title {
		font-size: 20px;
		line-height: 21px;
		font-weight: 900;
		font-family: "Avenir Black", Arial, Helvetica, sans-serif;
		margin-bottom: 12px;
	}
	.product-finder__info {
		text-align: left;
		margin-bottom: 4px;
		ul {
			li {
				font-size: 13px;
				line-height: 18px;
				margin-bottom: 10px;
				font-family: "Avenir Medium", Arial, Helvetica, sans-serif;
				padding-right: 25px;
				margin-left: 15px;
				list-style: decimal;
				&:last-child {
					list-style: none;
					padding-right: 0;
				}
				&::marker {
					font-family: "Avenir Black", Arial, Helvetica, sans-serif;
				}
			}
		}
	}
	.rec-calc-size {
		width: 100%;
	}
	.finding-perfect-fit-wrap {
		.product-finder__title {
			text-align: left;
		}
		opacity: 0;
		height: 0;
		pointer-events: none;
		visibility: hidden;
		-webkit-transform: translateY(1rem);
		transform: translateY(1rem);
		-webkit-transition: all .3s ease-in-out;
		transition: all .3s ease-in-out;
	}
	.finding-perfect-fit-wrap.active {
		opacity: 1;
		pointer-events: all;
		height: auto;
		visibility: visible;
		-webkit-transform: translateY(0rem);
		transform: translateY(0);
	}
}
[product-finder="fit_finder"] article.imagebox-finder-slide.color-option-slide {
	label {
		padding: 16px 0;
	}
}
[product-finder="fit_finder"] p.cal_error {
	color: #E73950;
	font-family: "Avenir Black", Arial, Helvetica, sans-serif;
}
@media only screen and (min-width:1480px) {
	[product-finder="fit_finder"] main.product-finder__products {
		.product-finder__product-detail {
			.product-summary__row {
				padding: 32px 0;
				margin: 0 56px;
			}
			.product-summary__row--bottom {
				max-width: 100%;
				padding: 32px 20px;
			}
		}
	}
}
@media only screen and (max-width:1024px) and (min-width:768px) {
	[product-finder="fit_finder"] aside.product-finder__quiz {
		article.imagebox-finder-slide {
			.product-finder__options {
				label {
					img {
						width: 15%;
					}
				}
			}
		}
	}
	[product-finder="fit_finder"] .product-finder__decoration-melin {
		background: url('https://cdn.shopify.com/s/files/1/1175/0278/files/responseLogo_Background_Mask_Group_2.png?v=1680764136') right 78% no-repeat;
	}
}
@media only screen and (max-width:767px) {
	[product-finder="fit_finder"] aside.product-finder__quiz {
		article.imagebox-finder-slide {
			.product-finder__options {
				label {
					img {
						width: 40%;
					}
				}
			}
		}
		padding: 92px 0 48px;
	}
	[product-finder="fit_finder"] main.product-finder__products {
		.product-finder__product-detail {
			.product-summary__row {
				padding: 0;
				.product__colors {
					padding-left: 0;
					h3.product__current {
						margin-left: 0;
						margin-left: 0;
						font-size: 14px;
						font-family: 'Avenir Medium';
						color: #231F20;
						line-height: 22px;
					}
				}
			}
			.product-summary__row--bottom {
				article.product-summary__fit-guide.product-summary__fit-guide_custom {
					padding-bottom: 45px;
				}
				border: 0;
				article.product-summary__enunciation {
					padding-top: 0;
					padding-bottom: 0;
				}
			}
		}
		header.product-finder__sticky-header {
			display: none !important;
		}
	}
    [product-finder="fit_finder"] .product-finder__decoration {
		display: none;
	}
	[product-finder="fit_finder"] .product-finder__decoration-melin {
		background-size: contain !important;
		background: url('https://cdn.shopify.com/s/files/1/1175/0278/files/responseLogo_Background_Mask_Group_2.png?v=1680764136') bottom no-repeat;
		height: 260px;
	}
}
@media only screen and (max-width:576px) {
	[product-finder="fit_finder"] main.product-finder__products {
		section.pro_finder_wrap {
			.product-tile-wrap {
				.product-tile__container {
					.product-tile__quick-add-mobile {
						svg {
							height: 40px;
							width: 40px;
						}
						right: 0;
					}
				}
			}
		}
	}
}


[product-finder="fit_finder"] p.product-finder__instruction {
	font-style: italic;
}
[product-finder="fit_finder"] main.product-finder__products {
	.product__colors {
		h3.product__current {
			color: #000;
		}
	}
	.product-summary__row {
		.product__list {
			a.product__list-item {
				background: none;
			}
		}
	}
}

[product-finder="fit_finder"] .rec-calc-size p.availability_error {
    color: #e73950;
    font-family: Avenir Black,Arial,Helvetica,sans-serif;
    font-size: 14px;
}
[product-finder="fit_finder"] section.no-recommendation {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: 600;
	text-align: center;
	width: 100%;
}

[product-finder="fit_finder"] .product-finder__options input[type=checkbox]:disabled+label, .product-finder__options input[type=checkbox][disabled]+label{
    display: block;
}

[product-finder="fit_finder"] .initial-finder-slide .product-finder__instruction{
    text-align: left;
    max-width: 260px;
    font-size: 13px;
    line-height: 21px;
    margin-bottom: 30px;
    font-family: Avenir Medium,Arial,Helvetica,sans-serif;
    font-style: normal !important;
}

[product-finder="fit_finder"] aside.product-finder__quiz .product-finder__options {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

[product-finder="fit_finder"] aside.product-finder__quiz .product-finder__options label {
    background: none;
    border: 2px solid #000;
    border-radius: 3px;
    padding: 11px 16px;
    color: #000;
    font-size: 16px;
    text-transform: capitalize;
    width: 100%;
    font-family: Avenir Medium,Arial,Helvetica,sans-serif;
    line-height: 22px;
    margin-bottom: 8px;
    height: auto;
    font-weight: 500;
    letter-spacing: 1.6px;
    cursor: pointer;
}

[product-finder="fit_finder"] aside.product-finder__quiz .product-finder__options label:hover {
    background: #e3e3e3;
}

[product-finder="fit_finder"] .perfect-fit .perfect-fit-detail .cal_form .form-control select{
    background: none;
    border-radius: 3px;
    border: 2px solid #858383;
    padding: 10px;
    height: 54px;
    width: 45%;
    font-size: 16px;
    background-image: url(https://melin.com/cdn/shop/files/select-box-dropdown-melin-PRO.png?v=1680183445);
    background-repeat: no-repeat;
    background-position: 90% center;
    appearance: none;
}

[product-finder="fit_finder"] .product-item__title{
    font-size: 14px;
    margin-bottom: 4px;
    font-weight: 700;
    color: #000;
    line-height: 18px;
    font-family: Avenir Black,Arial,Helvetica,sans-serif;
}

[product-finder="fit_finder"] .product-item__images{
    background: none;
    border-bottom: 1px solid #707070;
}

.no-results-message{
    text-align: center;
}
.no-results-message div{
    margin-top: 220px;
    font-size: 16px;
    font-weight: 700;
    color: #000;
    line-height: 18px;
    font-family: Avenir Black,Arial,Helvetica,sans-serif;
}
[product-finder="fit_finder"] .product-finder__quiz-pagination{
    display:none;
}
[product-finder="fit_finder"] .product-finder__options input[type="checkbox"]:disabled + label, .product-finder__options input[type="checkbox"][disabled] + label{
	display: none;
}