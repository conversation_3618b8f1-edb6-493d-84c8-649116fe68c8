{%- capture checkout_header -%}
  <a href="/" class="checkout__logo" aria-label="{{ shop.name }} Logo">
    <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/ xlink" x="0px" y="0px" width="155px" height="19px" viewBox="0 0 155 19" style="enable-background:new 0 0 155 19;" xml:space="preserve">
      <g>
        <path d="M0.202,0.071v18.858h17.02h17.02V0.071l-17.02,8.422L0.202,0.071z M9.13,16.693H3.492V5.352L9.13,8.047V16.693zM16.49,16.693h-5.914V8.738l5.914,2.827V16.693z M25.314,8.047l5.639-2.695v11.341h-5.639V8.047z M17.955,11.565l5.914-2.827v7.956h-5.914V11.565z"/>
        <path d="M119.509,6.447h0.002c1.188-0.003,2.161-0.922,2.166-2.053c-0.005-1.125-0.978-2.043-2.168-2.046c-1.19,0.003-2.162,0.921-2.168,2.051C117.346,5.526,118.319,6.445,119.509,6.447z"/>
        <path d="M146.626,4.539c-2.368-1.318-5.248-1.986-8.559-1.986c-3.292,0-6.176,0.676-8.572,2.009c-2.528,1.398-3.81,3.163-3.81,5.245v9.051h4.385v-9.05c0.002-1.049,0.686-1.921,2.152-2.744c1.605-0.897,3.572-1.351,5.845-1.351c2.263,0,4.212,0.454,5.796,1.351c1.488,0.825,2.183,1.698,2.187,2.744v9.051h4.383v-9.05C150.438,7.714,149.157,5.94,146.626,4.539z"/>
        <path d="M109.811,15.518c-0.148-0.089-0.148-0.098-0.154-0.23V2.372h-4.343v12.916c0,0.417,0.027,0.968,0.23,1.521c0.226,0.654,0.658,1.174,1.253,1.505c0.927,0.503,2.045,0.547,3.206,0.549h0.001c0.178,0,0.356-0.001,0.531-0.002l3.097-0.002v-3.141h-2.574C110.226,15.717,109.886,15.56,109.811,15.518z"/>
        <rect x="117.323" y="9.034" width="4.38" height="9.812"/>
        <path d="M97.848,4.536c-2.368-1.316-5.235-1.983-8.52-1.983c-3.291,0-6.174,0.676-8.568,2.009c-2.492,1.375-3.806,3.19-3.802,5.246v2.047c-0.005,2.096,1.274,3.868,3.819,5.278c2.384,1.192,5.26,1.797,8.551,1.797c3.411,0,6.359-0.652,8.76-1.937l1.316-0.703l-3.657-2.028l-0.389,0.24c-1.661,0.976-3.69,1.471-6.03,1.471c-2.224,0-4.198-0.448-5.855-1.326c-1.219-0.687-1.902-1.406-2.108-2.233h20.33V9.808C101.698,7.696,100.402,5.922,97.848,4.536z M81.368,9.253c0.209-0.808,0.891-1.513,2.107-2.189c1.611-0.897,3.581-1.352,5.853-1.352c2.268,0,4.217,0.454,5.798,1.351c1.204,0.665,1.901,1.385,2.116,2.189H81.368z"/>
        <path d="M70.553,4.129c-1.84-1.046-4.071-1.576-6.629-1.576c-1.653,0-3.186,0.222-4.554,0.659c-1.131,0.354-2.1,0.822-2.885,1.394c-0.802-0.571-1.772-1.039-2.888-1.393c-1.362-0.438-2.891-0.66-4.543-0.66c-2.617,0-4.851,0.532-6.638,1.582c-1.968,1.145-2.966,2.55-2.966,4.178v10.546h4.342l0-10.586L43.79,8.188c-0.002-0.551,0.43-1.063,1.319-1.563c1.086-0.605,2.414-0.912,3.945-0.912c1.516,0,2.829,0.306,3.905,0.911c1.224,0.689,1.365,1.294,1.365,1.689v10.546h4.334V8.248c0.035-0.398,0.226-0.995,1.405-1.643c1.056-0.591,2.355-0.892,3.861-0.892c1.498,0,2.784,0.298,3.826,0.89c1.181,0.649,1.366,1.245,1.397,1.641v10.615h4.381V8.247C73.53,6.629,72.5,5.204,70.553,4.129z"/>
        <polygon points="149.989,3.26 150.704,3.26 150.704,5.351 151.3,5.351 151.3,3.26 152.018,3.26 152.018,2.712 149.989,2.712 	"/>
        <path d="M154.634,2.712h-0.625l-0.454,1.233c-0.043,0.121-0.08,0.23-0.113,0.331c-0.031-0.101-0.068-0.21-0.108-0.331L152.9,2.712h-0.625l-0.184,2.639h0.581l0.071-1.144c0.004-0.059,0.007-0.12,0.01-0.181c0.01,0.03,0.021,0.062,0.031,0.094l0.414,1.217h0.435l0.45-1.234c0.016-0.046,0.033-0.091,0.048-0.135c0.004,0.074,0.008,0.146,0.011,0.215l0.068,1.168h0.588L154.634,2.712z"/>
      </g>
    </svg>
  </a>

  <div class="checkout-breadcrumbs breadcrumb-container">
    <div class="breadcrumb-container__wrap">{{ breadcrumb }}</div>
  </div>
{%- endcapture -%}

{%- capture icon -%}
{%- comment -%}don't adjust whitespace{%- endcomment -%}
          <svg width="20" height="19" xmlns="http://www.w3.org/2000/svg" class="order-summary-toggle__icon">
            <path d="M17.178 13.088H5.453c-.454 0-.91-.364-.91-.818L3.727 1.818H0V0h4.544c.455 0 .91.364.91.818l.09 1.272h13.45c.274 0 .547.09.73.364.18.182.27.454.18.727l-1.817 9.18c-.09.455-.455.728-.91.728zM6.27 11.27h10.09l1.454-7.362H5.634l.637 7.362zm.092 7.715c1.004 0 1.818-.813 1.818-1.817s-.814-1.818-1.818-1.818-1.818.814-1.818 1.818.814 1.817 1.818 1.817zm9.18 0c1.004 0 1.817-.813 1.817-1.817s-.814-1.818-1.818-1.818-1.818.814-1.818 1.818.814 1.817 1.818 1.817z" />
          </svg>
{%- endcapture -%}

{%- capture new_icon -%}
  {%- render 'checkout-melin-icon-bag', fill: '#000000' -%}
{%- endcapture -%}

{%- capture total_span -%}
  <span class="order-summary__emphasis total-recap__final-price skeleton-while-loading"></span>
{%- endcapture -%}

{%- capture new_total_span -%}
  <span class="order-summary__currency-code">{{ checkout.currency }}</span><span class="order-summary__emphasis total-recap__final-price skeleton-while-loading"></span>
{%- endcapture -%}

<!doctype html>
  <!--[if IE 9]> <html class="ie9 no-js supports-no-cookies {{ checkout_html_classes }}" lang="{{ locale }}" dir="{{ direction }}"> <![endif]-->
  <!--[if (gt IE 9)|!(IE)]><!--> <html class="no-js supports-no-cookies {{ checkout_html_classes }}" lang="{{ locale }}" dir="{{ direction }}"> <!--<![endif]-->
  <head>
    {% if settings.melin_google_optimize_id != blank %}
      <!-- Anti-flicker snippet (recommended)  -->
      <style>.async-hide { opacity: 0 !important} </style>
      <script>(function(a,s,y,n,c,h,i,d,e){s.className+=' '+y;h.start=1*new Date;
      h.end=i=function(){s.className=s.className.replace(RegExp(' ?'+y),'')};
      (a[n]=a[n]||[]).hide=h;setTimeout(function(){i();h.end=null},c);h.timeout=c;
      })(window,document.documentElement,'async-hide','dataLayer',4000,
      {'{{ settings.melin_google_optimize_id }}':true});</script>

      <script type="text/javascript">
        /**
        * Please ensure a cookie named "optVal" is set with a random value between 1-10
        * This should be set from the server to ensure no issues from ITP rules or similar
        */
        (function() {
          function optGetCookie(cname) {
            var name = cname + "=";
            var decodedCookie = decodeURIComponent(document.cookie);
            var ca = decodedCookie.split(';');
            for (var i = 0; i < ca.length; i++) {
              var c = ca[i];
              while (c.charAt(0) == ' ') {
                c = c.substring(1);
              }
              if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
              }
            }
            return "";
          }
          var optSetCrossDomainCookie = function(name, value, days) {
            var expires;
            if (days) {
              var date = new Date();
              date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
              expires = "; expires=" + date.toGMTString();
            } else {
              expires = "";
            }
            document.cookie = name + "=" + value + expires + "; path=/; domain={{ request.host | replace: 'www.', '' }}";
          };
          var retrieve = optGetCookie("optVal");
          if (!retrieve) {
            var optVal = Math.floor(Math.random() * 10) + 1;
            optSetCrossDomainCookie("optVal", optVal, 1000);
          }
        })();
      </script>
      <script src="https://www.googleoptimize.com/optimize.js?id={{ settings.melin_google_optimize_id }}"></script>
    {% endif %}

    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','{{ settings.melin_gtm_container_id }}');</script>
    <!-- End Google Tag Manager -->

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1 , user-scalable=0">
    <meta name="theme-color" content="{{ settings.color_primary }}">
    <link rel="canonical" href="{{ canonical_url }}">
    <title>{{ page_title }}</title>

    {% if settings.favicon_light != blank %}
      <!-- Favicon Light Mode -->
      <link rel="icon" type="image/png" href="{{ settings.favicon_light | img_url: '64x64' }}" sizes="192x192">
      <link rel="shortcut icon" href="{{ settings.favicon_light | img_url: '64x64' }}o" type="image/x-icon">
      <link rel="appletouchicon" href="{{ settings.apple_touch_light | img_url: '180x180' }}">
      <meta name="msapplicationsquare70x70logo" content="{{ settings.ms_square_sm_light | img_url: '70x70' }}">
      <meta name="msapplicationsquare150x150logo" content="{{ settings.ms_square_md_light | img_url: '150x150' }}">
      <meta name="msapplicationsquare310x310logo" content="{{ settings.ms_square_lg_light | img_url: '310x310' }}">
      <meta name="msapplicationwide310x150logo" content="{{ settings.ms_rec_sm_light | img_url: '310x150' }}">
      <meta name="msapplicationTileColor" content="{{ settings.ms_fav_tile_color_light }}">
      <script>
        var darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        handleDarkmode(darkModeMediaQuery);
        function handleDarkmode(e) {
          var darkModeOn = e.matches; // true if dark mode is enabled
          var favicon = document.querySelector('link[rel="shortcut icon"]');
          var largeFavicon = document.querySelector('link[rel="icon"]');
          var appletouchicon = document.querySelector('link[rel="appletouchicon"]');
          var square70x70logo = document.querySelector('meta[name="msapplicationsquare70x70logo"]');
          var square150x150logo = document.querySelector('meta[name="msapplicationsquare150x150logo"]');
          var square310x310logo = document.querySelector('meta[name="msapplicationsquare310x310logo"]');
          var wide310x150logo = document.querySelector('meta[name="msapplicationwide310x150logo"]');
          var TileColor = document.querySelector('meta[name="msapplicationTileColor"]');
          if (!favicon || !largeFavicon) {
            return;
          }
          // replace icons with dark/light themes as appropriate
          if (darkModeOn) {
            favicon.href = '{{ settings.favicon_dark | img_url: "64x64" }}';
            largeFavicon.href = '{{ settings.favicon_dark | img_url: "64x64" }}';
            appletouchicon = '{{ settings.apple_touch_dark | img_url: "180x180" }}';
            square70x70logo = '{{ settings.ms_square_sm_dark | img_url: "70x70" }}';
            square150x150logo = '{{ settings.ms_square_md_dark | img_url: "150x150" }}';
            square310x310logo = '{{ settings.ms_square_lg_dark | img_url: "310x310" }}';
            wide310x150logo = '{{ settings.ms_rec_sm_dark | img_url: "310x150" }}';
            TileColor.content = '{{ settings.ms_fav_tile_color_dark }}';
          } else {
            favicon.href = '{{ settings.favicon_light | img_url: "64x64" }}';
            largeFavicon.href = '{{ settings.favicon_light | img_url: "64x64" }}';
            appletouchicon = '{{ settings.apple_touch_light | img_url: "180x180" }}';
            square70x70logo = '{{ settings.ms_square_sm_light | img_url: "70x70" }}';
            square150x150logo = '{{ settings.ms_square_md_light | img_url: "150x150" }}';
            square310x310logo = '{{ settings.ms_square_lg_light | img_url: "310x310" }}';
            wide310x150logo = '{{ settings.ms_rec_sm_light | img_url: "310x150" }}';
            TileColor.content = '{{ settings.ms_fav_tile_color_light }}';
          }
        }
        darkModeMediaQuery.addListener(handleDarkmode);
      </script>
    {% endif %}

    <style data-styles="custom-properties">
      :root {
        --button-text-payment: "{{ 'shopify.checkout.general.continue_to_payment_method' | t }}";
        --button-text-shipping: "{{ 'shopify.checkout.general.continue_to_shipping_method' | t }}";
        --button-text-complete: "{{ 'shopify.checkout.general.complete_purchase_button_label' | t }}";
        --button-text-thanks-continue: "{{ 'shopify.checkout.thank_you.return_to_store_link_label' | t }}";
      }
    </style>

    <style>
      @font-face {
        font-family: 'Avenir Black';
        src: url("{{ 'Avenir-Black.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: normal;
      }

      @font-face {
        font-family: 'Avenir Medium';
        src: url("{{ 'Avenir-Medium.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: normal;
      }

      @font-face {
        font-family: 'Avenir Book';
        src: url("{{ 'Avenir-Book.woff' | asset_url }}") format('woff');
        font-style: normal;
        font-weight: normal;
      }

      .shipping_address_notice {
        padding: 0.5285714286em;
        margin-bottom: 10px;
      }

      label.checkbox__label b {
        color: #000000;
        font-family: "Avenir Book", Arial, Helvetica, sans-serif;
        font-size: 13px;
        line-height: 19px;
      }

      label.checkbox__label p,
      label.checkbox__label a {
        line-height: 16px;
        font-size: 11px;
        color: #444444;
      }
    </style>

    {% if false %}
      {{ content_for_header }}
    {% endif %}

    {{ content_for_header | replace: "<body onload='document._boomrl();'>", "<bodx onload='document._boomrl();'>" }}
    {{ checkout_stylesheets }}

    {% render 'checkout-melin-css' %}

    {{ checkout_scripts }}
    <script src="https://code.jquery.com/jquery-3.7.0.min.js" integrity="sha256-2Pmvv0kuTBOenSvLm6bvfBSSHrUJ+3A7x6P5Ebd07/g=" crossorigin="anonymous"></script>

    <script>
      window.freeShipping = ''

      {% if settings.melin_free_shipping_amount != blank %}
        {% assign amount = settings.melin_free_shipping_amount | split: '.' %}

        {% if amount != blank %}
          window.freeShipping = {
            amount: '{{ amount[0] | plus: 0 | times: 100 }}'
          };
        {% endif %}
      {% endif %}

      document.documentElement.className.replace('no-js', 'js');

      window.theme = {
        strings: {
          addToCart: {{ 'products.product.add_to_cart' | t | json }},
          soldOut: {{ 'products.product.sold_out' | t | json }},
          unavailable: {{ 'products.product.unavailable' | t | json }}
        },
        moneyFormat: {{ shop.money_format | json }}
      };

      window.exponeaConsents = [{{ settings.melin_exponea_consents }}];
      window.exponeaSmsConsents = [{{ settings.melin_sms_exponea_consents }}];
    </script>

    {%- comment -%} Exponea Initilization Script {%- endcomment -%}
    {% render 'checkout-exponea' %}
    
    {% if settings.melin_gtm_container_id != blank %}
      {% include 'checkout-melin-analyzify-gtm-checkout' %}
    {% endif %}
  </head>

  <body class="template-checkout">
    <script>
      if ({{ settings.melin_enable_gift_order | json }} && window.Shopify.Checkout.hasOwnProperty('isOrderStatusPage') && window.location.hash.includes('#gift=')) {
        document.body.classList.add('gift-wrap')
      }
    </script>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ settings.melin_gtm_container_id }}"
    height="0" width="0" style="display:none;visibility:hidden" title="Google tag manager"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

    {{ skip_to_content_link }}

    <div class="content checkout__content" data-content>
      <div class="wrap">
        <header class="checkout__header checkout__header--mobile">
          {{ checkout_header }}
        </header>

        {{ order_summary_toggle | replace: icon, new_icon | replace: total_span, new_total_span }}

        <div class="main" role="main">
          <div class="checkout__main-inner">
            <header class="checkout__header checkout__header--desktop">
              {{ checkout_header }}
            </header>

            <div class="main__header">{{ alternative_payment_methods }}</div>

            <div class="main__content">
              {{ content_for_layout }}
            </div>

            {% render 'checkout-melin-footer' %}

          </div>
        </div>

        <div class="sidebar" role="complementary">
          <div class="checkout__sidebar-inner">
            <div class="sidebar__content">
              {%- capture sidebar_header -%}
                <header class="sidebar__cart-header">
                  <span class="sidebar__cart-header-title">Your Bag</span>
                </header>
              {%- endcapture -%}
              {{ content_for_order_summary | replace: '<h2 class="visually-hidden-if-js">Order summary</h2>', sidebar_header }}
            </div>
          </div>
        </div>
      </div>
    </div>

    {{ tracking_code }}
    {% render 'checkout-plus_show_discount' plus_show_discount:settings.melin_plus_show_discount_enable %}
    {% if settings.melin-pobox-blocker_enable %}
      {% render 'checkout-melin-pobox' %}
    {% endif %}
    <script>
      /* set window var to used in checkout.js */
      window.totalPrice = {{ checkout.total_price | amount_with_apostrophe_separator }} / 100;

      window.giftOrder = {
        "customer": {{ checkout.customer.name | json }},
        "icon": {{ settings.melin_gift_svg | json }},
        "display": {{ settings.melin_enable_gift_order | json }},
        "checkoutToken": {{ order.id | md5 | json }},
        "orderName": {{ checkout.order_name | json }},
        "orderStatusUrl": {{ order.order_status_url | json }}
      }
    </script>
    <script src="{{ 'bundle.vendor.js' | asset_url }}"></script>
    <script src="{{ 'vendor.js' | asset_url }}"></script>
    <script src="{{ 'theme.js' | asset_url }}"></script>
    <script src="{{ 'bundle.index.js' | asset_url }}"></script>

    {% if settings.melin_site_mode != "vip" and shop.domain != 'melin.ca' %}
      <script src="{{ 'bundle.vip.js' | asset_url }}"></script>
    {% else %}
      {% render 'checkout-giftcard-message' %}
      <script src="{{ 'bundle.checkout.js' | asset_url }}"></script>
    {% endif %}

    {% if settings.melin_site_mode == 'standard' and settings.vip_redirect_modal_enabled %}
      {% render 'checkout-melin-redirecttovip-message' %}
      <script src="{{ 'bundle.redirecttovip_checkout.js' | asset_url }}"></script>
    {% endif %}

    {% render 'checkout-melin-newsletter' %}

    {% if settings.melin_estimated_shipping_enabled %}
      {% render 'checkout-melin-shipping-dates' %}
    {% endif %}

    {% if settings.melin_address_validation_enable %}
      {% render 'checkout-melin-address-validation' %}
    {% endif %}

    <script>
      $(document).ready(function() {
        $('[name="checkout[reduction_code]"]').attr('autocorrect', 'off');
      });

      {% if checkout.customer %}
        exponea.identify({
          {% if checkout.customer.id %}"registered": {{ checkout.customer.id | json }},{% endif %}
          {% if checkout.customer.email %}"email_id": {{ checkout.customer.email | json }}{% endif %}
        });

        document.addEventListener('DOMContentLoaded', function() {
          var emailField = document.querySelector('[data-shopify-pay-email-flow]');
          if (emailField && emailField.classList.contains('field--error')) {
            // don't update if email field exists and has errors
          } else {
            exponea.update({
              {% if checkout.customer.email %}"email": {{ checkout.customer.email | json }},{% endif %}
              {% if checkout.customer.first_name %}"first_name": {{ checkout.customer.first_name | json }},{% endif %}
              {% if checkout.customer.last_name %}"last_name": {{ checkout.customer.last_name | json }},{% endif %}
              {% if shop %}"domain": {{ shop.permanent_domain|json }}{% endif %}
            });
          }
        });

        document.addEventListener('page:load', function() {
          if (Shopify && Shopify.hasOwnProperty('Checkout') && Shopify.Checkout.hasOwnProperty('step')) {
            exponea.track('checkout', {
              referrer: document.referrer,
              location: window.location.href,
              path: window.location.pathname,
              step: Shopify.Checkout.step
            });
          }

          var phone = $('#checkout_shipping_address_phone');

          if (phone.length) {
            $('form.edit_checkout').on('submit', function() {
              var number = phone.val();

              if (number === '') return

              exponea.update({
                phone: number
              });
            });
          }
        });
      {% endif %}

      $(document).on('page:load page:change', function() {
        if ($('.shipping__notice')[0]) {} else {
          if (Shopify.Checkout.step == "shipping_method") {
            $('<p class="shipping__notice">{{ settings.melin_shipping_text_message }}</p>').insertBefore('[data-shipping-methods]');
          }
        }

        if ($('.shipping_address_notice')[0]) {} else {
          if (Shopify.Checkout.step == "contact_information") {
            $('<p class="shipping_address_notice">{{ settings.melin_shipping_address_notice }}</p>').insertBefore('[data-address-fields]');
          }
        }

        if ($('.shipping__notice')[0]) {} else {
          if (Shopify.Checkout.step == "shipping_method") {
            $('<p class="shipping__notice">{{ settings.melin_shipping_text_message }}</p>').insertBefore('[data-shipping-methods]');
          }
        }
      });

      $(document).ready(function() {
        var cookie = window.Cookies.get('gc_value');

        if (typeof cookie !== 'undefined') {
          $('#checkout_reduction_code').val(cookie);
          $('[data-trekkie-id="apply_discount_button"]').attr('disabled', false).removeClass('btn--disabled').click();
        }
      })

      $(document).ready(function() {
        $('.product__description__property.order-summary__small-text').each(function() {
          if ($(this).html().indexOf('giftwrap: 0') > -1) {
            $(this).html('');
          } else {
            $(this).html('Gift Wrap');
          }
        });
      })

      if (Shopify.Checkout.step == 'contact_information') {
        document.addEventListener('DOMContentLoaded', function() {
          function formatPhoneNumber(value) {
            var phoneNumber = value.replace(/[^\d]/g, "")
            var phoneNumberLength = phoneNumber.length

            if (phoneNumberLength < 4) return phoneNumber

            if (phoneNumberLength < 7) return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3)}`;

            return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
          }

          var sms_form_input_text = document.querySelector('[data-phone-formatter]');

          if (sms_form_input_text) {
            // Format the phone number in input on keyup
            sms_form_input_text.addEventListener('keyup', function() {
              var phoneValue = sms_form_input_text.value;
              var output;
              phoneValue = phoneValue.replace(/[^0-9]/g, '');
              var area = phoneValue.substr(0, 3);
              var pre = phoneValue.substr(3, 3);
              var tel = phoneValue.substr(6, 4);
              if (area.length < 3) {
                output = "(" + area;
              } else if (area.length == 3 && pre.length < 3) {
                output = "(" + area + ")" + " " + pre;
              } else if (area.length == 3 && pre.length == 3) {
                output = "(" + area + ")" + " " + pre + "-"+tel;
              }
              sms_form_input_text.value = output;
            })
          }

          $('[data-phone-formatter]').on('keydown', function() {
            $(this).val(formatPhoneNumber($(this).val()))
          })
        })
      }

      {% if settings.melin_site_mode == 'vip' %}
        $(document).ready(function() {
          if ($('#order-summary .tags-list').length > 0) {
            var str = $('#order-summary .tags-list .tag__text .reduction-code span[aria-hidden=true]').text();
            var pattern = new RegExp("^•••• ....$");
            var result = pattern.test(str);

            if ($('#order-summary .payment-due__price').attr('data-checkout-payment-due-target') > 0 && result) {
              $('#order-summary .tag__button').click();
            }
          }
        });
      {% endif %}
    </script>

    {%- if settings.melin_enable_consent_checkout -%}
      {%- render 'checkout-melin-sms-checkout' -%}
    {%- endif -%}

    {% if settings.melin_site_mode == 'standard' and settings.melin_vip_redirect_modal_enabled %}
      <input type="hidden" name="vip_startswith_array" id="vip_startswith_array" value="{{ settings.melin_vip_startswith_array }}">
      <input type="hidden" name="vip_redirect_url" id="vip_redirect_url" value="{{ settings.melin_vip_redirect_url }}">
    {% endif %}

    {% render 'checkout-melin-avant-tracking-checkout' %}

    {% if settings.melin_site_mode == 'standard' %}
      {% render 'checkout-melin-hide-messages' %}
    {% endif %}

    <script>
      document.addEventListener('page:load', function() {
        var afterpay_available = false;
        var checkout_total = {{ checkout.total_price | json }};
        if (checkout_total && checkout_total >= 3500 && checkout_total <= 100000) {
          {%- if settings.melin_afterpay_enabled -%}
            afterpay_available = true;
          {%- endif -%}
        }
        var paymentMethodSection = document.querySelector('.section--payment-method');

        function checkAfterpayRange(afterpay_available) {
          var images = paymentMethodSection.getElementsByTagName('img');

          for (i = 0, len = images.length; i < len; i++) {
            // check image exist
            if (images[i]) {
              var altContent = images[i].alt;

              // check if the image is Afterpay Image
              if (altContent.length && (altContent.toLowerCase() == 'afterpay' || altContent.toLowerCase() == 'afterpay north america')) {
              // console.log("AFTERPAY: found AU or North America Gateway");
                if (afterpay_available) {
                  // replace the image so its not blurry on hi-res (retina screens)
                  images[i].parentElement.innerHTML = '<p style="display: inline;">Installments by </p><img class="afterpay-logo" src="https://static.afterpay.com/integration/product-page/logo-afterpay-colour.png" srcset="https://static.afterpay.com/integration/product-page/logo-afterpay-colour.png 1x, https://static.afterpay.com/integration/product-page/<EMAIL> 2x, https://static.afterpay.com/integration/product-page/<EMAIL> 3x" width="100" height="21" alt="Afterpay" style="vertical-align: middle; width: 100px;">';
                } else {
                  // Disable afterpay radio button and place afterpay messaging
                  images[i].parentElement.parentElement.parentElement.innerHTML = '<p class="afterpay-paragraph"><span class="afterpay-text1">Installments by </span><img class="afterpay-logo" src="https://static.afterpay.com/integration/product-page/logo-afterpay-colour.png" srcset="https://static.afterpay.com/integration/product-page/logo-afterpay-colour.png 1x, https://static.afterpay.com/integration/product-page/<EMAIL> 2x, https://static.afterpay.com/integration/product-page/<EMAIL> 3x" width="100" height="21" alt="Afterpay" style="vertical-align: middle; width: 100px;"><span class="afterpay-text2"> available between </span><strong class="afterpay-instalments">$35 - $1000</strong></p>';

                  var afterpayParentRow = images[i].parentElement.parentElement.parentElement;
                  var afterpayInputs = afterpayParentRow.getElementsByTagName('input');

                  // If Afterpay is the initial selection
                  if (afterpayInputs[0].checked) {
                    // set the selected method to the first row
                    var inputs = paymentMethodSection[0].getElementsByTagName('input');

                    for (j = 0, len = inputs.length; j < len; j++) {
                      if (
                        inputs[j].type == 'radio' &&
                        inputs[j].parentElement.parentElement !== afterpayParentRow
                      ) {
                        inputs[j].checked = 'checked';
                        return;
                      }
                    }
                  }
                }
              }
            }
          }
        }

        if (paymentMethodSection) {
          checkAfterpayRange(afterpay_available);
        }
      });
    </script>
    {% render 'checkout-olukai-gift-order' %}
  </body>
</html>
