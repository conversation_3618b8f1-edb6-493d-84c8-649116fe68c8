import 'regenerator-runtime/runtime';
import './main.scss';
import './globals.scss';
import './theme.scss';
import './fit-finder.scss';

import '../../components/carousels/carousels';
import '../../components/forms/forms';
import '../../components/cart'
import '../../components/customer'
import '../../components/customer.segmentation'
import '../../components/offers'
import '../../components/track'
import '../../components/wishlist'
import '../../components/quick-add'
import '../../components/sections'
import '../../components/preload'
import '../../components/aria'
import '../../components/focus'
import '../../components/modal'
import '../../components/loyalty'
import '../../components/async-video';

import '../../components/@integrations/alpine'

import '../../components/@integrations/archipelago'


import '../../components/@integrations/reviewsio'
import '../../components/@integrations/mParticle'

import Alpine from 'alpinejs'
import mask from '@alpinejs/mask'
import intersect from '@alpinejs/intersect'
Alpine.plugin(mask)
Alpine.plugin(intersect)
window.Alpine = Alpine

window.addEventListener('DOMContentLoaded', Alpine.start)

Alpine.directive('import', (el, { modifiers, expression }, { evaluateLater, effect }) => {
    
    let moveElement = evaluateLater(expression)
 
    effect(() => {
        moveElement(querySelector => { 
            if(!!document.querySelector(querySelector)) {
              if(modifiers.includes('beforeparent')){ 
                el.parentNode.before(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('afterparent')){ 
                el.parentNode.after(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('before')){ 
                el.before(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('after')){ 
                el.after(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('inner')){ 
                el.innerHTML = document.querySelector(querySelector).innerHTML
              }
            }
        })
    })

})

Alpine.directive('max', (el, { modifiers, expression }, { evaluateLater, effect }) => {
    
    let max = evaluateLater(expression)
 
    effect(() => {
        moveElement(querySelector => { 
            if(!!document.querySelector(querySelector)) {
              if(modifiers.includes('beforeparent')){ 
                el.parentNode.before(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('afterparent')){ 
                el.parentNode.after(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('before')){ 
                el.before(document.querySelector(querySelector), el.parentNode)
              } else if(modifiers.includes('after')){ 
                el.after(document.querySelector(querySelector), el.parentNode)
              }
            }
        })
    })

})



window.money = {
	currencies: {
    default:{label:true},
    USD:{symbol:'$'},
    CAD:{symbol:'$',label:'CAD'},
    EUR:{symbol:'€'},
    JPY:{symbol:'¥'}
  },
  format: v => {
  	const currency = money.currencies[Shopify.currency.active] || money.currencies.default;
  	money.currency = currency
  	let val = `${currency.symbol ? currency.symbol : ``}${(typeof v== 'string'&&v.includes('.')) ? v : (v / 100).toFixed(2)}${currency.label ? ` ${Shopify.currency.active}`:``}`.replace('.00','') 
  	
    return val
  }
}

window.image = {
  format: (url, config={} ) => {
    if(!url) return '';
    return `${url.split('?')[0]}?${Util.urlparams.build(config)}`;
  }
}


window.addEventListener('QuickAdd:opened', loadReviewsIoRatingSnippets)



