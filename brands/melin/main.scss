@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	@import './helpers.scss';
  @import './globals.scss';
  @import './fit-finder.scss';
}

@layer utilities {

	.scroll-snap-none {
		scroll-snap-type: none;
	}
	.scroll-snap-x {
		scroll-snap-type: x mandatory;
	}
	.scroll-snap-y {
		scroll-snap-type: y mandatory;
	}
	
	.snap-align-center > * {
		scroll-snap-align: center;
	}
	.snap-align-start > * {
		scroll-snap-align: start;
	}
	.snap-align-end > * {
		scroll-snap-align: end;
	}
	.flex-slider.snap-align-none > * {
		scroll-snap-align: none;
	}

	.no-scrollbar {
		overflow: -moz-scrollbars-none;
		-ms-overflow-style: none;
	}
	.no-scrollbar::-webkit-scrollbar { 
		width: 0 !important; 
		background-color: transparent;
		height: 0 !important
	}
	.no-scrollbar::-webkit-scrollbar-track {
		background-color: transparent;
	}
	.no-scrollbar::-webkit-scrollbar-thumb {
		background-color: transparent;
	}
}

.icon svg {
	width: 100%;
}
.product-form__option .product-form__option-label-wrapper {
	display:flex;
	justify-content: space-between;
	
	margin-bottom: 0.625rem;
    padding-left: 1.333rem;
    padding-right: 1.333rem;
}
.product-form__option .product-form__option-label-wrapper div{
	display: flex;
}
.product-form__option .product-form__option-label-wrapper .button--micro-link {
	margin-bottom: 0px;
    padding-left: 0px;
    padding-right: 0px;
}  

.product-form__option .product-form__option-label-wrapper .product-form__option-selected{
	margin-bottom: 0px;
    padding-left: 0px;
    padding-right: 10px;
}

.field__buttons .field__button.field__buttons--colors-swatch{
	border-radius:50%;
	aspect-ratio: 1;
	padding:0px;
	max-width: 42px;
	max-height: 42px;
}
.product-form__option--color .field__buttons--colors-swatch.field__button:has(input:checked){
	border-radius:50% !important;
}
@media (max-width: 1024px) {
	.field__buttons.field__buttons--colors-swatch{
		gap:9px !important;
	}
	.field__buttons.field__buttons--colors.field__buttons--colors-wrap {
		margin-left: 0rem;
		margin-right: 0rem;

	}
	.field__buttons.field__buttons--colors.field__buttons--colors-wrap .field__button:first-of-type {
		margin-left: 0rem;
	}
	.product-form__option--color .field__button.field__buttons--colors-only {
		height: auto;
	}
}
